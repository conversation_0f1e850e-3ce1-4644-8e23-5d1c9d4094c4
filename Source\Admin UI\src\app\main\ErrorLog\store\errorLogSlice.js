import { createSlice } from '@reduxjs/toolkit';
import axios from 'axios';
import { decrypt, encrypt } from '../../../security/crypto';
import { showMessage } from 'app/store/fuse/messageSlice';

export const InsertErrorLog = (data) => async dispatch => {
    try {
        await axios.post(`admin/api/errorlogs/InserErrorLog`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status === 200) {
                }
            });
    } catch (e) {
        return console.error(e.message);
    }
};

export const UpdateIsResolvedFlag = (data, pagingDetails) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/errorlogs/updateResolvedFlag`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status === 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    if (response.isSucess) {
                        dispatch(showMessage({
                            message: "Error Resolved",
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    }
                    dispatch(getErrorLogSearchData(
                        pagingDetails.pageIndex, pagingDetails.rowsPerPage,
                        pagingDetails.selectedFromDate, pagingDetails.selectedToDate,
                        pagingDetails.search, pagingDetails.source, pagingDetails.isResolvedValue, pagingDetails.agency,
                        pagingDetails.sortField,
                        pagingDetails.sortDirection
                    ));
                }
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};


//for searching
export const getErrorLogSearchData = (pageIndex, pageLimit, selectedFromDate,
    selectedToDate, searchText, searchSource, isResolved, agency, sortField, sortDirection) => async dispatch => {
        dispatch(setLoading(true));
        try {
            var data = {
                pageIndex: pageIndex,
                pageLimit: pageLimit,
                selectedFromDate: selectedFromDate,
                selectedToDate: selectedToDate,
                searchText: searchText,
                searchSource: searchSource,
                isResolved: isResolved,
                agency: agency,
                sortField: sortField,
                sortDirection: sortDirection
            }
            axios.post(`admin/api/errorlogs/searchDateWiseErrorLogs`, encrypt(JSON.stringify(data)))
                .then(response => {
                    dispatch(setLoading(false));
                    if (response.status == 200) {
                        let data = JSON.parse(decrypt(response.data));
                        dispatch(setErrorLogTotalCount(data.totalCount))
                        return dispatch(setErrorLog(data.filteredErrorLogs));
                    }
                    else {
                        dispatch(setLoading(false));
                        dispatch(setErrorLogTotalCount(0))
                        return dispatch(setErrorLog([]));
                    }
                });
        } catch (e) {
            dispatch(setLoading(false));
            return console.error(e.message);
        }
    }

const initialState = {
    success: false,
    data: [],
    isloading: false,
    totalCount: 0,
    filterAgencyValue: "",
};

const errorLogSlice = createSlice({
    name: 'errorLog',
    initialState,
    reducers: {
        errorSuccess: (state, action) => {
            state.success = true;
        },
        setErrorLog: (state, action) => {
            state.data = action.payload;
        },
        setErrorLogTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
    },
    extraReducers: {}
});

export const {
    errorInsertSuccess,
    setErrorLog,
    setErrorLogTotalCount,
    setLoading,
} = errorLogSlice.actions;

export default errorLogSlice.reducer;
