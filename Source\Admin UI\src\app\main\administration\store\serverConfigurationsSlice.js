import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';

export const getServerConfigurations = (agencyCode) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/serverConfigurations/` + agencyCode)
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    return dispatch(setServerConfigurations(JSON.parse(decrypt(response.data))))
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const setServerConfigurationSearchText = (event) => async dispatch => {
    try {
        return dispatch(setSearchText(event.target.value));
    } catch (e) {
        return console.error(e.message);
    }
}

export const saveServerConfiguration = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/serverConfigurations/serverConfiguration`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))

                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//To set Agency ID
export const setServerConfigurationID = (serverConfigurationID) => async dispatch => {
    try {
        return dispatch(setServerConfigurationId(serverConfigurationID));
    } catch (e) {
        return console.error(e.message);
    }
}

//To get Agency By ID
export const getServerConfigurationByID = (_id) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/serverConfigurations/serverConfigurationGetByID/` + _id)
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    return dispatch(setServerConfigurationBYID(JSON.parse(decrypt(response.data))));
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))

                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

// To update Server Configuration
export const updateServerConfiguration = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/serverConfigurations/serverConfigurationEdit`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    // history.push("/admin/serverconfiguration");
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),

                );
            });
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    data: [],
    searchText: '',
    serverConfigurationID: "0",
    serverConfigurationData: [],
    isloading: false
};

const serverConfigurationsSlice = createSlice({
    name: 'administration/serverConfigurations',
    initialState,
    reducers: {
        setServerConfigurations: (state, action) => {
            state.data = action.payload;
        },
        setSearchText: (state, action) => {
            state.searchText = action.payload;
        },
        setServerConfigurationId: (state, action) => {
            state.serverConfigurationID = action.payload;
        },
        setServerConfigurationBYID: (state, action) => {
            state.serverConfigurationData = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
    },
    extraReducers: {}
});

export const { setServerConfigurations,
    setSearchText,
    setServerConfigurationId,
    setServerConfigurationBYID,
    setLoading
} = serverConfigurationsSlice.actions;

export default serverConfigurationsSlice.reducer;
