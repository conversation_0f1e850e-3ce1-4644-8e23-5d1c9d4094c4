import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import Button from '@mui/material/Button';
import { Controller, useForm } from 'react-hook-form';
import { showMessage } from 'app/store/fuse/messageSlice';
import { useDispatch, useSelector } from 'react-redux';
import * as XLSX from 'xlsx';
import { importUsers } from '../../administration/store/usersSlice'
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import "./AddMultipleUserDialog.css";
import Divider from '@mui/material/Divider';
import { selectMainTheme, selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import { checkValueEmptyOrNull } from '../../utils/utils';
import DisplayMultipleUserList from './DisplayMultipleUserList';
import { InputLabel, Grid } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import Box from '@mui/material/Box';
import Select from '@mui/material/Select';
import FormControlLabel from '@mui/material/FormControlLabel';

import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import FormGroup from '@mui/material/FormGroup';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';

const AddMultipleUserDialog = forwardRef((props, ref) => {
    const userRef = useRef();
    const user = useSelector(({ auth }) => auth.user);

    const [open, setOpen] = React.useState(false);
    const { t } = useTranslation('laguageConfig');
    const fileUpload = useRef(null);
    const dispatch = useDispatch();
    const navbarTheme = useSelector(selectNavbarTheme);
    const [fileData, setFileData] = React.useState();
    const [defaultApp, setDefaultApp] = React.useState();
    const [defaultAgency, setDefaultAgency] = React.useState(null);
    let file = user.data.ImportUserTemplateUrl
    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
    });
    const mainTheme = useSelector(selectMainTheme);

    const defaultApplications = [
        { id: 1, name: t('call911') },
        { id: 2, name: t('dispatch') },
        { id: 3, name: t('incident') },
        { id: 4, name: t('quikTip') }
    ]

    useEffect(() => {
        setDefaultAgency(null)
        setDefaultApp(null)
    }, []);


    const handleClickOpen1 = () => {
        setOpen(true);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen() {
            handleClickOpen1();
        },
    }));

    const handleClose = () => {
        setDefaultAgency(null)
        setDefaultApp(null)
        setOpen(false);
    };

    function uploadMultipleFiles(e) {
        const files = e.target.files;
        if (files && files[0]) {
            setFileData(files[0]);
        }
    };


    const downloadExcelFile = (data) => {
        const a = document.createElement('a');
        a.href = file;
        a.download = file;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };


    function onSubmit() {
        if (defaultAgency) {
            /* Boilerplate to set up FileReader */
            const reader = new FileReader();
            const rABS = !!reader.readAsBinaryString;
            reader.onload = (e) => {
                /* Parse data */
                const bstr = e.target.result;
                const wb = XLSX.read(bstr, { type: rABS ? 'binary' : 'array', bookVBA: true });
                /* Get first worksheet */
                const wsname = wb.SheetNames[0];
                const ws = wb.Sheets[wsname];
                /* Convert array of arrays */
                const data = XLSX.utils.sheet_to_json(ws);
                /* Update state */
                dispatch(importUsers(data, defaultAgency, defaultApp))
                userRef.current.handleClickOpen()
                handleClose()
            };

            if (rABS) {
                reader.readAsBinaryString(fileData);
            } else {
                reader.readAsArrayBuffer(fileData);
            };
        }
        else {
            ShowErroMessage("You must select an agency")
        }
    }


    const handleDefaultAppChange = (event, newValue) => {
        setDefaultApp(newValue)
    }

    const handleDefaultAgencyChange = (event, newValue) => {
        setDefaultAgency(newValue);
    }

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    return (
        <div >
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
                id='add-multiple-user-dialog'
            >
                <div style={{ backgroundColor: mainTheme.palette.background.paper }}>
                    <IconButton
                        onClick={handleClose}
                        aria-label="show more"
                        className='flex justify-end mr-14 ml-[94%] float-right'
                        sx={{
                            color: mainTheme.palette.mode === 'light' ? 'black' : 'white'
                        }}
                    >
                        <Icon>close</Icon>
                    </IconButton>
                    <DialogTitle id="responsive-dialog-title" sx={{
                        color: mainTheme.palette.mode === 'light' ? 'black' : 'white'
                    }}>{t("importUsers")}</DialogTitle>
                    <Divider style={{ backgroundColor: "black" }} />
                    <DialogContent dividers>
                        <form
                            className="flex flex-col justify-center w-full pb-16"
                            onSubmit={handleSubmit(onSubmit)}
                            // autoComplete="off"
                            autoSave={false}
                        >

                            <Grid container spacing={1} >
                                <Grid item xs={12} sm={12} md={12} lg={4} xl={4}>
                                    <input
                                        type="file"
                                        ref={fileUpload}
                                        accept=".xlsx, .xls, .csv"
                                        style={{
                                            padding: '13px',
                                            border: '1px solid lightgray',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            height: '53px',
                                            width: '93%',
                                            marginRight: '2%'
                                        }}
                                        onChange={uploadMultipleFiles}
                                    />

                                </Grid>
                                <Grid item xs={12} sm={12} md={12} lg={4} xl={4}>
                                    <Box sx={{ minWidth: 120 }}>
                                        {props.agencyList &&
                                            <CommonAutocomplete
                                                value={defaultAgency || null}
                                                parentCallback={handleDefaultAgencyChange}
                                                options={props.agencyList || []}
                                                fieldName={t("defaultAgency")}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        }
                                    </Box>
                                </Grid>
                                <Grid item xs={12} sm={12} md={12} lg={4} xl={4}>
                                    <Box sx={{ minWidth: 120 }}>
                                        <FormControl className="mb-16 w-full">
                                            <CommonAutocomplete
                                                value={defaultApp || null}
                                                parentCallback={handleDefaultAppChange}
                                                options={defaultApplications || []}
                                                fieldName={t("defaultApplication")}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />

                                        </FormControl>
                                    </Box>

                                </Grid>
                            </Grid>


                            <div className='flex justify-end'>
                                {/* {checkValueEmptyOrNull(file) !== "" &&
                                <CloudDownloadIcon onClick={() => downloadExcelFile(fileData)} style={{ fontSize: '40px', width: '5%' }} />
                            } */}
                                <Button
                                    variant="contained"
                                    color="primary"
                                    className=" w-auto mr-16 mt-8"
                                    aria-label="Register"
                                    type="button"
                                    size="large"
                                    onClick={() => downloadExcelFile(fileData)}
                                >
                                    {t("downloadTemplate")}
                                </Button>
                                <Button
                                    variant="contained"
                                    color="primary"
                                    className=" w-auto mr-16 mt-8"
                                    aria-label="Register"
                                    type="submit"
                                    size="large"
                                >
                                    {t("import")}
                                </Button>
                                <Button
                                    variant="contained"
                                    color="secondary"
                                    className=" w-auto mt-8"
                                    aria-label="Register"
                                    type="button"
                                    size="large"
                                    onClick={handleClose}
                                >
                                    {t("cancel")}
                                </Button>
                            </div>

                        </form>
                    </DialogContent>

                </div>
            </Dialog>
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="DisplayMultipleUserList" />} onReset={() => { }} >
                <DisplayMultipleUserList ref={userRef}></DisplayMultipleUserList>
            </ErrorBoundary>
        </div>
    );
});
export default AddMultipleUserDialog;