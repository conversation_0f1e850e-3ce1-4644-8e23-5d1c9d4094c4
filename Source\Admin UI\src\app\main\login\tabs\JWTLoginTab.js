import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import React, { useEffect, useRef, useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useDispatch, useSelector } from 'react-redux';
import { submitLogin, submitOTP, setmfaType, getOtp, without_Otp } from '../../../auth/store/loginSlice';
import * as yup from 'yup';
import TextField from '@mui/material/TextField';
import { useForm, Controller } from 'react-hook-form';
import _ from '@lodash';
import { MenuList, Select, FormControl, InputLabel } from '@mui/material';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import MfaOtpLogin from './MfaOtpLogin';
import EmailOtpLogin from './EmailOtpLogin';
import SMSTextOtpLogin from './SMSTextOtpLogin'
import { getDeviceLicenseCode, removeTokenFromStorage } from '../../utils/utils';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';

const schema = yup.object().shape({
  email: yup.string().email('You must enter a valid email').required('You must enter a email'),
  password: yup
    .string()
    .required('Please enter your password.')
    .min(4, 'Password is too short - should be 4 chars minimum.'),
});

const defaultValues = {
  email: '',
  password: '',
};

function JWTLoginTab(props) {
  localStorage.setItem("defaultLanguage", "en")
  removeTokenFromStorage()

  const dispatch = useDispatch();
  const login = useSelector(({ auth }) => auth.login);
  const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
    mode: 'onChange',
    defaultValues,
    resolver: yupResolver(schema),
  });

  const isloadingvalue = useSelector(({ auth }) => auth.login.isloading);
  const [loading, setLoading] = useState();
  const { isValid, dirtyFields, errors } = formState;
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [deviceLicenceId, setDeviceLicenceId] = useState("RED432");
  const [OTP, setOTP] = useState('');
  const mfaType = useSelector(({ auth }) => auth.login.mfaType);
  const isOtpRequired = useSelector(({ auth }) => auth.login.isOtpRequired);

  const formRef = useRef(null);

  useEffect(() => {
    if (login.error && (login.error.email || login.error.password || login.error.otp)) {
      formRef.current.updateInputsWithError({
        ...login.error
      });
    }
  }, [login.error, login.otpRequired, login.isSuperAdmin]);

  async function onSubmit(model) {
    setLoading(true)
    let data = await getDeviceLicenseCode();
    setDeviceLicenceId(data)
    setEmail(model.email.toLowerCase());
    setPassword(model.password);
    dispatch(submitLogin(model, data));
  }

  const changeMfaType = async (mfaType) => {
    setLoading(true)
    let data = await getDeviceLicenseCode();
    if (mfaType === "email" || mfaType === "text") {
      setDeviceLicenceId(data)
      dispatch(getOtp({ email, password, mfaType }, data));
    }
    setLoading(false)
    dispatch(setmfaType(mfaType));
  };

  useEffect(() => {
    setLoading(isloadingvalue)
  }, [isloadingvalue]);


  return (
    (isOtpRequired ?
      (login.otpRequired) ?
        <div className="w-full">
          {loading && < CircularProgressLoader loading={loading} />}

          {mfaType === "email" &&
            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="EmailOtpLogin" />} onReset={() => { }} >
              <EmailOtpLogin mfaType={changeMfaType} data="test" email={email} password={password} deviceLicenceId={deviceLicenceId} />
            </ErrorBoundary>
          }
          {mfaType === "authenticator" &&
            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="MfaOtpLogin" />} onReset={() => { }} >
              <MfaOtpLogin mfaType={changeMfaType} deviceLicenceId={deviceLicenceId} />
            </ErrorBoundary>
          }
          {mfaType === "text" &&
            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="SMSTextOtpLogin" />} onReset={() => { }} >
              <SMSTextOtpLogin mfaType={changeMfaType} data="test" email={email} password={password} deviceLicenceId={deviceLicenceId} />
            </ErrorBoundary>
          }

        </div>
        :
        <div className="w-full">
          {loading && < CircularProgressLoader loading={loading} />}
          <form key={1} className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  className="mb-16"
                  type="text"
                  error={!!errors.email}
                  helperText={errors?.email?.message}
                  label="Email"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Icon className="text-20" color="action">
                          person_outline
                        </Icon>
                      </InputAdornment>
                    ),
                  }}
                  variant="outlined"
                />
              )}
            />

            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  className="mb-16"
                  label="Password"
                  type="password"
                  error={!!errors.password}
                  helperText={errors?.password?.message}
                  variant="outlined"
                  InputProps={{
                    className: 'pr-2',
                    type: showPassword ? 'text' : 'password',
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={() => setShowPassword(!showPassword)} size="large">
                          <Icon className="text-20" color="action">
                            {showPassword ? 'visibility' : 'visibility_off'}
                          </Icon>
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  required
                />
              )}
            />

            <Button
              type="submit"
              variant="contained"
              color="primary"
              className="w-full mx-auto mt-16"
              aria-label="LOG IN"
              disabled={_.isEmpty(dirtyFields) || !isValid}
              value="legacy"
            >
              Login
            </Button>
          </form>
        </div>
      :
      <div className="flex flex-col items-center justify-center pt-16 text-15">
        Please Wait You are being logged into {process.env.REACT_APP_TITLE}!
      </div>
    )

  )
}


export default JWTLoginTab;
