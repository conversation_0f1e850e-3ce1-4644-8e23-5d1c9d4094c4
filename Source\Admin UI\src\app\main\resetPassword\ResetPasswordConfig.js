import { authRoles } from '../../auth';
import React from 'react';

const ResetPassword= React.lazy(() => import('./ResetPassword'))

const ResetPasswordConfig = {
	settings: {
		layout: {
			config: {
				navbar: {
					display: false
				},
				toolbar: {
					display: false
				},
				footer: {
					display: false
				},
				leftSidePanel: {
					display: false
				},
				rightSidePanel: {
					display: false
				}
			}
		}
	},
	auth: authRoles.onlyGuest,
	routes: [
		{
		  path: '/resetPass',
		  element: <ResetPassword />,
		}		
	]
};

export default ResetPasswordConfig;
