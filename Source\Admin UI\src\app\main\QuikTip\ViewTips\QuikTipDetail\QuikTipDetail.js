import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { styled, lighten, alpha } from "@mui/material/styles";
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import clsx from "clsx";
import InputBase from '@mui/material/InputBase';
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import FuseScrollbars from "@fuse/core/FuseScrollbars";
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import Chip from '@mui/material/Chip';
import Paper from '@mui/material/Paper';
import DetailsIcon from '@mui/icons-material/Details';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import MuiAccordion from '@mui/material/Accordion';
import MuiAccordionSummary from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import Grid from '@mui/material/Grid';
import CardContent from '@mui/material/CardContent';
import "./QuikTipDetail.css"
import Avatar from "@mui/material/Avatar";
import format from 'date-fns/format';
///import ReactPlayer from 'react-player/lazy'
import ReactPlayer from 'react-player'
import ReactAudioPlayer from 'react-audio-player';
import Badge from '@mui/material/Badge';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import { showMessage } from "app/store/fuse/messageSlice";
import { addComments, changeStatus, getAttachments, getComments, getTipDetailByTipIdForAdmin } from "src/app/main/store/quikTipSlice";
import { getIcon, isEmptyOrNull } from "src/app/main/utils/utils";
import CircularProgressLoader from "src/app/main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import CommonButton from 'src/app/main/SharedComponents/ReuseComponents/CommonButton';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import Divider from '@mui/material/Divider';
import DepartmentDropdownSetting from "src/app/main/SharedComponents/DepartmentDropdownSettings/DepartmentDropdownSetting";
import StatusDropdownSettings from "src/app/main/SharedComponents/StatusDropdownSettings/StatusDropdownSettings";
import SocketIoInitialization from "src/app/main/SharedComponents/SocketIoInitialization/SocketIoInitialization";
import moment from 'moment/moment';
import ViewImageDialog from "src/app/main/Dialog/ViewImageDialog";
import ImageListItem from '@mui/material/ImageListItem';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
const Accordion = styled((props) => (
    <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
    border: `1px solid ${theme.palette.divider}`,
    '&:not(:last-child)': {
        borderBottom: 0,
    },
    '&:before': {
        display: 'none',
    },
}));

// const divstyle = {
//     overflow: 'auto',
//     scrollBehavior: 'smooth',
//     //height: '600px'//window.innerHeight - 100
//     height: window.innerHeight - 170
// };

const itemstyle = {
    hidden: { opacity: 0, y: 40 },
    show: { opacity: 1, y: 0 },
};

const divstylePadding = {
    padding: window.innerHeight - 920
};

const StyledMessageRow = styled('div')(({ theme }) => ({
    '&.contact': {
        '& .bubble': {
            backgroundColor: theme.palette.secondary.light,
            color: theme.palette.secondary.contrastText,
            borderTopLeftRadius: 5,
            borderBottomLeftRadius: 5,
            borderTopRightRadius: 20,
            borderBottomRightRadius: 20,
            '& .time': {
                marginLeft: 12,
            },
        },
        '&.first-of-group': {
            '& .bubble': {
                borderTopLeftRadius: 20,
            },
        },
        '&.last-of-group': {
            '& .bubble': {
                borderBottomLeftRadius: 20,
            },
        },
    },
    '&.me': {
        paddingLeft: 40,

        '& .bubble': {
            marginLeft: 'auto',
            backgroundColor: theme.palette.primary.light,
            color: theme.palette.primary.contrastText,
            borderTopLeftRadius: 20,
            borderBottomLeftRadius: 20,
            borderTopRightRadius: 5,
            borderBottomRightRadius: 5,
            '& .time': {
                justifyContent: 'flex-end',
                right: 0,
                marginRight: 12,
            },
        },
        '&.first-of-group': {
            '& .bubble': {
                borderTopRightRadius: 20,
            },
        },

        '&.last-of-group': {
            '& .bubble': {
                borderBottomRightRadius: 20,
            },
        },
    },
    '&.contact + .me, &.me + .contact': {
        paddingTop: 20,
        marginTop: 20,
    },
    '&.first-of-group': {
        '& .bubble': {
            borderTopLeftRadius: 20,
            paddingTop: 13,
        },
    },
    '&.last-of-group': {
        '& .bubble': {
            borderBottomLeftRadius: 20,
            paddingBottom: 13,
            '& .time': {
                display: 'flex',
            },
        },
    },
}));

const AccordionSummary = styled((props) => (
    <MuiAccordionSummary
        expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
        {...props}
    />
))(({ theme }) => ({
    backgroundColor:
        theme.palette.mode === 'dark'
            ? 'rgba(255, 255, 255, .05)'
            : 'rgba(0, 0, 0, .03)',
    flexDirection: 'row-reverse',
    '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
        transform: 'rotate(90deg)',
    },
    '& .MuiAccordionSummary-content': {
        marginLeft: theme.spacing(1),
    },
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
    padding: theme.spacing(2),
    borderTop: '1px solid rgba(0, 0, 0, .125)',
}));

const Item = styled(Paper)(({ theme }) => ({
    backgroundColor: theme.palette.mode === 'dark' ? '#1A2027' : '#fff',
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: 'center',
    color: theme.palette.text.secondary,
}));


const rows = [
    {
        id: "TipID",
        align: "left",
        disablePadding: false,
        label: "tipID",
        sort: true,
    },
    {
        id: "CommentDateTime",
        align: "left",
        disablePadding: false,
        label: "DateTime",
        sort: true,
    },
    {
        id: "CommentFrom",
        align: "left",
        disablePadding: false,
        label: "From",
        sort: true,
    },
    {
        id: "CommentText",
        align: "left",
        disablePadding: false,
        label: "TipComment",
        sort: true,
    },
    {
        id: "CommentType",
        align: "left",
        disablePadding: false,
        label: "CommentType",
        sort: true,
    },
    {
        id: "Answered",
        align: "left",
        disablePadding: false,
        label: "Answered",
        sort: true,
    },

];


function QuikTipDetail() {
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const routeParams = useParams();
    const chatRef = useRef(null);
    const onToolbarRef = useRef(null);
    const gridRef = useRef(null);
    const imageOpenPopupRef = useRef();
    const user = useSelector(({ auth }) => auth.user);
    const quikTipDetail = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.quikTipDetail);
    const commentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.commentData);
    const commentflag = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.commentflag);
    const commentsTotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.commentsTotalCount);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.isloading);
    const attachmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.attachementData);
    const [expanded, setExpanded] = React.useState('Tipdetailspanel');
    const [expandedComment, setExpandedComment] = React.useState("TipCommentsPanel");
    const [expandedAttachment, setExpandedAttachment] = React.useState("AttachmentPanel");
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [comment, setComment] = React.useState('');
    const [loading, setLoading] = useState();
    const [departmentValue, setDepartmentValue] = React.useState(quikTipDetail.DepartmentID);
    const [status, setStatus] = React.useState(quikTipDetail.StatusKey);
    const [isToggle, setToggle] = React.useState(false);
    const handleCommentChange = (panel) => (event, newExpanded) => {
        setExpandedComment(newExpanded ? panel : false);
    };

    const [commentsRecords, setCommentRecords] = React.useState(commentData.length == 0 ? [] : commentData.reduce((acc, item) => [item].concat(acc), []))

    const fileUpload = useRef(null);
    const imageRef = useRef();
    const [preview, setPreview] = React.useState(null)
    const [audio, setAudio] = React.useState(null)
    const [fileType, setFileType] = React.useState(null)
    const [fileData, setFileData] = React.useState(null)
    const handleTipdetailspanelChange = (panel) => (event, newExpanded) => {
        setExpanded(newExpanded ? panel : false);
    };


    // //For Auto resize browser
    const [windowWidth, setWindowWidth] = useState(window.innerWidth)
    const [windowHeight, setWindowHeight] = useState(window.innerHeight)

    const setWindowDimensions = () => {
        setWindowWidth(window.innerWidth)
        setWindowHeight(window.innerHeight)
    }
    useEffect(() => {
        window.addEventListener('resize', setWindowDimensions);
        return () => {
            window.removeEventListener('resize', setWindowDimensions)
        }
    }, [])

    const divstyle = {
        overflow: "auto",
        scrollBehavior: 'smooth',
        height: windowHeight - 400,
    }


    const handleAttachmentChange = (panel) => (event, newExpanded) => {
        setExpandedAttachment(newExpanded ? panel : false);
    };

    function getCommentType(item) {
        if (!isEmptyOrNull(item)) {
            switch (item) {
                case "publiccomment":
                    return "Public Comment";
                case "internalcomment":
                    return "Internal Comment";
                case "isquestion":
                    return "Question";
                default:
                    return "";
            }
        }
        else {
            return ""
        }
    }


    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
    }

    function handleChangeComment(event) {
        setComment(event.target.value);
    }

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    useEffect(() => {
        dispatch(getComments(pageIndex, rowsPerPage, quikTipDetail._id, routeParams.code));
        // dispatch(getAttachments(quikTipDetail._id, routeParams.code));
        if (quikTipDetail._id !== undefined) {
            dispatch(getTipDetailByTipIdForAdmin(quikTipDetail._id, routeParams.code));
        }
    }, [dispatch, pageIndex, rowsPerPage])


    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    useEffect(() => {
        setCommentRecords(commentData.length == 0 ? [] : commentData)
        //setCommentRecords(commentData.length == 0 ? [] : commentData.reduce((acc, item) => [item].concat(acc), []))
        scrollToBottom();
    }, [commentflag, commentData])

    useEffect(() => {
        scrollToBottom();
    })

    const sendComment = (value, fileData) => {
        let data = {
            TipID: quikTipDetail._id,
            CommentFrom: user.data.fullName,
            UserID: localStorage.getItem("userId"),
            UserName: user.data.fullName,
            CommentText: comment,
            ViewableByUser: value === "publiccomment" ? true : false,
            FromTipster: false,
            IsQuestion: value === "isquestion" ? true : false,
            DeviceTypeID: 0,
            DeviceTypeText: "Web",
            QuestionAnswered: false,
            CommentType: value,
            Status: quikTipDetail.Status,
            code: routeParams.code,
            fileData: fileData
        }
        //if (!isEmptyOrNull(comment)) {
        dispatch(addComments(data));
        scrollToBottom();

        setComment('');

        // } else {
        //     ShowErroMessage(t('CommentShouldNotBeEmptyOrNull'));
        // }
    }

    const handleDepartment = (departmentId) => {
        setDepartmentValue(departmentId)

    };
    const handleStatus = (statusId) => {
        setStatus(statusId)

    };
    const submitStatus = () => {
        let data = {
            departmentValue: departmentValue,
            status: status,
            code: routeParams.code,
            _id: quikTipDetail._id
        }
        dispatch(changeStatus(data));
    };

    async function onFileChange(e) {
        setComment("")
        const objectUrl = URL.createObjectURL(e.currentTarget.files[0])
        if (e.currentTarget.files[0].type.split('/')[0] === "image") {
            setFileType("image")
            setPreview(objectUrl)
        }
        else if (e.currentTarget.files[0].type.split('/')[0] === "audio") {
            setFileType("audio")
            setAudio(objectUrl)
        }

        const file = e.target.files[0]
        const formData = new FormData();
        formData.append("file", file);
        var rightNow = new Date();
        var res = rightNow.toISOString().slice(0, 10).replace(/-/g, "");
        var time = rightNow.toLocaleTimeString().replace(/:/g, "").split(' ')[0];
        setFileData([{
            mimetype: file.type,
            name: `${quikTipDetail._id}_IMG_${res}_${time}_${file.name}`
        }])
        sendComment("publiccomment", formData)

    }

    const convertBase64 = (file) => {
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader();
            fileReader.readAsDataURL(file)
            fileReader.onload = () => {
                resolve(fileReader.result);
            }
            fileReader.onerror = (error) => {
                reject(error);
            }
        })
    }

    // function scrollToBottom() {
    //     chatRef?.current?.scrollIntoView({ behavior: 'smooth' });
    // }

    function scrollToBottom() {
        if (chatRef.current !== null) {
            chatRef.current.scrollTop = chatRef.current.scrollHeight;
        }
    }

    const handleToggleChange = (event) => {
        setToggle(event.target.checked)
    }


    return (
        <>
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="SocketIoInitialization" />} onReset={() => { }} >
                <SocketIoInitialization tipID={quikTipDetail._id} code={routeParams.code}></SocketIoInitialization>
            </ErrorBoundary>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/quiktip/viewtips/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 w-full items-center justify-between"

                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                    style={{ alignItems: 'center', display: "flex" }}
                                >
                                    <DetailsIcon style={{ fontSize: '40px' }} />
                                </Icon>

                                <Typography className="item hidden sm:flex mx-0 sm:mx-12" variant="h6">
                                    {t("quikTipDetails")}
                                </Typography>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col" >
                        <FuseScrollbars className="flex-grow overflow-x-auto m-16">
                            <div>
                                <Accordion expanded={expanded === 'Tipdetailspanel'} onChange={handleTipdetailspanelChange('Tipdetailspanel')}>
                                    <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
                                        <Typography className="font-semibold mb-4 text-15">{t("tipDetail")}</Typography>
                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <CardContent className="px-32 py-24">
                                            {/* <GoogleReact></GoogleReact> */}
                                            <div className="mb-24">
                                                <Grid container spacing={1} className="displayDiv">

                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">Name:</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={3} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.FullName}</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">Tip Date:</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.CreatedDate}</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">Phone Number:</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.PhoneNumber}</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">Email:</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.Email}</Typography>
                                                    </Grid>
                                                </Grid>

                                                <Grid container spacing={1} className="displayDiv">
                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">Location:</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.Address}</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">Tip Type:</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.tipTypes !== undefined ? quikTipDetail.tipTypes.name : ""}</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">Status:</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.Status}</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">Department:</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.Department}</Typography>
                                                    </Grid>


                                                </Grid>

                                                <Grid container spacing={1} className="displayDiv">

                                                    <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                                                        <Typography className="font-semibold mb-4 text-15">TipText:</Typography>
                                                    </Grid>

                                                    <Grid item xs={12} sm={11} md={11} lg={11} xl={11} style={{ padding: '0px' }}>
                                                        <Typography>{quikTipDetail.TipText}</Typography>
                                                    </Grid>
                                                </Grid>


                                                <Divider style={{ backgroundColor: "black" }} />
                                                <div style={{ display: "flex", gap: "10px" }}>
                                                    <Grid container spacing={1} className="displayDiv1">
                                                        <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                            <ErrorBoundary
                                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="DepartmentDropdownSetting" />} onReset={() => { }}>
                                                                <DepartmentDropdownSetting code={routeParams.code} flag={false} departmentId={handleDepartment} departmentIdValue={quikTipDetail.DepartmentID}>
                                                                </DepartmentDropdownSetting>
                                                            </ErrorBoundary>
                                                        </Grid>
                                                        <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                            <ErrorBoundary
                                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="StatusDropdownSettings" />} onReset={() => { }}>
                                                                <StatusDropdownSettings code={routeParams.code} flag={false} statusID={handleStatus} statusIdValue={quikTipDetail.StatusKey} >
                                                                </StatusDropdownSettings>
                                                            </ErrorBoundary>
                                                        </Grid>
                                                        <Grid item xs={12} sm={2} md={2} lg={2} xl={2} style={{ padding: '0px' }}>
                                                            <ErrorBoundary
                                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_changeStatus" />} onReset={() => window.location.reload()} >
                                                                <CommonButton styleClass="whitespace-nowrap normal-case mt-16" btnName={t("changeStatus")} parentCallback={submitStatus}></CommonButton>
                                                            </ErrorBoundary>
                                                        </Grid>
                                                    </Grid>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </AccordionDetails>
                                </Accordion>
                                <Accordion expanded={expandedAttachment === 'AttachmentPanel'} onChange={handleAttachmentChange('AttachmentPanel')}>
                                    <AccordionSummary aria-controls="panel2d-content" id="panel2d-header">
                                        <Typography className="font-semibold mb-4 text-15">{t("attachments")}</Typography>
                                        <Stack spacing={2} direction="row" style={{ paddingLeft: '54px' }}>
                                            <Badge badgeContent={attachmentData.length !== 0 ? attachmentData.length : '0'} color="secondary">
                                                <AttachFileIcon color="action" />
                                            </Badge>
                                        </Stack>
                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <Grid container rowSpacing={2} columnSpacing={{ xs: 1, sm: 2, md: 3 }}>
                                            {attachmentData.map(x => (
                                                <Grid item xs={12} sm={3} md={3} lg={3} xl={3} style={{ padding: '28px' }} >
                                                    {x.attachmentType === 'video' &&
                                                        <Item>
                                                            <div className='player-wrapper'>
                                                                <ReactPlayer
                                                                    url={x.attachmentUrl}
                                                                    className='react-player'
                                                                    //playing
                                                                    width='100%'
                                                                    height='100%'
                                                                    controls={true}
                                                                    onClick={() => imageRef.current.openCitation(x.attachmentUrl, x.attachmentType, "", "")}
                                                                />
                                                            </div>
                                                        </Item>
                                                    }
                                                    {x.attachmentType === 'audio' &&
                                                        <Item>
                                                            <ReactAudioPlayer
                                                                src={x.attachmentUrl}
                                                                controls
                                                                width='90%'
                                                                height='90%'
                                                            />
                                                        </Item>
                                                    }
                                                    {x.attachmentType === 'image' &&
                                                        <Item>
                                                            <img src={x.attachmentUrl} className="imgStyle" onClick={() => imageRef.current.openCitation(x.attachmentUrl, x.attachmentType, "", "")}></img>
                                                        </Item>
                                                    }
                                                </Grid>
                                            ))}
                                        </Grid>
                                    </AccordionDetails>
                                </Accordion>
                                <Accordion expanded={expandedComment === 'TipCommentsPanel'} onChange={handleCommentChange('TipCommentsPanel')}>
                                    <AccordionSummary aria-controls="panel3d-content" id="panel3d-header">
                                        <Typography className="font-semibold mb-4 text-15">{t("tipComments")}</Typography>

                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <div className={clsx('flex flex-1 z-10 flex-col relative')}>
                                            <div ref={chatRef} className="flex flex-col pt-16 px-16 ltr:pl-56 rtl:pr-56 pb-40" style={divstyle} id="scrollableDiv">
                                                {/* <FuseScrollbars ref={chatRef} className="flex flex-1 flex-col overflow-y-auto"> */}
                                                {commentsRecords?.length > 0 && (
                                                    <div className="flex flex-col pt-16 px-16 pb-40">
                                                        {commentsRecords.map((item, i) => {
                                                            return (
                                                                <>
                                                                    {!item.isInitialAttachment &&
                                                                        <StyledMessageRow
                                                                            className={clsx('flex flex-col grow-0 shrink-0 items-start justify-end relative px-16 pb-4',
                                                                                i + 1 === commentsRecords.length && "pb-96")}
                                                                        >
                                                                            <div className="w-full">
                                                                                <Divider><Chip label={format(new Date(item.CommentDateTime), 'dd MMM yyyy')} /></Divider>
                                                                            </div>

                                                                            <div className="flex flex-row">
                                                                                <div className="flex flex-cols max-w-full">
                                                                                    {item.CommentFrom === "tipster" ?
                                                                                        <Avatar>T</Avatar>
                                                                                        :
                                                                                        <Avatar>{item.CommentFrom.charAt(0)}</Avatar>
                                                                                    }
                                                                                </div>
                                                                                <div className="px-12">
                                                                                    <div className="flex items-center justify-center max-w-full">
                                                                                        <Typography
                                                                                            className="whitespace-nowrap font-bold"
                                                                                            color="text.secondary"
                                                                                        >
                                                                                            {item.CommentFrom}
                                                                                        </Typography>
                                                                                        <Typography
                                                                                            className="pl-8 whitespace-nowrap"
                                                                                            color="text.secondary"
                                                                                        >
                                                                                            {/* {formatDistanceToNow(new Date(item.timetoken / 10000), { addSuffix: true })} */}
                                                                                            {moment(new Date(item.CommentDateTime)).format('h:mm:ss a')}
                                                                                        </Typography>
                                                                                    </div>

                                                                                    <div className="flex relative items-center py-10 max-w-full">
                                                                                        {item.isAttachment === false && <Typography
                                                                                            className="whitespace-nowrap font-bold"
                                                                                            color="text.secondary"
                                                                                        >
                                                                                            {item.CommentText}
                                                                                        </Typography>}

                                                                                        {(item.attachmentType === "image" && item.isAttachment === true) &&
                                                                                            <ImageListItem
                                                                                                component={motion.div}
                                                                                                variants={itemstyle}
                                                                                                className="w-full rounded-16 shadow overflow-hidden"
                                                                                                style={{ height: '100px', width: '150px' }}
                                                                                            >
                                                                                                {item.attachmentType === "image" && <img src={item.attachmentUrl} width="10" height="10" onClick={() => imageRef.current.openCitation(item.attachmentUrl, item.attachmentType, "", "")}></img>}

                                                                                            </ImageListItem>
                                                                                        }

                                                                                        {item.isAttachment === true &&
                                                                                            <>
                                                                                                {item.attachmentType === "audio" &&
                                                                                                    <ReactAudioPlayer
                                                                                                        src={item.attachmentUrl}
                                                                                                        controls
                                                                                                        width='40%'
                                                                                                        height='40%'
                                                                                                    />
                                                                                                }
                                                                                            </>
                                                                                        }

                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </StyledMessageRow>
                                                                    }
                                                                </>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                            {/* </FuseScrollbars> */}
                                            <Paper
                                                square
                                                component="form"
                                                className="absolute border-t-1 bottom-0 right-0 left-0 py-0 px-16"
                                                sx={{
                                                    backgroundColor: (theme) =>
                                                        theme.palette.mode === 'light'
                                                            ? lighten(theme.palette.background.default, 0.4)
                                                            : lighten(theme.palette.background.default, 0.02),
                                                }}
                                            >
                                                <div className="flex items-center relative" style={{ padding: "5px" }}>
                                                    <Tooltip title={isToggle == true ? "Add an internal comment to this tip. The comment will not be viewable to the tipster." : "Add a comment to the tip, viewable by the tipster. This can be used to thank the tipster for sending the tip or to let them known the tip is being handled"} placement="bottom">
                                                        <div className="mt-6">
                                                            {isToggle == true ? <LockIcon color="action" style={{ marginRight: "14px" }} /> : <LockOpenIcon style={{ marginRight: "14px" }} color="action" />
                                                            }

                                                            <FormControlLabel
                                                                control={
                                                                    <Switch
                                                                        checked={isToggle}
                                                                        onChange={handleToggleChange}
                                                                        name="checked"
                                                                        inputProps={{
                                                                            "aria-label": "secondary checkbox",
                                                                        }}
                                                                    />
                                                                }
                                                            //label={}
                                                            />
                                                        </div>
                                                    </Tooltip>
                                                    <Tooltip title="Attachments" placement="bottom">

                                                        <label htmlFor="button-file">
                                                            <input
                                                                className="hidden"
                                                                id="button-file"
                                                                type="file"
                                                                ref={fileUpload}
                                                                accept="audio/*,image/*"
                                                                onChange={(e) => onFileChange(e)}
                                                            />
                                                            <IconButton className="w-32 h-32 mx-4 p-0" component="span" size="large">
                                                                <FuseSvgIcon className="text-48" size={24} color="action">material-outline:attach_file</FuseSvgIcon>
                                                            </IconButton>
                                                        </label>
                                                    </Tooltip>

                                                    <InputBase
                                                        autoFocus={false}
                                                        id="message-input"
                                                        className="flex-1 flex grow shrink-0 h-44 mx-8 px-16 border-2 rounded-full"
                                                        placeholder="Type your message"
                                                        onChange={handleChangeComment}
                                                        value={comment}
                                                        sx={{ backgroundColor: 'background.paper' }}
                                                    />
                                                    <IconButton className="" size="large" onClick={() => sendComment("publiccomment", null)} >
                                                        <FuseSvgIcon className="rotate-90" color="action">
                                                            heroicons-outline:paper-airplane

                                                        </FuseSvgIcon>
                                                    </IconButton>
                                                </div>

                                            </Paper>
                                        </div>
                                    </AccordionDetails>
                                </Accordion>
                            </div>
                        </FuseScrollbars>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ViewImageDialog" />} onReset={() => { }} >
                            <ViewImageDialog ref={imageRef} />
                        </ErrorBoundary>
                    </div>
                }
                innerScroll
            />
        </>
    )
}





export default QuikTipDetail;



