import React, { forwardRef, useEffect, useState, useImperativeHandle } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { Button, DialogActions, Table, TableContainer, TableBody, TableCell, TableHead, TableRow, TextField } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CloseIcon from '@mui/icons-material/Close';
import { styled } from '@mui/material/styles';
import XLSX from 'xlsx';
import { useForm } from 'react-hook-form';
import Paper from '@mui/material/Paper';
import Autocomplete from '@mui/material/Autocomplete';
import { Box } from '@mui/system';
import CircularProgressLoader from '../CircularProgressLoader/CircularProgressLoader';
import { useTranslation } from 'react-i18next';

const ImportFromExcelDialog = forwardRef((props, ref) => {

    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");

    const { handleSubmit } = useForm({
        mode: 'onChange',
    });

    const VisuallyHiddenInput = styled('input')({
        clip: 'rect(0 0 0 0)',
        clipPath: 'inset(50%)',
        height: 1,
        overflow: 'hidden',
        position: 'absolute',
        bottom: 0,
        left: 0,
        whiteSpace: 'nowrap',
        width: 1,
    });

    const [open, setOpen] = useState(false);
    const [title, setTitle] = useState("");
    const [loading, setLoading] = useState(false);
    const [fileDetails, setFileDetails] = useState(null);
    const [excelColumns, setExcelColumns] = useState(null);
    const [mappings, setMappings] = useState({});
    const [excelData, setExcelData] = useState([]);
    const [columnsData, setColumnsData] = useState();
    // const [masterIntersectionFlag, setMasterIntersectionFlag] = useState(null);
    const [filteredFieldsList, setFilteredFieldsList] = useState(columnsData ?? []);
    const [selectedItems, setSelectedItems] = useState([]);
    const [removedItems, setRemovedItems] = useState([]);
    const [defaultField, setDefaultFields] = useState([]);
    const [defaultMappings, setDefaultMappings] = useState([]);

    useImperativeHandle(ref, () => ({
        handleOpen(title, columnsData, defaultFields) {
            setTitle(title);
            setColumnsData(columnsData);
            setDefaultFields(defaultFields);
            // setMasterIntersectionFlag(masterIntersectionFlag);
            handleClickOpen();
        }
    }));

    // useEffect(() => {
    //     if (defaultField !== null && defaultField.length > 0) {
    //         setSelectedItems((prev) => [...prev, ...defaultField]);
    //         // setMappings((prevMappings) => ({ ...prevMappings, defaultField }));
    //     }
    // }, [defaultField]);

    const handleClickOpen = () => {
        setOpen(true);
    }

    const handleClose = (event, reason) => {
        if (reason && reason === "backdropClick") return;
        setOpen(false);
        setFileDetails(null);
        setExcelColumns(null);
        setFilteredFieldsList(columnsData);
        setSelectedItems([]);
        setRemovedItems([]);
    }

    // const uploadFiles = (event) => {
    //     const files = event.target.files;
    //     if (files && files[0]) {
    //         setFileDetails(event.target.files[0]);
    //     }

    // }
    //const [excelColumns, setExcelColumns] = useState([]);

    const uploadFiles = (event) => {
        setLoading(true);
        const file = event.target.files[0];
        const reader = new FileReader();
        setFileDetails(event.target.files[0]);

        const worker = new Worker(new URL('./excelWorker.js', import.meta.url));

        worker.onmessage = (e) => {
            const { headers, data } = e.data;
            setExcelColumns(headers);
            setExcelData(data);
            setLoading(false);  // Stop loading once processing is done
        };

        worker.onerror = (err) => {
            console.error('Error in worker:', err);
            setLoading(false);
        };

        reader.onload = (e) => {
            // const data = new Uint8Array(e.target.result);
            // const workbook = XLSX.read(data, { type: "array" });

            // // Read the first sheet
            // const worksheet = workbook.Sheets[workbook.SheetNames[0]];

            // // Convert the sheet to JSON and extract headers
            // const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }); // Read raw data
            // const sheetHeaders = jsonData[0]; // First row contains the headers

            // setExcelColumns(sheetHeaders);
            // setExcelData(jsonData.slice(1)); // Exclude the header row

            worker.postMessage(e.target.result); // Send file data to worker
        };

        reader.readAsArrayBuffer(file);
    };


    const handleMapData = () => {

        const mappedData = excelData.map((row) => {
            const mappedRow = {};
            Object.entries(mappings).forEach(([source, destination]) => {
                const sourceIndex = excelColumns.indexOf(source);
                if (sourceIndex !== -1) {
                    mappedRow[destination] = row[sourceIndex];
                }
            });
            return mappedRow;
        });

        let destinationColumns = new Set();
        Object.entries(mappings).forEach((([source, destination]) => {
            const row = columnsData.find(item => item.name === destination);
            if (row !== undefined && row !== null)
                destinationColumns.add(row);
        }))

        destinationColumns = [...destinationColumns];

        // Object.entries(mappings).forEach((([source, destination]) => {
        //     const row = columnsData.find(item => item.name === destination);
        //     destinationColumns.push(row);
        // }))

        props.passChildData(mappedData, fileDetails, destinationColumns);

        handleClose();

    };

    useEffect(() => {
        // This useEffect will only run when `excelColumns` or `defaultField` changes
        if (excelColumns !== null && excelColumns.length > 0) {
            if (defaultField !== null && defaultField.length > 0) {
                let defaultFields = defaultField.filter(x => x !== null)
                excelColumns.forEach((row) => {
                    let defaultColumnFound = defaultFields.find(x => x.name === row);
                    let columnFound = columnsData.find(x => x.name === row);
                    if (defaultColumnFound && columnFound) {
                        setMappings((prevMappings) => ({ ...prevMappings, [row]: defaultColumnFound.name }));
                        setSelectedItems((prev) => [...prev, defaultColumnFound]);
                        setDefaultMappings((prevMappings) => ({ ...prevMappings, [row]: defaultColumnFound }));
                    } else {
                        let columnFound = columnsData.find(x => x.name === row);
                        if (columnFound) {
                            setMappings((prevMappings) => ({ ...prevMappings, [row]: columnFound.name }));
                            setSelectedItems((prev) => [...prev, columnFound]);
                            setDefaultMappings((prevMappings) => ({ ...prevMappings, [row]: columnFound }));
                        }
                    }
                });
            } else {
                excelColumns.forEach((row) => {
                    let columnFound = columnsData.find(x => x.name === row);
                    if (columnFound) {
                        setMappings((prevMappings) => ({ ...prevMappings, [row]: columnFound.name }));
                        setSelectedItems((prev) => [...prev, columnFound]);
                        setDefaultMappings((prevMappings) => ({ ...prevMappings, [row]: columnFound }));
                    }
                });
            }
        }
    }, [excelColumns]);

    const getFileValues = (index, row) => {
        if (defaultMappings[row]) {

            const rowMapping = defaultMappings[row];

            const itemToRemove = removedItems.find((item) => rowMapping["name"] === item.name);

            if (itemToRemove === null || itemToRemove === undefined) {    // remove items from autocomplete so that it doesn't return result for the field
                return rowMapping;
            }

        }

    }

    const onSubmit = () => {
        handleMapData();
    }

    useEffect(() => {
        if (columnsData !== null && columnsData !== undefined) {
            setFilteredFieldsList(columnsData);
        }
    }, [columnsData])

    const getFilteredOptions = (rowId) => {
        const selectedSet = new Set(Object.values(selectedItems).map((item) => item.id));     // Extract IDs of selected items
        const removedSet = new Set(Object.values(removedItems).map(item => item.id));         // Extract Ids of removed items
        return filteredFieldsList.filter(
            (option) => {
                return !selectedSet.has(option.id);
            }
        );
    };

    const filterFields = (event, value) => {

        if (value !== null) {
            const activeValue = event.currentTarget.ownerDocument.activeElement.value;

            if(activeValue !== "") {
                // console.log(removedItems);   
                const item = columnsData.find(x => x.name === activeValue);     // method for handling dropdown change directly without using clear button
                setRemovedItems((prev) => [...prev, item]);

                setSelectedItems((prev) => {                                    // setting selected options and filtering it out from  so it appears in next dropdown
                    const updatedValues = [...prev];
                    updatedValues.push(value);
                    const newUpdated = updatedValues.filter(x => x.id !== item.id);     
                    return newUpdated;  
                });
                
                setRemovedItems((prev) => prev.filter(item => item.id !== value.id));
            } else {
                setSelectedItems((prev) => {
                    const updatedValues = [...prev];
                    updatedValues.push(value);
                    return updatedValues;
                });
    
                setRemovedItems((prev) => prev.filter(item => item.id !== value.id));
            }

        } else {
            const childNodes = event.currentTarget.parentElement.parentElement.childNodes;      

            for (let node of childNodes) {
                if (node.tagName.toLowerCase() === "input") {                                          // getting value from input and marking it as removed when cleared using button
                    const removedItemObj = columnsData.find(x => x.name === node.value);   

                    setRemovedItems((prev) => [...prev, removedItemObj]);

                    setSelectedItems((prev) => {
                        const updatedValues = [...prev];
                        const newUpdated = updatedValues.filter(x => x.id !== removedItemObj.id);
                        // delete updatedValues[index];
                        return newUpdated;
                    });
                }
            }

        }
    }

    const handleMappingChange = (source, destination) => {
        if (destination !== "") {
            setMappings((prevMappings) => ({
                ...prevMappings,
                [source]: destination,
            }));
        }

    };



    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >

                <DialogTitle id="excel-import-dialog-title">
                    <Box display="flex" alignItems="center">
                        <Box flexGrow={1} >{title}</Box>
                        <Box>
                            <IconButton onClick={handleClose}>
                                <CloseIcon />
                            </IconButton>
                        </Box>
                    </Box>
                </DialogTitle>
                <form
                    className="flex flex-col justify-center w-full pb-16"
                    onSubmit={handleSubmit(onSubmit)}
                    // autoComplete="off"
                    autoSave={false}
                >
                    <DialogContent dividers>

                        <div style={{ display: 'flex' }}>
                            <div>
                                <Button
                                    component='label'
                                    variant='contained'
                                    startIcon={<CloudUploadIcon />}
                                    color='primary'
                                >
                                    {t("selectFile")}
                                    <VisuallyHiddenInput
                                        id="docpicker"
                                        type="file"
                                        onChange={uploadFiles}
                                        accept='.xlsx, .xls, .xlsm, .xlsb, .xltx, .xltm, .csv, .tsv, .xml, '
                                    />
                                </Button>
                            </div>
                            <div style={{ alignSelf: 'center', marginLeft: '16px' }}>
                                <p style={{ fontStyle: 'italic', fontSize: '16px' }}>{fileDetails !== null ? fileDetails.name : t("noFileSelectMsg")}</p>
                            </div>
                        </div>

                        <div style={{ display: 'flex', alignItems: 'center', marginTop: '16px' }}>
                            <TextField label={t("source")} variant="outlined" size='small' fullWidth />
                        </div>

                        <div style={{ marginTop: '16px' }}>
                            {loading === true ? <CircularProgressLoader loading={loading} /> :
                                <Paper sx={{ width: '100%', overflow: 'hidden' }}>
                                    <TableContainer sx={{ maxHeight: 500 }}>
                                        <Table sx={{ width: 'inherit' }} stickyHeader aria-label="simple table">

                                            <TableHead>
                                                <TableRow>
                                                    <TableCell>{t("sourceFields")}</TableCell>
                                                    <TableCell>{t("destinationFields")}</TableCell>
                                                </TableRow>
                                            </TableHead>

                                            <TableBody>
                                                {excelColumns !== null && excelColumns.map((row, index) => (

                                                    <TableRow
                                                        key={index}
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    // sx={{border: '1px solid #ddd'}}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {row}
                                                        </TableCell>
                                                        <TableCell sx={{ padding: '15px' }} >
                                                            <Autocomplete
                                                                key={index}
                                                                size='small'
                                                                value={getFileValues(index, row)}
                                                                onChange={(event, value) => {
                                                                    handleMappingChange(row, value?.name || "", value, index);
                                                                    filterFields(event, value);
                                                                }}
                                                                disablePortal
                                                                options={getFilteredOptions(index)}
                                                                getOptionLabel={(option) => option.name}
                                                                renderInput={(params) => <TextField {...params} label={t("selectField")} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </TableContainer>
                                </Paper>
                            }
                        </div>

                    </DialogContent>
                    <DialogActions>
                        <div style={{ marginLeft: 'auto', marginTop: '10px', marginRight: '10px' }}>
                            <Button variant='contained' color='secondary' className='ml-5' type='submit'>
                                Import
                            </Button>
                            <Button variant='contained' color='primary' className='ml-12' type='button' onClick={handleClose}>
                                Close
                            </Button>
                        </div>
                    </DialogActions>
                </form>
            </Dialog>
        </div>
    )

})


export default ImportFromExcelDialog;