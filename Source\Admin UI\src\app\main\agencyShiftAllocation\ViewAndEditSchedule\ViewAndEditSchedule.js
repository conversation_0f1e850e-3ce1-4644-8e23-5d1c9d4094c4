import React, { useState, useEffect, useRef } from "react";
import _ from "@lodash";
import Button from '@mui/material/Button';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { useParams } from "react-router";
import history from '@history';
import { Grid } from '@mui/material';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import Typography from '@mui/material/Typography';
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import FuseScrollbars from "@fuse/core/FuseScrollbars";
import AssignmentIcon from '@mui/icons-material/Assignment';
import { Tree, TreeItem, TreeItemsGroup } from 'smart-webcomponents-react/tree';
import { checkValueEmptyOrNull, convertTo12HourFormat } from "../../utils/utils";
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import moment from "moment";
import "./ViewAndEditSchedule.css";
import { clearData, getShiftAllocationSchedule, removeShiftAllocationScheduleUser, updateShiftAllocationSchedule } from "../../store/shiftAllocationScheduleSlice";
import CircularProgressLoader from "../../SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { showMessage } from "app/store/fuse/messageSlice";
import Schedule from "../../components/Schedule/Schedule";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import AddUserDialog from "./AddUserDialog";

let dateData = [];

function ViewAndEditSchedule() {
    const { t } = useTranslation('laguageConfig');
    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
    });

    const routeParams = useParams();
    const dispatch = useDispatch();

    const user = useSelector(({ auth }) => auth.user);
    const agencyCode = routeParams.code !== "list" ? routeParams.code : user.data.defaultAgency

    const navigateBack = () => {
        dispatch(clearData())
        if (routeParams.shiftID === '0') {
            history.push(`/admin/ShiftAllocation/${routeParams.code}/0`);
        }
        else {
            history.push(`/admin/ShiftAllocation/${routeParams.code}/${routeParams.shiftID}`);
        }
    };

    const ShiftAllocationData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shiftallocation.data);
    const ScheduleAllocationData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.schedule.data);
    const TeamData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.teammaster.data);
    const shiftsArray = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shift.data);
    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.schedule.isloading);

    const [open, setOpen] = React.useState(false);
    const [openEdit, setOpenEdit] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [dropFlag, setDropFlag] = useState(false);
    const treeref = useRef(null);

    let filterdata = ShiftAllocationData.filter(x => x._id === routeParams.id)
    let filteredDepeartmentData = DepartmentData.filter(x => x._id === filterdata[0].department[0]._id)
    let filteredShiftsData = shiftsArray.filter(x => x.department[0]._id === filterdata[0].department[0]._id)

    useEffect(() => {
        dispatch(getShiftAllocationSchedule(routeParams.code, routeParams.id))
    }, []);

    let displayToday = moment(filterdata[0].StartDate).format('DD-MM-YYYY')
    let dsiplayEndDate = moment(filterdata[0].EndDate).format('DD-MM-YYYY')

    const getData = () => {
        let data = [];
        ScheduleAllocationData.forEach(function (item, index) {
            let event = {
                label: item.userName + ' - ' + item.teamName,
                dateStart: new Date(item.startDateTime),
                dateEnd: new Date(item.endDateTime),
                status: 'tentative',
                backgroundColor: item.shiftColor,
                repeat: {
                    repeatFreq: 'daily',
                    repeatInterval: 1,
                    repeatEnd: new Date(item.endDateTime),
                },
                data: item
            };
            data.push(event);
        })
        return data;
    }

    let data = getData();

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const getTime = (OriginalStartDate, OriginalEndDate, DragStartDate, DragEndDate, shiftDuration) => {
        const StartHours = OriginalStartDate.getHours();
        const StartMinutes = OriginalStartDate.getMinutes();
        const EndMinutes = OriginalEndDate.getMinutes();
        const newStartDate = new Date(DragStartDate);
        newStartDate.setHours(StartHours);
        newStartDate.setMinutes(StartMinutes);
        const newEndDate = new Date(DragEndDate);
        newEndDate.setHours(new Date(newStartDate).getHours() + parseInt(shiftDuration));
        newEndDate.setMinutes(EndMinutes);
        let data = {
            newStartDate,
            newEndDate
        }
        return data
    }

    const dragEnd = (event) => {
        let eventID = event.detail.item.data._id
        let dragData = ScheduleAllocationData.filter(x => x._id === eventID)[0]
        let dragData1 = ScheduleAllocationData.filter(x => new Date(x.startDateTime).toLocaleDateString() == new Date(event.detail.itemDateRange.dateStart).toLocaleDateString())
        let flag = dragData1.filter(x => x.userID == event.detail.item.data.userID && x.shiftcode == event.detail.item.data.shiftcode)
        if (dragData) {
            if (new Date(dragData.endDateTime).toLocaleDateString() !== new Date(event.detail.itemDateRange.dateEnd).toLocaleDateString()) {
                if (flag.length == 0) {
                    setDropFlag(false)
                    let dates = getTime(event.detail.item.dateStart, event.detail.item.dateEnd, event.detail.itemDateRange.dateStart, event.detail.itemDateRange.dateEnd, event.detail.item.data.shiftDuration)
                    let x = {
                        StartDate: new Date(dates.newStartDate),
                        EndDate: new Date(dates.newEndDate),
                    }
                    dispatch(updateShiftAllocationSchedule(x, eventID, routeParams.code, routeParams.id))
                }
                else {
                    setDropFlag(true)
                    ShowErroMessage(t("UserExist"));
                }
            }
            else {
                setDropFlag(true)
            }
        }
    }

    const onItemClick = (event) => {
        dateData = []
        if (event.detail.item.data) {
            setOpen(true);
            setRemoveID(event.detail.item.data._id);
        }
        else {
            setOpenEdit(true)
            dateData.push({
                startDate: new Date(event.detail.item.dateStart).toLocaleDateString(),
                endDate: new Date(event.detail.item.dateEnd).toLocaleDateString()
            })
        }
    }

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeShiftAllocationScheduleUser(removeID, routeParams.code));
        }
    };

    const handleClose1 = (newValue) => {
        setOpenEdit(false);
    };

    const [loading, setLoading] = useState();

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        <div className="flex flex-1 items-center justify-between">
                            <div className="flex items-center">
                                <Tooltip title="Back to agency" style={{ float: "right" }}>
                                    <Stack direction="row" spacing={2}>
                                        <Button
                                            className="backButton"
                                            variant="contained"
                                            startIcon={<ArrowBackOutlinedIcon />}
                                            onClick={navigateBack}
                                        >
                                            {t("back")}
                                        </Button>
                                    </Stack>
                                </Tooltip>
                            </div>
                        </div>
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center pb-25">
                                <AssignmentIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    <div className="flex flex-col" style={{ fontSize: "1.9rem" }}>
                                        <div>
                                            {t("department")} {" - " + checkValueEmptyOrNull(filteredDepeartmentData[0].name)}
                                        </div>
                                        <div>
                                            {t("scheduleType")} {" - " + checkValueEmptyOrNull(filteredDepeartmentData[0].shiftType[0].name)}
                                        </div>
                                        <div>
                                            {t("schedule")} - {t("from")}: {displayToday + " - "} {t("to")}:  {dsiplayEndDate}
                                        </div>
                                    </div>
                                </Typography>
                            </div>

                        </div>
                    </div>}
                content={
                    <div className="w-full flex flex-col">

                        <FuseScrollbars
                            className="flex-grow overflow-x-auto m-16"
                        >
                            <Grid container>
                                <Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
                                    <Tree ref={treeref} filterable={false} toggleElementPosition="far" style={{ width: '99%' }}>
                                        <TreeItemsGroup expanded>Legend
                                            {filteredShiftsData.map((label) => (
                                                <TreeItem ><FiberManualRecordIcon style={{ color: label.color }} />{checkValueEmptyOrNull(label.ShiftName)} - {convertTo12HourFormat(checkValueEmptyOrNull(label.StartTime))} To {convertTo12HourFormat(checkValueEmptyOrNull(label.EndTime))}</TreeItem>
                                            ))}
                                        </TreeItemsGroup>
                                    </Tree>
                                </Grid>

                                <Grid item xs={12} sm={10} md={10} lg={10} xl={10}>
                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Schedule" />} onReset={() => { }} >
                                        <Schedule
                                            data={data}
                                            todayDate={filterdata[0].StartDate}
                                            ScheduleAllocationData={ScheduleAllocationData}
                                            isDragAndDrop={false}
                                            onDragEnd={dragEnd}
                                            dropFlag={dropFlag}
                                            onItemClick={onItemClick}
                                        ></Schedule>
                                    </ErrorBoundary>
                                </Grid>
                            </Grid>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteShiftText")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>
                            {openEdit &&
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="AddUserDialog" />} onReset={() => { }}>
                                    <AddUserDialog
                                        id="ringtone-menu"
                                        keepMounted
                                        open={openEdit}
                                        text={t("deleteShiftText")}
                                        onClose={handleClose1}
                                        // value={dateData}
                                        TeamData={TeamData}
                                        shiftsArray={shiftsArray}
                                        DepartmentData={DepartmentData}
                                        filterdata={filterdata}
                                        code={routeParams.code}
                                        agencyCode={agencyCode}
                                        scheduleAllocationID={routeParams.id}
                                        dates={dateData}
                                        ScheduleAllocationData={ScheduleAllocationData}
                                    >
                                    </AddUserDialog>
                                </ErrorBoundary>
                            }
                        </FuseScrollbars>
                    </div>
                }
                innerScroll
            />
        </>
    );
}



export default ViewAndEditSchedule;