import Button from '@mui/material/Button';
import makeStyles from '@mui/styles/makeStyles';
import React, { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { GetCountyExtentsData, GetCityExtentsData, UserDefaultLocationUpdate } from '../../../auth/store/registerSlice';

import { useTranslation } from 'react-i18next';
import { Toolbar, Grid, CircularProgress, IconButton, Typography } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import FormControl from '@mui/material/FormControl';

import FormLabel from '@mui/material/FormLabel';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import { showMessage } from 'app/store/fuse/messageSlice';
import { isEmptyOrNull } from '../../utils/utils';
import "./UserDefaultLocationSetting.css"
import CommonAutocomplete, { handleSelectKeyDown } from '../ReuseComponents/CommonAutocomplete';
import CircularProgressLoader from '../CircularProgressLoader/CircularProgressLoader';
import "./UserDefaultLocationSetting.css"

const useStyles = makeStyles({
    w50: {
        width: '100%',
        marginBottom: "1rem"
    }
})


const schema = yup.object().shape({
    defaultlocationtype: yup.string().required('Please select location type.'),
});

const defaultValues = {
    defaultlocationtype: '',
    city: '',
    county: ''
}


function UserDefaultLocationSetting(props) {

    // const { defaultLocationValue, cityValue, countyValue } = props
    const { t } = useTranslation('laguageConfig');
    const classes = useStyles();
    const dispatch = useDispatch();
    const [loading, setLoading] = React.useState(false);
    const user = useSelector(({ auth }) => auth.user);
    const [defaultLocationValue, setdefaultLocationValue] = React.useState('CITY');
    const defaultlocation = useSelector(
        ({ auth }) => auth.user.defaultlocation
    );
    const isLoading = useSelector(({ auth }) => auth.register.isloading);
    const [cityValue, setCityValue] = React.useState(null);
    const [countyValue, setCountyValue] = React.useState(null);
    const register = useSelector(({ auth }) => auth.register);
    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });

    const { isValid, dirtyFields, errors } = formState;

    useEffect(() => {
        dispatch(GetCountyExtentsData());
        dispatch(GetCityExtentsData());
    }, []);
    
    useEffect(() => {
        setSelectedDefaultValue();
    }, [defaultlocation, register])

    function setSelectedDefaultValue() {
        //
        if (props.user !== null && props.user !== undefined) {
            if (!isEmptyOrNull(props.user.defaultLocationType)) {
                setdefaultLocationValue(props.user.defaultLocationType)
                if(props.user.defaultLocation) {
                    if (props.user.defaultLocationType === "CITY") {
                        const defaultLocationCityObj = register && register.cityData && register.cityData.find(x => x.CITYNAME === props.user.defaultLocation)
                        setCityValue(defaultLocationCityObj);
                    } else {
                        const defaultLocationCountyObj = register && register.countyData && register.countyData.find(x => x.COUNTY === props.user.defaultLocation)
                        setCountyValue(defaultLocationCountyObj);
                    }
                }
            }
        }
        else if (defaultlocation && props.flag == false) {

            if (!isEmptyOrNull(defaultlocation.defaultLocation)) {
                setdefaultLocationValue(defaultlocation.defaultLocationType)
                if (defaultlocation.defaultLocationType === "CITY") {
                    const defaultLocationObj = register && register.cityData && register.cityData.find(x => x.CITYNAME === defaultlocation.defaultLocation);
                    setCityValue(defaultLocationObj);
                } else {
                    const defaultLocationCountyObj = register && register.cityData && register.cityData.find(x => x.COUNTY === defaultlocation.defaultLocation)
                    setCountyValue(defaultLocationCountyObj);
                }
            }
        }
        else {
            if(props.flag) {
                props.handleDefaultLocation(defaultLocationValue)
                props.handleCity(cityValue)
                props.handleCounty(countyValue)
            }
        }

    }

    const handleDefaultLocationTypeChange = (event) => {
        if (props.flag == true) {
            props.handleDefaultLocation(event.target.value)
        }
        setdefaultLocationValue(event.target.value);
        setValue('defaultlocationtype', event.target.value);
        setCountyValue(null);
        setCityValue(null);
    };


    const handleCityChange = (event, newValue) => {
        if (props.flag == true) {
            props.handleCity(newValue);
        }
        setCityValue(newValue);
        setCountyValue(null);
        setValue('defaultlocationtype', "CITY");
    };

    const handleCountyChange = (event, newValue) => {
        if (props.flag == true) {
            props.handleCounty(newValue);
        }

        setCountyValue(newValue);
        setCityValue(null);
        setValue('defaultlocationtype', "COUNTY");
    };

    const onSDefaultLocationSubmit = (model) => {

        if (!cityValue && !countyValue && props.flag !== true) {
            ShowErroMessage(t("PLEASE_CHOOSE_LOCATION"));
        }
        else {
            dispatch(UserDefaultLocationUpdate({
                _id: isEmptyOrNull(props.user._id) ? props.user.id : props.user._id,
                defaultLocation: isEmptyOrNull(cityValue) ? countyValue?.COUNTY : cityValue?.CITYNAME,
                defaultLocationType: model.defaultlocationtype
            }));
        }

    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }


    return <>
        <form key={1} className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSDefaultLocationSubmit)}>


            {
                (window.location.pathname == '/admin/create_New_User') ?
                    (
                        <>
                            {isLoading && <CircularProgressLoader loading={isLoading} />}
                            <Grid container spacing={2}>
                                <Grid item xs={4}>

                                    <FormControl component="fieldset" className={`${classes.w50}`}>
                                        <FormLabel component="legend">{t("defaultLocationType")}</FormLabel>
                                        <RadioGroup
                                            row aria-label="gender"
                                            name="defaultlocationtype"
                                            value={defaultLocationValue}
                                            onChange={handleDefaultLocationTypeChange}
                                        >
                                            <FormControlLabel value="CITY" control={<Radio />} label={t("city")} sx={{ zIndex: 0 }} />
                                            <FormControlLabel value="COUNTY" control={<Radio />} label={t("county")} sx={{ zIndex: 0 }} />
                                        </RadioGroup>
                                    </FormControl>
                                </Grid>
                                <Grid item xs={4}>
                                    <FormLabel component="legend">{t("defaultLocation")} </FormLabel>
                                    {defaultLocationValue === "CITY" ?
                                        <FormControl sx={{ mb: 2, minWidth: 120, width: '80%' }}>
                                            <CommonAutocomplete
                                                parentCallback={handleCityChange}
                                                options={register?.cityData || []}
                                                value={cityValue}
                                                fieldName={t("selectLocation")}
                                                optionLabel={"CITYNAME"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </FormControl>
                                        :
                                        <FormControl sx={{ mb: 2, minWidth: 120, width: '80%' }} >
                                            <CommonAutocomplete
                                                parentCallback={handleCountyChange}
                                                options={register?.countyData || []}
                                                value={countyValue || null}
                                                fieldName={t("selectLocation")}
                                                optionLabel={"COUNTY"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </FormControl>
                                    }
                                </Grid>

                                <Grid item xs={4} >
                                    <FormControl component="fieldset" className='ml-16'>
                                        <FormLabel component="legend">{t("mapZoomLevel")}</FormLabel>
                                        <RadioGroup
                                            row aria-label="gender"
                                            name="mapZoomLevel"
                                            value={props.zoomLevelValue}
                                            onChange={props.handleZoomLevelChange}
                                        >
                                            <FormControlLabel value="CITY" control={<Radio />} label={t("city")} />
                                            <FormControlLabel value="COUNTY" control={<Radio />} label={t("county")} />
                                        </RadioGroup>
                                    </FormControl>
                                </Grid></Grid>
                        </>)
                    :
                    (<Grid container spacing={2}>
                        <Grid item xs={6}>

                            <FormControl component="fieldset" className={`${classes.w50}`}>
                                <FormLabel component="legend">{t("defaultLocationType")}</FormLabel>
                                <RadioGroup
                                    row aria-label="gender"
                                    name="defaultlocationtype"
                                    value={defaultLocationValue}
                                    onChange={handleDefaultLocationTypeChange}
                                >
                                    <FormControlLabel value="CITY" control={<Radio />} label={t("city")} sx={{ zIndex: 0 }} />
                                    <FormControlLabel value="COUNTY" control={<Radio />} label={t("county")} sx={{ zIndex: 0 }} />
                                </RadioGroup>
                            </FormControl>
                        </Grid>
                        <Grid item xs={6}>
                            <FormLabel component="legend">{t("defaultLocation")} </FormLabel>
                            {defaultLocationValue === "CITY" ?
                                <FormControl sx={{ mb: 2, minWidth: 120, width: '80%' }}>
                                    <CommonAutocomplete
                                        parentCallback={handleCityChange}
                                        options={register?.cityData || []}
                                        value={cityValue}
                                        fieldName={t("selectLocation")}
                                        optionLabel={"CITYNAME"}
                                        onKeyDown={handleSelectKeyDown}
                                    />
                                </FormControl>
                                :
                                <FormControl sx={{ mb: 2, minWidth: 120, width: '80%', zIndex: 99 }} >
                                    <CommonAutocomplete
                                        parentCallback={handleCountyChange}
                                        options={register?.countyData || []}
                                        value={countyValue || null}
                                        fieldName={t("selectLocation")}
                                        optionLabel={"COUNTY"}
                                        onKeyDown={handleSelectKeyDown}
                                    />
                                </FormControl>
                            }
                        </Grid>
                    </Grid>
                    )
            }



            {props.flag != true && <div>
                <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    className="normal-case"
                    aria-label="UPDATE"
                    value="legacy">
                    {/* Update */}
                    {t('update')}
                </Button>
                {loading && <CircularProgress size={24} />}
            </div>
            }
        </form>
    </>
}


export default UserDefaultLocationSetting;