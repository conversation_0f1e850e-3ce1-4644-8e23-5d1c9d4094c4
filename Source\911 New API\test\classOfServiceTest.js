process.env.NODE_ENV = 'test';

let mongoose = require("mongoose");


//Require the dev-dependencies
let chai = require('chai');
let chaiHttp = require('chai-http');
let server = require('../index');
let should = chai.should();


chai.use(chaiHttp);
//Our parent block
describe('classOfService', () => {
   
  describe('/Get classOfService', () => {
      it('it should GET all the classOfService', (done) => {
        chai.request(server)
            .get('/api/classOfService')
            .end((err, res) => {
                  res.should.have.status(200);
                  res.body.should.be.a('array');
                //   res.body.length.should.be.eql(0);
              done();
            });
      });
  });
  describe('/Post classOfServiceAdd', () => {
    it('Add new classOfServiceAdd', (done) => {
      chai.request(server)
          .post('/api/classOfService/classOfServiceAdd')
          .end((err, res) => {
                res.should.have.status(200);
                // res.body.should.be.a('array');
              //   res.body.length.should.be.eql(0);
            done();
          });
    });
});
});