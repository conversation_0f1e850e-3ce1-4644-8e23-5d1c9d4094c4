import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import "./ChangeMugshotCreadentialsDialog.css"
import { useDispatch, useSelector } from 'react-redux';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import ChangeMugshotCredentials from '../../SharedComponents/ChangeMugshotCredentials/ChangeMugshotCredentials';

const ChangeMugshotCreadentialsDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation('laguageConfig');
    const [open, setOpen] = React.useState(false);
    const [flag, setflag] = React.useState(false);
    const [data, setData] = React.useState(null);

    const handleClickOpen1 = () => {
        setOpen(true);
    };
    useImperativeHandle(ref, () => ({
        handleClickOpen(data, flag) {
            setData(data)
            setflag(flag)
            handleClickOpen1();
        },
    }));


    const handleClose = () => {
        setOpen(false);
    };
    const formRef = useRef(null);

    const handledata = () => {
        handleClose();

    }

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className="closeButton"
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title"><h1><b>{t("mugshotCredentialsFor")} {data === null ? "" : data.fname}</b></h1></DialogTitle>
                <DialogContent dividers>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangeMugshotCredentials" />} onReset={() => { }}>
                        <ChangeMugshotCredentials data={data} newFunc={handledata} flag={flag}></ChangeMugshotCredentials>
                    </ErrorBoundary>
                </DialogContent>
            </Dialog>
        </div>
    );
});
export default ChangeMugshotCreadentialsDialog;

