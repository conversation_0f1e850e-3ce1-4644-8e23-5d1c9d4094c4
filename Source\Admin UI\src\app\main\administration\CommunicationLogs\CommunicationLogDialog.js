import React, { forwardRef, useRef, useImperativeHandle, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { saveCommunicatioLog } from '../store/communicationLogsSlice';
// import { saveProbableCause } from '../store/probableCausesSlice';

const defaultValues = {
    title: '',
    description: ''
};

const CommunicationLogDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState(null);
    const [code, setCode] = React.useState("");
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [PagingDetails, setPagingDetails] = React.useState();
    const [id, setid] = React.useState("");
    // const probableCauses = useSelector(({ administration }) => administration.probableCauses.probableCauses);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues
    });

    const { isValid, dirtyFields, errors } = formState;
    const titleRef = useRef(null);


    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            titleRef.current?.focus();
        }, 0);
    };

    const ClearAll = () => {
        setValue('title', "");
        setValue('description', "");
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, PagingDetails, flag) {
            setData(data)
            setCode(code)
            setPagingDetails(PagingDetails)
            // causeData = probableCauses.filter(y => y._id != data._id)
            setIsUpdate(flag);
            setCommunicatioLogValue(data)
            handleClickOpen1();
        },
    }));

    const setCommunicatioLogValue = async (data) => {
        setid(data._id);
        setValue('title', data.title);
        setValue('description', data.description);
    };

    const handleClose = () => {
        setOpen(false);
        ClearAll();
    };

    function onSubmit(model) {
        dispatch(
            saveCommunicatioLog({
                id: id,
                title: model.title,
                description: model.description,
                isUpdate: isUpdate,
                code: code,
            }
            ));
        setIsUpdate(false);
        setid(null);
        handleClose();
    }

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("communicationLogs")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        name="registerForm"
                        noValidate
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        autoSave={false}
                    >
                        <Controller
                            name="title"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("title")}
                                    type="text"
                                    error={!!errors.title}
                                    helperText={errors?.title?.message}
                                    inputRef={titleRef}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="description"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("description")}
                                    type="text"
                                    error={!!errors.description}
                                    helperText={errors?.description?.message}
                                    multiline
                                    rows={4}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
});

export default CommunicationLogDialog;