import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';
import Cookies from 'js-cookie';

export const getIncidentListData = (code, userId, sortField, sortDirection, pageIndex, pageLimit, searchText) => async dispatch => {
	try {
		dispatch(setLoading(true));
		await axios.get(`incident/api/incident/getIncidentList/${code}/${userId}/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}`)
			.then(response => {
				if (response.status === 200) {
					let listData = JSON.parse(decrypt(response.data));
					dispatch(setLoading(false));
					dispatch(setIncidentListdata(listData.incidentList));
					return dispatch(setIncidentListCount(listData.totalCount));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(setLoading(false));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			})
			.catch(error => {
				dispatch(setLoading(false));
			});
	} catch (e) {
		dispatch(setLoading(false));
		return console.error(e.message);
	}
};


//for searching
export const searchIncident = (searchText, code, userId) => async dispatch => {
	try {
		dispatch(setLoading(true));
		axios.get(`incident/api/incident/searchIncident/${searchText}/${code}/${userId}`)
			.then(response => {
				if (response.status == 200) {
					let data = JSON.parse(decrypt(response.data));
					dispatch(setLoading(false));
					dispatch(setIncidentListCount(data.length));
					return dispatch(setIncidentListdata(data));
				}
			})
	} catch (e) {
		return console.error(e.message);
	}
}


const initialState = {
	incidentListdata: [],
	incidentListCount: 0,
	isloading: false
};

const incidentSlice = createSlice({
	name: 'incident',
	initialState,
	reducers: {
		setIncidentListdata: (state, action) => {
			state.incidentListdata = action.payload;
		},
		setLoading: (state, action) => {
			state.isloading = action.payload;
		},
		setIncidentListCount: (state, action) => {
			state.incidentListCount = action.payload;
		},

	},
	extraReducers: {}
});

export const {
	setIncidentListdata,
	setIncidentListCount,
	setLoading
} = incidentSlice.actions;

export default incidentSlice.reducer;
