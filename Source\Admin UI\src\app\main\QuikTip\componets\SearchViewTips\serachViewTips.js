import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import { TextField } from '@mui/material';
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import FormControl from '@mui/material/FormControl';
import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker';
import { getDepartmentDetails } from "src/app/main/store/departmentSlice";
import { Controller, useForm } from 'react-hook-form';
import { showMessage } from "app/store/fuse/messageSlice";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import DepartmentDropdownSetting from "src/app/main/SharedComponents/DepartmentDropdownSettings/DepartmentDropdownSetting";
import { getQuikTipDepartment } from "src/app/main/store/quikTipSlice";
import CommonButton from "../../../SharedComponents/ReuseComponents/CommonButton";
import StatusDropdownSettings from "src/app/main/SharedComponents/StatusDropdownSettings/StatusDropdownSettings";

function serachViewTips(props) {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const { handleSubmit } = useForm({
        mode: 'onChange',
    });

    const [departmentValue, setDepartmentValue] = React.useState('');
    const [selectedStartDate, setSelectedStartDate] = React.useState(null);
    const [selectedEndDate, setSelectedEndDate] = React.useState(null);
    const [status, setStatus] = React.useState('');
    const [clear, setclear] = React.useState(false);

    useEffect(() => {
        dispatch(getQuikTipDepartment(props.code));
    }, []);

    const handleDepartment = (departmentId) => {
        setDepartmentValue(departmentId)
    };

    const handleFromDateChange = (date) => {
        setSelectedStartDate(new Date(date));
    };

    const handleToDateChange = (date) => {
        setSelectedEndDate(new Date(date));
    };

    const handleStatus = (statusID) => {
        setStatus(statusID);
    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const handleClose = () => {
        setSelectedStartDate(null)
        setSelectedEndDate(null)
    };

    const handleClear = () => {
        setclear(true);
        setSelectedStartDate(null);
        setSelectedEndDate(null);
        setDepartmentValue('');
        setStatus('');
    };

    function onSubmit(model) {
        if (!validateDates(selectedStartDate, selectedEndDate)) {
            ShowErroMessage(t('dateValidation'));
        }
        else {
            props.handleSearchData(departmentValue, selectedStartDate, selectedEndDate, status)
        }
        // handleClose()

    }

    function validateDates(startdate, enddate) {
        // Convert the date strings into Date objects
        var startDateObj = new Date(startdate);
        var endDateObj = new Date(enddate);

        startDateObj.setHours(0, 0, 0, 0);
        endDateObj.setHours(0, 0, 0, 0);

        // Compare the Date objects
        if (startDateObj <= endDateObj) {
            return true; // startdate is less than enddate
        } else {
            return false; // startdate is not less than enddate
        }
    }

    return (
        <>
            <div className="w-full">
                <form
                    className="flex flex-col justify-center pb-16"
                    style={{ flexDirection: "row", justifyContent: "space-between", gap: "30px" }}
                    onSubmit={handleSubmit(onSubmit)}
                    // autoComplete="off"
                    autoSave={false}
                >

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="DepartmentDropdownSetting" />} onReset={() => { }}>
                        <DepartmentDropdownSetting code={props.code} flag={true} departmentId={handleDepartment}
                            clear={clear} departmentValue={departmentValue} setclear={setclear}>
                        </DepartmentDropdownSetting>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="StatusDropdownSettings" />} onReset={() => { }}>
                        <StatusDropdownSettings code={props.code} flag={true} statusID={handleStatus}
                            clear={clear} status={status} setclear={setclear}>
                        </StatusDropdownSettings>
                    </ErrorBoundary>

                    <FormControl fullWidth className="w-full backButton">
                        <DesktopDatePicker
                            name="StartDate"
                            color="action"
                            label={t("startDate")}
                            inputFormat="MM/dd/yyyy"
                            defaultDate={" "}
                            value={selectedStartDate}
                            onChange={handleFromDateChange}
                            emptyLabel="custom label"
                            renderInput={(field) => <TextField  {...field} sx={{ mt: 2 }} />}
                        />
                    </FormControl>

                    <FormControl fullWidth className="w-full">
                        <DesktopDatePicker
                            name="EndDate"
                            color="action"
                            className="w-full"
                            label={t("endDate")}
                            inputFormat="MM/dd/yyyy"
                            defaultDate={" "}
                            value={selectedEndDate}
                            onChange={handleToDateChange}
                            emptyLabel="custom label"
                            renderInput={(field) => <TextField  {...field} sx={{ mt: 2 }} />}
                        />
                    </FormControl>
                    <div className='flex justify-end'>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_Search" />} onReset={() => window.location.reload()} >
                            <CommonButton styleClass="w-auto mr-16 mt-16" btnName={t("search")} parentCallback={onSubmit}></CommonButton>
                        </ErrorBoundary>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_Clear" />} onReset={() => window.location.reload()} >
                            <CommonButton styleClass="w-auto mr-16 mt-16" btnName={t("clear")} parentCallback={handleClear}></CommonButton>
                        </ErrorBoundary>
                    </div>
                </form>
            </div>
        </>
    )
}

export default serachViewTips;