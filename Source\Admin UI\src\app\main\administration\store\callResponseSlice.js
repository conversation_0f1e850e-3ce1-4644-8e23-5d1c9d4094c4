import { createSlice } from "@reduxjs/toolkit";
import { showMessage } from "app/store/fuse/messageSlice";
import { decrypt, encrypt } from "../../../security/crypto";
import axios from "axios";

export const saveCallResponse = (data, pageIndex, pageLimit, sortField, searchText, sortDirection) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/callResponse`, encrypt(JSON.stringify(data)))
            .then(async (res) => {
                if (res.status == 200) {
                    res.data = await JSON.parse(decrypt(res.data));
                    dispatch(setLoading(false));
                    if (!res.data.exist) {
                        dispatch(getCallResponse(sortField, sortDirection, pageIndex, pageLimit, searchText, data.code));
                        return dispatch(
                            showMessage({
                                message: data.isUpdate ? res.data.message : res.data.message,
                                autoHideDuration: 2000,
                                anchorOrigin: {
                                    vertical: "top",
                                    horizontal: "right",
                                },
                                variant: "success",
                            })
                        );
                    } else {
                        return dispatch(showMessage({
                            message: res.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "center",
                            }, variant: "warning",
                        })
                        );
                    }
                }
                else {
                    let response = JSON.parse(decrypt(res.response.data));
                    dispatch(setLoading(false));
                    return dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getCallResponse = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/callResponse/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(setCallResponseTotalCount(listData.totalCount));
                    return dispatch(setCallResponseData(listData.callResponseList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    return dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const removeResponse = (ID, code, pageIndex, pageLimit, sortField, searchText, sortDirection) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .delete(`admin/api/callResponse/${ID}/${code}`)
            .then(async (res) => {
                if (res.status == 200) {
                    res = JSON.parse(decrypt(res.data));
                    dispatch(setLoading(false));
                    dispatch(getCallResponse(sortField, sortDirection, pageIndex, pageLimit, null, code));
                    return dispatch(showMessage({
                        message: res.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: "top",
                            horizontal: "right",
                        },
                        variant: "success",
                    }));
                }
                else {
                    let response = JSON.parse(decrypt(res.response.data));
                    dispatch(setLoading(false));
                    return dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

const initialState = {
    success: false,
    callResponses: [],
    isloading: false,
};

const callResponseSlice = createSlice({
    name: "administration/CallResponse",
    initialState,
    reducers: {
        setCallResponseData: (state, action) => {
            state.callResponses = action.payload;
        },
        setCallResponseTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
    },
    extraReducers: {},
});

export const {
    setCallResponseData,
    setCallResponseTotalCount,
    setLoading,
} = callResponseSlice.actions;

export default callResponseSlice.reducer;