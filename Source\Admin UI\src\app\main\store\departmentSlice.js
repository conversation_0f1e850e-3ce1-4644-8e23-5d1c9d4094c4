import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const getDepartmentDetails = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/department/getDepartmentDetails/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then(async (response) => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    let listData = await JSON.parse(decrypt(response.data));
                    dispatch(setDepartmentTotalCount(listData.totalCount));
                    dispatch(setShiftTypeData(listData.shiftType));
                    return dispatch(setDepartmentData(listData.departmentData));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const getMasterDepartmentDetails = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/department/getMasterDepartmentDetails/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(setShiftTypeData(listData.shiftType));
                    dispatch(setDepartmentMasterTotalCount(listData.totalCount));
                    return dispatch(setMasterDepartmentData(listData.departmentData));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const saveDepartment = (data) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/department/createDepartment`, encrypt(JSON.stringify(data)))
            .then(async (response) => {
                if (response.status == 200) {
                    response.data = await JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "right",
                            },
                            variant: "success",
                        })
                    );
                    if (data.isUpdate) {
                        dispatch(setDepartmentEditData(response.data.newData))
                        dispatch(setDepartmentTotalCount(response.data.totalCount));
                    }
                    else {
                        dispatch(setDepartmentAddData(response.data.newData))
                        dispatch(setDepartmentTotalCount(response.data.totalCount));
                    }
                    dispatch(setLoading(false));
                    dispatch(getMasterDepartmentDetails("name", "asc", 0, 10000, null, data.code));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const removeDepartment = (ID, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .delete(`admin/api/department/removeDepartment/${ID}/${code}`)
            .then(async (response) => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: "top",
                            horizontal: "right",
                        },
                        variant: "success",
                    })
                    );
                    dispatch(getMasterDepartmentDetails("name", "asc", 0, 10000, null, code));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const ClearDepartmentData = () => async dispatch => {
    try {
        dispatch(setDepartmentData([]))
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    data: [],
    shiftTypeData: [],
    masterdata: [],
    isloading: false,
};

const departmentSlice = createSlice({
    name: 'department',
    initialState,
    reducers: {
        setDepartmentData: (state, action) => {
            state.data = action.payload;
        },
        setShiftTypeData: (state, action) => {
            state.shiftTypeData = action.payload;
        },
        setMasterDepartmentData: (state, action) => {
            state.masterdata = action.payload;
        },
        setDepartmentTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setDepartmentMasterTotalCount: (state, action) => {
            state.totalMasterCount = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setDepartmentEditData: (state, action) => {
            const index = state.masterdata.findIndex(obj => obj._id === action.payload._id);
            if (index !== -1) {
                state.masterdata[index] = action.payload;
            }
        },
        setDepartmentAddData: (state, action) => {
            state.masterdata = [...state.masterdata, action.payload];
        },
        removeDepartmentData: (state, action) => {
            state.masterdata = state.masterdata.filter(x => x._id !== action.payload);
        },
        clearDepartment: (state, action) => {
            state.masterdata = [];
        },
    },
    extraReducers: {}
});

export const {
    setDepartmentData,
    setDepartmentTotalCount,
    setLoading,
    setShiftTypeData,
    setDepartmentEditData,
    removeDepartmentData,
    setDepartmentAddData,
    setMasterDepartmentData,
    setDepartmentMasterTotalCount, clearDepartment
} = departmentSlice.actions;

export default departmentSlice.reducer;
