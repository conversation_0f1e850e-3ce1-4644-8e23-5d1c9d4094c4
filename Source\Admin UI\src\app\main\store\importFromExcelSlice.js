import { createSlice } from '@reduxjs/toolkit';
import axios from "axios";
import { showMessage } from 'app/store/fuse/messageSlice';
import { encrypt, decrypt } from 'src/app/security';


export const saveExcelIntersectionData = (data, county, importState) => async dispatch => {
    try {

        const tenant = localStorage.getItem('tenant');
        const eventSource = new EventSource(`${tenant}admineventsource/api/intersectionPointDetail/ImportIntersectionPointDetailStream/${county}/${importState}`);

        eventSource.onmessage = (event) => {
            const message = JSON.parse(event.data);

            if (message.complete) {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: message.message,
                    autoHideDuration: 6000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'success',
                }));
                eventSource.close();
                dispatch(setIntersectionImportStatus(null));
            } else if (message.error) {
                dispatch(setLoading(false));
                eventSource.close();
                dispatch(setIntersectionImportStatus(null));
            } else {
                // Handle intermediate updates
                dispatch(showMessage({
                    message: message.message,
                    autoHideDuration: 6000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'info',
                }));
                dispatch(setIntersectionImportStatus(message.message));
            }
        };

        eventSource.onerror = (err) => {
            dispatch(setLoading(false));
            dispatch(showMessage({
                message: 'An error occurred while importing data',
                autoHideDuration: 2000,
                anchorOrigin: { vertical: 'top', horizontal: 'right' },
                variant: 'error',
            }));
            eventSource.close();
            dispatch(setIntersectionImportStatus(null));
        };

        // POST the data
        const response = await axios.post(
            `admin/api/intersectionPointDetail/ImportIntersectionPointDetail/${county}/${importState}`,
            encrypt(JSON.stringify(data))
        );

        if (response.status === 200) {
            dispatch(showMessage({
                message: `Import started for Master Address ${county}, ${importState}`,
                autoHideDuration: 6000,
                anchorOrigin: { vertical: 'top', horizontal: 'right' },
                variant: 'info',
            }));
        } else if (response === undefined || response.status === 400) {
            response.data = JSON.parse(decrypt(response.response.data));
            dispatch(setLoading(false));
            dispatch(showMessage({
                message: response.data.message, autoHideDuration: 2000,
                anchorOrigin: {
                    vertical: 'top',
                    horizontal: 'right'
                },
                variant: 'error'
            }));
        } else {
            response = JSON.parse(decrypt(response.response.data));
            dispatch(setLoading(false));
            dispatch(showMessage({
                message: response.data.message,
                autoHideDuration: 2000,
                anchorOrigin: {
                    vertical: 'top',
                    horizontal: 'right'
                },
                variant: 'warning'

            }))
        }
    } catch (e) {
        dispatch(setLoading(false));
        console.error(e);
        dispatch(showMessage({
            message: 'An error occurred',
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'right' },
            variant: 'error',
        }));
    }
};

export const saveExcelMasterAddressData = (data, county, importState) => async dispatch => {
    try {

        const tenant = localStorage.getItem('tenant');
        const eventSource = new EventSource(`${tenant}admineventsource/api/masterAddressPointDetail/ImportMasterAddressStream/${county}/${importState}`);

        eventSource.onmessage = (event) => {
            const message = JSON.parse(event.data);

            if (message.complete) {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: message.message,
                    autoHideDuration: 6000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'success',
                }));
                eventSource.close();
                dispatch(setMasterAddressImportStatus(null));
            } else if (message.error) {
                dispatch(setLoading(false));
                eventSource.close();
                dispatch(setMasterAddressImportStatus(null));
            } else {
                // Handle intermediate updates
                dispatch(showMessage({
                    message: message.message,
                    autoHideDuration: 6000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'info',
                }));
                dispatch(setMasterAddressImportStatus(message.message));
            }
        };

        eventSource.onerror = (err) => {
            dispatch(setLoading(false));
            dispatch(showMessage({
                message: 'An error occurred while importing data',
                autoHideDuration: 2000,
                anchorOrigin: { vertical: 'top', horizontal: 'right' },
                variant: 'error',
            }));
            eventSource.close();
            dispatch(setMasterAddressImportStatus(null));
        };


        await axios.post(`admin/api/masterAddressPointDetail/ImportMasterAddressPointDetail/${county}/${importState}`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status === 200) {
                    dispatch(showMessage({
                        message: `Import started for Master Address ${county}, ${importState}`,
                        autoHideDuration: 6000,
                        anchorOrigin: { vertical: 'top', horizontal: 'right' },
                        variant: 'info',
                    }));
                } else if (response === undefined || response.status === 400) {
                    response.data = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                } else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            })
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e);
    }
}

export const saveExcelViolationData = (data, importState) => async dispatch => {
    try {

        const tenant = localStorage.getItem('tenant');
        const eventSource = new EventSource(`${tenant}admineventsource/api/stateViolation/ImportStateViolationStream/${importState}`);

        eventSource.onmessage = (event) => {
            const message = JSON.parse(event.data);

            if (message.complete) {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: message.message,
                    autoHideDuration: 6000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'success',
                }));
                eventSource.close();
                // dispatch(setMasterAddressImportStatus(null));
            } else if (message.error) {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: message.message,
                    autoHideDuration: 3000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'error',
                }));
                eventSource.close();
                // dispatch(setMasterAddressImportStatus(null));
            } else {
                // Handle intermediate updates
                dispatch(showMessage({
                    message: message.message,
                    autoHideDuration: 6000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'info',
                }));
                // dispatch(setMasterAddressImportStatus(message.message));
            }
        };

        eventSource.onerror = (err) => {
            dispatch(setLoading(false));
            dispatch(showMessage({
                message: 'An error occurred while importing data',
                autoHideDuration: 2000,
                anchorOrigin: { vertical: 'top', horizontal: 'right' },
                variant: 'error',
            }));
            eventSource.close();
            dispatch(setMasterAddressImportStatus(null));
        };



        await axios.post(`admin/api/stateViolation/ImportViolationDetails/${importState}`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status === 200) {
                    dispatch(showMessage({
                        message: `Import started for Violation, ${importState}`,
                        autoHideDuration: 6000,
                        anchorOrigin: { vertical: 'top', horizontal: 'right' },
                        variant: 'info',
                    }));
                } else if (response === undefined || response.status === 400) {
                    response.data = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: 'Error', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                } else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: 'Error',
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: 'Error',//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            })
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e);
    }
}

export const getMasterAddressAndInterSectionColumn = () => async (dispatch) => {
    try {
        await axios.get(`admin/api/masters/getMasterAddressAndInterSectionColumn`)
            .then((response) => {
                if (response.status == 200) {
                    const listData = JSON.parse(decrypt(response.data));
                    dispatch(SetMasterAddressColumnList(listData.data.length > 0 ? listData.data.filter(x => x.TableName === "MasterAddress")[0] : null));
                    dispatch(SetInterSectionColumnList(listData.data.length > 0 ? listData.data.filter(x => x.TableName === "MasterIntersection")[0] : null));
                }
                else {
                    dispatch(showMessage({
                        message: "Failed",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                return dispatch(showMessage({
                    message: "Error", //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const getInterSectionPointDetailsLogs = (sortField, sortDirection, pageIndex, pageLimit, county, state, isLoading) => async (dispatch) => {
    try {
        dispatch(setLoading(isLoading));
        await axios.get(`admin/api/intersectionPointDetail/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${county}/${state}`)
            .then((response) => {
                if (response.status == 200) {
                    const intersectionLogs = JSON.parse(decrypt(response.data));
                    dispatch(setIntersectionPointSummary(intersectionLogs.intersectionPointDetailSummary));
                    dispatch(setIntersectionPointSummaryCount(intersectionLogs.intersectionPointDetailSummaryCount));
                    dispatch(setLoading(false));
                }
                else {
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: "Failed",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: "Error", //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const getMasterAddressDetailsLogs = (sortField, sortDirection, pageIndex, pageLimit, county, stateCode) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/masterAddressPointDetail/getMasterAddressDetailsLogs/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${county}/${stateCode}`)
            .then((response) => {
                if (response.status == 200) {
                    const masterLogs = JSON.parse(decrypt(response.data));
                    dispatch(setMasterPointSummary(masterLogs));
                    dispatch(setLoading(false));
                }
                else {
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: "Failed",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: "Error", //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const getIntersectionPointErrorLogsById = (sortField, sortDirection, pageIndex, pageLimit, id) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/intersectionPointDetail/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${id}`)
            .then((response) => {
                if (response.status == 200) {
                    const intersectionErrorLogs = JSON.parse(decrypt(response.data));
                    dispatch(setIntersectionPointErrorLogs(intersectionErrorLogs.intersectionImportErrorLogs));
                    dispatch(setIntersectionPointErrorLogsCount(intersectionErrorLogs.intersectionImportErrorLogsCount));
                    dispatch(setLoading(false));
                }
                else {
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: "Failed",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: "Error", //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const getMasterAddressErrorLogsById = (sortField, sortDirection, pageIndex, pageLimit, id) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/masterAddressPointDetail/getMasterAddressErrorLogsById/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${id}`)
            .then((response) => {
                if (response.status == 200) {
                    const masterErrorLogs = JSON.parse(decrypt(response.data));
                    dispatch(setMasterAddressErrorLogs(masterErrorLogs));
                    dispatch(setLoading(false));
                }
                else {
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: "Failed",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: "Error", //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const setIntersectionSelectedSummaryId = (id) => async (dispatch) => {
    try {
        dispatch(setIntersectionSummaryId(id));
    } catch (e) {
        console.error("Failed to set Intersection Error Id");
    }
};

export const setMasterSelectedSummaryId = (id) => async (dispatch) => {
    try {
        dispatch(setMasterSummaryId(id));
    } catch (e) {
        console.error("Failed to set Master Error Id");
    }
};

export const uploadFile = (file, type, countyName) => async dispatch => {
    try {
        // dispatch(setLoading(true));
        if (file) {
            await axios.post(`fileupload/uploadExcelFile/${type}/${countyName}`, file, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            })
                .then((response) => {
                    dispatch(setFileUploadResponse(response.data));
                    // dispatch(saveExcelMasterAddressData(data));
                    dispatch(setLoading(false));
                })
                .catch((err) => {
                    dispatch(clearFileUploadResponse());
                    console.log(err);
                });
        }
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(error);
    }
};


export const uploadViolationFile = (file, stateCode) => async dispatch => {
    try {
        if (file) {
            await axios.post(`fileupload/uploadViolationFile/${stateCode}`, file, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            })
                .then((response) => {
                    if (response.status === 200) {
                        dispatch(setViolationUploadResponse(response.data));
                        dispatch(showMessage({
                            message: `Violation File Upload Successfull for ${stateCode}`,
                            autoHideDuration: 5000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'

                        }))
                    }
                    dispatch(setLoading(false));
                    debugger;
                })
                .catch((err) => {
                    console.log(err);
                });
        }
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(error);
    }
}


const initialState = {
    isLoading: false,
    excelAddDataInitiated: false,
    intersectionImportStatus: null,
    masterAddressImportStatus: null,
    fileUploadResponseData: null,
    masterAddressColumnList: null,
    interSectionColumnList: null,
    intersectionPointDetailSummary: null,
    intersectionPointDetailSummaryCount: 0,
    intersectionPointErrorLogs: null,
    intersectionPointErrorLogsCount: 0,
    masterAddressSummary: null,
    masterAddressErrorLogs: null,
    intersectionSummaryId: null,
    masterSummaryId: null,
    violationUploadResponse: null,
}

const importFromExcelSlice = createSlice({
    name: 'importExcel',
    initialState,
    reducers: {
        setLoading: (state, action) => {
            state.isLoading = action.payload;
        },
        setAddExcelData: (state, action) => {
            state.excelAddDataInitiated = true;
        },
        setIntersectionImportStatus: (state, action) => {
            state.intersectionImportStatus = action.payload;
        },
        setMasterAddressImportStatus: (state, action) => {
            state.masterAddressImportStatus = action.payload;
        },
        setFileUploadResponse: (state, action) => {
            state.fileUploadResponseData = action.payload;
        },
        clearFileUploadResponse: (state, action) => {
            state.fileUploadResponseData = null;
        },
        SetMasterAddressColumnList: (state, action) => {
            state.masterAddressColumnList = action.payload;
        },
        SetInterSectionColumnList: (state, action) => {
            state.interSectionColumnList = action.payload;
        },
        setIntersectionPointSummary: (state, action) => {
            state.intersectionPointDetailSummary = action.payload;
        },
        setMasterPointSummary: (state, action) => {
            state.masterAddressSummary = action.payload;
        },
        setIntersectionPointErrorLogs: (state, action) => {
            state.intersectionPointErrorLogs = action.payload
        },
        setMasterAddressErrorLogs: (state, action) => {
            state.masterAddressErrorLogs = action.payload;
        },
        setIntersectionPointSummaryCount: (state, action) => {
            state.intersectionPointDetailSummaryCount = action.payload;
        },
        setIntersectionPointErrorLogsCount: (state, action) => {
            state.intersectionPointErrorLogsCount = action.payload;
        },
        setIntersectionSummaryId: (state, action) => {
            state.intersectionSummaryId = action.payload;
        },
        setMasterSummaryId: (state, action) => {
            state.masterSummaryId = action.payload;
        },
        setViolationUploadResponse: (state, action) => {
            state.violationUploadResponse = action.payload;
        }
    }
});

export default importFromExcelSlice.reducer;

export const {
    setLoading,
    setAddExcelData,
    setIntersectionImportStatus,
    setMasterAddressImportStatus,
    setFileUploadResponse,
    clearFileUploadResponse,
    SetMasterAddressColumnList,
    SetInterSectionColumnList,
    setIntersectionPointSummary,
    setMasterPointSummary,
    setIntersectionPointErrorLogs,
    setMasterAddressErrorLogs,
    setIntersectionPointSummaryCount,
    setIntersectionPointErrorLogsCount,
    setIntersectionSummaryId,
    setMasterSummaryId,
    setViolationUploadResponse
} = importFromExcelSlice.actions;