import ReactCodeInput from "react-verification-code-input";
import Button from '@mui/material/Button';
import { useDispatch, useSelector } from 'react-redux';
import { submit_MFA_OTP } from '../../../auth/store/loginSlice';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import ErrorMessage from '../../SharedComponents/ErrorMessage/ErrorMessage';
import TextMessage from '../../SharedComponents/TextMessage/TextMessage';
import "./MfaOtpLogin.css"
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { getDeviceLicenseCode } from "../../utils/utils";

function MfaOtpLogin(props) {

  const dispatch = useDispatch();
  const login = useSelector(({ auth }) => auth.login);
  const isloadingvalue = useSelector(({ auth }) => auth.login.isloading);
  const mfaOtpvalid = useSelector(({ auth }) => auth.login.mfaOtpvalid);
  const [loading, setLoading] = useState();
  const [deviceLicenseCode, setDeviceLivenseCode] = useState("");

  useEffect(() => {
    setLoading(isloadingvalue)
  }, [isloadingvalue]);



  const handleComplete = async (event) => {

    let agencyCode;
    if (login.isSuperAdmin)
      agencyCode = 'System';
    else if (login.UserAgencyAccessList[0])
      agencyCode = login.userData.defaultAgency;
    else
      agencyCode = null;

    dispatch(submit_MFA_OTP({ email: login.userData.email, code: event, agencyCode }, props.deviceLicenceId));
  }

  // let url = `${process.env.REACT_APP_GateWayAPI_URL}adminqrcode/api/auth/mfa_qr_code/${login.userData.email}`;
  let url = `${process.env.REACT_APP_LoginAPI_URL}api/auth/mfa_qr_code/${login.userData.email}`;

  return (
    <div>
      {loading && < CircularProgressLoader loading={loading} />}
      {!login.userData.mfaEnabled && (
        <div>
          <ErrorBoundary
            FallbackComponent={(props) => <ErrorPage {...props} componentName="TextMessage" />} onReset={() => { }}>
            <TextMessage message="Scan the QR code on your authenticator app" />
          </ErrorBoundary>
          <img src={url} className="QRCode" />
        </div>
      )}

      <ErrorBoundary
        FallbackComponent={(props) => <ErrorPage {...props} componentName="TextMessage" />} onReset={() => { }}>
        <TextMessage message="Please enter the verification code generated from your authenticator app" />
      </ErrorBoundary>
      <ReactCodeInput className="mfa-codebox" onComplete={handleComplete} fieldWidth={40} fieldHeight={40} />

      {mfaOtpvalid &&
        <ErrorBoundary
          FallbackComponent={(props) => <ErrorPage {...props} componentName="ErrorMessage" />} onReset={() => { }}>
          <ErrorMessage message="Invalid verification code" />
        </ErrorBoundary>
      }
      <span className="mt-16" style={{ textAlign: 'center', paddingLeft: '85px' }}>
        <Button variant="text" onClick={() => props.mfaType("email")} color="secondary">Use email</Button>
        <Button variant="text" onClick={() => props.mfaType("text")} color="secondary">Use text</Button>
      </span>
    </div>
  );
}

export default MfaOtpLogin;
