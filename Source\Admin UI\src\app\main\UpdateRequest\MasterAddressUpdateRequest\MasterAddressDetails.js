import { Button, Dialog, DialogActions, DialogContent, DialogTitle, Grid, Icon, IconButton } from "@mui/material";
import React, { useEffect, useState } from "react";
import CircularProgressLoader from "../../SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setMasterAddressByID } from "../../administration/store/masterAddressSlice";
import { Box } from "@mui/system";

const f_w8 = {
    fontWeight: "600",
    fontSize: "15px"
}

function MasterAddressDetails(props) {
    const { onClose, open } = props;
    const dispatch = useDispatch();

    const { t } = useTranslation('laguageConfig');

    const handleOk = () => {
        onClose(true);
        dispatch(setMasterAddressByID(null));
    };

    const [loading, setLoading] = useState(false);
    const [address, setAddress] = useState(null);


    const MasterAddressData = useSelector(({ administration }) => administration.masterAddressSlice.MasterAddressData)
    const isloading = useSelector(({ administration }) => administration.masterAddressSlice.isloading)

    useEffect(() => {
        if (MasterAddressData) {
            setAddress(MasterAddressData);
        }
    }, [MasterAddressData]);

    useEffect(() => {
        setLoading(isloading);
    }, [isloading]);

    return (
        <Dialog
            fullWidth={true}
            maxWidth="md"
            open={open}
            aria-labelledby="responsive-dialog-title"
        >
            <DialogTitle style={{ display: "flex", justifyContent: "space-between" }} id="responsive-dialog-title">
                <div>
                    {t("addressDetails")}
                </div>
                <div>
                    <IconButton onClick={handleOk} aria-label="show more">
                        <Icon>close</Icon>
                    </IconButton>
                </div>
            </DialogTitle>
            <DialogContent dividers style={{ height: "510px", overflow: "auto" }}>
                {loading && <CircularProgressLoader loading={loading} />}
                {address && (
                    <Box component="fieldset"
                        sx={{
                            border: "1px solid #ccc",
                            borderRadius: "8px",
                            padding: "8px",
                            boxSizing: "border-box",
                        }} >
                        <Grid container spacing={2} className="p-16">
                            {Object.entries(address).map(([key, value], index) => (
                                <Grid item xs={6} key={key}>
                                    <div>
                                        <span style={f_w8}>{key}:</span>{" "}
                                        <span style={{ fontSize: "16px" }}>
                                            {typeof value === "object" && value !== null
                                                ? JSON.stringify(value) // Display nested objects as JSON
                                                : value === null
                                                    ? "null"
                                                    : value.toString()} {/* Display other values as strings */}
                                        </span>
                                    </div>
                                </Grid>
                            ))}
                        </Grid>
                    </Box>
                )}
            </DialogContent>
            <DialogActions>
                <Button onClick={handleOk} variant="contained" color="primary">
                    {t("close")}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default MasterAddressDetails;