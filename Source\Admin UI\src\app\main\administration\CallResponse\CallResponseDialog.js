import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { saveCallResponse } from '../store/callResponseSlice';
import { showMessage } from 'app/store/fuse/messageSlice';

let update = false

const CallResponseDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [code, setCode] = React.useState("");
    const [id, setId] = React.useState("");
    const [PagingDetails, setPagingDetails] = React.useState();

    const CallResponse = useSelector(({ administration }) => administration.callResponse.callResponses);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
    });

    const { isValid, dirtyFields, errors } = formState;
    const responseIDRef = useRef(null);
    const responseCodeRef = useRef(null);

    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            if (update) {
                responseCodeRef.current?.focus();
            } else {
                responseIDRef.current?.focus();
            }
        }, 0);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, PagingDetails, flag) {
            setCode(code);
            setPagingDetails(PagingDetails);
            update = flag;
            setCallResponseValue(data);
            handleClickOpen1();
        },
    }));

    const setCallResponseValue = (data) => {
        if (data._id !== null && data._id !== undefined) {
            setId(data._id);
            setValue('ResponseCode', data.ResponseCode);
            setValue('ResponseID', data.ResponseID);
            setValue('ResponseCodeDescription', data.ResponseCodeDescription);
            setValue('ResponseCodeProQA', data.ResponseCodeProQA);
            setValue('ResponseCodeWestNet', data.ResponseCodeWestNet);
        }
    };

    const clear = () => {
        setValue('ResponseCode', "");
        setValue('ResponseID', "");
        setValue('ResponseCodeDescription', "");
        setValue('ResponseCodeProQA', "");
        setValue('ResponseCodeWestNet', "");
        setId("");
    }

    const handleClose = () => {
        setOpen(false);
        clear();
        update = false;
    };

    function handleCallResponseID(e) {
        let ResponseID = e.target.value;
        let filteredData = CallResponse.filter((x) => x.ResponseID === parseInt(ResponseID));
        setValue('ResponseID', ResponseID);
        if (filteredData.length > 0) {
            ShowErroMessage(t(`The ID ${ResponseID} is already associated with another call response. Please enter a different call ID and proceed.`));
        }
    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    };

    function onSubmit(model) {
        const data = {
            id: id,
            ResponseCode: model.ResponseCode,
            ResponseID: model.ResponseID === "" ? 0 : parseInt(model.ResponseID),
            ResponseCodeDescription: model.ResponseCodeDescription,
            ResponseCodeProQA: model.ResponseCodeProQA,
            ResponseCodeWestNet: model.ResponseCodeWestNet,
            isUpdate: update,
            code: code,
        }
        dispatch(
            saveCallResponse(
                data,
                PagingDetails.pageIndex,
                PagingDetails.rowsPerPage,
                PagingDetails.id,
                PagingDetails.searchText,
                PagingDetails.direction
            )
        );
        handleClose();

    }

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("callResponseCode")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        name="registerForm"
                        noValidate
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        // autoComplete="off"
                        autoSave={false}
                    >
                        <Controller
                            name="ResponseID"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("id")}
                                    type="number"
                                    onChange={handleCallResponseID}
                                    error={!!errors.ResponseID}
                                    helperText={errors?.ResponseID?.message}
                                    disabled={update ? true : false}
                                    variant="outlined"
                                    inputRef={responseIDRef}
                                />
                            )}
                        />
                        <Controller
                            name="ResponseCode"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("code")}
                                    type="text"
                                    error={!!errors.ResponseCode}
                                    helperText={errors?.ResponseCode?.message}
                                    variant="outlined"
                                    required
                                    inputRef={responseCodeRef}
                                />
                            )}
                        />

                        <Controller
                            name="ResponseCodeDescription"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("codeDescription")}
                                    type="text"
                                    error={!!errors.ResponseCodeDescription}
                                    helperText={errors?.ResponseCodeDescription?.message}
                                    variant="outlined"
                                />
                            )}
                        />

                        <Controller
                            name="ResponseCodeProQA"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("codeProQA")}
                                    type="text"
                                    error={!!errors.ResponseCodeProQA}
                                    helperText={errors?.ResponseCodeProQA?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <Controller
                            name="ResponseCodeWestNet"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("codeWestNet")}
                                    type="text"
                                    error={!!errors.ResponseCodeWestNet}
                                    helperText={errors?.ResponseCodeWestNet?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );

});

export default CallResponseDialog;