import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { submitLogin, submitOTP, getOtp } from '../../../auth/store/loginSlice';
import _ from '@lodash';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import ReactCodeInput from "react-verification-code-input";
import ErrorMessage from '../../SharedComponents/ErrorMessage/ErrorMessage';
import TextMessage from '../../SharedComponents/TextMessage/TextMessage';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { ErrorBoundary } from 'react-error-boundary';

function SMSTextOtpLogin(props) {
    const { email, password } = props;
    const dispatch = useDispatch();

    const login = useSelector(({ auth }) => auth.login);

    const isloadingvalue = useSelector(({ auth }) => auth.login.isloading);
    const emailOtpvalid = useSelector(({ auth }) => auth.login.emailOtpvalid);
    const [loading, setLoading] = useState();



    const handleComplete = (event) => {
        let mfaType = "text";
        let agencyCode;
        if (login.isSuperAdmin)
            agencyCode = 'System';
        else if (login.UserAgencyAccessList[0])
            agencyCode = login.userData.defaultAgency;
        else
            agencyCode = null;

        dispatch(submitOTP({ email, otp: event, agencyCode, mfaType }, props.deviceLicenceId));
    }


    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    function GetOTP() {
        let mfaType = "text";
        dispatch(getOtp({ email, password, mfaType }, props.deviceLicenceId));
    }

    return (
        <div>
            {loading && < CircularProgressLoader loading={loading} />}
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="TextMessage" />} onReset={() => { }}>
                <TextMessage message="Please enter the verification code sent to your mobile" />
            </ErrorBoundary>
            <ReactCodeInput className="mfa-codebox" onComplete={handleComplete} fieldWidth={40} fieldHeight={40} />
            {emailOtpvalid &&
                <ErrorBoundary
                    FallbackComponent={(props) => <ErrorPage {...props} componentName="ErrorMessage" />} onReset={() => { }}>
                    <ErrorMessage message="Invalid verification code" />
                </ErrorBoundary>
            }
            <span className="mt-16" style={{ textAlign: 'center', paddingLeft: '38px' }}>Didn't receive an OTP?...
                <Button variant="text" onClick={GetOTP} color="secondary">Resend OTP</Button>
            </span>
            <span className="mt-16" style={{ textAlign: 'center', paddingLeft: '55px' }}>
                <Button variant="text" onClick={() => props.mfaType("authenticator")} color="secondary">Use authenticator</Button>
                <Button variant="text" onClick={() => props.mfaType("email")} color="secondary">Use email</Button>
            </span>
        </div>
    )
}

export default SMSTextOtpLogin;
