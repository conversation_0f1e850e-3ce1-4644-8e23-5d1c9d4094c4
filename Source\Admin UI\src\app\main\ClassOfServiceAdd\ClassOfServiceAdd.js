import Button from "@mui/material/Button";
import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageSimple from "@fuse/core/FusePageSimple";
import Typography from "@mui/material/Typography";
// import { useForm } from "@fuse/hooks";
import history from "@history";
import { useTranslation } from "react-i18next";
import makeStyles from "@mui/styles/makeStyles";
import { HexColorPicker } from "react-colorful";
// import "react-colorful/dist/index.css";
import "./classOfServiceStyle.css";
import {
  getClassOfServiceByID,
  newClassOfService,
  updateClassOfService,
} from "../ClassOfServicePage/store/classOfServiceSlice";
import { Controller, useForm } from "react-hook-form";
import { TextField } from "@mui/material";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

const useStyles = makeStyles((theme) => ({
  input: {
    display: "none",
  },
}));

function ClassOfServiceAddPage(props) {
  const classes = useStyles(props);

  const {
    control,
    setValue,
    formState,
    handleSubmit,
    reset,
    trigger,
    setError,
  } = useForm({
    mode: "onChange",
  });

  const { isValid, dirtyFields, errors } = formState;

  const dispatch = useDispatch();
  const formRef = useRef(null);
  let pageType = "Add";

  const { t } = useTranslation("laguageConfig");
  const [color, setColor] = useState("");

  const classOfServiceID = useSelector(
    ({ classOfService }) => classOfService.classOfService.classOfServiceID
  );
  const classOfService = useSelector(
    ({ classOfService }) => classOfService.classOfService.classOfServiceData
  );
  const classOfServiceColor = useSelector(
    ({ classOfService }) => classOfService.classOfService.classOfServiceColor
  );

  useEffect(() => {
    if (classOfServiceID !== "0") {
      dispatch(getClassOfServiceByID(classOfServiceID));
    }
  }, [dispatch, classOfServiceID]);

  useEffect(() => {
    if (classOfServiceID !== "0") {
      setColor(classOfServiceColor);
    } else {
      setColor("");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [classOfServiceColor]);

  //   if (classOfServiceID === "0") {
  //     setValue("code", "");
  //     setValue("description", "");
  //     setValue("color", "");
  //     setValue("icon", "");
  //   } else {
  //     setValue("code", classOfService.code);
  //     setValue("description", classOfService.description);
  //     setValue("color", classOfService.color);
  //     setValue("icon", classOfService.icon);
  //   }

  if (classOfServiceID !== "0") {
    setValue("code", classOfService.code);
    setValue("description", classOfService.description);
    setValue("color", classOfService.color);
    setValue("icon", classOfService.icon);
  }

  const navigateBack = () => {
    history.push("/911/classOfServiceList");
  };

  //   alert(color)
  function onSubmit(classOfService) {
    
    const data = {
      _id: "",
      code: classOfService.code.toUpperCase(),
      description: classOfService.description,
      color: color,
      iconPic: "",
      file: fileUpload.current.files[0] ? fileUpload.current.files[0] : null,
    };

    if (classOfServiceID === "0") {
      dispatch(newClassOfService(data));
    } else {
      data._id =
        classOfService._id === undefined
          ? classOfServiceID
          : classOfService._id;
      dispatch(updateClassOfService(data));
    }
  }

  const fileUpload = useRef(null);

  return (
    <FusePageSimple
      header={
        <div className="flex flex-1 items-center justify-between p-24">
          <div className="flex flex-col">
            <Typography variant="h5">
              {t(pageType)} {t("classOfService")}
            </Typography>
          </div>
        </div>
      }
      content={
        <div className="w-full p-16">
          <form
            onSubmit={handleSubmit(onSubmit)}
            ref={formRef}
            className="flex flex-col justify-center w-full"
            // key={0}
          >
            {classOfServiceID === "0" && (
              <Controller
                name="code"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    className="mb-16"
                    type="text"
                    //   error={!!errors.email}
                    //   helperText={errors?.email?.message}
                    label={t("codeName")}
                    variant="outlined"
                    required
                  />
                )}
              />
            )}

            {/* 
						{pageType === "EDIT" &&
							<TextFieldFormsy
								className="mb-16"
								type="text"
								name="code"
								label={t('codeName')}
								variant="outlined"
								value={form.code}
								disabled
							/>
						} */}

            {classOfServiceID !== "0" && (
              <Controller
                name="code"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    className="mb-16"
                    type="text"
                    //   error={!!errors.email}
                    //   helperText={errors?.email?.message}
                    //label={t("codeName")}
                    label={t("codeName")}
                    variant="outlined"
                    disabled={true}
                  />
                )}
              />
            )}

            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  className="mb-16"
                  type="text"
                  //label={t("description")}
                  label={t("description")}
                  variant="outlined"
                />
              )}
            />

            {t("color")}
            <Controller
              name="color"
              control={control}
              render={({ field }) => (
                <HexColorPicker
                  {...field}
                  className="responsive mb-16 mt-10"
                  color={color}
                  onChange={setColor}
                  style={{ width: "auto" }}
                />
              )}
            />

            {/* <Controller
            name="colorPicker"
			
              control={control}
              render={({ field }) => (
                <HexColorPicker
                  {...field}
                  className="responsive mb-16 mt-10"
                  color={color}
                  onChange={setColor}
                />
              )}
            /> */}

            {/* <Controller
              name="color"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  //className={classes.input}
                  type="color"
                  //label={t("color")}
                  variant="outlined"
                  //disabled={true}
                />
              )}
            /> */}

            {t("icon")}
            {classOfService.iconPic !== "" &&
              classOfService.iconPic !== undefined && (
                <div className="w-52">
                  <img
                    className="w-full block rounded"
                    src={classOfService.iconPic}
                    alt="icon"
                  />
                </div>
              )}

            <input
              type="file"
              ref={fileUpload}
              accept="image/*"
              style={{
                padding: "13px",
                border: "1px solid lightgray",
                borderRadius: "4px",
                backgroundColor: "antiquewhite",
                cursor: "pointer",
              }}
            />

            <div className="mx-auto">
              <Button
                type="submit"
                variant="contained"
                color="primary"
                className="normal-case m-16"
                aria-label="REGISTER"
                value="legacy"
                //disabled={_.isEmpty(dirtyFields) || !isValid}
              >
                {classOfServiceID === "0" ? t("save") : t("update")}
              </Button>
              <Button
                type="button"
                variant="contained"
                className="normal-case m-16"
                aria-label="Back"
                value="legacy"
                onClick={navigateBack}
              >
                {t("back")}
              </Button>
            </div>
          </form>
        </div>
      }
    />
  );
}

export default ClassOfServiceAddPage;
