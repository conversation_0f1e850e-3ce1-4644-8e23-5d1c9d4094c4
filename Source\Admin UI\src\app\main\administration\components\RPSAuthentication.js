import Button from '@mui/material/Button';
import React, { useState, useRef, useEffect } from 'react';
import InputAdornment from '@mui/material/InputAdornment';
import { useSelector, useDispatch } from 'react-redux';
import Icon from '@mui/material/Icon';
import { ClearUserData } from '../../../auth/store/registerSlice';
import { useTranslation } from 'react-i18next';
import { CircularProgress, IconButton, Typography } from '@mui/material';
import { newUserAudit } from '../../../main/userAuditPage/store/userAuditSlice';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { UpdateRPSAuthentication } from '../store/usersSlice';

const rpsSchema = yup.object().shape({
    rpsUser: yup.string()
        .required('Please enter your RPS userName.'),
    rpsPassword: yup
        .string()
        .required('Please enter your RPS password.')
});

const rpsDefaultValues = {
    rpsUser: '',
    rpsPassword: '',
};

function RPSAuthentication(props) {
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();
    const [showPassword, setShowPassword] = useState(false);
    const [loading, setLoading] = React.useState(false);

    const user = useSelector(({ auth }) => auth.user);

    const { control: rpsControl, setValue: setRPSValue, formState: rpsFormState, handleSubmit: rpsHandleSubmit,
        reset: rpsReset, trigger: rpsTrigger, setError: setRpsErrors } = useForm({
            mode: 'onChange',
            defaultValues: rpsDefaultValues,
            resolver: yupResolver(rpsSchema),
        });

    const { isValid: isRpsValid, dirtyFields: rpsDirtyFields, errors: rpsErrors } = rpsFormState;

    useEffect(() => {
        dispatch(newUserAudit({
            activity: "Access User Profile",
            user: user,
            appName: "Admin",
        }));
        // eslint-disable-next-line
    }, []);

    function ShowErrorMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const onRPSSubmit = async (model) => {
        if (model.rpsUser !== "" && model.rpsPassword !== "") {
            setLoading(true);
            await dispatch(UpdateRPSAuthentication({
                userName: model.rpsUser,
                password: model.rpsPassword,
                deviceID: "test",
                deviceType: "webadmin",
            },
                user.data.defaultAgency,
                user.data.id,
            ));
            setLoading(false);
        }
        else {
            ShowErrorMessage(t("pleaseEnterUsernameAndPassword"))
        }
    };

    useEffect(() => {
        setRPSValue('rpsPassword', user.data?.rpsUser?.password);
        setRPSValue('rpsUser', user.data?.rpsUser?.userName);
    }, [user])

    return <>
        <form key={1} className="flex flex-col justify-center w-full" onSubmit={rpsHandleSubmit(onRPSSubmit)}>
            {
                user.data?.lastRpsChange &&
                <Typography className="font-semibold text-14 mb-6">{t("lastRpsChange")}: {new Date(user.data?.lastRpsChange)?.toLocaleString() ?? null}</Typography>
            }
            <Controller
                name="rpsUser"
                control={rpsControl}
                render={({ field }) => (
                    <TextField
                        {...field}
                        className="mb-16"
                        label={t('userName')}
                        type="text"
                        disabled={loading}
                        error={!!rpsErrors.rpsUser}
                        helperText={rpsErrors?.rpsUser?.message}
                        variant="outlined"
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <Icon className="text-20" color="action">
                                        account_circle
                                    </Icon>
                                </InputAdornment>
                            ),
                        }}
                        required
                    />
                )}
            />
            <Controller
                name="rpsPassword"
                control={rpsControl}
                render={({ field }) => (
                    <TextField
                        {...field}
                        className="mb-16"
                        label={t('password')}
                        type="text"
                        disabled={loading}
                        error={!!rpsErrors.rpsPassword}
                        helperText={rpsErrors?.rpsPassword?.message}
                        variant="outlined"
                        InputProps={{
                            className: 'pr-2',
                            type: showPassword ? 'text' : 'password',
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton onClick={() => setShowPassword(!showPassword)} size="large">
                                        <Icon className="text-20" color="action">
                                            {showPassword ? 'visibility' : 'visibility_off'}
                                        </Icon>
                                    </IconButton>
                                </InputAdornment>
                            ),
                        }}
                        required
                    />
                )}
            />


            <div >
                <Button
                    type="buttom"
                    variant="contained"
                    color="primary"
                    className="normal-case m-16"
                    //   disabled={_.isEmpty(rpsDirtyFields) || !isRpsValid || loading}
                    value="legacy">
                    {t('verify')}
                </Button>
                {loading && <CircularProgress size={24} />}
            </div>
        </form>
    </>
}


export default RPSAuthentication;