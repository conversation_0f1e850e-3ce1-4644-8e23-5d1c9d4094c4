// import  motion.div from '@fuse/core/ motion.div';
import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import Input from '@mui/material/Input';
import Paper from '@mui/material/Paper';
import { ThemeProvider } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import history from '@history';
import { motion } from 'framer-motion';



import { setClassOfServiceID, setUsersSearchText } from '../store/classOfServiceSlice';
import { selectMainTheme } from 'app/store/fuse/settingsSlice';

function ClassOfServiceListHeader(props) {
	const dispatch = useDispatch();

	const searchText = useSelector(({ classOfService }) => classOfService.classOfService.searchText);	
	
	const mainTheme = useSelector(selectMainTheme);

	const { t } = useTranslation('laguageConfig');

	const  addClassOfService = () => {
		dispatch(setClassOfServiceID("0"));
		//history.push("/classOfServiceAdd");
	};

	return (
		<div className="flex flex-1 w-full items-center justify-between">
			<div className="flex items-center">
				< motion.div animation="transition.expandIn" delay={300}>
					<Icon className="text-32">contacts</Icon>
				</ motion.div>
				< motion.div animation="transition.slideLeftIn" delay={300}>
					<Typography className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
						{t('classOfService')}
					</Typography>
				</ motion.div>
			</div>

			<div className="flex flex-1 items-center justify-center px-12">
				<ThemeProvider theme={mainTheme}>
					< motion.div animation="transition.slideDownIn" delay={300}>
						<Paper className="flex items-center w-full max-w-512 px-8 py-4 rounded-8" elevation={1}>
							<Icon color="action">search</Icon>

							<Input
								placeholder={t('search')}
								className="flex flex-1 mx-8"
								disableUnderline
								fullWidth
								value={searchText}
								inputProps={{
									'aria-label': 'Search'
								}}
								onChange={ev => dispatch(setUsersSearchText(ev))}
							/>
						</Paper>
					</ motion.div>
				</ThemeProvider>
			</div>
			< motion.div animation="transition.slideRightIn" delay={300}>
			<Link to={'/911/classOfServiceAdd'} role="button">
				<Button
					//component={Link}
					className="whitespace-no-wrap normal-case"
					variant="contained"
					color="secondary"
					onClick={addClassOfService}
				>
					<span className="hidden sm:flex">{t('addNewCode')}</span>
					<span className="flex sm:hidden">{t('addNewCode')}</span>
				</Button>
				</Link>
			</ motion.div>
		</div>
	);
}

export default ClassOfServiceListHeader;
