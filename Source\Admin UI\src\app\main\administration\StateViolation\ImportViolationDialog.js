import React, { forwardRef, useEffect, useState, useImperativeHandle } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import {
    Button, DialogActions, Table, TableContainer, TableBody, TableCell, TableHead, TableRow, TextField,
    Grid
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CloseIcon from '@mui/icons-material/Close';
import CheckIcon from "@mui/icons-material/Check";
import { styled } from '@mui/material/styles';
import XLSX from 'xlsx';
import { useForm } from 'react-hook-form';
import Paper from '@mui/material/Paper';
import Autocomplete from '@mui/material/Autocomplete';
import { Box } from '@mui/system';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { useTranslation } from "react-i18next";

const ImportViolationDialog = forwardRef((props, ref) => {
    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();

    const { handleSubmit } = useForm({
        mode: 'onChange',
    });

    const VisuallyHiddenInput = styled('input')({
        clip: 'rect(0 0 0 0)',
        clipPath: 'inset(50%)',
        height: 1,
        overflow: 'hidden',
        position: 'absolute',
        bottom: 0,
        left: 0,
        whiteSpace: 'nowrap',
        width: 1,
    });

    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [fileDetails, setFileDetails] = useState(null);
    const [excelColumns, setExcelColumns] = useState(null);
    const [excelData, setExcelData] = useState([]);
    const [displayFields, setDisplayFields] = useState([]);
    const [stateDetails, setStateDetails] = useState(null);
    const [NIBRSCode, setNIBRSCode] = useState(null);
    const [statute, setStatute] = useState(null);
    const [title, setTitle] = useState(null);
    const [description, setDescription] = useState(null);
    const [misdOrFelony, setMisdOrFelony] = useState(null);
    const [searchFields, setSearchFields] = useState([]);
    const [sortColumns, setSortColumns] = useState([]);


    useImperativeHandle(ref, () => ({
        handleOpen(value) {
            setStateDetails(value);
            handleClickOpen();
        }
    }));


    const handleClickOpen = () => {
        setOpen(true);
    }

    const handleClose = (event, reason) => {
        if (reason && reason === "backdropClick") return;
        setOpen(false);
        setFileDetails(null);
        setExcelColumns(null);
        setExcelData(null);
        setDisplayFields([]);
        setNIBRSCode(null);
        setStatute(null);
        setTitle(null);
        setDescription(null);
        setMisdOrFelony(null);
        setSearchFields([]);
        setSortColumns([]);
    }

    const onSubmit = () => {
        const columnsConfig = {
            State: stateDetails.StateCode,
            DisplayFields: displayFields,
            NIBRSCode,
            Statute: statute,
            Title: title,
            Description: description,
            MisdOrFelony: misdOrFelony,
            Projection: displayFields,
            SearchFields: searchFields,
            SortColumns: sortColumns
        }

        const mappedData = excelData.map((row) => {
            const mappedRow = {};
            excelColumns.forEach((column, index) => {
                column = column.replace(/\s+/g, '');
                mappedRow[column] = row[index];
            });
            return mappedRow;
        });

        props.processedData(columnsConfig, fileDetails, mappedData);

        handleClose();
    }

    const uploadFiles = (event) => {
        setLoading(true);
        const file = event.target.files[0];
        const reader = new FileReader();
        setFileDetails(event.target.files[0]);

        const worker = new Worker(new URL('../../SharedComponents/ImportFromExcelDialog/excelWorker.js', import.meta.url));

        worker.onmessage = (e) => {
            const { headers, data } = e.data;
            setExcelColumns(headers);
            setExcelData(data);
            setLoading(false);  // Stop loading once processing is done
        };

        worker.onerror = (err) => {
            console.error('Error in worker:', err);
            setLoading(false);
        };

        reader.onload = (e) => {
            worker.postMessage(e.target.result); // Send file data to worker
        };

        reader.readAsArrayBuffer(file);
    };


    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >

                <DialogTitle id="excel-import-dialog-title">
                    <Box display="flex" alignItems="center">
                        <Box flexGrow={1} >{t("importViolation")} - {stateDetails ? stateDetails.StateCode : ""}</Box>
                        <Box>
                            <IconButton onClick={handleClose}>
                                <CloseIcon />
                            </IconButton>
                        </Box>
                    </Box>
                </DialogTitle>
                <form
                    className="flex flex-col justify-center w-full pb-16"
                    onSubmit={handleSubmit(onSubmit)}
                    // autoComplete="off"
                    autoSave={false}
                >
                    <DialogContent dividers>
                        <Grid container spacing={1} className="mb-8" alignItems="center">
                            <Grid item xs={12} sm={12} md={2.3} lg={2.3} xl={2.3}>
                                <div style={{ display: 'flex' }}>
                                    <div>
                                        <Button
                                            component='label'
                                            variant='contained'
                                            startIcon={<CloudUploadIcon />}
                                            color='primary'
                                        >
                                            {t("selectFile")}
                                            <VisuallyHiddenInput
                                                id="docpicker"
                                                type="file"
                                                onChange={uploadFiles}
                                                accept='.xlsx, .xls, .xlsm, .xlsb, .xltx, .xltm, .csv, .tsv, .xml, '
                                            />
                                        </Button>
                                    </div>
                                </div>
                            </Grid>
                            <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
                                <div >
                                    <p style={{ fontStyle: 'italic', fontSize: '16px' }}>{fileDetails !== null ? fileDetails.name : t("noFileSelectMsg")}</p>
                                </div>
                            </Grid>
                            <Grid item xs={12} sm={12} md={5.7} lg={5.7} xl={5.7}>
                                <div style={{ marginLeft: "30px" }}>
                                    <TextField label={t("source")} variant="outlined" size='small' fullWidth />
                                </div>
                            </Grid>
                        </Grid>


                        <div style={{ marginTop: '16px' }}>
                            {loading === true ? <CircularProgressLoader loading={loading} /> :
                                <Paper sx={{ width: '100%', overflow: 'hidden' }}>
                                    {excelColumns !== null &&
                                        <TableContainer sx={{ height: "650px" }}>
                                            <Table sx={{ width: 'inherit' }} stickyHeader aria-label="simple table">

                                                <TableHead>
                                                    <TableRow>
                                                        <TableCell>{t("sourceFields")}</TableCell>
                                                        <TableCell>{t("destinationFields")}</TableCell>
                                                    </TableRow>
                                                </TableHead>


                                                <TableBody>

                                                    <TableRow
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {t("displayFields")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Autocomplete
                                                                size='small'
                                                                multiple={true}
                                                                value={displayFields}
                                                                options={excelColumns !== null ? excelColumns : []}
                                                                disableCloseOnSelect
                                                                getOptionLabel={(option) => option}
                                                                onChange={(event, value) => {
                                                                    // handleMappingChange(row, value?.name || "", value, index);
                                                                    // filterFields(event, value);
                                                                    // setSelectedNames((prev) => [...prev, selectedNames])
                                                                    setDisplayFields(value);
                                                                }}
                                                                disablePortal
                                                                renderInput={(params) => <TextField {...params} label={`Select Multiple`} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>

                                                    <TableRow
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {t("nibrsCode")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Autocomplete
                                                                size='small'
                                                                value={NIBRSCode}
                                                                options={excelColumns !== null ? excelColumns : []}
                                                                getOptionLabel={(option) => option}
                                                                onChange={(event, value) => {
                                                                    // handleMappingChange(row, value?.name || "", value, index);
                                                                    // filterFields(event, value);
                                                                    // setSelectedNames((prev) => [...prev, selectedNames])
                                                                    setNIBRSCode(value);
                                                                }}
                                                                disablePortal
                                                                renderInput={(params) => <TextField {...params} label={`Select One`} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>

                                                    <TableRow
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {t("title")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Autocomplete
                                                                size='small'
                                                                value={title}
                                                                options={excelColumns !== null ? excelColumns : []}
                                                                getOptionLabel={(option) => option}
                                                                onChange={(event, value) => {
                                                                    // handleMappingChange(row, value?.name || "", value, index);
                                                                    // filterFields(event, value);
                                                                    // setSelectedNames((prev) => [...prev, selectedNames])
                                                                    setTitle(value);
                                                                }}
                                                                disablePortal
                                                                renderInput={(params) => <TextField {...params} label={`Select One`} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>

                                                    <TableRow
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {t("statute")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Autocomplete
                                                                size='small'
                                                                value={statute}
                                                                options={excelColumns !== null ? excelColumns : []}
                                                                getOptionLabel={(option) => option}
                                                                onChange={(event, value) => {
                                                                    // handleMappingChange(row, value?.name || "", value, index);
                                                                    // filterFields(event, value);
                                                                    // setSelectedNames((prev) => [...prev, selectedNames])
                                                                    setStatute(value);
                                                                }}
                                                                disablePortal
                                                                renderInput={(params) => <TextField {...params} label={`Select One`} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>

                                                    <TableRow
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {t("description")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Autocomplete
                                                                size='small'
                                                                value={description}
                                                                options={excelColumns !== null ? excelColumns : []}
                                                                getOptionLabel={(option) => option}
                                                                onChange={(event, value) => {
                                                                    // handleMappingChange(row, value?.name || "", value, index);
                                                                    // filterFields(event, value);
                                                                    // setSelectedNames((prev) => [...prev, selectedNames])
                                                                    setDescription(value);
                                                                }}
                                                                disablePortal
                                                                renderInput={(params) => <TextField {...params} label={`Select One`} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>

                                                    <TableRow
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {t("misdFelony")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Autocomplete
                                                                size='small'
                                                                value={misdOrFelony}
                                                                options={excelColumns !== null ? excelColumns : []}
                                                                getOptionLabel={(option) => option}
                                                                onChange={(event, value) => {
                                                                    // handleMappingChange(row, value?.name || "", value, index);
                                                                    // filterFields(event, value);
                                                                    // setSelectedNames((prev) => [...prev, selectedNames])
                                                                    setMisdOrFelony(value);
                                                                }}
                                                                disablePortal
                                                                renderInput={(params) => <TextField {...params} label={`Select One`} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>

                                                    <TableRow
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {t("searchFields")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Autocomplete
                                                                size='small'
                                                                multiple={true}
                                                                value={searchFields}
                                                                options={excelColumns !== null ? excelColumns : []}
                                                                disableCloseOnSelect
                                                                getOptionLabel={(option) => option}
                                                                onChange={(event, value) => {
                                                                    // handleMappingChange(row, value?.name || "", value, index);
                                                                    // filterFields(event, value);
                                                                    // setSelectedNames((prev) => [...prev, selectedNames])
                                                                    setSearchFields(value);
                                                                }}
                                                                disablePortal
                                                                renderInput={(params) => <TextField {...params} label={`Select Multiple`} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>

                                                    <TableRow
                                                        sx={{ '& td, & th': { border: '1px solid #ddd' /*, borderLeft: 0, borderRight: 0 */ } }}
                                                    >
                                                        <TableCell component="th" scope="row" sx={{ width: 250, fontSize: '1.5rem', }}>
                                                            {t("sortColumns")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Autocomplete
                                                                size='small'
                                                                multiple={true}
                                                                value={sortColumns}
                                                                options={excelColumns !== null ? excelColumns : []}
                                                                disableCloseOnSelect
                                                                getOptionLabel={(option) => option}
                                                                onChange={(event, value) => {
                                                                    // handleMappingChange(row, value?.name || "", value, index);
                                                                    // filterFields(event, value);
                                                                    // setSelectedNames((prev) => [...prev, selectedNames])
                                                                    setSortColumns(value);
                                                                }}
                                                                disablePortal
                                                                renderInput={(params) => <TextField {...params} label={`Select Multiple`} />}
                                                            />
                                                        </TableCell>
                                                    </TableRow>

                                                </TableBody>
                                            </Table>
                                        </TableContainer>
                                    }
                                </Paper>
                            }
                        </div>

                    </DialogContent>
                    <DialogActions>
                        <div style={{ marginLeft: 'auto', marginTop: '10px', marginRight: '10px' }}>
                            <Button variant='contained' color='secondary' className='ml-5' type='submit'>
                                {t("import")}
                            </Button>
                            <Button variant='contained' color='primary' className='ml-12' type='button' onClick={handleClose}>
                                {t("close")}
                            </Button>
                        </div>
                    </DialogActions>
                </form>
            </Dialog>
        </div >
    )

})


export default ImportViolationDialog;