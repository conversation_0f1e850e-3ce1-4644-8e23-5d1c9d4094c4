import { authRoles } from '../../auth';
import React from 'react';

const InvalidSession = React.lazy(() => import('./invalidSession'));

const invalidSessionConfig = {
	settings: {
		layout: {
			config: {
				navbar: {
					display: false
				},
				toolbar: {
					display: false
				},
				footer: {
					display: false
				},
				leftSidePanel: {
					display: false
				},
				rightSidePanel: {
					display: false
				}
			}
		}
	},
	auth: authRoles.onlyGuest,
	routes: [
		{
			path: '/invalidSession',
			element: <InvalidSession />,
		},
		
	]
};

export default invalidSessionConfig;
