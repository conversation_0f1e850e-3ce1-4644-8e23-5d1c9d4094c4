import Close from "@mui/icons-material/Close";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import Tooltip from "@mui/material/Tooltip";
import { useTranslation } from "react-i18next";

const CloseButton = ({ onClose }) => {
  const { t } = useTranslation("laguageConfig");

  if (typeof onClose !== "function") return null;

  return (
    <Grid
      item
      xs={12}
      sm={1}
      md={1}
      sx={{
        display: "flex",
        justifyContent: "flex-end",
        alignSelf: "flex-start",
      }}
    >
      <Tooltip title={t("close")} placement="right">
        <IconButton onClick={onClose} aria-label="close">
          <Close />
        </IconButton>
      </Tooltip>
    </Grid>
  );
};

export default CloseButton;
