import React, { useRef, useEffect, useState, useMemo } from 'react';
import './PhoneWithCountryCode.css';
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from 'react-i18next';
import PhoneInput from 'react-phone-input-2'

function PhoneWithCountryCode(props) {
    const user = useSelector(({ auth }) => auth.user);
    const [value, setValue] = React.useState(0);
    const dispatch = useDispatch();
    const { t } = useTranslation('laguageConfig');

    function handleOnChange(phoneMumber, data, event, formattedValue) {
        // setCountryCode(data.countryCode);
        // setPhoneNumber(phoneMumber);
        // setDialCode(formattedValue.split(' ')[0]);
        props.countrycode(data.countryCode)
        props.dialcode(formattedValue.split(' ')[0])
        props.phonenum(formattedValue.split(' ')[1])

    }

    return (
        <PhoneInput
            value={props.phoneNumber}
            //country={props.country}
            onChange={handleOnChange}
        />
    )
}

export default PhoneWithCountryCode;