import React, {
    forwardRef,
    useRef,
    useImperativeHandle,
    useEffect,
} from "react";
import { useDispatch } from "react-redux";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import "./DrivingLicenses.css";
import IconButton from '@mui/material/IconButton';
import Icon from '@mui/material/Icon';
import { Controller, useForm } from "react-hook-form";
import TextField from '@mui/material/TextField';
import { clearDrivinglicenseData, getDrivingLicense } from "../../store/PersonIncidentSlice";
import { useSelector } from "react-redux";
import Grid from "@mui/material/Grid";
import Typography from '@mui/material/Typography';
import useScanDetection from 'use-scan-detection';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import CircularProgressLoader from "../CircularProgressLoader/CircularProgressLoader";

const DrivingLicenses = (props, ref) => {
    const { handleSubmit } = useForm({ mode: "onChange" });
    const [open, setOpen] = React.useState(false);
    const [loading, setLoading] = React.useState(false);

    const PersonDataFromLicense = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.personIncident.PersonDataFromLicense);
    const PersonDrivingLicenseDataSuccess = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.personIncident.PersonDrivingLicenseDataSuccess);

    let licenseData = null
    const dispatch = useDispatch();

    const sendLicenseData = (licenseData) => {
        debugger;
        setLoading(false)
        if (licenseData !== null) {
            try {
                var unescapedString = licenseData.replace(/@\n\nANSI/g, '@\n\x1e\rANSI')
                let buffer = Buffer.from(unescapedString, 'binary');
                // Encode the Buffer as a Base64 string
                let base64String = buffer.toString('base64');
                base64String = base64String.replace(/\=/g, '')
                dispatch(getDrivingLicense(base64String));
            }
            catch (e) {
                console.log(e.message);
            }
        }
        else {
            console.log("Your data is not Valid");
        }
    };

    useEffect(() => {
        if (PersonDrivingLicenseDataSuccess) {
            debugger;
            props.SetControlEnterFlag(true);
            licenseData = null;
            dispatch(clearDrivinglicenseData([]));
        }
    }, [PersonDrivingLicenseDataSuccess]);

    useEffect(() => {
        props.SetControlEnterFlag(false);
    }, [PersonDrivingLicenseDataSuccess]);

    const formRef = useRef(null);

    const SetLicenseData = (event) => {
        props.SetControlEnterFlag(false);
        debugger;
        if (document.getElementById('drivinglicense').value.match(/\n{3}/g)) {
            sendLicenseData(document.getElementById('drivinglicense').value)
        }
    };

    return (
        <div>
            <div>
                Scan Drivers License
            </div>
            {!PersonDrivingLicenseDataSuccess ?
                <form
                    ref={formRef}
                    autoSave={false}
                    autoComplete={false}
                >
                    <textarea autoFocus={true} id="drivinglicense" name="drivinglicense" rows="3" cols="50" value={licenseData}
                        onChange={SetLicenseData}>
                    </textarea>
                </form>
                :
                <div className="mb-24">
                    <TableContainer component={Paper}>
                        <Table sx={{ minWidth: 650 }} aria-label="simple table">
                            <TableHead>
                                <TableRow>
                                    <TableCell>Fields</TableCell>
                                    <TableCell align="left">Values</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {PersonDataFromLicense !== undefined && PersonDataFromLicense.fields.map((row) => {
                                    return (
                                        <TableRow
                                            key={row.key}
                                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                        >
                                            <TableCell component="th" scope="row">{row.key}</TableCell>
                                            <TableCell component="td" scope="row" align="left">{row.value}</TableCell>
                                        </TableRow>
                                    )
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </div>
            }
        </div >
    );
};

export default DrivingLicenses;

// @  ANSI 636000100002DL00410278ZV03190008DLDAQT64235789 DCSSAMPLE DDEN DACMICHAEL DDFN DADJOHN DDGN DCUJR DCAD DCBK DCDPH DBD06062019 DBB06061986 DBA12102024 DBC1 DAU068 in DAYBRO DAG2300 WEST BROAD STREET DAIRICHMOND DAJVA DAK232690000 DCF2424244747474786102204 DCGUSA DCK123456789 DDAF DDB06062018 DDC06062020 DDD1 ZVZVA01