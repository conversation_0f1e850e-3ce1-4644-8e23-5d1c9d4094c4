import PropTypes from "prop-types";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import AppBar from "@mui/material/AppBar";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Grid from "@mui/material/Grid";
import { useTranslation } from "react-i18next";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import ActionComponent from "./ActionComponent";
import GroupComponent from "./GroupComponent";
import { isMobile } from "react-device-detect";
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useDispatch, useSelector } from "react-redux";
import React, { useState, useEffect, useRef } from "react";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

const divstyleRecent = {
    overflow: "auto",
    height: window.innerHeight - 70,
};

function Call911GroupActionComponent(props) {
    const classes = props.classes;
    const { t } = useTranslation("laguageConfig");
    const navbarTheme = useSelector(selectNavbarTheme);
    const [value, setValue] = React.useState(0);
    const [expanded, setExpanded] = React.useState("currentCall");

    const handlePanelChange = (panel) => (event, newExpanded) => { setExpanded(newExpanded ? panel : false); };

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    const nav_css = {
        backgroundColor: navbarTheme.palette.primary.main,
        color: navbarTheme.palette.primary.contrastText,
    };

    return (
        <>
            {isMobile ? (
                <Accordion onChange={handlePanelChange("other")}>
                    <AccordionSummary
                        expandIcon={<ExpandMoreIcon style={nav_css} />}
                        aria-controls="panel3a-content"
                        id="panel3a-header"
                        style={nav_css}
                    >
                        <Typography className={classes.title} variant="h3">
                            {t("group")} {"&"} {t("action")}
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                        <Grid item xs={12}>
                            <div style={divstyleRecent} tabIndex="2">
                                <div className={classes.rootTab}>
                                    <AppBar position="static">
                                        <Tabs
                                            value={value}
                                            style={nav_css}
                                            onChange={handleChange}
                                            variant="fullWidth"
                                            aria-label="full width tabs 911Call"
                                        >
                                            <Tab label={t("group")} {...a11yProps(0)} />
                                            <Tab label={t("action")} {...a11yProps(1)} />
                                        </Tabs>
                                    </AppBar>
                                    <TabPanel value={value} index={0}>
                                        <ErrorBoundary
                                            FallbackComponent={(props) => <ErrorPage {...props} componentName="GroupComponent" />} onReset={() => { }} >
                                            <GroupComponent
                                                countyValue={props.countyValue} cityValue={props.cityValue} zoneValue={props.zoneValue} pdZoneValue={props.pdZoneValue}
                                            ></GroupComponent>
                                        </ErrorBoundary>
                                    </TabPanel>
                                    <TabPanel value={value} index={1}>
                                        <ErrorBoundary
                                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ActionComponent" />} onReset={() => { }} >
                                            <ActionComponent ></ActionComponent>
                                        </ErrorBoundary>
                                    </TabPanel>
                                </div>
                            </div>
                        </Grid>
                    </AccordionDetails>
                </Accordion>
            ) : (
                <div style={divstyleRecent} tabIndex="2">
                    <div className={classes.rootTab}>
                        <AppBar position="static">
                            <Tabs
                                value={value}
                                style={nav_css}
                                onChange={handleChange}
                                variant="fullWidth"
                                aria-label="full width tabs 911Call"
                            >
                                <Tab
                                    label={t("group")}
                                    {...a11yProps(0)}
                                    style={nav_css}
                                />
                                <Tab
                                    label={t("action")}
                                    {...a11yProps(1)}
                                    style={nav_css}
                                />
                            </Tabs>
                        </AppBar>
                        <TabPanel value={value} index={0}>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="GroupComponent" />} onReset={() => { }} >
                                <GroupComponent></GroupComponent>
                            </ErrorBoundary>
                        </TabPanel>
                        <TabPanel value={value} index={1}>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ActionComponent" />} onReset={() => { }} >
                                <ActionComponent></ActionComponent>
                            </ErrorBoundary>
                        </TabPanel>
                    </div>
                </div>
            )}
        </>
    );
}

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box p={2}>
                    <Typography component={"span"}>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.any.isRequired,
    value: PropTypes.any.isRequired,
};

function a11yProps(index) {
    return {
        id: `full-width-tab-${index}`,
        "aria-controls": `full-width-tabpanel-${index}`,
    };
}

export default Call911GroupActionComponent;