import React, { useState, useCallback } from 'react';
import { ReactSearchAutocomplete } from 'react-search-autocomplete';
import axios from 'axios';
import { decrypt } from 'src/app/security';
import { useSelector } from 'react-redux';
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import { useTranslation } from 'react-i18next';

const VehicleSearchReactAutoComplete = ({
    onSelect,
    handleClear,
    isGlobal = false,
    code,
}) => {
    const [items, setItems] = useState([]);
    const { t } = useTranslation('laguageConfig');
    const navbarTheme = useSelector(selectNavbarTheme);

    const nameFormatter = (item) =>
        `${item.tagNumber || ''}, ${item.modelYear || ''}, ${item.makeDesc || ''}, ${item.vehicleColor1 || ''}, ${item.tagState || ''}`;

    const search = useCallback(
        async (searchTerm) => {
            if (!searchTerm?.trim()) {
                setItems([]);
                return;
            }

            try {
                const url = isGlobal
                    ? `admin/api/vehicleSearch/vehicleList/${searchTerm}`
                    : `admin/api/vehicleSearch/vehicleList/${searchTerm}/${code}`;

                const response = await axios.get(url);
                if (response.status === 200 && response.data) {
                    const decryptedData = JSON.parse(decrypt(response.data)) || [];

                    const formattedItems = Array.isArray(decryptedData)
                        ? decryptedData.map((item) => ({
                            ...item,
                            name: nameFormatter(item),
                        }))
                        : [];

                    setItems(formattedItems);
                } else {
                    setItems([]);
                }
            } catch (error) {
                console.error('Vehicle search error:', error);
                setItems([]);
            }
        },
        [code, isGlobal]
    );

    const handleOnSearch = useCallback((input) => {
        search(input);
    }, [search]);

    const handleOnSelect = useCallback((item) => {
        onSelect?.(item);
    }, [onSelect]);

    const handleOnClear = useCallback(() => {
        setItems([]);
        handleClear?.();
    }, [handleClear]);

    const onFocus = (e) => {
        e.target.setAttribute('autocomplete', 'new-password');
    };

    const styling = {
        border: '1px solid #ccc',
        borderRadius: '4px',
        backgroundColor: navbarTheme?.palette?.mode === 'light' ? 'white' : 'black',
        color: navbarTheme?.palette?.mode === 'light' ? 'black' : 'white',
        hoverBackgroundColor: navbarTheme?.palette?.secondary?.main,
        placeholderColor: navbarTheme?.palette?.primary?.light,
        boxShadow: 'none',
        fontSize: '15px',
        iconColor: 'grey',
        lineColor: 'grey',
        clearIconMargin: '3px 8px 0 0',
        zIndex: 99,
        cursor: 'text',
        height: '40px',
    };

    return (
        <ReactSearchAutocomplete
            items={items}
            inputDebounce={500}
            onSearch={handleOnSearch}
            onClear={handleOnClear}
            onSelect={handleOnSelect}
            onFocus={onFocus}
            maxResults={100}
            fuseOptions={{ keys: ['tagNumber', 'makeDesc'] }}
            autoFocus
            placeholder={t('searchByVehicle')}
            showNoResultsText={t('gatheringResults')}
            styling={styling}
        />
    );
};

export default VehicleSearchReactAutoComplete;
