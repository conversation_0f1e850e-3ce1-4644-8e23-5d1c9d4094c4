import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { Autocomplete, TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { showMessage } from 'app/store/fuse/messageSlice';
import FormControl from "@mui/material/FormControl";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import InputLabel from '@mui/material/InputLabel';
import { saveNonCallStatus } from '../store/callStatusSlice';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import ErrorMessage from "../../SharedComponents/ErrorMessage/ErrorMessage";
import { textAlign } from '@mui/system';
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';

let update = false;
let pageIndexData;
let rowsPerPageData;
let LocationRequirementData = [
    {
        mLocationRequirementKey: 0,
        value: "NotAllowed"
    },
    {
        mLocationRequirementKey: 1,
        value: "Allow"
    },
    {
        mLocationRequirementKey: 2,
        value: "Required"
    }
]

const AddNonCallStatusDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState(null);
    const [code, setCode] = React.useState("");
    const [Id, setID] = React.useState("");
    const [ShortCodeValue, setShortCodeValue] = React.useState("");
    const [LocationRequirement, setLocationRequirementValue] = React.useState('');
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [PagingDetails, setPagingDetails] = React.useState();
    const [IdPresent, setIdPresent] = React.useState(false);
    const NonCallStatus = useSelector(({ administration }) => administration.callStatusSlice.data);
    const nameRef = useRef(null);
    const shortCodeRef = useRef(null);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
    });

    const { isValid, dirtyFields, errors } = formState;

    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            if (update) {
                nameRef.current?.focus();
            } else {
                shortCodeRef.current?.focus();
            }
        }, 0);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, flag, pageIndex, rowsPerPage) {
            setData(data)
            setCode(code)
            setCallResponseValue(data)
            update = flag
            handleClickOpen1();
            setIdPresent(false)
            pageIndexData = pageIndex
            rowsPerPageData = rowsPerPage
        },
    }));

    const handleClose = () => {
        setOpen(false);
    };
    const handleStatusChange = (event, newValue) => {
        if (newValue) {
            setLocationRequirementValue(newValue.mLocationRequirementKey);
        }
    };


    const setCallResponseValue = data => {
        if (data._id !== null && data._id !== undefined) {
            setID(data._id)
            setValue('mName', data.mName);
            setValue('mUnitStatusID', data.mUnitStatusID);
            setLocationRequirementValue(data.mLocationRequirement)
            setShortCodeValue(data.mUnitStatusID)
        }
        else {
            setID(0)
            setValue('mName', "");
            setValue('mUnitStatusID', "");
            setLocationRequirementValue('')
            setShortCodeValue('')
        }


    };
    function onSubmit(model) {
        let statusData = {
            _id: Id,
            mUnitStatusID: ShortCodeValue,
            status: model.mName,
            LocationRequirement: LocationRequirement,
            isUpdate: update,
            code: code,
        }
        dispatch(saveNonCallStatus(statusData, pageIndexData, rowsPerPageData))
        handleClose()
    }

    const isIdPresent = (event) => {
        setShortCodeValue(event.target.value)
        let x = NonCallStatus.filter(status => status.mUnitStatusID == event.target.value)
        if (x.length > 0) {
            setIdPresent(true)

        } else {
            setIdPresent(false)

        }
    };


    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("nonCallStatus")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        name="registerForm"
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        // autoComplete="off"
                        autoSave={false}
                    >
                        <Controller
                            name="mUnitStatusID"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    value={ShortCodeValue}
                                    className={IdPresent ? "w-full" : "mb-16 w-full"}
                                    label={t("shortCode")}
                                    type="number"
                                    // error={!!errors.mUnitStatusID}
                                    // helperText={errors?.mUnitStatusID?.message}
                                    variant="outlined"
                                    disabled={update}
                                    required
                                    onChange={(e) => isIdPresent(e)}
                                    inputRef={shortCodeRef}
                                />
                            )}
                        />
                        {IdPresent && !update &&
                            < div className="mb-16 w-full">
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton" />} onReset={() => { }}>
                                    <ErrorMessage message="This Short Code is already exist. Please enter different code" />
                                </ErrorBoundary>
                            </div>
                        }

                        <Controller
                            name="mName"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("name")}
                                    type="text"
                                    error={!!errors.mName}
                                    helperText={errors?.mName?.message}
                                    variant="outlined"
                                    required
                                    inputRef={nameRef}
                                />
                            )}
                        />
                        <FormControl className="mb-16 w-full" >
                            <CommonAutocomplete
                                parentCallback={handleStatusChange}
                                options={LocationRequirementData || []}
                                value={LocationRequirementData.find((req) => req.mLocationRequirementKey === LocationRequirement) || null}
                                fieldName={t("locationRequirement")}
                                optionLabel={"value"}
                                onKeyDown={handleSelectKeyDown}
                            />
                        </FormControl>
                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>
        </div >
    );

});

export default AddNonCallStatusDialog;