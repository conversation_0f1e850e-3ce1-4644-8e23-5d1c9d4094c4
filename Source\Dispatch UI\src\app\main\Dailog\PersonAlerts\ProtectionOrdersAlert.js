import React from 'react';
import { Box, Typography } from '@mui/material';

const ProtectionOrdersAlert = ({ protectionOrders }) => {
  const getInitials = (name) => {
    if (!name || typeof name !== 'string') return '';
    const parts = name.split(',').map(p => p.trim());
    const last = parts[0] || '';
    const firstWords = (parts[1]?.split(' ') || []).filter(Boolean);

    const initials = firstWords.slice(0, 2).map(word => word[0]).join('');
    return (initials + (last[0] || '')).toUpperCase();
  };

  return (
    <div className="p-8">
      <Box className="text-center mb-16">
        <Typography variant="h3" className="text-red-600 font-extrabold text-4xl">
          🛡️ {t("protectionOrdersRegistry")}
        </Typography>
        {/* <Typography variant="subtitle1" className="text-gray-600 text-xl mt-2">
          Legal Safety Oversight Dashboard
        </Typography> */}
      </Box>

      {protectionOrders.length === 0 ? (
        <div className="text-center text-gray-500 text-xl mt-10">
          {t("noProtetionOrdersFound")}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-16">
          {protectionOrders.map((order, index) => (
            <div
              key={index}
              className="rounded-2xl shadow-lg p-6 border-t-4 border-red-600 transition-all duration-300 hover:shadow-2xl hover:-translate-y-1"
            >
              <div className="flex items-center space-x-6 mb-6">
                <div className="h-40 w-40 rounded-full bg-red-600 flex items-center justify-center font-extrabold text-xl text-white">
                  {getInitials(order.defDisplayName)}
                </div>
                <div>
                  <div className="text-xl font-semibold">
                    {order.defDisplayName || 'N/A'}
                  </div>
                  <div className="text-blue-600 text-sm">
                    {t("order")} #: {order.protectionOrderNumber || 'N/A'}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 text-base gap-4 border-t pt-4">
                <div>
                  <div className="font-bold text-gray-500">{t("orderDate")}</div>
                  <div className="font-semibold">{order.orderDate || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("expires")}</div>
                  <div className="font-semibold">{order.orderExpires || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("dobSexRace")}</div>
                  <div className="font-semibold">
                    {order.defDOB || 'N/A'} / {order.defSex || 'N/A'} / {order.defRace || 'N/A'}
                  </div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("nextCourtDate")}</div>
                  <div className="font-semibold">{order.nextCourtDate || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("dateServed")}</div>
                  <div className="font-semibold">{order.dateServed || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("remoteSource")}</div>
                  <div className="font-semibold">{order.remotesource || 'N/A'}</div>
                </div>
              </div>

              <div className="mt-6 border-t pt-4">
                <div className="text-700 font-semibold mb-2 text-gray-500">Victim(s):</div>
                <ul className="space-y-3">
                  {order.victims?.map((vic, idx) => (
                    <li key={idx} className="bg-50 p-3 rounded-md shadow-sm">
                      <div className="font-semibold">{vic.victmDisplayname}</div>
                      <div className="text-sm text-700">
                        {t("dobAbbreviation")}: {vic.vicDOB || 'N/A'} — {t("sex")}: {vic.vicSex || 'N/A'} — {t("race")}: {vic.vicRace || 'N/A'}
                      </div>
                      <div className="text-sm text-600">
                        {t("home")}: {vic.vicHomeAddress || 'N/A'}
                        <br />
                        {t("work")}: {vic.vicWorkAddress || 'N/A'}
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProtectionOrdersAlert;
