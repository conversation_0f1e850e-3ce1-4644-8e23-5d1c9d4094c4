import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const getUnitDetails = (searchText, code) => async dispatch => {
    const data = { searchText, code }
    try {
        dispatch(setLoading(true));
        await axios
            .post(`admin/api/unit/UnitDetails`, encrypt(JSON.stringify(data)))
            .then(response => {
                dispatch(unitListSuccess(JSON.parse(decrypt(response.data))));
                dispatch(setLoading(false));
                if (response.status == 200) {
                    dispatch(
                        showMessage({
                            message: 'Unit details found.',
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        })
                    );
                } else {
                    dispatch(
                        showMessage({
                            message: 'Unit details not found.',
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'warning'
                        })
                    );
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                dispatch(unitListError(error));
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};


export const clearuploadfileobject = () => (dispatch) => {
    dispatch(Clearuploadfile());
};

export const getUnits = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/unit/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setUnitsTotalCount(listData.totalCount));
                    return dispatch(setUnits(listData.unitsList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const getNFIRS = () => async (dispatch) => {
    try {
        await axios
            .get(`admin/api/unit/getNFIRS`)
            .then((response) => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    return dispatch(setNFIRSlist(data));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const uploadFileObject = (fileData) => async dispatch => {
    try {
        return dispatch(setUPLOADFILEOBJ(fileData));
    } catch (e) {
        return console.error(e.message);
    }
}

export const saveUnit = (formData, file, item) => async dispatch => {
    let filetype = file != null ? file.type : null;
    try {
        dispatch(setLoading(true));
        if (file) {
            await axios.post(`fileupload/uploadUnitFile/UnitFile`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            }).then(async response => {
                if (response.status === 400) {
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    const data = {
                        fileUrl: response.data.Location,
                        fileType: filetype,
                        key: response.data.Key,
                        data: item,
                        code: item.code
                    }
                    await axios.post(`admin/api/unit`, encrypt(JSON.stringify(data)))
                        .then(response => {
                            if (response.status == 200) {
                                response = JSON.parse(decrypt(response.data));
                                dispatch(setLoading(false));
                                dispatch(
                                    showMessage({
                                        message: data.data.isUpdate ? response.message : response.message,
                                        autoHideDuration: 2000,
                                        anchorOrigin: {
                                            vertical: 'top',
                                            horizontal: 'right'
                                        },
                                        variant: 'success'
                                    }))
                                if (data.data.isUpdate) {
                                    dispatch(setUnitEditData(response.newData))
                                    dispatch(setUnitsTotalCount(response.totalCount));
                                }
                                else {
                                    dispatch(setUnitAddData(response.newData))
                                    dispatch(setUnitsTotalCount(response.totalCount));
                                }
                                return dispatch(setUPLOADSUCCESS(true)), dispatch(setUnitResponse(true));
                            }
                            else {
                                response = JSON.parse(decrypt(response.response.data));
                                dispatch(setLoading(false));
                                dispatch(showMessage({
                                    message: response.data.message,
                                    autoHideDuration: 2000,
                                    anchorOrigin: {
                                        vertical: 'top',
                                        horizontal: 'right'
                                    },
                                    variant: 'warning'

                                }))
                            }
                        }).catch(error => {
                            response = JSON.parse(decrypt(response.response.data));
                            dispatch(setLoading(false));
                            return dispatch(
                                showMessage({
                                    message: response.data.message,
                                    autoHideDuration: 2000,//ms
                                    anchorOrigin: {
                                        vertical: 'top',
                                        horizontal: 'right'
                                    },
                                    variant: 'warning'
                                }),
                            );
                        });
                }
            })
        }
        else {
            const data = {
                data: item,
                code: item.code
            }
            await axios.post(`admin/api/unit`, encrypt(JSON.stringify(data)))
                .then(response => {
                    if (response.status == 200) {
                        response = JSON.parse(decrypt(response.data));
                        dispatch(setLoading(false));
                        dispatch(
                            showMessage({
                                message: data.data.isUpdate ? response.message : response.message,
                                autoHideDuration: 2000,
                                anchorOrigin: {
                                    vertical: 'top',
                                    horizontal: 'right'
                                },
                                variant: 'success'
                            }))
                        if (data.data.isUpdate) {
                            dispatch(setUnitEditData(response.newData))
                            dispatch(setUnitsTotalCount(response.totalCount));
                        }
                        else {
                            dispatch(setUnitAddData(response.newData))
                            dispatch(setUnitsTotalCount(response.totalCount));
                        }
                        return dispatch(setUPLOADSUCCESS(true)), dispatch(setUnitResponse(true));
                    }
                    else {
                        response = JSON.parse(decrypt(response.response.data));
                        dispatch(setLoading(false));
                        dispatch(showMessage({
                            message: response.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'warning'

                        }))
                    }
                })
        }
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const removeUnit = (ID, code, pageIndex, pageLimit, sortField, sortDirection) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/unit/${ID}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    dispatch(removeUnitData(ID));
                    return dispatch(setUnitResponse(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
}

//for searching
export const searchUnits = (searchText, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        axios.get(`admin/api/unit/searchUnits/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchUnits(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    unitsuccess: false,
    unitdata: [],
    data: [],
    searchUnits: [],
    success: false,
    isloading: false,
    uploadsuccess: false,
    uploadFileObj: null,
    nfirsdata: [],
    totalCount: 0
};

const unitSlice = createSlice({
    name: 'unit',
    initialState,
    reducers: {
        unitListSuccess: (state, action) => {
            state.unitsuccess = true;
            state.unitdata = action.payload;
        },
        setSearchUnits: (state, action) => {
            state.searchUnits = action.payload;
        },
        unitListError: (state, action) => {
            state.unitsuccess = false;
            state.unitdata = [];
        },
        setUnits: (state, action) => {
            state.data = action.payload;
        },
        setUnitResponse: (state, action) => {
            state.success = action.payload;
        },
        setUnitsTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setNFIRSlist: (state, action) => {
            state.nfirsdata = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setUPLOADSUCCESS: (state, action) => {
            state.uploadsuccess = action.payload;
        },
        setUPLOADFILEOBJ: (state, action) => {
            state.uploadFileObj = action.payload;
        },
        setUnitEditData: (state, action) => {
            const index = state.data.findIndex(obj => obj._id === action.payload._id);
            if (index !== -1) {
                state.data[index] = action.payload;
            }
        },
        setUnitAddData: (state, action) => {
            state.data = [...state.data, action.payload];
        },
        removeUnitData: (state, action) => {
            state.data = state.data.filter(x => x._id !== action.payload);
        },
        Clearuploadfile: (state) => void (state.uploadFileObj = null),
        setUPLOADFILEOBJ: (state, action) => void (state.uploadFileObj = action.payload),
    },
    extraReducers: {}
});

export const {
    unitListSuccess,
    setUnits,
    setSearchUnits,
    setUnitResponse,
    setUnitsTotalCount,
    setNFIRSlist,
    setLoading,
    unitListError,
    setUPLOADSUCCESS,
    setUnitEditData,
    setUnitAddData,
    removeUnitData,
    Clearuploadfile,
    setUPLOADFILEOBJ
} = unitSlice.actions;

export default unitSlice.reducer;
