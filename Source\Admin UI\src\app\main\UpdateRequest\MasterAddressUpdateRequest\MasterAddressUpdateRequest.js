import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useEffect, useRef, useState } from 'react';
import {
    Icon, Input, Paper, StyledEngineProvider, TablePagination, IconButton,
    ThemeProvider, Typography, InputAdornment, Tooltip, Stack, Button,
    Grid,
} from '@mui/material';
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useSelector, useDispatch } from 'react-redux';
import history from '@history';
import { useDebounce } from '@fuse/hooks';
import CancelIcon from '@mui/icons-material/Cancel';
import {
    IgrGridModule, IgrGridToolbar,
    IgrGridToolbarActions, IgrGridToolbarAdvancedFiltering, IgrGridToolbarHiding,
    IgrGridToolbarPinning, IgrGrid, IgrColumn, ColumnPinningPosition, ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import { getRowsPerPageOptions, getNavbarTheme, useWindowResizeHeight } from '../../utils/utils';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { getMasterAddressRequest, updateMasterAddressRequest } from '../../store/updateRequestSlice';
import TaskAltOutlinedIcon from '@mui/icons-material/TaskAltOutlined';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import ImportContactsIcon from '@mui/icons-material/ImportContacts';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import './MasterAddressUpdateRequest.css';
import DetailsIcon from '@mui/icons-material/Details';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import MasterAddressDetails from './MasterAddressDetails';
import { getMasterAddressByID, saveMasterAddress } from '../../administration/store/masterAddressSlice';
import CommonButton from '../../SharedComponents/ReuseComponents/CommonButton';
import AutoCompleteByList from './AutoCompleteByList';

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "addressId",
        align: "left",
        disablePadding: false,
        label: "addressId",
        sort: true,
    },
    {
        id: "fieldChanged",
        align: "left",
        disablePadding: false,
        label: "fieldChanged",
        sort: true,
    },
    {
        id: "oldValues",
        align: "left",
        disablePadding: false,
        label: "oldValues",
        sort: true,
    },
    {
        id: "newValues",
        align: "left",
        disablePadding: false,
        label: "newValues",
        sort: true,
    },
    {
        id: "updatedBy",
        align: "left",
        disablePadding: false,
        label: "updatedBy",
        sort: true,
    },
    {
        id: "modifiedByEmail",
        align: "left",
        disablePadding: false,
        label: "modifiedByEmail",
        sort: true,
    },
    {
        id: "modifiedByUserId",
        align: "left",
        disablePadding: false,
        label: "modifiedByUserId",
        sort: true,
    },
    {
        id: "updatedOn",
        align: "left",
        disablePadding: false,
        label: "updatedOn",
        sort: true,
    },
    {
        id: "updateAddress",
        align: "left",
        disablePadding: false,
        label: "updateAddress",
        sort: true,
    },
    {
        id: "status",
        align: "left",
        disablePadding: false,
        label: "status",
        sort: true,
    },
    {
        id: "county",
        align: "left",
        disablePadding: false,
        label: "county",
        sort: true,
    },
    {
        id: "state",
        align: "left",
        disablePadding: false,
        label: "state",
        sort: true,
    },
    {
        id: "acceptedRejectedBy",
        align: "left",
        disablePadding: false,
        label: "acceptedRejectedBy",
        sort: true,
    },
    {
        id: "acceptedRejectedByEmail",
        align: "left",
        disablePadding: false,
        label: "acceptedRejectedByEmail",
        sort: true,
    },
    {
        id: "acceptedRejectedByOn",
        align: "left",
        disablePadding: false,
        label: "acceptedRejectedByOn",
        sort: true,
    },
    {
        id: "acceptedRejectedByUserId",
        align: "left",
        disablePadding: false,
        label: "acceptedRejectedByUserId",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function MasterAddressUpdateRequest(props) {
    const { t } = useTranslation("laguageConfig");
    const mainTheme = useSelector(selectMainTheme);
    const dispatch = useDispatch();
    const gridRef = useRef(null);
    const rowsPerPageOptions = getRowsPerPageOptions();
    let colorCode = getNavbarTheme();

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [page, setPage] = useState(0);
    const [searchText, setSearchText] = React.useState("");
    const [open, setOpen] = React.useState(false);
    const [state, setState] = useState(null);
    const [county, setCounty] = useState(null);
    const [order, setOrder] = React.useState({
        direction: "desc",
        id: "updatedOn",
    });

    const user = useSelector(({ auth }) => auth.user);
    const addressUpdateList = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.updateRequest.addressUpdateList);
    const TotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.updateRequest.totalCount);
    const isloading = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.updateRequest.loading);
    const updateSuccess = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.updateRequest.updateSuccess);
    const countyList = useSelector(({ administration }) => administration.masterAddressSlice.countyList);
    const stateList = useSelector(({ administration }) => administration.masterAddressSlice.stateList);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [loading, setloading] = useState(false);

    useEffect(() => {
        setloading(isloading);
    }, [isloading]);

    const approveRequest = async (value) => {
        let address;
        if (value.addressId === "") {
            const crossStreetLocation = [];
            const firstStreetLocation = {
                "CrossStreetName": "",
                "CrossStreetType": "Nearest",
                "CrossStreetDist": ""
            };
            crossStreetLocation.push(firstStreetLocation);
            const secondStreetLocation = {
                "CrossStreetName": "",
                "CrossStreetType": "Second Nearest",
                "CrossStreetDist": ""
            }
            crossStreetLocation.push(secondStreetLocation);

            address = {
                _id: addressUpdate.addressId,
                DiscrpAgID: "",
                DateUpdate: "",
                Effective: "",
                Expire: "",
                NGUID: "",
                Country: null,
                State: "AR",
                County: "Craighead",
                AddCode: "",
                AddDataURI: "",
                Inc_Muni: "",
                Uninc_Comm: "",
                Nbrhd_Comm: "",
                Post_Comm: "",
                Post_Code: "",
                PostCodeEx: "",
                MunicipalityType: "",
                St_PreMod: "",
                St_PreDir: "",
                St_PreTyp: "",
                St_PreSep: "",
                St_Name: "",
                St_PosTyp: "",
                St_PosDir: "",
                St_PosMod: "",
                St_DirOfTravel: "",
                StNam_Full: "",
                AddNum_Pre: "",
                Add_Number: "",
                AddNum_Suf: "",
                Adr_Num_Comp: "",
                MilePost: "",
                Site: "",
                SubSite: "",
                Structure: "",
                Wing: "",
                Floor: "",
                UnitPreType: "",
                UnitValue: "",
                Unit: "",
                Room: "",
                Section: "",
                Row: "",
                Seat: "",
                AddressBuilding: "",
                SubAddress: "",
                Addtl_Loc: "",
                AdditionalAttributes: "",
                Marker: "",
                PlaceType: "",
                lst_predir: "",
                lst_name: "",
                lst_type: "",
                lst_posdir: "",
                LgcyAdd: "",
                LocalName: "",
                Longitude: "",
                Latitude: "",
                Elevation: "",
                CrossStreets: crossStreetLocation,
                AddPlaceName: "",
                AddPt_FullAddress: "",
                adr_label: "",
                Placement: "",
                MapPage: "",
                PoliceZone: "",
                FireZone: "",
                FireAutoAssist: "",
                FireMutualAssist: "",
                EMSZone: "",
                EMSAutoAssist: "",
                EMSMutualAssist: "",
                PSAP: "",
                ESN: "",
                MSAGComm: "",
                NationalGrid: ""
            };

            // Dynamically update fields based on fieldChanged array
            value.fieldChanged.forEach((field, index) => {
                address[field] = value.newValues[index];
            });
        }
        const body = {
            county: value.county,
            state: value.state,
            _id: value._id,
            status: "Approved",
            fieldChanged: value.fieldChanged,
            newValues: value.newValues,
            addressId: value.addressId,
            acceptedRejectedBy: user.data.fullName,
            acceptedRejectedByEmail: user.data.email,
            acceptedRejectedByUserId: user.data.id,
            acceptedRejectedByOn: new Date(),
            address: address,
        }
        dispatch(updateMasterAddressRequest(body));
    };

    const rejectRequest = async (value) => {
        const body = {
            county: value.county,
            state: value.state,
            _id: value._id,
            status: "Rejected",
            fieldChanged: value.fieldChanged,
            newValues: value.newValues,
            addressId: value.addressId,
            acceptedRejectedBy: user.data.fullName,
            acceptedRejectedByEmail: user.data.email,
            acceptedRejectedByUserId: user.data.id,
            acceptedRejectedByOn: new Date(),
        }
        dispatch(updateMasterAddressRequest(body));
    }

    const showAddressDetails = async (value) => {
        dispatch(getMasterAddressByID(value.addressId, value.county, value.state));
        setOpen(true);
    }

    const handleClose = () => {
        setOpen(false);
    };

    function handleChangePage(event, value) {
        setPage(value);
        setloading(true);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
        setloading(true);
    }

    const Status = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;
            return (
                <div>
                    {x.status === "Pending"
                        ?
                        <span style={{ color: 'orange' }}>{x.status}</span>
                        :
                        x.status === "Approved"
                            ?
                            <span style={{ color: 'green' }}>{x.status}</span>
                            :
                            <span style={{ color: 'red' }}>{x.status}</span>}
                </div>
            );
        }
    }

    const ActionIcons = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;
            return (
                <div className="flex">
                    {x.addressId !== "" &&
                        <Tooltip title={t("addressDetails")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => showAddressDetails(x)}
                                size="large"
                            >
                                <DetailsIcon />
                            </IconButton>
                        </Tooltip>
                    }

                    {x.status === "Pending" &&
                        <>
                            <Tooltip title={t("approve")}>
                                <IconButton
                                    aria-label="edit"
                                    color="success"
                                    onClick={() => approveRequest(x)}
                                    size="large"
                                >
                                    <TaskAltOutlinedIcon />
                                </IconButton>
                            </Tooltip>

                            <Tooltip title={t("reject")}>
                                <IconButton
                                    aria-label="edit"
                                    color="error"
                                    onClick={() => rejectRequest(x)}
                                    size="large"
                                >
                                    <CancelOutlinedIcon />
                                </IconButton>
                            </Tooltip>
                        </>
                    }
                </div >
            );
        }
    };

    const rowData = (data).map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["_id"] = item._id
            row["updateAddress"] = item.updateAddress ? item.updateAddress : '';
            row["updatedBy"] = item.updatedBy ? item.updatedBy : '';
            row["acceptedRejectedBy"] = item.acceptedRejectedBy ? item.acceptedRejectedBy : '';
            row["status"] = item.status ? item.status : '';
            row["updatedOn"] = item.updatedOn ? new Date(item.updatedOn).toLocaleString() : '';
            row["acceptedRejectedByOn"] = item.acceptedRejectedByOn !== undefined ? new Date(item.acceptedRejectedByOn).toLocaleString() : "";
            row["addressId"] = item.addressId;
            row["fieldChanged"] = item.fieldChanged;
            row["oldValues"] = item.oldValues;
            row["newValues"] = item.newValues;
            row["action"] = ActionIcons(item);
        });
        return row;
    });

    useEffect(() => {
        if (addressUpdateList !== null && addressUpdateList !== undefined) {
            setData(addressUpdateList);
            setTotalCount(TotalCount);
        }
    }, [addressUpdateList, totalCount])

    const search = useDebounce((search, page, rowsPerPage) => {
        dispatch(getMasterAddressRequest(order.id, order.direction, page * rowsPerPage, rowsPerPage,
            search === '' ? null : search, state !== null ? state.StateCode : null,
            county !== null ? county.CountyName : null
        ));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, page, rowsPerPage);
        } else {
            dispatch(getMasterAddressRequest(order.id, order.direction, page * rowsPerPage, rowsPerPage,
                searchText === '' ? null : searchText, state !== null ? state.StateCode : null,
                county !== null ? county.CountyName : null
            ));
        }
    }, [dispatch, searchText, page, rowsPerPage, order, updateSuccess, state, county]);

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    const addressDetailTemplate = (ctx) => {
        const data = ctx.dataContext.implicit;

        // Combine fieldChanged and oldValues into key-value pairs
        const updatedOldFields = data.fieldChanged.reduce((acc, field, index) => {
            acc[field] = data.oldValues[index];
            return acc;
        }, {});
        // Combine fieldChanged and newValues into key-value pairs
        const updatedNewFields = data.fieldChanged.reduce((acc, field, index) => {
            acc[field] = data.newValues[index];
            return acc;
        }, {});

        return (
            <>
                {/* horizontal table  */}
                {/* <div className="contact-container">
                    <table className="fields-table">
                        <thead>
                            <tr>
                                <th>{t(" ")}</th>
                                {Object.keys(updatedOldFields).map((key) => (
                                    <th style={{ fontSize: "14px" }} key={key}>{key}</th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style={{ fontSize: "14px", fontWeight: "bold" }}>{t("oldFields")}</td>
                                {Object.keys(updatedOldFields).map((key) => (
                                    <td key={`old-${key}`} className="field-item-old">
                                        {updatedOldFields[key] === null ? "null" : updatedOldFields[key]}
                                    </td>
                                ))}
                            </tr>
                            <tr>
                                <td style={{ fontSize: "14px", fontWeight: "bold" }}>{t("newFields")}</td>
                                {Object.keys(updatedNewFields).map((key) => (
                                    <td key={`new-${key}`} className="field-item-new">
                                        {updatedNewFields[key] === null ? "null" : updatedNewFields[key]}
                                    </td>
                                ))}
                            </tr>
                        </tbody>
                    </table>
                </div> */}
                {/* vertical table  */}
                <div className="contact-container">
                    <table className="fields-table">
                        <thead>
                            <tr>
                                <th>{t(" ")}</th>
                                <th>{t("oldFields")}</th>
                                <th>{t("newFields")}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {Object.keys(updatedOldFields).map((key) => (
                                <tr key={key}>
                                    <td><strong>{key}</strong></td>
                                    <td className="field-item-old">{updatedOldFields[key] === null ? "null" : updatedOldFields[key]}</td>
                                    <td className="field-item-new">{updatedNewFields[key]}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </>
        );
    };

    const navigateToOptions = () => {
        history.push(`/admin/updateRequest`);
    };

    const handleClear = () => {
        setCounty(null);
        setState(null);
        setSearchText("");
    };

    const handleOnStateSelect = (item) => {
        setState(item);
    };

    const handleOnCountySelect = (item) => {
        setCounty(item);
    };

    const handleOnStateClear = () => {
        setState(null);
    };
    const handleOnCountyClear = () => {
        setCounty(null);
    };

    const formatResult = (item) => {
        return (item.name);
    };

    const customNameCountyFormatter = (item) => {
        return `${item.CountyName || ''}`;
    };

    const customNameStateFormatter = (item) => {
        return `${item.StateCode || ''}`;
    };

    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {user.data.isSuperAdmin && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => navigateToOptions()}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 w-full items-center justify-between"
                            style={user.data.isSuperAdmin ? {} : { paddingTop: "29px" }}
                        >
                            <Grid container spacing={1}>
                                <Grid item xs={12} sm={12} md={3} lg={3} xl={3}>
                                    <div className="flex items-center">
                                        <ImportContactsIcon style={{ fontSize: '40px' }} />
                                        <Typography
                                            component={motion.span}
                                            initial={{ x: -20 }}
                                            animate={{ x: 0, transition: { delay: 0.2 } }}
                                            delay={300}
                                            className="hidden sm:flex mx-0 sm:mx-12"
                                            variant="h6"
                                        >
                                            {t("masterAddressUpdateRequest")}
                                        </Typography>
                                    </div>
                                </Grid>
                                <Grid item xs={12} sm={12} md={8} lg={8} xl={8}>
                                    <div className="flex flex-1 items-center justify-center px-12">
                                        <StyledEngineProvider injectFirst>
                                            <ThemeProvider theme={mainTheme}>
                                                <Paper
                                                    component={motion.div}
                                                    sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                                    initial={{ y: -20, opacity: 0 }}
                                                    animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                                    className="flex items-center w-full max-w-512 px-8 py-4 rounded-8 mr-20"
                                                    elevation={1}
                                                >
                                                    <Icon color="action">search</Icon>

                                                    <Input
                                                        placeholder={t("search")}
                                                        className="flex flex-1 mx-8"
                                                        disableUnderline
                                                        fullWidth
                                                        defaultValue={searchText}
                                                        inputProps={{
                                                            "aria-label": "Search",
                                                        }}
                                                        onChange={(ev) => setSearchText(ev.target.value)}
                                                        endAdornment={
                                                            <InputAdornment position='end'>
                                                                <IconButton
                                                                    onClick={e => setSearchText("")}
                                                                >
                                                                    <CancelIcon />
                                                                </IconButton>
                                                            </InputAdornment>
                                                        }
                                                    />
                                                </Paper>
                                            </ThemeProvider>
                                        </StyledEngineProvider>
                                        <Grid item xs={12} sm={12} md={9} lg={9} xl={9} className="m-2 ml-18" style={{ textAlign: 'left', maxWidth: '200px' }}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_STATE" />}
                                                onReset={() => { }}
                                            >
                                                <AutoCompleteByList
                                                    dataList={countyList}
                                                    placeholder={t("state")}
                                                    onSelect={handleOnStateSelect}
                                                    handleClear={handleOnStateClear}
                                                    formatResult={formatResult}
                                                    nameFormatter={customNameStateFormatter}
                                                />
                                            </ErrorBoundary>
                                        </Grid>
                                        <Grid item xs={12} sm={12} md={9} lg={9} xl={9} className="m-2 ml-12" style={{ textAlign: 'left', maxWidth: '300px' }}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_COUNTY" />}
                                                onReset={() => { }}
                                            >
                                                <AutoCompleteByList
                                                    dataList={countyList}
                                                    placeholder={t("county")}
                                                    onSelect={handleOnCountySelect}
                                                    handleClear={handleOnCountyClear}
                                                    formatResult={formatResult}
                                                    nameFormatter={customNameCountyFormatter}
                                                />
                                            </ErrorBoundary>
                                        </Grid>
                                    </div>
                                </Grid>
                                <Grid item xs={12} sm={12} md={1} lg={1} xl={1}>
                                    <div className="flex justify-end">
                                        <ErrorBoundary
                                            FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addNfirs" />} onReset={() => window.location.reload()} >
                                            <CommonButton styleClass="whitespace-no-wrap normal-case m-10 ml-16" btnName={t("clear")} parentCallback={handleClear}></CommonButton>
                                        </ErrorBoundary>
                                    </div>
                                </Grid>
                            </Grid>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    detailTemplate={addressDetailTemplate}
                                    autoGenerate={false}
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="updateAddress"
                                        header={t(" ")}
                                        width="150px"
                                        resizable={false}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="status"
                                        header={t("status")}
                                        width="130px"
                                        resizable={false}
                                        groupable={true}
                                        sortable={true}
                                        bodyTemplate={Status}
                                    />
                                    <IgrColumn
                                        field="updatedBy"
                                        header={t("updatedBy")}
                                        width="250px"
                                        resizable={false}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="updatedOn"
                                        header={t("updatedOn")}
                                        width="220px"
                                        resizable={false}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="acceptedRejectedBy"
                                        header={t("acceptedRejectedBy")}
                                        width="250px"
                                        resizable={false}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="acceptedRejectedByOn"
                                        header={t("acceptedRejectedByOn")}
                                        width="250px"
                                        resizable={false}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="county"
                                        header={t("county")}
                                        width="140px"
                                        resizable={false}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="state"
                                        header={t("state")}
                                        width="100px"
                                        resizable={false}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={false}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="MasterAddressDetails" />} onReset={() => { }}>
                            <MasterAddressDetails
                                keepMounted
                                open={open}
                                onClose={handleClose}
                            >
                            </MasterAddressDetails>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    );
}

export default MasterAddressUpdateRequest;