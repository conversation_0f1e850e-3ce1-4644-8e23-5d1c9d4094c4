import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import _ from "@lodash";
import history from "@history";
import TablePagination from "@mui/material/TablePagination";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import { ErrorBoundary } from "react-error-boundary";
import HistoryIcon from '@mui/icons-material/History';
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import CircularProgressLoader from "../../SharedComponents/CircularProgressLoader/CircularProgressLoader";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils";
import AgencyDepartmentDialog from "./AgencyDepartmentDialog";
import { getMasterDepartmentDetails, removeDepartment, searchDepartments } from "../../store/departmentSlice";
import { getTeams } from "../../store/teamSlice";
import NoteAddIcon from '@mui/icons-material/NoteAdd';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { showMessage } from "app/store/fuse/messageSlice";
import { getShiftTime } from "../../store/shiftTimeSlice";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
import { IgrGrid, IgrColumn } from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "name",
        align: "left",
        disablePadding: false,
        label: "Name",
        sort: true,
    },
    {
        id: "description",
        align: "left",
        disablePadding: false,
        label: "Description",
        sort: true,
    },
    {
        id: "users",
        align: "left",
        disablePadding: false,
        label: "UserList",
        sort: true,
    },

    {
        id: "teams",
        align: "left",
        disablePadding: false,
        label: "TeamList",
        sort: true,
    },
    {
        id: "shiftType",
        align: "left",
        disablePadding: false,
        label: "ShiftType",
        sort: true,
    },
    {
        id: "shiftID",
        align: "left",
        disablePadding: false,
        label: "shiftID",
        sort: true,
    },

    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

let Data = []
function AgencyDepartmentDetails() {
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const gridRef = useRef(null);
    const DepartmentRef = useRef();
    const dispatch = useDispatch();
    const routeParams = useParams();
    const user = useSelector(({ auth }) => auth.user);
    const agencyCode = routeParams.code !== "list" ? routeParams.code : user.data.defaultAgency
    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.isloading);
    const DepartmentTotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.totalMasterCount);
    const shiftTypeData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.shiftTypeData);
    const ShiftTimeData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shift.data);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [searchText, setSearchText] = React.useState("");
    const [data, setData] = React.useState(DepartmentData);
    const [countData, setCountData] = React.useState(DepartmentTotalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "Name",
    });
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    let colorCode = getNavbarTheme();

    useEffect(() => {
        dispatch(getTeams("name", order.direction, 0, 10000, null, routeParams.code));
        dispatch(getShiftTime("ShiftName", order.direction, 0, 10000, null, routeParams.code));
    }, []);

    const deleteCallViolation = (n) => {
        setOpen(true);
        setRemoveID(n._id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeDepartment(removeID, agencyCode));
            setCountData(countData - 1);
        }
    };

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const search = useDebounce((search, pageIndex, rowsPerPage) => {
        dispatch(getMasterDepartmentDetails(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, search === '' ? null : search, agencyCode));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, pageIndex, rowsPerPage);
        } else {
            dispatch(getMasterDepartmentDetails(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, searchText === '' ? null : searchText, agencyCode));
        }
    }, [dispatch, pageIndex, order, rowsPerPage, searchText, agencyCode]);

    useEffect(() => {
        setData(DepartmentData);
        setCountData(DepartmentTotalCount)
    }, [DepartmentData]);

    const [loading, setLoading] = useState();
    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    const generateShift = (n) => {
        //Here we need to generate the shift...
        history.push(`/admin/ShiftTime/${routeParams.code}/${n._id}`);
    };

    const getValidationData = (n) => {
        let x = shiftTypeData.filter(x => x._id === n.shiftID)
        let y = DepartmentData.filter(x => x._id === n._id)
        let z = ShiftTimeData.filter(x => x.department[0]._id === n._id)

        if (x[0].noofteams === y[0].teams.filter(x => x.code == agencyCode).length && z.length === x[0].noofshifts) {
            return true
        }
        else {
            return false
        }
    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    };

    const generateShiftAllocation = (n) => {
        //Here we need to generate the shift...
        let x = getValidationData(n)
        // history.push(`/admin/ShiftAllocation/${routeParams.code}/${n._id}`);
        if (x) {
            history.push(`/admin/ShiftAllocation/${routeParams.code}/${n._id}`);
        }
        else {
            ShowErroMessage("Please fullfill the requirements related to shift type.");
        }
    };

    const ActionIcons = (n) => {
        // let x = checkData(n)
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => DepartmentRef.current.handleClickOpen(x, routeParams.code, true)}
                                    size="large"
                                >
                                    <EditIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("delete")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    // disabled={n.isActive}
                                    onClick={() => deleteCallViolation(x)}
                                    size="large"
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("generateShift")}>
                                <IconButton
                                    aria-label="Shift"
                                    color="inherit"
                                    onClick={() => generateShift(x)}
                                    size="large"
                                >
                                    <HistoryIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("generateShiftAllocation")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => generateShiftAllocation(x)}
                                    size="large"
                                >
                                    <NoteAddIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                    }
                </>
            );
        }
    };

    function getUserData(item) {
        let data = ""
        if (item.users === undefined || item.users === null) {
            return ("");
        }
        else {
            return (
                data = item.users.map(number => number.fname + ' ' + number.lname).join(", ")
            );
        }
    }

    function getTeamsData(item) {
        let data = ""
        if (item.teams === undefined || item.teams === null) {
            return ("");
        }
        else {
            return (data = item.teams.map(number => number.name).join(", "));
        }
    }

    const rowData = data.map(item => {
        let name = getUserData(item)
        let team = getTeamsData(item)
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["name"] = item.name
            row["description"] = item.description
            row["shiftType"] = item.shiftType[0] !== undefined ? item.shiftType[0].name : ""
            row["shiftID"] = item.shiftType[0] !== undefined ? item.shiftType[0]._id : ""
            row["users"] = name
            row["teams"] = team
            row["action"] = ActionIcons(item)
        });
        return row;
    });

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <DashboardIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("department")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addDepiartment" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addDepartment")} parentCallback={() => DepartmentRef.current.handleClickOpen(Data, routeParams.code, false)}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>

                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={countData}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{
                                    "aria-label": "Previous Page",
                                }}
                                nextIconButtonProps={{
                                    "aria-label": "Next Page",
                                }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />
                        </div>
                        <div className="igrGridClass" >

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="name"
                                        field="name"
                                        header={t("name")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        pinned={false}
                                    />
                                    <IgrColumn
                                        key="description"
                                        header={t("description")}
                                        field="description"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        pinned={false}
                                    />
                                    <IgrColumn
                                        key="users"
                                        header={t("users")}
                                        field="users"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        pinned={false}
                                    />
                                    <IgrColumn
                                        key="teams"
                                        header={t("teams")}
                                        field="teams"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        pinned={false}
                                    />
                                    <IgrColumn
                                        key="shiftType"
                                        header={t("shiftType")}
                                        field="shiftType"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        pinned={false}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="250px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteDepartmentMsg")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="AgencyDepartmentDialog" />} onReset={() => { }} >
                                <AgencyDepartmentDialog ref={DepartmentRef} />
                            </ErrorBoundary>
                        </div>
                    </div>
                }
            />
        </>
    );
}

export default AgencyDepartmentDetails;
