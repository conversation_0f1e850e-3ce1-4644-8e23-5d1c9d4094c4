var Polyglot = require('node-polyglot');
var messages = require('../config/i18n.js');
exports.startPolyglot = (req, res, next) => {

    // Get the locale from express-locale
    const locale = req.locale.language

    // Start Polyglot and add it to the req
    req.polyglot = new Polyglot()

    // Decide which phrases for polyglot will be used
    if (locale == 'es') {
    req.polyglot.extend(messages.messages.es)
    } else {
        req.polyglot.extend(messages.messages.en)
    }


    next()
}