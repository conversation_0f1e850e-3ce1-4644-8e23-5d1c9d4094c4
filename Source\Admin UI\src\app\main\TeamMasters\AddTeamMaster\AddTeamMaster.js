import React, { forwardRef, useEffect, useState, useImperativeHandle, useRef } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import Button from '@mui/material/Button';
import { TextField, Autocomplete } from "@mui/material";
import { saveTeam } from '../../store/teamSlice';
import { getUsers } from '../../administration/store/usersSlice';
import FormControl from '@mui/material/FormControl';
import { useParams } from "react-router";
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ListItemText from '@mui/material/ListItemText';
import Select from '@mui/material/Select';
import Checkbox from '@mui/material/Checkbox';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import ConfirmationDialog from '../../components/ConfirmationDialog/ConfirmationDialog';
import { ChromePicker } from 'react-color';
import { getDepartmentDetails, getMasterDepartmentDetails } from '../../store/departmentSlice';
import { showMessage } from 'app/store/fuse/messageSlice';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';

const defaultValues = {
    name: '',
    users: []
};

const schema = yup.object().shape({
    name: yup.string().required('Please enter Team Name.'),
});

let selectedUser;
let userlabel;
let lastValue;

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
    PaperProps: {
        style: {
            maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
            width: 250,
        },
    },
};

let teamLeaderFlag = false;
const AddTeamMaster = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const routeParams = useParams();
    const [users, setusers] = useState([]);
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState([]);
    const [userData, setUserData] = React.useState([]);
    const [teamLeaderData, setTeamLeaderData] = React.useState([]);
    const [title, setTitle] = React.useState("");
    const [isUpdate, setisUpdate] = React.useState(false);
    const [PagingDetails, setPagingDetails] = React.useState();
    const [opendialog, setOpendialog] = React.useState(false);
    const [personName, setPersonName] = React.useState([]);
    const [teamLeaders, setTeamLeaders] = React.useState([]);
    const [selecteduserid, setselecteduserid] = React.useState();
    const [color, setColor] = useState('#000000');
    const [dropdownOpen, setDropdownOpen] = React.useState(false);
    const [departmentValue, setDepartmentValue] = React.useState([]);
    const [teamleadValue, setTeamLeadValue] = React.useState([]);

    const loggedinuser = useSelector(({ auth }) => auth.user);
    const user = useSelector(({ administration }) => administration.user.allusers);
    const TeamData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.teammaster.data);
    const departmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
    const shiftTypeData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.shiftTypeData);
    const agencyCode = routeParams.code !== "list" ? routeParams.code : loggedinuser.data.defaultAgency
    const userlist = user.filter(y => y.isSuperAdmin === false && y.defaultAgency === agencyCode).sort((a, b) => a.fname.localeCompare(b.fname));

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues
    });
    const { isValid, dirtyFields, errors } = formState;
    const nameRef = useRef(null);

    useEffect(() => {
        dispatch(getUsers("fname", "asc", 0, 10000, false, "ALL"));
        // dispatch(getDepartmentDetails("name", "asc", 0, 1000, agencyCode));
        dispatch(getMasterDepartmentDetails("name", "asc", 0, 10000, null, routeParams.code));
    }, []);

    useImperativeHandle(ref, () => ({
        handleOpen(data, PagingDetails, flag, title) {
            // dispatch(getDepartmentDetails("name", "asc", 0, 1000, agencyCode));
            // dispatch(getMasterDepartmentDetails("name", "asc", 0, 10000, null, routeParams.code));
            setData(data)
            setPagingDetails(PagingDetails)
            setTitle(title);
            setisUpdate(flag);
            setTeamvalue(data);
            handleClickOpen();
        },
    }));

    const handleClickOpen = () => {
        setOpen(true);
        setTimeout(() => {
            nameRef.current?.focus();
        }, 0);
    };

    const handleClose = () => {
        setOpen(false);
        defaultValues;
        setValue('name', "");
        setValue('users', []);
        setPersonName([]);
        setUserData([]);
        setTeamLeaderData([]);
        setTeamLeaders([]);
        setTeamLeadValue([]);
        setColor('#000000')
        teamLeaderFlag = false;
    };

    const setTeamvalue = async (data) => {
        setselecteduserid(data._id);
        setDepartmentValue(data.departmentid ? data.departmentid : '')
        setusers(userlist.map((x) => ({
            value: x._id,
            label: x.fname + ' ' + x.lname,
            fname: x.fname,
            lname: x.lname,
            email: x.email,
            code: x.defaultAgency,
            useragencies: x.userAgencies,
        })))
        setValue('name', data.name);
        setColor(data.color !== null ? data.color : "")
        let w = TeamData.filter((x) => x._id === data._id && x.code === agencyCode);
        selectedUser = w[0];
        if (selectedUser) {
            teamLeaderFlag = true
            setUserData(selectedUser.users.map((x) => ({
                value: x.value,
                label: x.label,
                fname: x.fname,
                lname: x.lname,
                email: x.email,
                code: x.code,
                useragencies: x.useragencies,
            })))

            setPersonName(selectedUser.users.map((x) => (x.label)));
            setTeamLeaders(selectedUser.users.map((x) => (x.label)));
            setTeamLeaderData(selectedUser.teamLeader.map((x) => ({
                value: x.value,
                label: x.label,
                fname: x.fname,
                lname: x.lname,
                email: x.email,
                code: x.code,
                useragencies: x.useragencies,
            })))
            setTeamLeadValue(selectedUser.teamLeader.map((x) => (x.label)));

        }
    }

    function setteamuserdata(teamuserdata) {
        setUserData(teamuserdata.map((x) => ({
            value: x?._id,
            label: x?.fname + ' ' + x?.lname,
            fname: x?.fname,
            lname: x?.lname,
            email: x?.email,
            code: x?.defaultAgency,
            useragencies: x?.userAgencies,
        })))
    }
    function setteamleaderdata(teamleaderdata) {
        setTeamLeaderData(teamleaderdata.map((x) => ({
            value: x?._id,
            label: x?.fname + ' ' + x?.lname,
            fname: x?.fname,
            lname: x?.lname,
            email: x?.email,
            code: x?.defaultAgency,
            useragencies: x?.userAgencies,
        })))
    }

    function onSubmit(model) {

        let x = departmentData.filter(x => x._id == departmentValue)
        if (departmentValue) {
            let department = {
                _id: x[0]._id,
                name: x[0].name
            }
            const items = {
                _id: data._id,
                name: model.name,
                users: userData,
                teamLeader: teamLeaderData,
                department: department,
                code: agencyCode,
                isUpdate: isUpdate,
                color: color
            }
            dispatch(saveTeam(items, PagingDetails.sortField, PagingDetails.sortDirection, PagingDetails.pageIndex, PagingDetails.pageLimit));
            setOpen(false);
            teamLeaderFlag = false;
            handleClose();
        }
        else {
            ShowErroMessage(t("departmentRequired"))
        }
    }

    const handleCloseDialog = (newValue) => {
        let matchingUserValues;
        setOpendialog(false);
        handleDropdownOpen();
        if (!newValue) {
            let t = userlabel.filter((r) => r != lastValue);
            setPersonName(t);
            setTeamLeaders(t)
            teamLeaderFlag = true
            matchingUserValues = personName.map((item) =>
                userlist.filter((x => x.fname + ' ' + x.lname === item))[0]
            )
        }
        else {
            setPersonName(userlabel);
            setTeamLeaders(userlabel)
            teamLeaderFlag = true
            matchingUserValues = userlabel.map((item) =>
                userlist.filter((x => x.fname + ' ' + x.lname === item))[0]
            )
        }
        setteamuserdata(matchingUserValues);
    };


    const handleChange = (event) => {
        if (event.target.value.length > 0) {
            userlabel = event.target.value.map((item) => item);
            lastValue = userlabel[userlabel.length - 1];
            let z = TeamData.filter((x) => x._id != selecteduserid);
            let a = z.map((x) => x.users.map((y) => y.label));
            let alreadyexistinguser = a.some(item => item.some(val => lastValue.includes(val)));
            if (alreadyexistinguser) {
                setOpendialog(true);
                handleDropdownClose();
                alreadyexistinguser = false;
                setselecteduserid();
            }
            else {
                setPersonName(event.target.value);
                setTeamLeaders(userlabel)
                teamLeaderFlag = true
                const matchingUserValues = event.target.value.map((item) =>
                    userlist.filter((x => x.fname + ' ' + x.lname === item))[0]
                )
                setteamuserdata(matchingUserValues);
            }
        }
        else {
            setPersonName(event.target.value);
            setTeamLeaders(event.target.value);
            teamLeaderFlag = false
        }

    };

    const handleteamLeadChange = (event) => {
        setTeamLeadValue(event.target.value)
        const matchingTeamLeaderValues = event.target.value.map((item) =>
            userlist.filter((x => x.fname + ' ' + x.lname === item))[0]
        )
        setteamleaderdata(matchingTeamLeaderValues);
    };

    const handleColorChange = (newColor) => {
        setColor(newColor.hex);
    };

    const handleDropdownClose = () => {
        setDropdownOpen(false);
    };

    const handleDropdownOpen = () => {
        setDropdownOpen(true);
    };


    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }
    const handleShiftTypeChange = (newValue) => {
        if (!newValue) return;  // If no department is selected, avoid further processing

        let filteredData = newValue; // `newValue` is the selected department object
        let x = filteredData.teams.filter((team) => team._id !== data._id && team.code === agencyCode);
        let shiftTypeFilteredData = shiftTypeData.find((shift) => shift._id === filteredData.shiftType[0]._id);

        if (x.length < shiftTypeFilteredData.noofteams) {
            setDepartmentValue(newValue._id); // Set the department value using the selected object's `_id`
        } else {
            ShowErroMessage(t("teamLimit"));
        }
    };


    return (
        <div>
            <Dialog
                fullWidth={true} maxWidth='md' open={open} onClose={handleClose} aria-labelledby="responsive-dialog-title" >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t(`${title}`)} {t("team")}</DialogTitle>
                <DialogContent dividers>
                    <form name="registerForm" className="flex flex-col justify-center w-full pb-16" onSubmit={handleSubmit(onSubmit)} autoSave={false} >
                        <Controller
                            name="name"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("name")}
                                    type="text"
                                    // error={!!errors.type}
                                    // helperText={errors?.type?.message}
                                    variant="outlined"
                                    required
                                    inputRef={nameRef}
                                />
                            )}
                        />

                        <FormControl fullWidth sx={{ mb: 2, minWidth: 120 }}>
                            <CommonAutocomplete
                                parentCallback={(event, newValue) => handleShiftTypeChange(newValue)}
                                options={departmentData || []}
                                value={departmentData.find((item) => item._id === departmentValue) || null}
                                fieldName={t('nfirsEquipmentType')}
                                optionLabel={"name"}
                                onKeyDown={handleSelectKeyDown}
                            />

                        </FormControl>

                        <FormControl className="mb-16 w-full" fullWidth sx={{ minWidth: 80 }} required>
                            <InputLabel id="demo-multiple-checkbox-label">{t("users")}</InputLabel>
                            <Select
                                labelId="demo-multiple-checkbox-label"
                                id="demo-multiple-checkbox"
                                multiple
                                value={personName}
                                open={dropdownOpen}
                                onChange={handleChange}
                                input={<OutlinedInput label={t("users")} />}
                                renderValue={(selected) => selected.join(', ')}
                                MenuProps={MenuProps}
                                onClose={handleDropdownClose}
                                onOpen={handleDropdownOpen}
                            >
                                {users.map((name) => (
                                    <MenuItem key={name.label} value={name.label}>
                                        <Checkbox checked={personName.indexOf(name.label) > -1} />
                                        <ListItemText primary={name.label} />
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        {teamLeaderFlag &&

                            <FormControl className="mb-16 w-full" fullWidth sx={{ minWidth: 80 }} required>
                                <InputLabel id="demo-multiple-checkbox-label">{t("teamlead")}</InputLabel>
                                <Select
                                    labelId="demo-multiple-checkbox-label"
                                    id="demo-multiple-checkbox"
                                    multiple
                                    value={teamleadValue}
                                    onChange={handleteamLeadChange}
                                    input={<OutlinedInput label={t("teamlead")} />}
                                    renderValue={(selected) => selected.join(', ')}
                                    MenuProps={MenuProps}
                                >
                                    {teamLeaders.sort((a, b) => a.localeCompare(b)).map((name) => (
                                        <MenuItem key={name} value={name}>
                                            <Checkbox checked={teamleadValue.indexOf(name) > -1} />
                                            <ListItemText primary={name} />
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        }

                        <label style={{ marginBottom: "2%" }}>{t("colorCode")}</label>
                        <ChromePicker color={color} onChange={handleColorChange}
                            styles={{
                                // Set background of the entire picker container to transparent
                                default: {
                                    picker: {
                                        background: 'transparent', // Transparent background
                                    },
                                    body: {
                                        background: 'transparent', // Transparent background
                                    },
                                },
                            }} />

                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-10"
                                aria-label="Register"
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-10"
                                aria-label="Register"
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>
                    </form>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                        <ConfirmationDialog
                            id="ringtone-menu"
                            keepMounted
                            open={opendialog}
                            text={t("confirmationmsg")}
                            onClose={handleCloseDialog}
                        >
                        </ConfirmationDialog>
                    </ErrorBoundary>

                </DialogContent>
            </Dialog>
        </div>
    );
});

export default AddTeamMaster;