import { combineReducers } from '@reduxjs/toolkit';
import wrecker from './wreckerSlice';
import person from './personSlice';
import location from './locationSlice';
import organization from './organizationSlice';
import replaySearch from './replayHistorySlice';
import gun from './gunSlice';
import boat from './boatSearchSlice';
import article from './articleSearchSlice';
import incident from './incidentSlice';
import violationSlice from './violationSlice'
import unit from './unitSlice';
import nfirs from './nfirsSlice';
import teammaster from './teamSlice';
import shift from './shiftTimeSlice';
import shiftallocation from './shiftAllocationSlice';
import department from './departmentSlice';
import schedule from './shiftAllocationScheduleSlice';
import loginSettings from './loginSettingsSlice';
import quiktip from './quikTipSlice';
import ImportFromExcel from './importFromExcelSlice';
import updateRequest from './updateRequestSlice';

const dispatchCallsReducers = combineReducers({
	wrecker,
	person,
	location,
	organization,
	replaySearch,
	gun,
	boat,
	article,
	incident,
	violationSlice,
	unit,
	nfirs,
	teammaster,
	shift,
	shiftallocation,
	department,
	schedule,
	loginSettings,
	quiktip,
	ImportFromExcel,
	updateRequest,
});

export default dispatchCallsReducers;
