import { motion } from 'framer-motion';
import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import Input from '@mui/material/Input';
import Paper from '@mui/material/Paper';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import Box from '@mui/material/Box';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { getAgencyData, setFilterAgencyDDlValue } from '../../agencyPage/store/agencySlice';
import { resetEditUser } from '../store/usersSlice';
import { ClearUserData } from '../../../auth/store/registerSlice';
import { selectMainTheme } from 'app/store/fuse/settingsSlice';
import History from '@history';
import AddMultipleUserDialog from '../../Dialog/AddMultipleUserDialog/AddMultipleUserDialog'
import { clearUsersSearchText } from '../store/usersSlice';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import CommonButton from '../../SharedComponents/ReuseComponents/CommonButton';
import './UserNewHeader.css';
import { IconButton, InputAdornment } from '@mui/material';
import CancelIcon from '@mui/icons-material/Cancel';

function UserNewHeader(props) {
	const dispatch = useDispatch();
	const userRef = useRef();
	//const searchText = useSelector(({ administration }) => administration.user.searchText);
	const mainTheme = useSelector(selectMainTheme);
	const { t } = useTranslation('laguageConfig');
	const [searchText, setSearchText] = useState("");

	dispatch(resetEditUser());

	const addNewUser = () => {
		dispatch(resetEditUser());
		dispatch(ClearUserData());
		History.push("/admin/create_New_User");
	};

	const user = useSelector(({ auth }) => auth.user);
	const agencyList = useSelector(({ agency }) => agency.agency.data);
	const filterAgencyValue = useSelector(({ agency }) => agency.agency.filterAgencyValue);
	const value = props.selectedPage;
	const [agency, setAgency] = React.useState(filterAgencyValue);

	const handleChange = (event) => {
		dispatch(setFilterAgencyDDlValue(event.target.value))
		setAgency(event.target.value);
		props.handleSelectedAgecny(event.target.value)
	};

	useEffect(() => {
		dispatch(getAgencyData());
		// eslint-disable-next-line
	}, []);

	const handleClear = () => {
		setAgency('ALL');
		props.handleSelectedAgecny('ALL')
		dispatch(setFilterAgencyDDlValue('ALL'))
		dispatch(clearUsersSearchText(''));
	};

	const AddMultiPleUser = () => {
		userRef.current.handleClickOpen()
	};

	const handleSearch = (value) => {
		setSearchText(value);
		props.setSearchText(value);
	};

	return (
		<>
			<div className='flex flex-col w-full'>
				<div className="flex flex-1 pt-20 w-full items-center justify-between">
					<div className="flex items-center">
						<Icon
							component={motion.span}
							initial={{ scale: 0 }}
							animate={{ scale: 1, transition: { delay: 0.2 } }}
							className="text-32">people</Icon>
						<Typography
							component={motion.span}
							initial={{ x: -20 }}
							animate={{ x: 0, transition: { delay: 0.2 } }}
							delay={300}
							className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
							{t('users')}
						</Typography>
					</div>

					<div className="flex flex-1 items-center justify-center px-12">
						<StyledEngineProvider injectFirst>
							<ThemeProvider theme={mainTheme}>
								<Paper
									sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
									component={motion.div}
									initial={{ y: -20, opacity: 0 }}
									animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
									className="flex items-center w-full max-w-512 px-8 py-4 rounded-8" elevation={1}>
									<Icon color="action">search</Icon>

									<Input
										placeholder={t('search')}
										className="flex flex-1 mx-8"
										disableUnderline
										fullWidth
										value={searchText}
										inputProps={{
											'aria-label': 'Search'
										}}
										onChange={ev => handleSearch(ev.target.value)}
										endAdornment={
											<InputAdornment position='end'>
												<IconButton
													onClick={e => handleSearch("")}
												>
													<CancelIcon />
												</IconButton>
											</InputAdornment>
										}
									/>
								</Paper>
							</ThemeProvider>
						</StyledEngineProvider>
					</div>

					{
						user.data.isSuperAdmin &&
						<>
							<div className="flex items-center">
								<Typography
									component={motion.span}
									initial={{ x: -20 }}
									animate={{ x: 0, transition: { delay: 0.2 } }}
									delay={300}
									className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
									{t('filterByAgency')}
								</Typography>
							</div>



							<Box component={motion.div}
								initial={{ y: -20, opacity: 0 }}
								animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
								className="flex items-center w-full max-w-512 px-8 py-4 rounded-8" elevation={1}>
								<FormControl fullWidth>
									<Select
										labelId="demo-simple-select-label"
										id="demo-simple-select-filter"
										name="defaultagency"
										className="Pagination-Dropdown-select float-right"
										value={agency !== "" ? agency : "ALL"}
										onChange={handleChange}

									>
										<MenuItem value="ALL">All</MenuItem>
										{agencyList.map((element) => (
											<MenuItem key={element.code} value={element.code}>
												{element.name}
											</MenuItem>
										))}
									</Select>
								</FormControl>

							</Box>
						</>
					}

					<ErrorBoundary
						FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_Clear" />} onReset={() => window.location.reload()} >
						<CommonButton styleClass="whitespace-no-wrap normal-case ml-16" btnName={t("clear")} parentCallback={handleClear}></CommonButton>
					</ErrorBoundary>

					<ErrorBoundary
						FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addNewUser" />} onReset={() => window.location.reload()} >
						<CommonButton styleClass="whitespace-no-wrap normal-case ml-16" btnName={t("newUser")} parentCallback={addNewUser}></CommonButton>
					</ErrorBoundary>


					{
						user.data.isSuperAdmin &&
						<>
							<ErrorBoundary
								FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addMultipleUser" />} onReset={() => window.location.reload()} >
								<CommonButton styleClass="whitespace-no-wrap normal-case ml-16" btnName={t("addMultipleUsers")} parentCallback={AddMultiPleUser}></CommonButton>
							</ErrorBoundary>

							<ErrorBoundary
								FallbackComponent={(props) => <ErrorPage {...props} componentName="AddMultipleUserDialog" />} onReset={() => window.location.reload()} >
								<AddMultipleUserDialog ref={userRef} agencyList={agencyList.filter(obj => obj.code !== "System")}></AddMultipleUserDialog>
							</ErrorBoundary>
						</>
					}
				</div>
			</div>
		</>
	);
}

export default UserNewHeader;
