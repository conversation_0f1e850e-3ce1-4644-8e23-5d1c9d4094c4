import Button from '@mui/material/Button';
import React, { useRef, useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { submitResetPassword } from '../../../auth/store/resetPasswordSlice';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import { TextField, InputAdornment, Icon } from '@mui/material';
import { passwordGenerator } from 'src/app/main/utils/utils';

const schema = yup.object().shape({
	email: yup.string().email('You must enter a valid email').required('You must enter a email'),	
  });
  const defaultValues = {
	email: '',
  };
 
function JWTResetPasswordTab() {
  
	const dispatch = useDispatch();
	const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
		mode: 'onChange',
		defaultValues,
		resolver: yupResolver(schema),
	});
	const { isValid, dirtyFields, errors } = formState;
	
	function onSubmit(data) {		
		dispatch(submitResetPassword(data.email));
	}

	return (
		<div className="w-full">
		<form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              className="mb-16"
              type="text"
              error={!!errors.email}
              helperText={errors?.email?.message}
              label="Email"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Icon className="text-20" color="action">
                      user
                    </Icon>
                  </InputAdornment>
                ),
              }}
              variant="outlined"
            />
          )}
        />

        <Button
          type="submit"
          variant="contained"
          color="primary"
          className="w-full mx-auto mt-16"
          aria-label="LOG IN"
          disabled={_.isEmpty(dirtyFields) || !isValid}
          value="legacy"
        >
          Forgot Password
        </Button>
      </form>

		</div>
	);
}

export default JWTResetPasswordTab;
