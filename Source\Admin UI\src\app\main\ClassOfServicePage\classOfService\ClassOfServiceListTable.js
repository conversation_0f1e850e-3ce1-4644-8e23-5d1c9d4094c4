import FuseScrollbars from '@fuse/core/FuseScrollbars';
import _ from '@lodash';
import Button from '@mui/material/Button';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
//import { withRouter } from 'react-router-dom';
import withRouter from '@fuse/core/withRouter';
import ClassOfServiceListTableHead from './ClassOfServiceListHead';
import EditIcon from '@mui/icons-material/Edit';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import DeleteIcon from '@mui/icons-material/Delete';
import history from '@history';

import PropTypes from 'prop-types';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Dialog from '@mui/material/Dialog';
import { makeStyles } from '@mui/styles';
import { useTranslation } from 'react-i18next';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";


import { getClassOfServiceList, setClassOfServiceID, removeClassOfService } from '../store/classOfServiceSlice'

function ClassOfServiceListTable() {
	const dispatch = useDispatch();
	const { t } = useTranslation('laguageConfig');
	const codeList = useSelector(({ classOfService }) => classOfService.classOfService.data);
	const searchText = useSelector(({ classOfService }) => classOfService.classOfService.searchText);
	const delSuccess = useSelector(({ classOfService }) => classOfService.classOfService.delSuccess);


	const [data, setData] = useState(codeList);
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(100);
	const [order, setOrder] = useState({
		direction: 'asc',
		id: null
	});

	useEffect(() => {
		dispatch(getClassOfServiceList());
	}, [dispatch]);

	useEffect(() => {
		if (delSuccess) {
			dispatch(getClassOfServiceList());
		}
		// eslint-disable-next-line
	}, [delSuccess]);



	useEffect(() => {
		if (searchText.length !== 0) {
			setData(
				_.filter(codeList, item =>
					// eslint-disable-next-line prefer-template
					(item.code + ' ' +
						item.description + ' ' +
						item.color + ' ' +
						item.icon).toLowerCase().includes(searchText.toLowerCase())
				)
			);
			setPage(0);
		} else {
			setData(codeList);
		}
	}, [codeList, searchText]);

	function handleRequestSort(property) {
		const id = property;
		let direction = 'desc';

		if (order.id === property && order.direction === 'desc') {
			direction = 'asc';
		}

		setOrder({
			direction,
			id
		});
	}


	const handleChangePage = (event, value) => {
		setPage(value);
	};

	function handleChangeRowsPerPage(event) {
		setRowsPerPage(event.target.value);
	}

	const handleClickEdit = (classOfService) => {
		dispatch(setClassOfServiceID(classOfService._id));
		history.push("/classOfServiceEdit");
	};

	const useStyles = makeStyles({
		root: {
			width: '100%',
		},
		container: {
			maxHeight: 440,
		},
	});

	const classes = useStyles();
	const [open, setOpen] = React.useState(false);
	const [value, setValue] = React.useState();

	const handleClickDelete = (agencyID) => {
		setValue(agencyID);
		setOpen(true);
	};

	const handleClose = (newValue) => {
		setOpen(false);
		if (newValue) {
			//after user select yes
			dispatch(removeClassOfService(newValue));
		}
	};

	return (
		<div className="w-full flex flex-col">
			<FuseScrollbars className="flex-grow overflow-x-auto">
				<Table className="min-w-xl" aria-labelledby="tableTitle" stickyHeader aria-label="sticky table">
					<ErrorBoundary
						FallbackComponent={(props) => <ErrorPage {...props} componentName="ClassOfServiceListTableHead" />} onReset={() => { }}>
						<ClassOfServiceListTableHead
							order={order}
							onRequestSort={handleRequestSort}
							rowCount={data.length}
						/>
					</ErrorBoundary>
					<TableBody>
						{_.orderBy(
							data,
							[
								o => {
									switch (order.id) {
										default: {
											return o[order.id];
										}
									}
								}
							],
							[order.direction]
						)
							.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
							.map((n, i) => {
								return (
									<TableRow key={i}>
										<TableCell component="th" scope="row">
											{n.code}
										</TableCell>

										<TableCell component="th" scope="row">
											{n.description}
										</TableCell>

										<TableCell component="th" scope="row">
											<div style={{ backgroundColor: n.color, width: '50%' }}>&nbsp;</div>
										</TableCell>

										{/* <TableCell component="th" scope="row">
											{n.icon}
										</TableCell> */}

										<TableCell className="w-52" component="th" scope="row" padding="none">
											{(n.iconPic !== "" && n.iconPic !== undefined) &&
												<img
													className="w-full block rounded"
													src={n.iconPic}
													alt="icon"
													style={{ height: '60px', width: '60px' }}
												/>
											}
										</TableCell>

										<TableCell component="th" scope="row">
											<Tooltip title="Edit">
												<IconButton aria-label="edit"
													color="primary"
													onClick={() => handleClickEdit(n)}>
													<EditIcon />
												</IconButton>
											</Tooltip>

											<Tooltip title="Delete">
												<IconButton aria-label="delete"
													color="primary"
													onClick={() => handleClickDelete(n._id)}>
													<DeleteIcon />
												</IconButton>
											</Tooltip>
										</TableCell>
									</TableRow>
								);
							})}
					</TableBody>
					<ConfirmationDialogRaw
						classes={{ paper: classes.paper, }}
						id="ringtone-menu"
						keepMounted
						open={open}
						onClose={handleClose}
						value={value}
					/>
				</Table>
			</FuseScrollbars>

			<TablePagination
				className="tablePaging"
				component="div"
				count={data.length}
				rowsPerPage={rowsPerPage}
				page={page}
				backIconButtonProps={{
					'aria-label': 'Previous Page'
				}}
				nextIconButtonProps={{
					'aria-label': 'Next Page'
				}}
				onPageChange={handleChangePage}
				onRowsPerPageChange={handleChangeRowsPerPage}
			/>
		</div>
	);
}


ConfirmationDialogRaw.propTypes = {
	onClose: PropTypes.func.isRequired,
	open: PropTypes.bool.isRequired,
	value: PropTypes.any,
};

function ConfirmationDialogRaw(props) {

	const { onClose, value: valueProp, open, ...other } = props;
	const { t } = useTranslation('laguageConfig');
	const handleCancel = () => {
		onClose();
	};

	const handleOk = () => {
		onClose(valueProp);
	};


	return (
		<Dialog
			//disableBackdropClick
			disableEscapeKeyDown
			maxWidth="xs"
			aria-labelledby="confirmation-dialog-title"
			open={open}
			{...other}
		>
			<DialogTitle id="confirmation-dialog-title" color="primary">{t('confirm')}</DialogTitle>
			<DialogContent dividers>
				<div className='m-16'>
					{t('delete')}
				</div>
			</DialogContent>
			<DialogActions>
				<Button autoFocus onClick={handleCancel} variant="contained" color="primary">
					{t('no')}
				</Button>
				<Button onClick={handleOk} variant="contained" color="primary">
					{t('yes')}
				</Button>
			</DialogActions>
		</Dialog>
	);
}


export default withRouter(ClassOfServiceListTable);
