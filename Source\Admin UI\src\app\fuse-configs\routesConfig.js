import React from 'react';
import { Navigate } from 'react-router-dom';
import FuseUtils from '@fuse/utils';
import LoginConfig from 'app/main/login/LoginConfig';
import ResetPasswordConfig from 'app/main/resetPassword/ResetPasswordConfig';
import AdministrationAppConfig from 'app/main/administration/AdministrationAppConfig';
import AgencyAppConfig from 'app/main/agencyPage/AgencyAppConfig';
import AgencyAddConfig from 'app/main/agencyAdd/AgencyAddConfig';
import ContactAppConfig from 'app/main/contactPage/ContactAppConfig';
import ContactAddConfig from 'app/main/contactAdd/ContactAddConfig';
import MaintenancePageConfig from 'app/main/maintenance/MaintenancePageConfig';
import UserAuditAppConfig from 'app/main/userAuditPage/UserAuditAppConfig';

import Call911Config from 'app/main/911Call/Call911Config';
import classOfServiceAppConfig from 'app/main/ClassOfServicePage/ClassOfServiceAppConfig';
import classOfServiceAddConfig from 'app/main/ClassOfServiceAdd/ClassOfServiceAddConfig';
import FileUploadConfig from 'app/main/FileUpload/FileUploadConfig';
import AgencyOption from 'app/main/agencyOptions/AgencyOptionsConfig';


const routeConfigs = [
	LoginConfig,
	ResetPasswordConfig,
	AdministrationAppConfig,
	AgencyAddConfig,
	AgencyAppConfig,
	ContactAddConfig,
	ContactAppConfig,
	MaintenancePageConfig,
	UserAuditAppConfig,


	Call911Config,
	classOfServiceAppConfig,
	classOfServiceAddConfig,
	FileUploadConfig,
	AgencyOption

];

const routes = [
	...FuseUtils.generateRoutesFromConfigs(routeConfigs),
	{
		path: '/',
		component: () => <Navigate to="/login" />
	}
];

export default routes;
