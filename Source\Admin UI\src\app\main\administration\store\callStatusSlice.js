import { createSlice } from "@reduxjs/toolkit";
import { showMessage } from "app/store/fuse/messageSlice";
import { decrypt, encrypt } from "../../../security/crypto";
import axios from "axios";

export const getNonCallStatus = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios.get(`admin/api/userCallStatus/getNonCallStatus/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setNonCallStatusTotalCount(listData.totalCount));
                    return dispatch(setNonCallStatus(listData.nonCallStatusList));
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch(error => {
                console.error("Error in API call:", error);
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        console.error("Caught an exception:", e.message);
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const saveNonCallStatus = (data, pageIndexData, rowsPerPageData) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.post(`admin/api/userCallStatus/saveNonCallStatus`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.data));
                    dispatch(getNonCallStatus("_id", "desc", pageIndexData, rowsPerPageData, null, data.code));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const removeUserNonCallStatus = (ID, code, pageIndex, pageLimit) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.delete(`admin/api/userCallStatus/removeUserNonCallStatus/${ID}/${code}`)
            .then(async response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.data));
                    dispatch(getNonCallStatus("_id", "desc", pageIndex, pageLimit, null, code));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const serachNonCallStatus = (code, searchText) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.get(`admin/api/userCallStatus/serachNonCallStatus/${code}/${searchText}`)
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setSearchNonCallStatusTotalCount(listData.totalCount));
                    return dispatch(setSearchdataNonCallStatus(listData.nonCallStatusList));
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}
const initialState = {
    data: [],
    searchdata: [],
    isloading: false,
};

const callStatusSlice = createSlice({
    name: "administration/callStatusSlice",
    initialState,
    reducers: {
        setNonCallStatus: (state, action) => {
            state.data = action.payload;
        },
        setSearchdataNonCallStatus: (state, action) => {
            state.searchdata = action.payload;
        },
        setNonCallStatusTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setSearchNonCallStatusTotalCount: (state, action) => {
            state.searchtotalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        }
    },
    extraReducers: {},
});

export const {
    setNonCallStatus,
    setNonCallStatusTotalCount,
    setLoading,
    setChangeFlag,
    setSearchdataNonCallStatus,
    setSearchNonCallStatusTotalCount
} = callStatusSlice.actions;

export default callStatusSlice.reducer;