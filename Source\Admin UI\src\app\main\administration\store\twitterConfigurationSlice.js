import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';


export const getTwitterConfigurations = () => async dispatch => {
    try {
        await axios.get(`admin/api/twitterConfiguration/GetTwitterConfiguration`)
            .then(response => {
                if (response.status == 200) {
                    return dispatch(setTwitterConfigurations(JSON.parse(decrypt(response.data))))
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
}


// To update Server Configuration
export const updateTwitterConfiguration = (data) => async dispatch => {
    try {
        await axios.post(`admin/api/twitterConfiguration/updateTwitterConfiguration`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    data: []
};

const twitterConfigurationSlice = createSlice({
    name: 'administration/twitterConfigurations',
    initialState,
    reducers: {
        setTwitterConfigurations: (state, action) => {
            state.data = action.payload;
        }
    },
    extraReducers: {}
});

export const { setTwitterConfigurations
} = twitterConfigurationSlice.actions;

export default twitterConfigurationSlice.reducer;
