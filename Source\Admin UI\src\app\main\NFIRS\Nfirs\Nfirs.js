import "./Nfirs.css";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import TablePagination from "@mui/material/TablePagination";
import {
    DialogTitle, DialogContent, DialogActions, Dialog, ThemeProvider, StyledEngineProvider, Paper, Input,
    Tooltip
} from "@mui/material";
import { newUserAudit } from "../../userAuditPage/store/userAuditSlice";
import { removeNFIRS, getNFIRS, searchNFIRS } from "../../store/nfirsSlice";
import { useTranslation } from "react-i18next";
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import CircularProgressLoader from "../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import AddNfirs from "./AddNfirs";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import { calculateColumnWidth, calculateOptimalMultiplier, getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils"
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { IgrGridModule, IgrGridToolbar, IgrGridToolbarActions, IgrGridToolbarAdvancedFiltering, IgrGridToolbarHiding, IgrGridToolbarPinning } from "@infragistics/igniteui-react-grids";
import {
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";

let Data = [];

IgrGridModule.register();

function Nfirs() {
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();
    const gridContainerRef = useRef(null);
    const gridRef = useRef(null);
    const nfirRef = useRef(null);

    const user = useSelector(({ auth }) => auth.user);
    const NfirsData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.nfirs.data);
    const searchnfirs = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.nfirs.searchnfirs);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.nfirs.isloading);
    const nfirsTotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.nfirs.totalCount);
    const success = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.nfirs.success);

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const ActionIconColor = navbarTheme.palette.primary.light;
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    const RowsSelectedBackgroundColor = navbarTheme.palette.secondary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);
    const [loading, setLoading] = useState();
    const [searchText, setSearchText] = React.useState("");
    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [data, setData] = React.useState(NfirsData);
    const [countData, setCountData] = React.useState(nfirsTotalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "name",
    });
    let colorCode = getNavbarTheme();
    const [gridWidth, setGridWidth] = useState(1200);
    const msg = t("deleteMsg");

    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);

    useEffect(() => {
        if (searchText.length > 0) {
            search(searchText);
        } else {
            setData(NfirsData);
            setCountData(nfirsTotalCount)
        }
    }, [searchText, NfirsData]);

    useEffect(() => {
        if (searchText.length !== 0) {
            setData(searchnfirs);
            setCountData(searchnfirs.length)
        } else {
            setData(NfirsData);
            setCountData(nfirsTotalCount)
        }
    }, [NfirsData, searchText, nfirsTotalCount, searchnfirs]);


    const search = useDebounce(search => {
        dispatch(searchNFIRS(search));
    }, 500);


    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access NFIRS",
                user: user,
                appName: "Admin",
            })
        );
    }, []);

    useEffect(() => {
        dispatch(getNFIRS(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage));
    }, [success, pageIndex, rowsPerPage, countData, order]);

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const deleteNfirs = (n) => {
        setOpen(true);
        setRemoveID(n._id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeNFIRS(newValue));
            setCountData(countData - 1);
        }
    };

    const ActionIcons = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div style={{ display: "flex" }}>
                    <Tooltip title={t("edit")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color='inherit'
                            onClick={() => nfirRef.current.handleOpen(x, PagingDetails, true, "update")}
                            size="large"
                        >
                            {/* <Icon>edit</Icon> */}
                            <EditIcon />
                        </IconButton>
                    </Tooltip>

                    <Tooltip title={t("delete")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color='inherit'
                            //disabled={x.isActive}
                            onClick={() => deleteNfirs(x)}
                            size="large"
                        >
                            {/* <Icon>delete</Icon> */}
                            <DeleteIcon />
                        </IconButton>
                    </Tooltip>
                </div>
            )
        }
    };

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            setOrder({ id: sortDesc.field, direction: sortDesc.sortDirection === 0 ? "asc" : "desc" });
        } else {
            console.log("No sorting applied.");
        }

    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    let PagingDetails = {
        sortField: order.id,
        sortDirection: order.direction,
        pageIndex: pageIndex * rowsPerPage,
        pageLimit: rowsPerPage
    }

    const rowsPerPageOptions = getRowsPerPageOptions();

    useEffect(() => {
        const updateGridWidth = () => {
            if (gridContainerRef.current) {
                const containerWidth = gridContainerRef.current.offsetWidth;
                setGridWidth(containerWidth);
            }
        };

        // Initial calculation
        updateGridWidth();

        // Add event listener for window resizing
        window.addEventListener('resize', updateGridWidth);
        return () => window.removeEventListener('resize', updateGridWidth);
    }, []);

    // Set your IgrDataGrid width here or calculate it dynamically
    const fields = ["type", "name", "type", "description"];
    const multiplier = calculateOptimalMultiplier(data, fields, gridWidth) - 3;
    // Calculate individual column widths with the calculated multiplier
    const typeWidth = calculateColumnWidth(data, "type", 200, multiplier);
    const nameWidth = calculateColumnWidth(data, "name", 200, multiplier);
    const descriptionWidth = calculateColumnWidth(data, "description", 200, multiplier);


    const pinningConfig = new ColumnPinning();
    pinningConfig.columns = ColumnPinningPosition.End;

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        <div className="flex flex-1 p-32 items-center justify-between" >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    api
                                </Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("nfirs")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addNfirs" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="whitespace-no-wrap normal-case ml-16" btnName={t("addNFIRS")} parentCallback={() => nfirRef.current.handleOpen(Data, PagingDetails, false, "add")}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={countData}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate={false}
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="type"
                                        header={t("type")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("name")}
                                        field="name"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("description")}
                                        field="description"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>

                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog_NFIRS" />} onReset={() => { }} >
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={msg}
                                onClose={handleClose}
                                value={removeID}
                            >
                            </ConfirmationDialog>
                        </ErrorBoundary>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="AddNfirs" />} onReset={() => { }} >
                            <AddNfirs ref={nfirRef} />
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    )

}

export default Nfirs;