.MuiBox-root {
    padding: 4px 0px 0px 0px !important;
}

.MuiTabs-indicator {
    background-color: #FFFF;
    height: 3px;

}

.p-13 {
    padding: 13px !important;
}

.container {
    padding: 0px 0px !important;
}

.MuiDialog-paperWidthFalse {
    max-width: calc(100% - 64px);
    min-width: 40%
}

.video-react .video-react-big-play-button {
    top: 65px !important;
    left: 101px !important;
}

.video-react.video-react-fluid,
.video-react.video-react-16-9,
.video-react.video-react-4-3 {
    padding-top: 17% !important;
    width: 30% !important;
}

.MuiCardContent-root:last-child {
    padding-bottom: 5px;
}

.MuiCardContent-root {
    padding: 5px;
}


.noPadding {
    padding: 0px !important;
    font-size: xx-small !important;
}

/* .MuiIconButton-root{
    padding: 0px !important
}
.MuiCardActions-root {
    padding: 3px !important;
} */

.cardborderclass {
    border-radius: 0px !important;
}


.cardborderclasswithhover {
    border-radius: 0px !important;
}

.cardborderclasswithhover:hover {
    border: solid 3px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.781);
    /* Add a subtle shadow on hover */
    background-color: #f0f0f0;
    /* Change the background color on hover */
    transition: border 0.3s ease-in-out, box-shadow 0.3s ease-in-out, background-color 0.3s ease-in-out;
    /* Apply transition to multiple properties */
}