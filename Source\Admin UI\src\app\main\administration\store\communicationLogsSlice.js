/* eslint-disable prettier/prettier */
import { createSlice } from "@reduxjs/toolkit";
import { showMessage } from "app/store/fuse/messageSlice";
import { decrypt, encrypt } from "../../../security/crypto";
import axios from "axios";

export const saveCommunicatioLog = (data) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/communicationLogs/AddEditCommunicationLog`, encrypt(JSON.stringify(data)))
            .then(async (res) => {
                if (res.status == 200) {
                    res.data = await JSON.parse(decrypt(res.data));
                    if (await res.data.isSuccess) {
                        dispatch(setLoading(false));
                        dispatch(
                            showMessage({
                                message: data.isUpdate ? res.data.message : res.data.message,
                                autoHideDuration: 2000,
                                anchorOrigin: { vertical: "top", horizontal: "right", },
                                variant: "success",
                            })
                        );
                        if (data.isUpdate) {
                            dispatch(setCommunicationLogEditData(res.data.newData))
                            dispatch(setCommunicationLogsTotalCount(res.data.totalCount));
                        }
                        else {
                            dispatch(setCommunicationAddData(res.data.newData))
                            dispatch(setCommunicationLogsTotalCount(res.data.totalCount));
                        }
                    }
                }
                else {
                    dispatch(setLoading(false));
                    res = JSON.parse(decrypt(res.response.data));
                    dispatch(showMessage({
                        message: res.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getCommunicationLog = (sortField, sortDirection, pageIndex, pageLimit,searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/communicationLogs/getCommunicationLog/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setCommunicationLogsTotalCount(listData.totalCount));
                    dispatch(setLoading(false));
                    return dispatch(setCommunicationLogsData(listData.communicationlogList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const removeCommunicationLog = (ID, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/communicationLogs/DeleteCommunicationLog/${ID}/${code}`)
            .then(async (response) => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    dispatch(removeCommunicationLogsData(ID));
                    response = JSON.parse(decrypt(response.data));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: "top",
                            horizontal: "right",
                        },
                        variant: "success",
                    })
                    );
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

//for searching
export const searchCommunicationLogs = (searchText, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        axios.get(`admin/api/communicationLogs/searchCommunicationLogs/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchCommunicationLogs(data));
                }
            })
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

const initialState = {
    success: false,
    searchcommunicationLogsData: [],
    communicationLogsData: [],
    isloading: false,
};

const communicationLogsSlice = createSlice({
    name: "administration/CommunicationLog",
    initialState,
    reducers: {
        setCommunicationLogsData: (state, action) => {
            state.communicationLogsData = action.payload;
        },
        setSearchCommunicationLogs: (state, action) => {
            state.searchcommunicationLogsData = action.payload;
        },
        setCommunicationLogsTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setCommunicationLogEditData: (state, action) => {
            const index = state.communicationLogsData.findIndex(obj => obj._id === action.payload._id);
            if (index !== -1) {
                state.communicationLogsData[index] = action.payload;
            }
        },
        setCommunicationAddData: (state, action) => {
            state.communicationLogsData = [...state.communicationLogsData, action.payload];
        },
        removeCommunicationLogsData: (state, action) => {
            state.communicationLogsData = state.communicationLogsData.filter(x => x._id !== action.payload);
        },
    },
    extraReducers: {},
});

export const {
    setCommunicationLogsTotalCount,
    setCommunicationLogsData,
    setLoading,
    setCommunicationLogEditData,
    setCommunicationAddData,
    removeCommunicationLogsData,
    setSearchCommunicationLogs
} = communicationLogsSlice.actions;

export default communicationLogsSlice.reducer;