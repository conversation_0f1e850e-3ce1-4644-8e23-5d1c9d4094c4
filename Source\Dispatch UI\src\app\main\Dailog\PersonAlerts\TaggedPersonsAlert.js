import React from "react";
import { useTranslation } from 'react-i18next';

const TaggedPersonAlert = ({ data }) => {

  const { t } = useTranslation('languageConfig');

  if (!Array.isArray(data) || data.length === 0) {
    return (
      <div className="p-6 rounded-xl shadow-md">
        <h2 className="text-2xl font-bold text-red-600 mb-1">🎯 {t("taggedPersons")}</h2>
        <p className="text-gray-500">{t("noTaggedPersonsFound") || "No tagged persons found."}</p>
      </div>
    );
  }

  return (
    <div className="p-6 rounded-xl shadow-md">
      <h2 className="text-2xl font-bold text-red-600 mb-1">🎯 {t("taggedPersons")}</h2>

      <div className="space-y-4">
        {data.map((person, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4 shadow-sm">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold text-800">{person?.tarDisplayname || "Unnamed Person"}</h3>
                <p className="text-sm text-500">{person?.personTagDetail || "No tag detail"}</p>
              </div>
              <div className="text-xs text-white bg-indigo-600 rounded-full px-2 py-1">
                {person?.matchType || "Tagged"}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 text-sm text-700">
              <div>
                <p><strong>{t("dobAbbreviation")}:</strong> {person?.tarDOB || "N/A"}</p>
                <p><strong>{t("race")}:</strong> {person?.tarRace || "N/A"}</p>
                <p><strong>{t("sex")}:</strong> {person?.tarSex || "N/A"}</p>
                <p><strong>{t("ssn")}:</strong> {person?.tarSSN || "N/A"}</p>
                <p><strong>{t("dlNumber")}:</strong> {person?.tarDLNumber || "N/A"}</p>
                <p><strong>{t("dlState")}:</strong> {person?.tarDLState || "N/A"}</p>
              </div>
              <div>
                <p><strong>{t("homeAddress")}:</strong> {person?.tarHomeAddress || "N/A"}</p>
                <p><strong>{t("workAddress")}:</strong> {person?.tarWorkAddress || "N/A"}</p>
                <p><strong>{t("contact")}:</strong> {person?.conDisplayName || "N/A"}</p>
                <p><strong>{t("contactRace")}:</strong> {person?.conRace || "N/A"}</p>
                <p><strong>{t("contactSex")}:</strong> {person?.conSex || "N/A"}</p>
                <p><strong>{t("contactDL")}:</strong> {person?.conDLNumber || "N/A"} ({person?.conDLState || "N/A"})</p>
              </div>
            </div>

            {person?.notes && (
              <div className="mt-4 text-sm text-600">
                <strong>{t("notes")}:</strong> {person.notes}
              </div>
            )}

            <div className="mt-3 text-xs text-400">
              {t("source")}: {person?.remotesource || "Unknown"}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TaggedPersonAlert;
