import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { Autocomplete, TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { showMessage } from 'app/store/fuse/messageSlice';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { saveShiftAllocation } from '../../store/shiftAllocationSlice';
import { getMasterDepartmentDetails } from '../../store/departmentSlice';
import Select from '@mui/material/Select';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker';
import { useParams } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';
import { DateTimePicker } from "@mui/x-date-pickers";

const defaultValues = {
};

const schema = yup.object().shape({
    // ShiftName: yup.string().required('Please enter Shift Name.'),
    // ShiftDuration: yup.string().required('Shift Duration not be empty.'),
});

let update;
let deptId;

const shiftAllocationDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const routeParams = useParams();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState(null);
    const [code, setCode] = React.useState("");
    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
    const [PagingDetails, setPagingDetails] = React.useState();
    const dtFrom = new Date();
    dtFrom.setHours(dtFrom.getHours() - 2);
    const departmentRef = useRef(null);
    const [dropdownOpen, setDropdownOpen] = React.useState(false);

    const [selectedFromDate, setSelectedFromDate] = React.useState(
        localStorage.getItem("selectedFromDate") === null
            ? dtFrom
            : new Date(localStorage.getItem("selectedFromDate"))
    );

    const [selectedToDate, setSelectedToDate] = React.useState(
        localStorage.getItem("selectedToDate") === null
            ? dtFrom
            : new Date(localStorage.getItem("selectedToDate"))
    );

    const [departmentValue, setDepartmentValue] = React.useState([]);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues
    });

    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            if (departmentRef.current) {
                departmentRef.current.focus(); // Focus on the Department field
                setDropdownOpen(true); // Open the dropdown
            }
        }, 0);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, PagingDetails, flag, deptID) {
            setData(data)
            setCode(code)
            update = flag
            deptId = deptID
            setPagingDetails(PagingDetails)
            setShiftTimeValue(data)
            handleClickOpen1();
        },
    }));

    useEffect(() => {
        dispatch(getMasterDepartmentDetails("name", "asc", 0, 10000, null, routeParams.code));
    }, []);

    const setShiftTimeValue = data => {
        if (deptId !== '0') {
            setDepartmentValue(deptId)
        }
        if (data._id !== null && data._id !== undefined) {
            let StartDate = new Date(data.StartDate)
            let EndDate = new Date(new Date(data.EndDate))
            setSelectedFromDate(data.StartDate ? new Date(data.StartDate) : null);
            setSelectedToDate(data.EndDate ? new Date(data.EndDate) : null);
            setDepartmentValue(data.departmentID)
        }
        else {
            setSelectedFromDate(new Date())
            setSelectedToDate(new Date())
        }
    };

    const handleClose = () => {
        setOpen(false);
        setSelectedFromDate(null);
        setSelectedToDate(null);
        setDepartmentValue(null);
    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    function onSubmit(model) {
        let id = update ? data._id : 0
        let deptData = DepartmentData.filter(x => x._id === departmentValue)[0]
        let filterDepartmetnData = {};
        if (deptData) {
            filterDepartmetnData = {
                _id: deptData._id,
                name: deptData.name
            }
        }
        if (selectedToDate < selectedFromDate) {
            ShowErroMessage(t('dateValidation'));
        }
        else {
            dispatch(
                saveShiftAllocation(
                    {
                        _id: id,
                        isUpdate: update,
                        code: code,
                        StartDate: new Date(selectedFromDate).setSeconds(0),
                        EndDate: new Date(selectedToDate).setSeconds(0),
                        department: filterDepartmetnData,
                    },
                    PagingDetails.pageIndex,
                    PagingDetails.rowsPerPage,
                    PagingDetails.id,
                    PagingDetails.direction
                )
            );
            handleClose();
        }
    }

    function removeDatefromStorage() {
        localStorage.removeItem("selectedToDate");
        localStorage.removeItem("selectedFromDate");
        localStorage.removeItem("pValue");
    }

    const handleFromDateChange = (date) => {
        removeDatefromStorage()
        setSelectedFromDate(date);
        let filterData = DepartmentData.filter(x => x._id === departmentValue)[0]
        if (filterData) {
            let x = filterData.shiftType[0].noofdefaultdays - 1
            let ToDate = new Date(date);
            setSelectedToDate(new Date(ToDate.setSeconds(x * 86400)))
        }
    };

    const handleToDateChange = (date) => {
        removeDatefromStorage();
        setSelectedToDate(date);
    };

    const handleShiftTypeChange = (newValue) => {
        if (newValue) {
            setDepartmentValue(newValue._id); // Set the selected department's ID as the value
        }
    };


    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("shiftAllocation")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        // autoComplete="off"
                        autoSave={false}
                    >
                        <FormControl fullWidth className="mt-16 w-full">
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsCriminalValue" />} onReset={() => { }}>
                                <CommonAutocomplete
                                    parentCallback={(event, newValue) => handleShiftTypeChange(newValue)}
                                    options={DepartmentData || []}
                                    value={DepartmentData.find((item) => item._id === departmentValue) || null}
                                    fieldName={t("department")}
                                    optionLabel={"name"}
                                    onKeyDown={handleSelectKeyDown}
                                />
                            </ErrorBoundary>

                            <DateTimePicker
                                className="mt-16 w-full"
                                autoOk={true}
                                size="medium"
                                inputVariant="standard"
                                format="MM/dd/yyyy HH:mm"
                                margin="normal"
                                id="date-picker-inline"
                                ampm={false}
                                value={selectedFromDate}
                                onChange={handleFromDateChange}
                                KeyboardButtonProps={{
                                    "aria-label": "change date",
                                }}
                                label={t("startDate")}
                                renderInput={(params) => (
                                    <TextField {...params} className="" />
                                )}
                            />
                            
                            <DateTimePicker
                                className="mt-16 w-full"
                                autoOk={true}
                                size="medium"
                                inputVariant="standard"
                                format="MM/dd/yyyy HH:mm"
                                margin="normal"
                                id="date-picker-inline"
                                ampm={false}
                                value={selectedToDate}
                                onChange={handleToDateChange}
                                disabled={true}
                                KeyboardButtonProps={{
                                    "aria-label": "change date",
                                }}
                                label={t("endDate")}
                                renderInput={(params) => (
                                    <TextField {...params} />
                                )}
                            />

                        </FormControl>

                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
});
export default shiftAllocationDialog;

