import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import "./CommonSwitch.css"

function CommonSwitch(props) {

    const handleToggleChange = (event) => {
        props.parentCallback(event)
    };

    return (
        <div className="mt-6">
            <FormControlLabel
                control={
                    <Switch
                        checked={props.toggleValue}
                        onChange={handleToggleChange}
                        name="checked"
                        inputProps={{
                            "aria-label": "secondary checkbox",
                        }}
                    />
                }
                label={props.switchName}
            />
        </div>
    );
}

export default CommonSwitch;