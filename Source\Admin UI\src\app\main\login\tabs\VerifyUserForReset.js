import CardContent from '@mui/material/CardContent';
import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { spacing } from '@mui/system';
import Typography from '@mui/material/Typography';
import { motion } from 'framer-motion';
import Card from '@mui/material/Card';
import { darken, styled } from '@mui/material/styles';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import { sendForgotPasswordCode, confirmResetPassword } from '../../../auth/store/userSlice';
import { useForm, Controller } from 'react-hook-form';
import { TextField } from '@mui/material';
import { useParams } from 'react-router-dom';

const Root = styled('div')(({ theme }) => ({
    background: `linear-gradient(to right, ${theme.palette.primary.dark} 0%, ${darken(
        theme.palette.primary.dark,
        0.5
    )} 100%)`,
    color: theme.palette.primary.contrastText,

    '& .Login-leftSection': {},

    '& .Login-rightSection': {
        background: `linear-gradient(to right, ${theme.palette.primary.dark} 0%, ${darken(
            theme.palette.primary.dark,
            0.5
        )} 100%)`,
        color: theme.palette.primary.contrastText,
    },
}));
const schema = yup.object().shape({
    verificationCode: yup.string()
        .required('Please enter your verification Code.')
        .min(6, 'Verification Code Invalid')
        .max(6, 'Verification Code Invalid'),
    password: yup
        .string()
        .required('Please enter your password.')
        .matches('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\\$%\\^&\\*])(?=.{8,})',
            "The password must be min. 8 characters & contains at least 1 uppercase,1 lowercase,1 numeric,1 special character"),
    passwordConfirm: yup
        .string()
        .required("Please confirm your password")
        .oneOf([yup.ref('password'), null], "Passwords don't match.")
});
const defaultValues = {
    verificationCode: '',
    password: '',
    passwordConfirm: ''
};

function VerifyUserForReset(props) {
    const dispatch = useDispatch();
    const routeParams = useParams();
    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });
    const { isValid, dirtyFields, errors } = formState;
    const userName = useSelector(({ auth }) => auth.user.userName);

    const [showVerificationCode, setShowVerificationCode] = useState(false);

    useEffect(() => {

        dispatch(sendForgotPasswordCode(routeParams.id));
        // eslint-disable-next-line
    }, [routeParams]);

    function onSubmit(model) {
        dispatch(confirmResetPassword({
            VerificationCode: model.verificationCode,
            Password: model.password,
            UserName: userName
        }));
    }



    return (
        <Root className="flex flex-col flex-auto items-center justify-center w-full shrink-0 p-16 md:p-24">
            <motion.div
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
            >
                <Card className="w-full min-w-384 max-w-384">
                    <CardContent className="flex flex-col items-center justify-center p-32">
                        <div className="m-16">
                            <Icon className="text-96" color="action">
                                verified_user
                            </Icon>
                        </div>

                        <Typography variant="h5" className="text-center mb-16">
                            Reset Password
                        </Typography>
                        <form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>
                            <Controller
                                type="number"
                                name="verificationCode"

                                control={control}
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        className="mb-16"
                                        label="Verification Code"
                                        type="number"
                                        error={!!errors.verificationCode}
                                        helperText={errors?.verificationCode?.message}
                                        variant="outlined"
                                        InputProps={{
                                            className: 'pr-2',
                                            type: showVerificationCode ? 'text' : 'password',
                                            endAdornment: (
                                                <InputAdornment position="end" className='pr-2'>
                                                    <IconButton
                                                        onClick={() => setShowVerificationCode(!showVerificationCode)}
                                                        size="large">
                                                        <Icon className="text-20" color="action">
                                                            {showVerificationCode ? 'visibility' : 'visibility_off'}
                                                        </Icon>
                                                    </IconButton>
                                                </InputAdornment>
                                            ),
                                        }}
                                        required
                                    />
                                )}
                            />
                            <Controller
                                type="password"
                                name="password"
                                control={control}
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        className="mb-16"
                                        label="New Password"
                                        type="password"
                                        error={!!errors.password}
                                        helperText={errors?.password?.message}
                                        variant="outlined"
                                        InputProps={{
                                            className: 'pr-2',
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <IconButton>
                                                        <Icon className="text-20" color="action">
                                                            vpn_key
                                                        </Icon>
                                                    </IconButton>
                                                </InputAdornment>
                                            ),
                                        }}
                                        required
                                    />
                                )}
                            />
                            <Controller
                                type="password"
                                name="passwordConfirm"
                                control={control}
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        className="mb-16"
                                        label="Confirm Password"
                                        type="password"
                                        error={!!errors.passwordConfirm}
                                        helperText={errors?.passwordConfirm?.message}
                                        variant="outlined"
                                        InputProps={{
                                            className: 'pr-2',
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <IconButton>
                                                        <Icon className="text-20" color="action">
                                                            vpn_key
                                                        </Icon>
                                                    </IconButton>
                                                </InputAdornment>
                                            ),
                                        }}
                                        required
                                    />
                                )}
                            />
                            <Button
                                type="submit"
                                variant="contained"
                                color="primary"
                                className="w-full mx-auto mt-16"
                                aria-label="LOG IN"
                                disabled={_.isEmpty(dirtyFields) || !isValid}
                                value="legacy"
                            >
                                Reset Password
                            </Button>
                        </form>



                    </CardContent>
                </Card>
            </motion.div>
        </Root>
    );
    // eslint-disable-next-line no-else-return	
}


export default VerifyUserForReset;
