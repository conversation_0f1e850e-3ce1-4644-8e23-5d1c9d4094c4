import React, { useEffect, useState } from 'react';
import { ReactSearchAutocomplete } from 'react-search-autocomplete';
import axios from 'axios';
import { decrypt } from "src/app/security";
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import { useSelector } from "react-redux";
import { useTranslation } from 'react-i18next';

const PersonSearchReactAutoComplete = ({
    onSelect,
    handleClear,
    isGlobal = false,
    code,
}) => {
    const [items, setItems] = useState([]);
    const { t } = useTranslation("laguageConfig");
    const navbarTheme = useSelector(selectNavbarTheme);

    const search = (async (search) => {
        try {
            // Construct dynamic URL based on passed parameters 
            let url;
            if (isGlobal === true) {
                url = `admin/api/personMaster/globalPersonSearchList/${search}`;
            } else {
                url = `admin/api/personMaster/agencyPersonSearchList/${search}/${code}`;
            }
            const response = await axios.get(url);
            if (response.status === 200) {
                const decryptedData = await JSON.parse(decrypt(response.data));
                const data = await decryptedData.map((item) => ({
                    ...item,
                    name: nameFormatter(item), // Use dynamic name formatter
                }));
                setItems(data);
            } else {
                setItems([]);
            }
        } catch (error) {
            setItems([]);
            console.error("Error fetching data:", error);
        }
    });

    const nameFormatter = (item) => `${item.FullName}, ${item.Sex !== "" && item.Sex !== null && item.Sex !== undefined ? item.Sex + ', ' : ""
        }${item.Race !== "" && item.Race !== null && item.Race !== undefined ? item.Race + ', ' : ""
        }${item.DateOfBirth !== "" && item.DateOfBirth !== null && item.DateOfBirth !== undefined
            ? new Date(item.DateOfBirth).toDateString()
            : ""
        }`;

    const handleOnSearch = (string) => {
        search(string);
    };

    const handleOnHover = (result) => {
        console.log(result);
    };

    const handleOnSelect = (item) => {
        onSelect(item);
    };

    const handleOnClear = () => {
        setItems([]);
        handleClear();
    };

    //For removing the auto suggestion
    const onFocus = (event) => {
        event.target.setAttribute('autocomplete', 'new-password');
    };

    return (
        <ReactSearchAutocomplete
            items={items}
            inputDebounce={700}
            onSearch={handleOnSearch}
            onClear={handleOnClear}
            onHover={handleOnHover}
            onSelect={handleOnSelect}
            autoFocus={true}
            onFocus={onFocus}
            maxResults={100}
            styling={{
                border: "1px solid #ccc",
                borderRadius: "4px",
                backgroundColor: navbarTheme.palette.mode === 'light' ? 'white' : 'black',
                boxShadow: "none",
                hoverBackgroundColor: navbarTheme.palette.secondary.main,
                color: navbarTheme.palette.mode === 'light' ? 'black' : 'white',
                fontSize: "15px",
                iconColor: "grey",
                lineColor: "grey",
                placeholderColor: navbarTheme.palette.primary.light,
                clearIconMargin: "3px 8px 0 0",
                zIndex: 4,
                cursor: 'text',
                height: "40px",
            }}
            fuseOptions={{ keys: ["FullName", "DateOfBirth"] }}
            // resultStringKeyName="FullName"
            placeholder={t("searchbyName")}
            showNoResultsText={t("gatheringResults")}
        />
    );
};

export default PersonSearchReactAutoComplete;
