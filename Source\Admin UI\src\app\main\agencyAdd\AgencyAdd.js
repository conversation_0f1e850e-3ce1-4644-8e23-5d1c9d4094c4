import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import FormGroup from '@mui/material/FormGroup';
import Grid from '@mui/material/Grid';
import FusePageSimple from '@fuse/core/FusePageSimple';
import Typography from '@mui/material/Typography';
import MenuItem from '@mui/material/MenuItem';
import history from '@history';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import axios from 'axios';
import { showMessage } from 'app/store/fuse/messageSlice';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../SharedComponents/ErrorPage/ErrorPage';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import {
	newAgency, updateAgency, getAgencyByID, removeSavedAgencyDetails, setLoading,
	getAgencyServerConfigurations, saveAgencyServerConfiguration,
	GetUserByEmail, checkAgencyCode, verifyConnectionString,
	getParentAgencyDetailsByID, clearParentAgencyById,
	addAudioFile
} from '../agencyPage/store/agencySlice';
import { submitInitialUserRegistration } from '../../auth/store/registerSlice';
import { FormControl, InputLabel, Select, CardContent, Card, AppBar, Toolbar, TextField, Autocomplete, createFilterOptions, RadioGroup, Radio, Tooltip } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import { GetTimeZonesInts, isEmptyOrNull } from '../utils/utils';
import AccessRightsSettings from '../SharedComponents/AccessRightsSettings/AccessRightsSettings';
import { decrypt } from 'src/app/security';
import CircularProgressLoader from '../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import AppHeaderBar from '../administration/components/AppHeaderBar';
import Box from '@mui/material/Box';
import { makeStyles } from '@mui/styles';
import CommonAutocomplete, { handleSelectKeyDown } from '../SharedComponents/ReuseComponents/CommonAutocomplete';
import { getCityCountyStateZipCode } from '../administration/store/masterAddressSlice';
import FileUploadComponent from '../SharedComponents/FileUpload/FileUploadComponent';
import RefreshIcon from '@mui/icons-material/Refresh';
import FreeFormAutocomplete, { handleSelectKeyDownFreeForm } from '../SharedComponents/ReuseComponents/FreeFormAutoComplete';

let tzInts = GetTimeZonesInts();

const useStyles = makeStyles((theme) => ({
	paperHeight: {
		maxHeight: 'none',
		overflowY: 'visible',
	},
	listbox: {
		maxHeight: 230,
		overflowY: 'auto',
	},
}));

//#region  fields Schema

const schema = yup.object().shape({
	name: yup.string()
		.required('Please enter Name.'),
	code: yup.string()
		.required('Please enter Code.'),
	email: yup.string()
		.email('Please valid email')
		.required('Please enter email.'),
	address: yup.string()
		.required('Please enter address.'),
	//New fields added----
	latitude: yup.string()
		.required('Please enter latitude.'),
	longitude: yup.string()
		.required('Please enter longtitude.'),
	billingAddress: yup.string()
		.required('Please enter billing address.'),
	noOfUserLicenses: yup.string()
		.required('Please enter No Of User Licenses.'),
	noOfDeviceLicenses: yup.string()
		.required('Please enter No Of Device Licenses.'),
	ConnectionString: yup.string()
		.required('Please enter Agency Connection String.'),
	ncicApiUrl: yup.string()
		.required('Please enter NCIC Api Url.'),

	socketListenerUrl: yup.string()
		.required('Please enter Socket Url.'),
	socketApiUrl: yup.string()
		.required('Please enter Socket Url.'),
});

const defaultValues = {
	name: '',
	code: '',
	email: '',
	address: '',
	timezone: 11,
	latitude: '',
	longitude: '',
	billingAddress: '',
	nibrsOri: '',
	ncicOri: '',
	fdId: '',
	nemsisId: '',
	facilityCode: '',
	initialCode: '',
	agencyConfigCode: '',
	agencyURL: '',
	mobileXAPIPath: '',
	agencyName: '',
	noOfUserLicenses: '',
	noOfDeviceLicenses: '',
	ConnectionString: '',
	ncicApiUrl: '',
	mugshotAgencyName: '',
	mugshotAgencyURL: '',
	mugshotAgencyID: '',
	mugshotInitialCode: '',
	liveStreamHost: '',
	liveStreamParserType: '',
	liveStreamPort: '',
	locationParserURL: '',
	socketListenerUrl: '',
	socketApiUrl: ''
};
//#endregion

let countyList = [];
let cityList = [];
let stateList = [];
let billingCountyList = [];
let billingCityList = [];
let billingStateList = [];

const filter = createFilterOptions();

function AgencyAddPage() {
	const dispatch = useDispatch();
	const { t } = useTranslation('laguageConfig');
	const classes = useStyles();
	const users = useSelector(({ administration }) => administration.user.data);
	const agencyID = useSelector(({ agency }) => agency.agency.agencyID);
	const agencyAdd = useSelector(({ agency }) => agency.agency.agencyAdd);
	const agency = useSelector(({ agency }) => agency.agency.agencyData);
	const parentAgencyData = useSelector(({ agency }) => agency.agency.parentAgencyData);
	const agencyList = useSelector(({ agency }) => agency.agency.data);
	const audioFileLocation = useSelector(({ agency }) => agency.agency.audioFileLocation);
	const callCategories = useSelector(({ agency }) => agency.agency.callCategories);
	const isloadingvalue = useSelector(({ agency }) => agency.agency.isloading);
	const emailExist = useSelector(({ agency }) => agency.agency.emailExist);
	const codeExist = useSelector(({ agency }) => agency.agency.codeExist);
	const ConnectionStringExist = useSelector(({ agency }) => agency.agency.ConnectionStringExist);
	const zipCodeList = useSelector(({ administration }) => administration.masterAddressSlice.zipcodeList);
	const countyListDB = useSelector(({ administration }) => administration.masterAddressSlice.countyList);
	const cityListDB = useSelector(({ administration }) => administration.masterAddressSlice.cityList);
	const stateListDB = useSelector(({ administration }) => administration.masterAddressSlice.stateList);

	const agencyTypes = [
		{ id: 1, name: t("none") },
		{ id: 2, name: t("global") },
		{ id: 3, name: t("local") },
		{ id: 4, name: t("main") },
		{ id: 5, name: t("sub") }
	];

	const [testCountyList, setTestCountyList] = useState([]);
	const [loading, setLoadingUI] = useState();
	const [alertAudioLocation, setAlertAudioLocation] = useState("");
	const [pageType, setPageType] = useState("add");
	const { control, setValue, setFocus, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
		mode: 'onChange',
		defaultValues,
		resolver: yupResolver(schema),
	});
	const { isValid, dirtyFields, errors } = formState;
	const [isAceesRightShow, setAccessRightShow] = React.useState(false);
	const [mugshotInitialCode, setMugshotInitialCode] = React.useState('');
	const [mugshotAgencyName, setMugshotAgencyName] = React.useState('');
	const [mugshotAgencyURL, setMugshotAgencyURL] = React.useState('');
	const [mugshotAgencyID, setMugshotAgencyID] = React.useState('');
	const [parentAgency, setParentAgency] = React.useState(null);
	const [county, setCounty] = useState(null);
	const [zipCode, setZipCode] = useState(null);
	const [city, setCity] = useState(null);
	const [state, setState] = useState(null);
	const [billingcounty, setBillingCounty] = useState(null);
	const [billingzipCode, setBillingZipCode] = useState(null);
	const [billingcity, setBillingCity] = useState(null);
	const [billingstate, setBillingState] = useState(null);
	const [isDeviceLicenseCode, setDeviceLicenseCode] = React.useState(false);
	const [isOtpRequired, setIsOtpRequired] = React.useState(false);
	const [showPersonAlert, setShowPersonAlert] = React.useState(false);
	const [isParentAgencyEdit, setIsParentAgencyEdit] = React.useState(false);
	const [versionValue, setVersionValue] = React.useState("");
	const [audioFileName, setAudioFileName] = React.useState(null);
	const [agencyType, setAgencyType] = React.useState(null);


	const defaultTimeZone = tzInts?.find(x => x?.ID === 11) || null;

	const [timezone, setTimezone] = React.useState(defaultTimeZone);

	//For FIRE and EMS Call Type
	const [callCategoryValue, setCallCategoryValue] = React.useState([]);
	const [callCategoryType, setCallCategoryType] = React.useState([]);
	const [accessRights, setAccessRights] = React.useState({
		Users: false,
		Contact: false,
		Server: false,
		Icons: false,
		CallCategory: false,
		CallViolation: false,
		CallType: false,
		CallResponse: false,
		Call: false,
		FileUpload: false,
		ClassofService: false,
		Incident: false,
		Dispatch: false,
		Chat: false,
		Current: false,
		Replay: false,
		SearchFeature: false,
		Watches: false,
		BroadcastMessages: false,
		MySchedule: false,
		DeviceLicense: false,
		Citation: false,
		Units: false,
		Nfirs: false,
		Shift: false,
		Team: false,
		ShiftAllocation: false,
		Department: false,
		Mugshot: false,
		TipType: false,
		ViewTip: false,
		Notification: false,
		QuikTip: false,
		DispatchCitation: false,
		TrafficStop: false,
		PersonSearch: false,
		VehicleSearch: false,
		GunSearch: false,
		ArticleSearch: false,
		BoatSearch: false,
		IncidentReport: false,
		AccidentReport: false,
		UserAudit: false,
		EmailConfiguration: false,
		TwitterConfiguration: false,
		TwitterAccountSettings: false,
		ErrorLog: false,
		ArchiveChatHistories: false,
		IncidentApp: false,
		IncidentWebsite: false
	});

	const [showAccessRight, setShowAccessRight] = React.useState([]);
	const [isEditmode, setIsEditMode] = useState(false);

	//for does not use old RPS flag 
	const [doesNotUseOldRps, setDoesNotUseOldRps] = useState(false);

	//For FIRE and EMS Call Type
	useEffect(() => {
		if (callCategories.length > 0) {
			setCallCategoryType(callCategories.map((x) => ({
				value: x._id,
				label: x.CallCategoryName,
				CallCategoryName: x.CallCategoryName,
				CallCategoryID: x.CallCategoryID,
				CallCategoryBranch: x.CallCategoryBranch,
				IconURL: x.IconURL,
			})));
		}
	}, [dispatch, callCategories]);

	useEffect(() => {
		if (agencyID !== 0 && agencyID !== "0" && agencyID !== null) {
			dispatch(getAgencyByID(agencyID));
		}
	}, [dispatch, agencyID]);

	useEffect(() => {
		if (audioFileLocation !== "") {
			setAlertAudioLocation(audioFileLocation);
		}
	}, [dispatch, audioFileLocation]);

	useEffect(() => {
		if (!state) {
			dispatch(getCityCountyStateZipCode(county, null));
		} else {
			dispatch(getCityCountyStateZipCode(null, null));
		}
	}, [city, state, county]);

	const FilterAccessRightByAgencyCode = (agencyAccessRightsData) => {
		var filterData = {}
		_(agencyAccessRightsData).forEach(function (a, k) {
			filterData[k] = k
		})
		setAccessRightShow(true);
		setShowAccessRight(filterData);
	};

	//#region Clear Function
	const clear = () => {
		setValue('name', '');
		setValue('code', '');
		setValue('type', '');
		setValue('email', '');
		setValue('address', '');
		setValue('timezone', 11);
		setValue('latitude', '');
		setValue('longitude', '');
		setValue('billingAddress', '');
		setValue('fdId', '');
		setValue('nemsisId', '');
		setValue('facilityCode', '');
		setValue('initialCode', '');
		setValue('agencyConfigCode', '');
		setValue('agencyURL', '');
		setValue('mobileXAPIPath', '');
		setValue('agencyName', '');
		setValue('noOfUserLicenses', '');
		setValue('noOfDeviceLicenses', '');
		setValue('ConnectionString', '');
		setValue('ncicApiUrl', '');
		setValue('mugshotAgencyName', '');
		setValue('mugshotAgencyURL', '');
		setValue('mugshotAgencyID', '');
		setValue('mugshotInitialCode', '');
		setValue('liveStreamHost', '');
		setValue('liveStreamParserType', '');
		setValue('liveStreamPort', '');
		setValue('locationParserURL', '');
		setDeviceLicenseCode(false);
		setIsOtpRequired(false);
		setShowPersonAlert(false);
		setParentAgency(null);
		setCallCategoryValue([]);
		setCallCategoryType([]);
		setMugshotInitialCode('');
		setMugshotAgencyName('');
		setMugshotAgencyURL('');
		setMugshotAgencyID('');
		setValue('socketListenerUrl', '');
		setValue('socketApiUrl', '');
		setValue('nibrsOri', '');
		setValue('ncicOri', '');
		setIsParentAgencyEdit(false);
		setCounty(null);
		setZipCode(null);
		setCity(null);
		setState(null);
		setBillingCounty(null);
		setBillingZipCode(null);
		setBillingCity(null);
		setBillingState(null);
		setAlertAudioLocation("");
		setVersionValue("");
		setAgencyType(null);
	};

	const clearParentAgencyData = () => {
		setValue('nibrsOri', '');
		setValue('ncicOri', '');
		setValue('agencyConfigCode', '');
		setValue('ncicApiUrl', '');
		setValue('mugshotAgencyName', '');
		setValue('mugshotAgencyURL', '');
		setValue('mugshotAgencyID', '');
		setValue('mugshotInitialCode', '');
		setValue('liveStreamHost', '');
		setValue('liveStreamParserType', '');
		setValue('liveStreamPort', '');
		setValue('locationParserURL', '');
		setMugshotAgencyURL('');
		setMugshotInitialCode('');
		setValue('socketListenerUrl', '');
		setValue('socketApiUrl', '');
		setValue('mobileXAPIPath', '');
		setValue('agencyURL', '');
		setValue('agencyName', '');
		setValue('initialCode', '');
		setValue('callCategories', []);
		setAccessRights(null);
		setCallCategoryValue([]);
	}
	//#endregion

	const setParentAgencyData = () => {
		if (isParentAgencyEdit) {
			if (parentAgencyData.agencyLicenses) {
				if (parentAgencyData.agencyLicenses.length > 0) {
					let updatedAgencyLicenses = parentAgencyData.agencyLicenses[0];
					const { CallCategory, ...updatedLicenses } = updatedAgencyLicenses;
					const trueKeyValues = Object.entries(updatedLicenses)
						.filter(([key, value]) => value === true)
						.reduce((result, [key, value]) => {
							result[key] = value;
							return result;
						}, {});
					setAccessRights(updatedLicenses);
					FilterAccessRightByAgencyCode(trueKeyValues);
				}
			}
		} else {
			if (agency.agencyLicenses !== undefined) {
				let updatedAgencyLicenses = parentAgencyData.agencyLicenses[0];
				const { CallCategory, ...updatedLicenses } = updatedAgencyLicenses;
				const trueKeyValues = Object.entries(updatedLicenses)
					.filter(([key, value]) => value === true)
					.reduce((result, [key, value]) => {
						result[key] = value;
						return result;
					}, {});
				FilterAccessRightByAgencyCode(trueKeyValues);
				setAccessRights(agency.agencyLicenses[0]);
			}
		}
		setValue('nibrsOri', parentAgencyData.nibrsOri !== undefined ? parentAgencyData.nibrsOri : '');
		setValue('ncicOri', parentAgencyData.ncicOri !== undefined ? parentAgencyData.ncicOri : '');
		setValue('agencyConfigCode', parentAgencyData.agencyConfigCode);
		setValue('ncicApiUrl', parentAgencyData.ncicApiUrl);
		setValue('mugshotAgencyName', parentAgencyData.mugshotAgencyName !== undefined ? parentAgencyData.mugshotAgencyName : '');
		setValue('mugshotAgencyURL', parentAgencyData.mugshotAgencyURL !== undefined ? parentAgencyData.mugshotAgencyURL : '');
		setValue('mugshotAgencyID', parentAgencyData.mugshotAgencyID !== undefined ? parentAgencyData.mugshotAgencyID : '');
		setValue('mugshotInitialCode', parentAgencyData.mugshotInitialCode !== undefined ? parentAgencyData.mugshotInitialCode : '');
		setValue('liveStreamHost', parentAgencyData.liveStreamHost !== undefined ? parentAgencyData.liveStreamHost : '');
		setValue('liveStreamParserType', parentAgencyData.liveStreamParserType !== undefined ? parentAgencyData.liveStreamParserType : '');
		setValue('liveStreamPort', parentAgencyData.liveStreamPort !== undefined ? parentAgencyData.liveStreamPort : '');
		setValue('locationParserURL', parentAgencyData.locationParserURL !== undefined ? parentAgencyData.locationParserURL : '');
		setMugshotAgencyURL(parentAgencyData.mugshotAgencyURL !== undefined ? parentAgencyData.mugshotAgencyURL : '')
		setMugshotInitialCode(parentAgencyData.mugshotInitialCode !== undefined ? parentAgencyData.mugshotInitialCode : '')
		setValue('socketListenerUrl', parentAgencyData.socketListenerUrl);
		setValue('socketApiUrl', parentAgencyData.socketApiUrl);
		setValue('mobileXAPIPath', parentAgencyData.mobileApiURL);
		setValue('agencyURL', parentAgencyData.mobileApiURL);
		setValue('agencyName', parentAgencyData.agencyName);
		setValue('initialCode', parentAgencyData.initialCode);
		if (parentAgencyData.callCategories != undefined) {
			setValue('callCategories', parentAgencyData.callCategories.map((x) => ({
				value: x.value,
				label: x.label,
				CallCategoryName: x.label,
				CallCategoryID: x.CallCategoryID,
				CallCategoryBranch: x.CallCategoryBranch,
				IconURL: x.IconURL,
			})));
			setCallCategoryValue(parentAgencyData.callCategories.map((x) => ({
				value: x.value,
				label: x.label,
				CallCategoryName: x.label,
				CallCategoryID: x.CallCategoryID,
				CallCategoryBranch: x.CallCategoryBranch,
				IconURL: x.IconURL,
			})));
		}
	}

	useEffect(() => {
		if (cityListDB !== null && cityListDB.length > 0) {
			cityList = cityListDB.filter((x) => x.StateCode === agency.state);
			billingCityList = cityListDB.filter((x) => x.StateCode === agency.state);
		}

		if (countyListDB !== null && countyListDB.length > 0) {
			countyList = countyListDB.filter((x) => x.StateCode === agency.state);
			billingCountyList = countyListDB.filter((x) => x.StateCode === agency.state);
		}
	}, [cityListDB, countyListDB, agency]);

	useEffect(() => {
		FilterAccessRightByAgencyCode(accessRights)
		if (agency._id !== undefined) {
			if (agencyID === "0") {
				setPageType("add");
			}
			else {
				if (agency.agencyLicenses !== undefined) {
					FilterAccessRightByAgencyCode(agency.agencyLicenses[0]);
					setAccessRights(agency.agencyLicenses[0]);
				}

				//Set the county and city list according to state
				setIsEditMode(true)
				cityList = cityListDB.filter((x) => x.StateCode === agency.state);
				countyList = countyListDB.filter((x) => x.StateCode === agency.state);
				billingCityList = cityListDB.filter((x) => x.StateCode === agency.state);
				billingCountyList = countyListDB.filter((x) => x.StateCode === agency.state);
				setPageType("edit");
				setIsParentAgencyEdit(false);
				setAlertAudioLocation(agency.alertAudioFileUrl);
				setValue('name', agency.name);
				setValue('code', agency.code);
				setValue('email', agency.email);
				// setValue('type', agency.type === undefined ? "" : agency.type);
				// setValue('timezone', parseInt(agency.timeZone));
				setValue('address', agency.address);
				if (agency.county !== undefined) {
					let County = countyListDB.find((x) => x.CountyName === agency.county);
					setCounty(County);
				}
				if (agency.zip !== undefined && agency.zip !== "" && agency.zip !== null) {
					let zipcode;
					zipcode = zipCodeList.find((x) => x.ZipCode === agency.zip);
					if (zipcode === undefined) {
						zipcode = { _id: 'local', ZipCode: agency.zip }
					}
					setZipCode(zipcode);
				}
				let city = cityListDB.find((x) => x.CityName === agency.city);
				setCity(city);
				let State = stateListDB.find((x) => x.StateCode === agency.state);
				setState(State);
				if (agency.billingCounty !== undefined) {
					let BillingCounty = countyListDB.find((x) => x.CountyName === agency.billingCounty);
					setBillingCounty(BillingCounty);
				}
				if (agency.billingZip !== undefined && agency.billingZip !== "" && agency.billingZip !== null) {
					let Billingzipcode;
					Billingzipcode = zipCodeList.find((x) => x.ZipCode === agency.billingZip);
					if (Billingzipcode === undefined) {
						Billingzipcode = { _id: 'local', ZipCode: agency.billingZip }
					}
					setBillingZipCode(Billingzipcode);
				}
				let Billingcity = cityListDB.find((x) => x.CityName === agency.billingCity);
				setBillingCity(Billingcity);
				let BillingState = stateListDB.find((x) => x.StateCode === agency.billingState);
				setBillingState(BillingState);
				setValue('latitude', agency.latitude);
				setValue('longitude', agency.longitude);
				setValue('billingAddress', agency.billingAddress);
				setValue('nibrsOri', agency.nibrsOri !== undefined ? agency.nibrsOri : '');
				setValue('ncicOri', agency.ncicOri !== undefined ? agency.ncicOri : '');
				setValue('fdId', agency.fdId);
				setValue('nemsisId', agency.nemsisId);
				setValue('facilityCode', agency.facilityCode);
				setValue('agencyConfigCode', agency.agencyConfigCode);
				setValue('noOfDeviceLicenses', agency.noOfDeviceLicenses);
				setValue('noOfUserLicenses', agency.noOfUserLicenses);
				setValue('ConnectionString', agency.ConnectionString);
				setValue('ncicApiUrl', agency.ncicApiUrl);
				setDeviceLicenseCode(agency.IsDeviceLicenseCodeRequired)
				setIsOtpRequired(agency.isOtpRequired);
				setShowPersonAlert(agency.showPersonAlert ?? false);
				setDoesNotUseOldRps(agency.doesNotUseOldRps)

				const parentAgencyObj = agencyList && agencyList.find(x => x._id === agency.parentId);
				setParentAgency(parentAgencyObj ?? null);

				const agencyTypeObj = agencyTypes && agencyTypes.find(x => x.name === agency.type);
				setAgencyType(agencyTypeObj);

				debugger;
				const timezoneObj = tzInts && tzInts?.find(x => x?.ID === agency.timeZone) || defaultTimeZone;

				setTimezone(timezoneObj);

				setValue('mugshotAgencyName', agency.mugshotAgencyName !== undefined ? agency.mugshotAgencyName : '');
				setValue('mugshotAgencyURL', agency.mugshotAgencyURL !== undefined ? agency.mugshotAgencyURL : '');
				setValue('mugshotAgencyID', agency.mugshotAgencyID !== undefined ? agency.mugshotAgencyID : '');
				setValue('mugshotInitialCode', agency.mugshotInitialCode !== undefined ? agency.mugshotInitialCode : '');
				setValue('liveStreamHost', agency.liveStreamHost !== undefined ? agency.liveStreamHost : '');
				setValue('liveStreamParserType', agency.liveStreamParserType !== undefined ? agency.liveStreamParserType : '');
				setValue('liveStreamPort', agency.liveStreamPort !== undefined ? agency.liveStreamPort : '');
				setValue('locationParserURL', agency.locationParserURL !== undefined ? agency.locationParserURL : '');
				setMugshotAgencyURL(agency.mugshotAgencyURL !== undefined ? agency.mugshotAgencyURL : '')
				setMugshotInitialCode(agency.mugshotInitialCode !== undefined ? agency.mugshotInitialCode : '')
				setValue('socketListenerUrl', agency.socketListenerUrl);
				setValue('socketApiUrl', agency.socketApiUrl);
				setValue('mobileXAPIPath', agency.mobileApiURL);
				setValue('agencyURL', agency.mobileApiURL);
				setValue('agencyName', agency.agencyName);
				setValue('initialCode', agency.initialCode);
				setVersionValue(agency.dispatchVersion);
				setAudioFileName(agency.alertAudioFileUrl ? agency.alertAudioFileUrl.split("/").pop() : null);
				if (agency.callCategories != undefined) {
					setValue('callCategories', agency.callCategories.map((x) => ({
						value: x.value,
						label: x.label,
						CallCategoryName: x.label,
						CallCategoryID: x.CallCategoryID,
						CallCategoryBranch: x.CallCategoryBranch,
						IconURL: x.IconURL,
					})));
					setCallCategoryValue(agency.callCategories.map((x) => ({
						value: x.value,
						label: x.label,
						CallCategoryName: x.label,
						CallCategoryID: x.CallCategoryID,
						CallCategoryBranch: x.CallCategoryBranch,
						IconURL: x.IconURL,
					})));
				}
			}
			if (agency.mobileXAPIPath !== undefined && agency.mobileXAPIPath !== null && agency.mobileXAPIPath !== '') {
				dispatch(getAgencyServerConfigurations(agency.code));
			}
		}
		// eslint-disable-next-line
	}, [agency, setPageType]);

	useEffect(() => {
		if (parentAgency !== null && parentAgency !== undefined && parentAgency !== '') {
			dispatch(getParentAgencyDetailsByID(parentAgency._id));
		} else {
			dispatch(clearParentAgencyById());
		}
	}, [parentAgency]);

	useEffect(() => {
		clearParentAgencyData();
		if (parentAgency !== null && parentAgency !== undefined && parentAgency !== '') {
			setParentAgencyData();
		}
	}, [parentAgencyData]);

	useEffect(() => {
		if (agencyAdd) {
			// Create Admin user after successfully creates the Agency.
			let x = []
			x.push({
				value: agency._id,
				label: agency.name,
				agencyCode: agency.code,
				RPSUserName: "",
				RPSPassword: "",
				RPSName: "",
				RPSUserGroupName: "",
				RPSUserID: 0,
				RMDUserID: 0,
				doesNotUseOldRps: doesNotUseOldRps
			})
			var user = {
				fname: agency.name,
				lname: "Agency admin",
				email: agency.email,
				password: "Abvira@123",
				agencyID: agency._id,
				agencies: x,
				agencyAdmin: true,
				isSuperAdmin: false,
				defaultAgency: agency.code,
				userAccessRights: agency.agencyLicenses[0],
				defaultApp: "admin",
				isOtpRequired: false,
				showPersonAlert: false,

			}
			dispatch(submitInitialUserRegistration(user));
			// dispatch(removeSavedAgencyDetails());
		}
		// eslint-disable-next-line
	}, [agencyAdd]);

	const navigateBack = () => {
		history.push("/admin/agencyList");
	};

	useEffect(() => {
	}, [accessRights]);

	const handleDeviceLicenseCodeRequired = (event) => {
		setDeviceLicenseCode(event.currentTarget.checked)
	};

	const otpRequiredValueChange = (event) => {
		setIsOtpRequired(event.currentTarget.checked);
	};

	const showPersonAlertValueChange = (event) => {
		setShowPersonAlert(event.currentTarget.checked);
	};

	const ShowErrorMessage = (message) => {
		dispatch(showMessage({
			message: message,
			autoHideDuration: 5000,
			anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
		}));
	}

	//#region Submit Function
	function onSubmit(model) {
		if (
			accessRights["Incident"] &&
			!accessRights["IncidentApp"] &&
			!accessRights["IncidentWebsite"]
		) {
			return ShowErrorMessage(t("selectIncidentAppOrWebsiteAccessRight"));
		}

		// If Dispatch is selected, DispatchApp or DispatchWebsite must be selected
		if (
			accessRights["Dispatch"] &&
			!accessRights["DispatchApp"] &&
			!accessRights["DispatchWebsite"]
		) {
			return ShowErrorMessage(t("selectDispatchAppOrWebsiteAccessRight"));
		}

		const data = {
			_id: "0",
			name: model.name,
			code: model.code,
			type: !isEmptyOrNull(agencyType) ? agencyType.name : null,
			email: model.email,
			address: model.address,
			zip: !isEmptyOrNull(zipCode) ? zipCode.ZipCode : null,
			city: !isEmptyOrNull(city) ? city.CityName : null,
			state: !isEmptyOrNull(state) ? state.StateCode : null,
			county: !isEmptyOrNull(county) ? county.CountyName : null,
			timeZone: !isEmptyOrNull(timezone) ? parseInt(timezone.ID) : null,
			latitude: model.latitude,
			longitude: model.longitude,
			billingAddress: model.billingAddress,
			billingCounty: !isEmptyOrNull(billingcounty) ? billingcounty.CountyName : null,
			billingZip: !isEmptyOrNull(billingzipCode) ? billingzipCode.ZipCode : null,
			billingCity: !isEmptyOrNull(billingcity) ? billingcity.CityName : null,
			billingState: !isEmptyOrNull(billingstate) ? billingstate.StateCode : null,
			nibrsOri: model.nibrsOri,
			ncicOri: model.ncicOri,
			fdId: model.fdId,
			nemsisId: model.nemsisId,
			facilityCode: model.facilityCode,
			agencyConfigCode: model.agencyConfigCode,
			agencyLicenses: accessRights,
			noOfUserLicenses: model.noOfUserLicenses,
			noOfDeviceLicenses: model.noOfDeviceLicenses,
			ConnectionString: model.ConnectionString,
			ncicApiUrl: model.ncicApiUrl,
			IsDeviceLicenseCodeRequired: isDeviceLicenseCode,
			isOtpRequired: isOtpRequired,
			showPersonAlert: showPersonAlert,
			parentId: parentAgency === null ? null : parentAgency._id,
			mugshotInitialCode: mugshotInitialCode,
			mugshotAgencyName: mugshotAgencyName,
			mugshotAgencyURL: mugshotAgencyURL,
			mugshotAgencyID: mugshotAgencyID,

			doesNotUseOldRps: doesNotUseOldRps,
			callCategories: doesNotUseOldRps ? null : callCategoryValue,
			liveStreamParserType: doesNotUseOldRps ? null : model.liveStreamParserType,
			liveStreamHost: doesNotUseOldRps ? null : model.liveStreamHost,
			liveStreamPort: doesNotUseOldRps ? null : model.liveStreamPort,
			locationParserURL: doesNotUseOldRps ? null : model.locationParserURL,
			initialCode: doesNotUseOldRps ? null : model.initialCode,
			mobileApiURL: doesNotUseOldRps ? null : model.agencyURL,
			agencyName: doesNotUseOldRps ? null : model.agencyName,
			alertAudioFileUrl: doesNotUseOldRps ? null : alertAudioLocation,
			dispatchVersion: doesNotUseOldRps ? null : versionValue,

			socketListenerUrl: model.socketListenerUrl,
			socketApiUrl: model.socketApiUrl,
		};
		if (!doesNotUseOldRps) {
			if (versionValue === "") {
				return ShowErrorMessage(t('Please selected Dispatch version'));
			}
		}

		if (!doesNotUseOldRps) {
			if (model.initialCode === "" && mugshotInitialCode === "") {
				return ShowErrorMessage(t('Please enter Dispatch details Or Mugshot details....'));
			}
		}

		if (isEmptyOrNull(county)) {
			return ShowErrorMessage(t('Please select County.'))
		};

		if (agencyID !== "0") {
			data._id = agency._id ?? agencyID;
			dispatch(updateAgency(data));
			return clear();
		};

		if (emailExist) {
			return ShowErrorMessage(t('Email is already registered with us, Please use different email.'));
		};

		if (codeExist) {
			return ShowErrorMessage(t('Agency Code is already registered with us, Please use different code.'));
		};

		if (ConnectionStringExist) {
			return ShowErrorMessage(t('Invalid Database Connection String please enter valid connection string..'));
		};

		dispatch(newAgency(data));
		clear();
	};
	//#endregion

	const getMugshotStreamData = async (URL) => {
		dispatch(setLoading(true));
		try {
			const axiosInstance = axios.create({
				baseURL: `${URL}LocationParserURLV3`,
				timeout: 5000, // Adjust timeout if needed
				headers: {
					'Content-Type': 'application/json',
				},
			});
			const data = await axiosInstance.get();
			if (data.status === 200) {
				dispatch(setLoading(false));
				setValue('liveStreamHost', data.data.liveStreamHost);
				setValue('liveStreamParserType', data.data.liveStreamParserType);
				setValue('liveStreamPort', data.data.liveStreamPort);
				setValue('locationParserURL', data.data.locationParserURL);
			}
			else {
				dispatch(setLoading(false));
				setValue('liveStreamHost', '');
				setValue('liveStreamParserType', '');
				setValue('liveStreamPort', '');
				setValue('locationParserURL', '');
			}
		} catch (error) {
			dispatch(setLoading(false));
		}

	};

	const getAgencyURL = () => {
		const initialCode = getValues('initialCode');
		if (initialCode === '') {
			setValue('agencyURL', '');
			setValue('agencyName', '');
			setValue('liveStreamHost', '');
			setValue('liveStreamParserType', '');
			setValue('liveStreamPort', '');
			setValue('locationParserURL', '');
			dispatch(showMessage({
				message: 'Please enter initial code..', autoHideDuration: 2000,
				anchorOrigin: {
					vertical: 'top',
					horizontal: 'right'
				},
				variant: 'warning'
			}));
		}
		else {
			dispatch(setLoading(true));
			axios.get('admin/api/agencies/GetAgencyDetailsByAgencyCode/' + initialCode)
				.then(response => {
					if (response.status === 200) {
						response.data = JSON.parse(decrypt(response.data));
						dispatch(setLoading(false));
						setValue('agencyName', response.data.AgencyName);
						setValue('agencyURL', response.data.URL);
						setValue('agencyConfigCode', initialCode);
						setValue('mobileXAPIPath', response.data.URL);
						if (response.data.URL) {
							// Live stream related api is calling from here...
							getMugshotStreamData(response.data.URL)
						}
						setValue('initialCode', initialCode);
						setValue('liveStreamHost', '');
						setValue('liveStreamParserType', '');
						setValue('liveStreamPort', '');
						setValue('locationParserURL', '');
						dispatch(showMessage({
							message: 'Agency details found successfully..', autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'success'
						}));
					}
					else {
						dispatch(setLoading(false));
						setValue('agencyName', '');
						setValue('agencyURL', '');
						setValue('agencyConfigCode', '');
						setValue('mobileXAPIPath', '');
						setValue('liveStreamHost', '');
						setValue('liveStreamParserType', '');
						setValue('liveStreamPort', '');
						setValue('locationParserURL', '');
						dispatch(showMessage({
							message: 'Agency details not found..', autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}));
					}
				}).catch(error => {
					dispatch(setLoading(false));
					setValue('agencyName', '');
					setValue('agencyURL', '');
					setValue('agencyConfigCode', '');
					setValue('mobileXAPIPath', '');
					setValue('liveStreamHost', '');
					setValue('liveStreamParserType', '');
					setValue('liveStreamPort', '');
					setValue('locationParserURL', '');
					return dispatch(
						showMessage({
							message: 'Agency details not found..',
							autoHideDuration: 2000,//ms
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}),
					);
				});
		}
	};

	const getMugshotAgencyURL = () => {
		const mugshotInitialCode = getValues('mugshotInitialCode');
		if (mugshotInitialCode === '') {
			setValue('mugshotAgencyName', '');
			setValue('mugshotAgencyURL', '');
			setValue('mugshotAgencyID', '');
			setMugshotAgencyName('');
			setMugshotAgencyURL('');
			setMugshotAgencyID('');
			dispatch(showMessage({
				message: 'Please enter mugshot initial code..', autoHideDuration: 2000,
				anchorOrigin: {
					vertical: 'top',
					horizontal: 'right'
				},
				variant: 'warning'
			}));
		}
		else {
			dispatch(setLoading(true));
			axios.get('admin/api/agencies/GetAgencyMugshotDetailsByMugShotAgencyCode/' + mugshotInitialCode)
				.then(response => {
					if (response.status === 200) {
						response.data = JSON.parse(decrypt(response.data));
						dispatch(setLoading(false));
						setValue('mugshotAgencyName', response.data.AgencyName);
						setValue('mugshotAgencyURL', response.data.URL);
						setValue('mugshotAgencyID', response.data.AgencyID);
						setMugshotAgencyName(response.data.AgencyName);
						setMugshotAgencyURL(response.data.URL);
						setMugshotAgencyID(response.data.AgencyID);
						setMugshotInitialCode(mugshotInitialCode);
						dispatch(showMessage({
							message: 'Mugshot Agency details found successfully..', autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'success'
						}));
					}
					else {
						dispatch(setLoading(false));
						setValue('mugshotAgencyName', '');
						setValue('mugshotAgencyURL', '');
						setValue('mugshotAgencyID', '');
						setMugshotAgencyName('');
						setMugshotAgencyURL('');
						setMugshotAgencyID('');
						dispatch(showMessage({
							message: 'Mugshot Agency details not found..', autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}));
					}
				}).catch(error => {
					dispatch(setLoading(false));
					setValue('mugshotAgencyName', '');
					setValue('mugshotAgencyURL', '');
					setValue('mugshotAgencyID', '');
					setMugshotAgencyName('');
					setMugshotAgencyURL('');
					setMugshotAgencyID('');
					return dispatch(
						showMessage({
							message: 'Mugshot Agency details not found..',
							autoHideDuration: 2000,//ms
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}),
					);
				});
		}
	};

	const handleAgencyAccessRightCallback = (data) => {
		setAccessRights({ ...accessRights, [data.name]: data.cheked });
	};

	const handleParentAgencyChange = async (event, newValue) => {
		await clearParentAgencyData();
		setParentAgency(newValue);
		setIsParentAgencyEdit(true);
	};

	const handleAgencyTypeChange = (event, newValue) => {
		setAgencyType(newValue);
	}

	const handleTimeZoneChange = (event, newValue) => {
		setTimezone(newValue);
	}

	useEffect(() => {
		setLoadingUI(isloadingvalue)
	}, [isloadingvalue]);

	function onEmailChange(e) {
		let email = e.target.value;
		setValue('email', email);
		dispatch(GetUserByEmail(email, users));
	};

	function onCodeChange(e) {
		let code = e.target.value;
		setValue('code', code);
		dispatch(checkAgencyCode(code, agencyList));
	};

	function onConnectionStringChange(e) {
		let ConnectionString = e.target.value;
		setValue('ConnectionString', ConnectionString);
		dispatch(verifyConnectionString(ConnectionString));
	};

	const backButton = (
		<Button
			type="button"
			variant="contained"
			color='secondary'
			className="normal-case m-16"
			aria-label="Back"
			value="legacy"
			onClick={navigateBack}>
			{t('back')}
		</Button>
	);

	const submitButton = (
		<Button
			type="submit"
			variant="contained"
			color="secondary"
			className="normal-case m-16"
			aria-label="REGISTER"
			value="legacy">
			{pageType === "add" ? t('save') : t('update')}
		</Button>
	);

	const agencyNameTextFieldRef = useRef(null);
	const textFieldRef = useRef(null);
	const billingtextFieldRef = useRef(null);

	useEffect(() => {
		if (agencyNameTextFieldRef.current) {
			agencyNameTextFieldRef.current.focus();
		}
	}, [agencyNameTextFieldRef]);

	const handleKeyDown = (event) => {
		if (event.key === 'Tab') {
			event.preventDefault(); // Prevent default tab behavior
			textFieldRef.current.focus(); // Focus the third text field
		}
	};

	const handleBillingKeyDown = (event) => {
		if (event.key === 'Tab') {
			event.preventDefault(); // Prevent default tab behavior
			billingtextFieldRef.current.focus(); // Focus the third text field
		}
	};

	const handleCounty = (event, value) => { setCounty(value) };
	const handleCity = (event, value) => { setCity(value) };
	const handleZipCode = (event, value) => {
		setZipCode(value)
		if (value) {
			//Set data for other fields
			if (value?.CityName) {
				let city = cityList.find((x) => x.CityName === value.CityName);
				setCity(city);
			}

			if (value?.StateCode) {
				let State = stateList.find((x) => x.StateCode === value.StateCode);
				setState(State);
			}

			if (value?.OfficialCountyName) {
				let County = countyList.find((x) => x.CountyName === value.OfficialCountyName);
				setCounty(County);
			}
		} else {
			setCity(null);
			setState(null);
			setCounty(null);
		};
	}

	const handleState = (event, value) => {
		if (value !== null) {
			setState(value);
			//Set city and county list according to state
			cityList = cityListDB && cityListDB.filter((x) => x.StateCode === value.StateCode);
			countyList = countyListDB && countyListDB.filter((x) => x.StateCode === value.StateCode);
		} else {
			setState(null);
			countyList = countyListDB;
			cityList = cityListDB;
		}
	};

	const handleBillingCounty = (event, value) => { setBillingCounty(value) };
	const handleBillingCity = (event, value) => { setBillingCity(value) };
	const handleBillingZipCode = (event, value) => {
		setBillingZipCode(value)
		if (value) {
			//Set data for other fields
			if (value?.CityName) {
				let city = cityListDB && cityListDB.find((x) => x.CityName === value.CityName);
				setBillingCity(city);
			}
			if (value?.StateCode) {
				let State = billingStateList.find((x) => x.StateCode === value.StateCode);
				setBillingState(State);
			}
			if (value?.OfficialCountyName) {
				let County = countyListDB && countyListDB.find((x) => x.CountyName === value.OfficialCountyName);
				setBillingCounty(County);
			}
		} else {
			setBillingCity(null);
			setBillingState(null);
			setBillingCounty(null);
		}
	};

	const handleBillingState = (event, value) => {
		if (value !== null) {
			setBillingState(value);
			//Set city and county list according to state
			billingCityList = billingCityList.filter((x) => x.StateCode === value.StateCode);
			billingCountyList = billingCountyList.filter((x) => x.StateCode === value.StateCode);
		} else {
			setBillingState(null);
			billingCountyList = countyListDB;
			billingCityList = cityListDB;
		}
	};

	useEffect(() => {
		countyList = countyListDB;
		cityList = cityListDB;
		stateList = stateListDB;
		billingCountyList = countyListDB;
		billingCityList = cityListDB;
		billingStateList = stateListDB;
	}, [countyListDB, cityListDB, stateListDB]);

	const handleFileChange = (event) => {
		const file = event.target.files[0];
		if (file) {
			var uid = (new Date().getTime()).toString(36);
			const data = {
				file: file,
				_id: uid
			}
			setAudioFileName(file.name);
			dispatch(addAudioFile(data));
		}
	};

	const handleVersionChange = (event) => {
		setVersionValue(event.target.value);
	};



	const handleDoesNotUseOldRps = (event) => {
		setDoesNotUseOldRps(event.currentTarget.checked)
	}

	useEffect(() => {
		if (doesNotUseOldRps) {
			setAccessRights(prev => ({
				...prev,
				Dispatch: false,
				Chat: false,
				Current: false,
				Replay: false,
				SearchFeature: false,
				Watches: false,
				BroadcastMessages: false,
				MySchedule: false,
			}));

		}
	}, [doesNotUseOldRps])


	return (
		<div className="p-16">
			{loading && < CircularProgressLoader loading={loading} />}
			<Card className=" m-16 rounded-8 shadow">
				<form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)} autoSave={false} autoComplete={false}>
					<AppHeaderBar headerText={`${t(pageType)} ${t('agency')}`}
						submitButton={submitButton}
						backButton={backButton}
					/>
					<CardContent style={{ overflowX: "scroll", height: "90%" }}>
						<div className="w-full p-16">
							<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
								<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("agencyDetails")}</legend>
								<div className={`grid gap-6 ${pageType === "add" ? "grid-cols-1 sm:grid-cols-3" : "grid-cols-1"}`}>
									<Controller
										name="name"
										control={control}
										render={({ field }) => (
											<TextField
												{...field}
												className="mb-16 mr-4 w-full"
												label={t('name')}
												type="text"
												inputRef={agencyNameTextFieldRef}
												error={!!errors.name}
												helperText={errors?.name?.message}
												variant="outlined"
												required
												fullWidth
												sx={{ mb: 2, mr: 4 }}
											/>
										)}
									/>

									{pageType === "add" &&
										<>
											<Controller
												name="code"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16 mr-4 w-full"
														label={t('code')}
														type="text"
														onChange={onCodeChange}
														error={!!errors.code}
														helperText={errors?.code?.message}
														variant="outlined"
														required
														sx={{ mb: 2, mr: 4 }}
													/>
												)}
											/>
											<Controller
												name="email"
												type="text"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16 w-full"
														label={t('email')}
														type="text"
														error={!!errors.email}
														helperText={errors?.email?.message}
														onChange={onEmailChange}
														variant="outlined"
														required
													/>
												)}
											/>
										</>
									}
								</div>
								<Grid container spacing={1} >
									<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
										<FormControl fullWidth sx={{ mb: 2, minWidth: 120 }}>
											<CommonAutocomplete
												value={parentAgency || null}
												parentCallback={handleParentAgencyChange}
												options={agencyList || []}
												fieldName={t("parentAgency")}
												optionLabel={"name"}
												onKeyDown={handleSelectKeyDown}
												disabled={isEditmode}
											/>
										</FormControl>
									</Grid>

									<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
										<FormControl className="mb-16  w-full" sx={{}}>
											<CommonAutocomplete
												value={agencyType || null}
												parentCallback={handleAgencyTypeChange}
												options={agencyTypes || []}
												fieldName={t("type")}
												optionLabel={"name"}
												onKeyDown={handleSelectKeyDown}
											/>
										</FormControl>
									</Grid>
									<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
										<FormGroup className='col-span-1'>
											<FormControlLabel
												control={<Checkbox checked={doesNotUseOldRps}
													onChange={handleDoesNotUseOldRps} name="doesNotUseOldRps" />}
												label={t('doesNotUseOldRps')}
											/>
										</FormGroup>
									</Grid>
								</Grid>
							</Box>

							<Grid container spacing={1} >
								<Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
									<FormControl className="mb-16 w-full">
										<CommonAutocomplete
											value={timezone || null}
											parentCallback={handleTimeZoneChange}
											options={tzInts || []}
											fieldName={t("timeZone")}
											optionLabel={"label"}
											onKeyDown={handleSelectKeyDown}
										/>
									</FormControl>
								</Grid>
							</Grid>

							<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
								<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("physicalAddressDetails")}</legend>
								<Controller
									name="address"
									control={control}
									render={({ field }) => (
										<TextField
											{...field}
											className="mb-16 w-full"
											label={t('physicalAddress')}
											type="text"
											error={!!errors.address}
											helperText={errors?.address?.message}
											variant="outlined"
											onKeyDown={handleKeyDown}
										// required
										/>
									)}
								/>

								<Grid container spacing={1} >
									<Grid item xs={12} sm={12} md={3} lg={3} xl={3} className="mb-16">
										<ErrorBoundary
											FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_CITY" />} onReset={() => { }}>
											<CommonAutocomplete parentCallback={handleCity} options={cityList}
												value={city} fieldName={t("city")} optionLabel={"CityName"}
												onKeyDown={handleSelectKeyDown} />
										</ErrorBoundary>
									</Grid>
									<Grid item xs={12} sm={12} md={3} lg={3} xl={3} className="mb-16">
										<ErrorBoundary
											FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_STATE" />} onReset={() => { }}>
											<CommonAutocomplete parentCallback={handleState} options={stateList}
												value={state} fieldName={t("state")} optionLabel={"StateCode"}
												onKeyDown={handleSelectKeyDown} />
										</ErrorBoundary>
									</Grid>
									<Grid item xs={4} sm={4} md={3} lg={3} xl={3} className="mb-16">
										<ErrorBoundary
											FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_county" />} onReset={() => { }}>
											<CommonAutocomplete parentCallback={handleCounty} options={countyList}
												value={county} fieldName={t("county")} optionLabel={"CountyName"}
												getOptionLabelFn={(option) => `${option.CountyName} - ${option.StateCode}`}
												onKeyDown={handleSelectKeyDown} />
										</ErrorBoundary>
									</Grid>

									<Grid item xs={12} sm={12} md={3} lg={3} xl={3} className="mb-16">
										<ErrorBoundary
											FallbackComponent={(props) => <ErrorPage {...props} componentName="FreeFormAutocomplete_zip" />} onReset={() => { }}>
											<FreeFormAutocomplete
												parentCallback={handleZipCode}
												options={zipCodeList}
												value={zipCode}
												fieldName={t("zip")}
												optionLabel={"ZipCode"}
												onKeyDown={handleSelectKeyDownFreeForm}
											/>
										</ErrorBoundary>
									</Grid>
								</Grid>

								<Grid container spacing={1} >
									<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
										<Controller
											name="latitude"
											control={control}
											render={({ field }) => (
												<TextField
													{...field}
													className="mb-16 w-full"
													label={t('latitude')}
													type="text"
													error={!!errors.latitude}
													helperText={errors?.latitude?.message}
													variant="outlined"
												// required
												/>
											)}
										/>

									</Grid>
									<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
										<Controller
											name="longitude"
											control={control}
											render={({ field }) => (
												<TextField
													{...field}
													className="mb-16 w-full"
													label={t('longitude')}
													type="text"
													error={!!errors.longitude}
													helperText={errors?.longitude?.message}
													variant="outlined"
												// required
												/>
											)}
										/>
									</Grid>
								</Grid>
							</Box>

							<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
								<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("billingAddressDetails")}</legend>
								<Controller
									name="billingAddress"
									control={control}
									render={({ field }) => (
										<TextField
											{...field}
											className="mb-16 w-full"
											label={t('billingAddress')}
											type="text"
											error={!!errors.billingAddress}
											helperText={errors?.billingAddress?.message}
											variant="outlined"
											// required
											onKeyDown={handleBillingKeyDown}
										/>
									)}
								/>

								<Grid container spacing={1} >
									<Grid item xs={12} sm={12} md={3} lg={3} xl={3} className="mb-16">
										<ErrorBoundary
											FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_BillingCITY" />} onReset={() => { }}>
											<CommonAutocomplete parentCallback={handleBillingCity} options={cityList}
												value={billingcity} fieldName={t("billingCity")} optionLabel={"CityName"}
												onKeyDown={handleSelectKeyDown} />
										</ErrorBoundary>
									</Grid>
									<Grid item xs={12} sm={12} md={3} lg={3} xl={3} className="mb-16">
										<ErrorBoundary
											FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_BillingSTATE" />} onReset={() => { }}>
											<CommonAutocomplete parentCallback={handleBillingState} options={stateList}
												value={billingstate} fieldName={t("billingState")} optionLabel={"StateCode"}
												onKeyDown={handleSelectKeyDown} />
										</ErrorBoundary>
									</Grid>
									<Grid item xs={4} sm={4} md={3} lg={3} xl={3} className="mb-16">
										<ErrorBoundary
											FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_Billingcounty" />} onReset={() => { }}>
											<CommonAutocomplete parentCallback={handleBillingCounty} options={countyList}
												value={billingcounty} fieldName={t("billingCounty")} optionLabel={"CountyName"}
												getOptionLabelFn={(option) => `${option.CountyName} - ${option.StateCode}`}
												onKeyDown={handleSelectKeyDown} />
										</ErrorBoundary>
									</Grid>
									<Grid item xs={12} sm={12} md={3} lg={3} xl={3} className="mb-16">
										<ErrorBoundary
											FallbackComponent={(props) => <ErrorPage {...props} componentName="FreeFormAutocomplete_BillingZIP" />} onReset={() => { }}>
											<FreeFormAutocomplete
												parentCallback={handleBillingZipCode}
												options={zipCodeList}
												value={billingzipCode}
												fieldName={t("billingZip")}
												optionLabel={"ZipCode"}
												onKeyDown={handleSelectKeyDownFreeForm}
											/>
										</ErrorBoundary>
									</Grid>
								</Grid>
							</Box>

							{!doesNotUseOldRps &&
								<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
									<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("codeIDDetails")}</legend>
									<Grid container spacing={1} >
										<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
											<Controller
												name="nibrsOri"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16 w-full"
														label={t("nibrsOri")}
														type="text"
														variant="outlined"
													/>
												)}
											/>
										</Grid>
										<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
											<Controller
												name="ncicOri"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16 w-full"
														label={t("ncicOri")}
														type="text"
														variant="outlined"
													/>
												)}
											/>
										</Grid>
									</Grid>

									<Grid container spacing={1} >
										<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
											<Controller
												name="fdId"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16  w-full"
														label={t('fdID')}
														type="text"
														error={!!errors.fdId}
														helperText={errors?.fdId?.message}
														variant="outlined"
													// required
													/>
												)}
											/>
										</Grid>
										<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
											<Controller
												name="nemsisId"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16  w-full"
														label={t('nemsisID')}
														type="text"
														error={!!errors.nemsisId}
														helperText={errors?.nemsisId?.message}
														variant="outlined"
													// required
													/>
												)}
											/>
										</Grid>
										<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
											<Controller
												name="facilityCode"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16  w-full"
														label={t('facilityCode')}
														type="text"
														error={!!errors.facilityCode}
														helperText={errors?.facilityCode?.message}
														variant="outlined"
													// required
													/>
												)}
											/>
										</Grid>
									</Grid>
								</Box>
							}

							{!doesNotUseOldRps &&
								<>
									<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
										<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("soundAlertSetting")}</legend>
										<Grid container spacing={1} >
											<Grid item xs={12} sm={12} md={3.7} lg={3.7} xl={3.7}>
												<FileUploadComponent
													accept="audio/*"
													onChange={handleFileChange}
													filePath={alertAudioLocation}
												/>
											</Grid>
											<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
												<div style={{ display: "flex", alignItems: "center", height: "100%", marginLeft: "10px" }}>
													<span style={{ fontStyle: 'italic', fontSize: '16px' }}>{audioFileName ? audioFileName : t("noFileSelectMsg")}</span>
												</div>
											</Grid>
										</Grid>
									</Box>

									<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
										<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t('dispatchDetails')}</legend>
										<Controller
											name="initialCode"
											control={control}
											render={({ field, fieldState: { isDirty } }) => {
												// Field is disabled if parentAgency is set or if it's pre-populated and not edited.
												const isDisabled =
													(parentAgency !== null && parentAgency !== undefined) || (!isDirty && !!field.value);

												return (
													<TextField
														{...field}
														className="mb-16 w-full"
														label={t('initialCode')}
														type="text"
														error={!!errors.initialCode}
														helperText={errors?.initialCode?.message}
														variant="outlined"
														InputProps={{
															className: 'pr-2',
															// Only show the icon when the field is not disabled
															endAdornment: (
																<InputAdornment position="end">
																	{isEditmode ? (
																		// Show refresh icon in edit mode only if there's NO parent agency
																		(parentAgency === null || parentAgency === undefined) && (
																			<IconButton onClick={getAgencyURL} size="large">
																				<Icon className="text-20" color="action">refresh</Icon>
																			</IconButton>
																		)
																	) : (
																		// Show search icon in add mode (only if not disabled)
																		!isDisabled && (
																			<IconButton onClick={getAgencyURL} size="large">
																				<Icon className="text-20" color="action">search</Icon>
																			</IconButton>
																		)
																	)}
																</InputAdornment>
															)
														}}
														disabled={isDisabled}
													/>
												);
											}}
										/>



										<Grid container spacing={1} >
											<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
												<Controller
													name="agencyConfigCode"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															label={t('agencyConfigCode')}
															type="text"
															error={!!errors.agencyConfigCode}
															helperText={errors?.agencyConfigCode?.message}
															variant="outlined"
															// disabled={parentAgency !== ''}
															disabled
														/>
													)}
												/>

											</Grid>
											<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
												<Controller
													name="mobileXAPIPath"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															label={t('mobileXAPIPath')}
															type="text"
															error={!!errors.mobileXAPIPath}
															helperText={errors?.mobileXAPIPath?.message}
															variant="outlined"
															disabled
														/>
													)}
												/>

											</Grid>
										</Grid>

										<Grid container spacing={1} >
											<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
												<Controller
													name="agencyName"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															label={t('agencyName')}
															type="text"
															error={!!errors.agencyName}
															helperText={errors?.agencyName?.message}
															variant="outlined"
															disabled
														/>
													)}
												/>
											</Grid>
											<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
												<Controller
													name="agencyURL"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															label={t('url')}
															type="text"
															error={!!errors.agencyURL}
															helperText={errors?.agencyURL?.message}
															variant="outlined"
															disabled
														/>
													)}
												/>
											</Grid>
										</Grid>

										<Grid container spacing={1}>
											<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
												<Controller
													name="liveStreamHost"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															label={t('liveStreamHost')}
															type="text"
															error={!!errors.liveStreamHost}
															helperText={errors?.liveStreamHost?.message}
															variant="outlined"
															disabled
														/>
													)}
												/>
											</Grid>
											<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
												<Controller
													name="liveStreamParserType"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															label={t('liveStreamParserType')}
															type="text"
															error={!!errors.liveStreamParserType}
															helperText={errors?.liveStreamParserType?.message}
															variant="outlined"
															disabled
														/>
													)}
												/>
											</Grid>
										</Grid>

										<Grid container spacing={1}>
											<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
												<Controller
													name="liveStreamPort"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															label={t('liveStreamPort')}
															type="text"
															error={!!errors.liveStreamPort}
															helperText={errors?.liveStreamPort?.message}
															variant="outlined"
															disabled
														/>
													)}
												/>
											</Grid>
											<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
												<Controller
													name="locationParserURL"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															label={t('locationParserURL')}
															type="text"
															error={!!errors.locationParserURL}
															helperText={errors?.locationParserURL?.message}
															variant="outlined"
															disabled
														/>
													)}
												/>
											</Grid>
										</Grid>
									</Box>
								</>
							}

							{!doesNotUseOldRps &&
								<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
									<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t('mugshotDetails')}</legend>
									<Controller
										name="mugshotInitialCode"
										control={control}
										render={({ field }) => (
											<TextField
												{...field}
												className="mb-16 w-full"
												label={t('initialCode')}
												type="text"
												error={!!errors.mugshotInitialCode}
												helperText={errors?.mugshotInitialCode?.message}
												variant="outlined"
												InputProps={{
													className: 'pr-2',
													endAdornment: (
														<InputAdornment position="end">
															<IconButton onClick={() => getMugshotAgencyURL()} size="large">
																<Icon className="text-20" color="action">
																	search
																</Icon>
															</IconButton>
														</InputAdornment>
													)
												}}
												disabled={(parentAgency !== null && parentAgency !== undefined)}
											/>
										)}
									/>

									<Grid container spacing={1} >
										<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
											<Controller
												name="mugshotAgencyName"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16 w-full"
														label={t('mugshotAgencyName')}
														type="text"
														error={!!errors.mugshotAgencyName}
														helperText={errors?.mugshotAgencyName?.message}
														variant="outlined"
														disabled
													/>
												)}
											/>
										</Grid>
										<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
											<Controller
												name="mugshotAgencyURL"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16 w-full"
														label={t('mugshotURL')}
														type="text"
														error={!!errors.mugshotAgencyURL}
														helperText={errors?.mugshotAgencyURL?.message}
														variant="outlined"
														disabled
													/>
												)}
											/>
										</Grid>
										<Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
											<Controller
												name="mugshotAgencyID"
												control={control}
												render={({ field }) => (
													<TextField
														{...field}
														className="mb-16 w-full"
														label={t('mugshotAgencyID')}
														type="text"
														error={!!errors.mugshotAgencyID}
														helperText={errors?.mugshotAgencyID?.message}
														variant="outlined"
														disabled
													/>
												)}
											/>
										</Grid>
									</Grid>
								</Box>
							}

							<Grid container spacing={1} >
								<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
									<Controller
										name="noOfUserLicenses"
										control={control}
										render={({ field }) => (
											<TextField
												{...field}
												className="mb-16 w-full"
												label={t('noOfUserLicenses')}
												type="text"
												error={!!errors.noOfUserLicenses}
												helperText={errors?.noOfUserLicenses?.message}
												variant="outlined"
												required
											/>
										)}
									/>
								</Grid>
								<Grid item xs={12} sm={12} md={12} lg={6} xl={6}>
									<Controller
										name="noOfDeviceLicenses"
										control={control}
										render={({ field }) => (
											<TextField
												{...field}
												className="mb-16 w-full"
												label={t('noOfDeviceLicenses')}
												type="text"
												error={!!errors.noOfDeviceLicenses}
												helperText={errors?.noOfDeviceLicenses?.message}
												variant="outlined"
												required
											/>
										)}
									/>
								</Grid>
							</Grid>

							<Grid container spacing={1} >
								<Grid item xs={12} sm={12} md={12} lg={2} xl={2} style={{ paddingBottom: '10px' }}>
									<FormGroup className='col-span-1'>
										<FormControlLabel
											control={<Checkbox checked={isDeviceLicenseCode}
												onChange={handleDeviceLicenseCodeRequired} name="IsDeviceLicenseCodeRequired" />}
											label={t('deviceLicenseRequired')}
										/>
									</FormGroup>
								</Grid>

								<Grid item xs={12} sm={12} md={12} lg={2} xl={2}>
									<FormControl component="fieldset" style={{ paddingBottom: '10px' }}>
										<FormControlLabel
											control={<Checkbox checked={isOtpRequired}
												onChange={otpRequiredValueChange} name="IsOtpRequired" />}
											label={t('isOtpRequired')}
										/>
									</FormControl>
								</Grid>
								<Grid item xs={12} sm={12} md={12} lg={2} xl={2}>
									<FormControl component="fieldset" style={{ paddingBottom: '10px' }}>
										<FormControlLabel
											control={<Checkbox checked={showPersonAlert}
												onChange={showPersonAlertValueChange} name="showPersonAlert" />}
											label={t('showPersonAlert')}
										/>
									</FormControl>
								</Grid>
							</Grid>

							{!doesNotUseOldRps &&
								<>
									{(parentAgency === null || parentAgency === undefined) &&
										<Grid container spacing={1} style={{ paddingBottom: '10px' }}>
											<Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ paddingBottom: '10px' }}>
												<FormControl className="mr-8" fullWidth>
													<Controller
														control={control}
														name='callCategories'
														defaultValue={[]}
														render={({ field: { onChange, value } }) => (
															<Autocomplete
																multiple
																freeSolo
																options={callCategoryType}
																value={value}
																onChange={(event, newValue) => {
																	onChange(newValue);
																	setCallCategoryValue(newValue);
																}}
																renderInput={(params) => (
																	<TextField
																		{...params}
																		placeholder={t("selectCallCategories")}
																		label={t("selectCallCategories")}
																		variant="outlined"
																		InputLabelProps={{
																			shrink: true,
																		}}
																	/>
																)}
															/>)
														}
													/>
												</FormControl>
											</Grid>
										</Grid>
									}
								</>
							}

							<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
								<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("urlDetails")}</legend>
								<Controller
									name="ConnectionString"
									control={control}
									render={({ field }) => (
										<TextField
											{...field}
											className="mb-16 w-full"
											label={t('connectionString')}
											onChange={onConnectionStringChange}
											type="text"
											error={!!errors.ConnectionString}
											helperText={errors?.ConnectionString?.message}
											variant="outlined"
											required
										/>
									)}
								/>


								<Grid container spacing={1}>
									<Grid item xs={12} sm={12} md={12} lg={4} xl={4}>
										<Controller
											name="ncicApiUrl"
											control={control}
											render={({ field }) => (
												<TextField
													{...field}
													className="mb-16 w-full"
													label={t('ncicApiUrl')}
													type="text"
													error={!!errors.ncicApiUrl}
													helperText={errors?.ncicApiUrl?.message}
													variant="outlined"
													//required
													disabled={parentAgency !== null && parentAgency !== undefined}
												/>
											)}
										/>
									</Grid>
									<Grid item xs={12} sm={12} md={12} lg={4} xl={4}>
										<Controller
											name="socketListenerUrl"
											control={control}
											render={({ field }) => (
												<TextField
													{...field}
													className="mb-16 w-full"
													label={t('socketListenerUrl')}
													type="text"
													error={!!errors.socketListenerUrl}
													helperText={errors?.socketListenerUrl?.message}
													variant="outlined"
													required
													disabled={parentAgency !== null && parentAgency !== undefined}
												/>
											)}
										/>

									</Grid>
									<Grid item xs={12} sm={12} md={12} lg={4} xl={4}>
										<Controller
											name="socketApiUrl"
											control={control}
											render={({ field }) => (
												<TextField
													{...field}
													className="mb-16 w-full"
													label={t('socketApiUrl')}
													type="text"
													error={!!errors.socketApiUrl}
													helperText={errors?.socketApiUrl?.message}
													variant="outlined"
													required
													disabled={parentAgency !== null && parentAgency !== undefined}
												/>
											)}
										/>
									</Grid>
								</Grid>
							</Box>

							{!doesNotUseOldRps &&
								<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
									<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("dispatchVersion")}</legend>
									<Grid container spacing={1} >
										<Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
											<RadioGroup
												row aria-label="gender"
												name="availability"
												value={versionValue}
												onChange={handleVersionChange}
											>
												<FormControlLabel value="socketVersion" control={<Radio />} label={t("socketVersion")} />
												<FormControlLabel value="pollingVersion" control={<Radio />} label={t("pollingVersion")} />
											</RadioGroup>
										</Grid>
									</Grid>
								</Box>
							}

							{/* Access Right section */}
							<Card>
								<Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
									{t('agencyLicenses')}:
								</Typography>

								{isAceesRightShow &&
									<ErrorBoundary
										FallbackComponent={(props) => <ErrorPage {...props} componentName="AccessRightsSettings" />} onReset={() => { }}>
										<AccessRightsSettings
											accessRights={accessRights}
											parentAgencyAceessRightCallback={handleAgencyAccessRightCallback}
											showAccessRight={showAccessRight}
											defaultApp={"Agency"}
											doesNotUseOldRps={doesNotUseOldRps}
											isSuperAdmin={parentAgency !== null && parentAgency !== undefined ? false : true}
										>
										</AccessRightsSettings>
									</ErrorBoundary>
								}
							</Card>

							<div className=" mx-auto" style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
								{submitButton}
								{backButton}
							</div>
						</div>
					</CardContent>
				</form>
			</Card>
		</div>
	);
}

export default AgencyAddPage;
