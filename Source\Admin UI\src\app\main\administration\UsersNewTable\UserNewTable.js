import React, { useRef, useEffect, useState, useMemo } from 'react';
import './UserNewTable.css';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import PropTypes from 'prop-types';
import RegularUsersTab from './RegularUsersTab';
import SuperAdminUsersTab from './SuperAdminUsersTab';
import { useDispatch, useSelector } from "react-redux";
import { newUserAudit } from '../../userAuditPage/store/userAuditSlice';
import { useTranslation } from 'react-i18next';
import { setFilterTabButtonValue } from '../../agencyPage/store/agencySlice';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';

function UserNewTable(props) {
    const user = useSelector(({ auth }) => auth.user);
    const filterTabValue = useSelector(({ agency }) => agency.agency.filterTabValue);
    const [value, setValue] = React.useState(filterTabValue);
    const dispatch = useDispatch();
    const { t } = useTranslation('laguageConfig');

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access Users",
                user: user,
                appName: "Admin",
            })
        );
    }, []);

    function a11yProps(index) {
        return {
            id: `simple-tab-${index}`,
            'aria-controls': `simple-tabpanel-${index}`,
        };
    }

    const handleChange = (event, newValue) => {
        setValue(newValue);
        dispatch(setFilterTabButtonValue(newValue))
        props.handlePagination(newValue)
    };
    function TabPanel(props) {
        const { children, value, index, ...other } = props;
        return (
            <div
                role="tabpanel"
                hidden={value !== index}
                id={`simple-tabpanel-${index}`}
                aria-labelledby={`simple-tab-${index}`}
                {...other}
            >
                {value === index && (
                    <Box sx={{ p: 3 }}>
                        <Typography>{children}</Typography>
                    </Box>
                )}
            </div>
        );
    }

    TabPanel.propTypes = {
        children: PropTypes.node,
        index: PropTypes.number.isRequired,
        value: PropTypes.number.isRequired,
    };

    return (
        <div className="w-full flex flex-col">
            {user.data.isSuperAdmin === false ?
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
                        <Tab label={t("regularUsers")} {...a11yProps(0)} />
                    </Tabs>
                    <TabPanel value={value} index={0}>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="RegularUsersTab" />} onReset={() => { }} >
                            <RegularUsersTab searchText={props.searchText} value={value} flag={true} selectedAgency={props.selectedAgency} />
                        </ErrorBoundary>
                    </TabPanel>
                </Box>
                :
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
                        <Tab label={t("regularUsers")} {...a11yProps(0)} />
                        <Tab label={t("superAdminUsers")} {...a11yProps(1)} />
                    </Tabs>
                    <TabPanel value={value} index={0}>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="RegularUsersTab" />} onReset={() => { }} >
                            <RegularUsersTab searchText={props.searchText} value={value} flag={true} selectedAgency={props.selectedAgency} />
                        </ErrorBoundary>
                    </TabPanel>
                    <TabPanel value={value} index={1}>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="SuperAdminUsersTab" />} onReset={() => { }} >
                            <SuperAdminUsersTab searchText={props.searchText} value={value} flag={false} isColumnHidden={false} selectedAgency={props.selectedAgency} />
                        </ErrorBoundary>
                    </TabPanel>
                </Box>
            }
        </div>
    )
}

export default UserNewTable;