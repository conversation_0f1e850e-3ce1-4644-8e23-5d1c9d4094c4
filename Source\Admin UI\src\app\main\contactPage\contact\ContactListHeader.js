import { motion } from 'framer-motion';
import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import Input from '@mui/material/Input';
import Paper from '@mui/material/Paper';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import history from '@history';
import { setContactID, setUsersSearchText } from '../store/contactSlice';
import { selectMainTheme } from 'app/store/fuse/settingsSlice';
import { Link } from '@mui/material';
import { useParams } from "react-router";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import CommonButton from '../../SharedComponents/ReuseComponents/CommonButton';

function ContactListHeader(props) {
	const dispatch = useDispatch();
	const routeParams = useParams();
	const searchText = useSelector(({ contact }) => contact.contact.searchText);
	const mainTheme = useSelector(selectMainTheme);
	const { t } = useTranslation('laguageConfig');

	const addContact = () => {
		dispatch(setContactID("0"));
		history.push(`/contactAdd/${routeParams.code}`);
	};

	return (
		<div style={{ width: "100%" }}>
			<div>
				{routeParams.code !== "list" && (
					<div className="flex flex-1 items-center justify-between">
						<div className="flex items-center">
							<Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
								<Stack direction="row" spacing={2}>
									<Button
										className="backButton"
										variant="contained"
										startIcon={<ArrowBackOutlinedIcon />}
										onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
									>
										{t('back')}
									</Button>
								</Stack>
							</Tooltip>
						</div>
					</div>
				)}
			</div>
			<div className="flex flex-1 p-24 w-full items-center justify-between">

				<div className="flex items-center">
					<Icon
						component={motion.span}
						initial={{ scale: 0 }}
						animate={{ scale: 1, transition: { delay: 0.2 } }}
						className="text-32">contacts</Icon>
					<Typography
						component={motion.span}
						initial={{ x: -20 }}
						animate={{ x: 0, transition: { delay: 0.2 } }}
						delay={300}
						className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
						{t('contact')}
					</Typography>
				</div>

				<div className="flex flex-1 items-center justify-center px-12">
					<StyledEngineProvider injectFirst>
						<ThemeProvider theme={mainTheme}>
							<Paper
								sx={{background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main}}
								component={motion.div}
								initial={{ y: -20, opacity: 0 }}
								animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
								className="flex items-center w-full max-w-512 px-8 py-4 rounded-8" elevation={1}>
								<Icon color="action">search</Icon>

								<Input
									placeholder={t('search')}
									className="flex flex-1 mx-8"
									disableUnderline
									fullWidth
									value={searchText}
									inputProps={{
										'aria-label': 'Search'
									}}
									onChange={ev => dispatch(setUsersSearchText(ev))}
								/>
							</Paper>
						</ThemeProvider>
					</StyledEngineProvider>
				</div>
				<motion.div
					initial={{ opacity: 0, y: 40 }}
					animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
				>
					<ErrorBoundary
						FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addNewContact" />} onReset={() => window.location.reload()} >
						<CommonButton styleClass="whitespace-no-wrap normal-case" btnName={t("addNewContact")} parentCallback={addContact}></CommonButton>
					</ErrorBoundary>
				</motion.div>
			</div>
		</div>

	);
}

export default ContactListHeader;
