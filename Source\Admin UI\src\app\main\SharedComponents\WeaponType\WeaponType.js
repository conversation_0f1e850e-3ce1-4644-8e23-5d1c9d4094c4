import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../ErrorPage/ErrorPage";
import CommonAutocomplete, { handleSelectKeyDown } from "../ReuseComponents/CommonAutocomplete";
import { useTranslation } from "react-i18next";

function WeaponType(props) {
    const { t } = useTranslation("laguageConfig");

    const handleCallBack = (event, value) => { props.handleCallBack(event, value) };

    return (
        <ErrorBoundary
            FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_weaponType" />} onReset={() => { }}>
            <CommonAutocomplete
                disabled={props.disabled}
                parentCallback={handleCallBack}
                options={props.weaponList}
                value={props.value}
                fieldName={t("weaponType")}
                optionLabel={"name"}
                onKeyDown={handleSelectKeyDown} />
        </ErrorBoundary>
    );
}

export default WeaponType;