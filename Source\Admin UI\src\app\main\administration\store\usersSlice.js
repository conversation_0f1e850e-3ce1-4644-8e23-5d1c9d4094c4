import { createSlice } from '@reduxjs/toolkit';
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';
import { showMessage } from 'app/store/fuse/messageSlice';
import { verifyUserSession } from 'src/app/auth/store/loginSlice';

export const importUsers = (data, defaultAgency, defaultApp) => async (dispatch) => {
	try {
		dispatch(setLoading(true));
		await axios.post(`admin/api/users/importUsers/${defaultAgency}/${defaultApp}`, encrypt(JSON.stringify(data)))
			.then(async (response) => {
				if (response.status == 200) {
					response.data = await JSON.parse(decrypt(response.data));
					dispatch(
						showMessage({
							message: response.data.message,
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: "top",
								horizontal: "right",
							},
							variant: "success",
						})
					);
					dispatch(setLoading(false));
					dispatch(getUsers("fname", "asc", 0 * 100, 100, true, "ALL"))
					dispatch(setImportUserResponse(response.data.data));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
					dispatch(setLoading(false));
				}
			}).catch(error => {
				return console.error(error);
			});
	}
	catch (e) {
		return console.error(e.message);
	}
};

export const getRegularUsers = (sortField, sortDirection, pageIndex, pageLimit, selectedAgency, searchText) => async dispatch => {
	try {
		dispatch(setLoading(true));
		axios.get(`admin/api/users/getRegularUsers/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${selectedAgency}/${searchText}`)
			.then(response => {
				if (response.status == 200) {
					dispatch(setLoading(false));
					response.data = JSON.parse(decrypt(response.data))
					dispatch(setUserTotalCount(response.data.totalCount));
					return dispatch(setRegularUsers(response.data.users));

				}
				else {
					// response.data = JSON.parse(decrypt(response.data));
					dispatch(setLoading(false));
					// return dispatch(showMessage({
					// 	message: response.data.message,
					// 	autoHideDuration: 2000,
					// 	anchorOrigin: {
					// 		vertical: 'top',
					// 		horizontal: 'right'
					// 	},
					// 	variant: 'warning'

					// }))
				}
			});
	}
	catch (error) {
		dispatch(setLoading(false));
		return dispatch(showMessage({
			message: error.message,
			autoHideDuration: 2000,
			anchorOrigin: {
				vertical: 'top',
				horizontal: 'right'
			},
			variant: 'warning'

		}))
	}
}

export const changesUserAgency = (data, pagingDetails) => async dispatch => {
	try {
		dispatch(setLoading(true));
		await axios.post(`admin/api/users/changesUserAgency`, encrypt(JSON.stringify(data)))
			.then(response => {
				if (response.status === 200) {
					response = JSON.parse(decrypt(response.data));
					dispatch(setLoading(false));
					if (response.isSuccess) {
						dispatch(showMessage({
							message: response.message,
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'success'
						}))
					}
					dispatch(getRegularUsers(
						"fname", "asc",
						pagingDetails.pageIndex, pagingDetails.pageLimit,
						pagingDetails.selectedAgency, pagingDetails.searchText,
					));
				}
			});
	} catch (e) {
		dispatch(setLoading(false));
		return console.error(e.message);
	}
};

export const getSuperAdminUsers = (sortField, sortDirection, pageIndex, pageLimit, selectedAgency, searchText) => async dispatch => {
	try {
		dispatch(setLoading(true));
		axios.get(`admin/api/users/getSuperAdminUsers/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${selectedAgency}/${searchText}`)
			.then(response => {
				if (response.status == 200) {
					dispatch(setLoading(false));
					response.data = JSON.parse(decrypt(response.data))
					dispatch(setSuparAdminUserTotalCount(response.data.totalCount));
					return dispatch(setSuperAdminUsers(response.data.users));
				}
				else {
					response.data = JSON.parse(decrypt(response.data));
					return dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			});
	}
	catch (error) {
		return dispatch(showMessage({
			message: error.message,
			autoHideDuration: 2000,
			anchorOrigin: {
				vertical: 'top',
				horizontal: 'right'
			},
			variant: 'warning'

		}))
	}

}

export const getAllUsers = () => async dispatch => {
	try {
		axios.get(`admin/api/users/getAllUsers`)
			.then(response => {
				if (response.status === 200) {
					let datas = JSON.parse(decrypt(response.data))
					dispatch(setAllusers(datas.allusers));
					return;
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
					return dispatch(setUsers([]));
				}
			});
	} catch (error) {
		return dispatch(showMessage({
			message: error.message,
			autoHideDuration: 2000,
			anchorOrigin: {
				vertical: 'top',
				horizontal: 'right'
			},
			variant: 'warning'

		}))
	}
}

export const getUsers = (sortField, sortDirection, pageIndex, pageLimit, isColumnHidden, selectedAgency) => async dispatch => {
	dispatch(setLoading(true));
	axios.get(`admin/api/users/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${isColumnHidden}/${selectedAgency}`)
		.then(response => {
			if (response.status == 200) {
				let datas = JSON.parse(decrypt(response.data))
				let data = datas.data;
				let allusers = datas.allusers;
				dispatch(setUserTotalCount(datas.totalCount));
				dispatch(setAllusers(datas.allusers));
				dispatch(setLoading(false));
				if (selectedAgency !== "ALL" && isColumnHidden === true) {
					//data = data.filter(d => d.userAgenciesArray.filter(s=>s.code === selectedAgency) )
					data = allusers.filter(c => c.isSuperAdmin === false && c.userAgencies.some(r => r.agencyCode === selectedAgency));
					dispatch(setUserTotalCount(data.length));
				}
				else if (selectedAgency !== "ALL" && isColumnHidden === false) {
					data = allusers.filter(c => c.isSuperAdmin === true && c.userAgencies.some(r => r.agencyCode === selectedAgency));
					dispatch(setUserTotalCount(data.length));
				}

				return dispatch(setUsers(data));
			}
			else {
				response = JSON.parse(decrypt(response.response.data));
				dispatch(showMessage({
					message: response.data.message,
					autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'warning'

				}))
				return dispatch(setUsers([]));
			}
		}
		);
}

export const removeUser = (userID) => async dispatch => {
	try {
		dispatch(setLoading(true))
		axios.delete(`admin/api/users/user/` + userID)
			.then(response => {
				if (response.status == 200) {
					response.data = JSON.parse(decrypt(response.data));
					if (response.data.isSuccess) {
						dispatch(setUsersDelete(true));
						dispatch(setLoading(false));
						return dispatch(showMessage({
							message: response.data.message,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							autoHideDuration: 2000,
							variant: 'success'
						}));
					}
					else {
						dispatch(setLoading(false))
						dispatch(showMessage({
							message: response.data.message,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							autoHideDuration: 2000,
							variant: 'warning'
						}))
					}
				}
				else {
					dispatch(setLoading(false))
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			});
	} catch (e) {
		dispatch(setLoading(false))
		return console.error(e.message);
	}
}

export const hardDeleteUser = (email, id) => async dispatch => {
	try {
		dispatch(setLoading(true))
		axios.delete(`admin/api/users/user/hardDelete/${email}/${id}`)
			.then(response => {
				if (response.status == 200) {
					response.data = JSON.parse(decrypt(response.data));
					if (response.data.isSuccess) {
						dispatch(setLoading(false))
						dispatch(setUsersHardDelete(true))
						return dispatch(showMessage({
							message: response.data.message,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							autoHideDuration: 2000,
							variant: 'success'
						}));
					}
					else {
						dispatch(setLoading(false))
						dispatch(showMessage({
							message: response.data.message,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							autoHideDuration: 2000,
							variant: 'warning'
						}))
					}
				}
				else {
					dispatch(setLoading(false))
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			});
	} catch (e) {
		dispatch(setLoading(false))
		return console.error(e.message);
	}
}

export const enableUser = (id,) => async dispatch => {
	try {
		dispatch(setLoading(true))
		axios.get(`admin/api/users/user/enableUser/${id}`)
			.then(response => {
				if (response.status == 200) {
					response.data = JSON.parse(decrypt(response.data));
					if (response.data.isSuccess) {
						dispatch(setUsersEnable(true));
						dispatch(setLoading(false))
						return dispatch(showMessage({
							message: response.data.message,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							autoHideDuration: 2000,
							variant: 'success'
						}))
					}
					else {
						dispatch(setLoading(false))
						return dispatch(showMessage({
							message: response.data.message,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							autoHideDuration: 2000,
							variant: 'warning'
						}))
					}
				}
				else {
					dispatch(setLoading(false))
					response = JSON.parse(decrypt(response.response.data));
					return dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			});
	} catch (e) {
		dispatch(setLoading(false))
		return dispatch(showMessage({
			message: e.message,
			autoHideDuration: 2000,
			anchorOrigin: {
				vertical: 'top',
				horizontal: 'right'
			},
			variant: 'warning'

		}))
	}
}

export const setEditUser = (user) => async dispatch => {
	return dispatch(setSeletedUser(user));
}

export const resetEditUser = () => async dispatch => {
	return dispatch(resetSeletedUser());
}

export const setUsersSearchText = (event) => async dispatch => {
	try {
		return dispatch(setUserSearchText(event.target.value));
	} catch (e) {
		return console.error(e.message);
	}
}

export const clearUsersSearchText = () => async dispatch => {
	try {
		return dispatch(setUserSearchText(''));
	} catch (e) {
		return console.error(e.message);
	}
}

export const archiveChatHistoriesUpdate = (data) => async dispatch => {
	try {
		dispatch(setLoading(true));
		await axios.post(`dispatch/api/dispatchchat/archivechathistories`, encrypt(JSON.stringify(data)))
			.then(async (response) => {
				if (response.status == 200) {
					response.data = await JSON.parse(decrypt(response.data));
					dispatch(
						showMessage({
							message: response.data.message,
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: "top",
								horizontal: "right",
							},
							variant: "success",
						})
					);
					dispatch(setLoading(false));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 4000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
					dispatch(setLoading(false));
				}
			}).catch(error => {
				return console.error(error);
			});
	} catch (e) {
		return console.error(e.message);
	}
}

export const UpdateRPSAuthentication = (body, agencyCode, userId) => async dispatch => {
	try {
		dispatch(setLoading(true))
		await axios.post(`admin/api/users/VarifyAndLoginRpsUser/${agencyCode}/${userId}`, encrypt(JSON.stringify(body)))
			.then(response => {
				if (response.status === 200) {
					dispatch(setLoading(false))
					dispatch(verifyUserSession());
					response.data = JSON.parse(decrypt(response.data));
					return dispatch(showMessage({
						message: response.data.data, autoHideDuration: 3000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
				}
				else {
					dispatch(setLoading(false))
					return dispatch(showMessage({
						message: "Incorrect credentials. Please try again with the correct credentials.",
						autoHideDuration: 4000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}))
				}
			});
	} catch (e) {
		dispatch(setLoading(false))
		return dispatch(showMessage({
			message: "RPS credentials failed.",
			autoHideDuration: 4000,
			anchorOrigin: {
				vertical: 'top',
				horizontal: 'right'
			},
			variant: 'warning'

		}))
	}
}


export const UpdateMugshotAuthentication = (body, agencyCode, userId) => async dispatch => {
	try {
		dispatch(setLoading(true))
		await axios.post(`admin/api/users/VarifyAndLoginMugshotUser/${agencyCode}/${userId}`, encrypt(JSON.stringify(body)))
			.then(response => {
				if (response.status === 200) {
					dispatch(setLoading(false))
					response.data = JSON.parse(decrypt(response.data));
					if (response.data.data.Success == true) {
						// dispatch(getRegularUsers("fname", "asc", 0, 100, "ALL", null))
						return dispatch(showMessage({
							message: "Mugshot Credentials set Successfully.",
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'success'
						}));
					}
					else {
						return dispatch(showMessage({
							message: "Incorrect credentials. Please try again with the correct credentials.",
							autoHideDuration: 4000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'

						}))
					}
				}
				else {
					dispatch(setLoading(false))
					//response.response.data = JSON.parse(decrypt(response.response.data));
					return dispatch(showMessage({
						message: "Incorrect credentials. Please try again with the correct credentials.",
						autoHideDuration: 4000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))

				}
			});
	} catch (e) {
		dispatch(setLoading(false))
		return dispatch(showMessage({
			message: "Incorrect credentials. Please try again with the correct credentials.",
			autoHideDuration: 4000,
			anchorOrigin: {
				vertical: 'top',
				horizontal: 'right'
			},
			variant: 'warning'

		}))
	}
}

export const getUsersByAgency = (code) => async dispatch => {
	try {
		axios.get(`admin/api/users/getUserByAgency/${code}`)
			.then(response => {
				if (response.status === 200) {
					let data = JSON.parse(decrypt(response.data))
					dispatch(setFilterdUsersByAgency(data));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
					return dispatch(setFilterdUsersByAgency([]));
				}
			});
	} catch (error) {
		dispatch(setFilterdUsersByAgency([]));
		return dispatch(showMessage({
			message: error.message,
			autoHideDuration: 2000,
			anchorOrigin: {
				vertical: 'top',
				horizontal: 'right'
			},
			variant: 'warning'

		}))
	}
}


export const SetPageValue = (data) => async dispatch => {
	dispatch(setPageValue(data));
}
export const SetRowsPerPageValue = (data) => async dispatch => {
	dispatch(setRowsPerPageValue(data));


}
export const SetSuperAdminPageValue = (data) => async dispatch => {
	dispatch(setSuperAdminPageValue(data));
}
export const SetSuperAdminRowsPerPageValue = (data) => async dispatch => {
	dispatch(setSuperAdminRowsPerPageValue(data));


}

const initialState = {
	data: [],
	searchText: '',
	searchUser: [],
	selectedUser: null,
	selectedUserAccessRights: null,
	userDelete: false,
	userHardDelete: false,
	userEnable: false,
	isloading: false,
	allusers: [],
	filteredUsersByAgency: [],
	ImportUserSuccess: false,
	ImportUserData: [],
	RegularUsers: [],
	SuperAdminUsers: [],
	SuperAdminUserTotalCount: 0,
	rowsPerPage: 100,
	page: 0,
	superAdminPage: 0,
	superAdminRowsPerPage: 100
};

const userSlice = createSlice({
	name: 'administration/user',
	initialState,
	reducers: {
		setUsers: (state, action) => {
			state.data = action.payload;
			state.selectedUser = null;
			state.selectedUserAccessRights = null;
			state.userDelete = false;
			state.userHardDelete = false;
			state.userEnable = false;
		},
		setUserSearchText: (state, action) => {
			state.searchText = action.payload;
		},
		setClearUserSearchText: (state, action) => {
			state.searchText = action.payload;
		},
		setsearchUser: (state, action) => {
			state.searchUser = action.payload;
		},
		setSeletedUser: (state, action) => {
			state.selectedUser = action.payload;
		},
		resetSeletedUser: (state, action) => {
			state.selectedUser = null;
		},
		setUsersDelete: (state, action) => {
			state.userDelete = action.payload;
		},
		setUsersHardDelete: (state, action) => {
			state.userHardDelete = action.payload;
		},
		setUsersEnable: (state, action) => {
			state.userEnable = action.payload;
		},
		setLoading: (state, action) => {
			state.isloading = action.payload;
		},
		setUserTotalCount: (state, action) => {
			state.totalCount = action.payload
		},
		setAllusers: (state, action) => {
			state.allusers = action.payload
		},
		setFilterdUsersByAgency: (state, action) => {
			state.filteredUsersByAgency = action.payload;
		},
		setImportUserResponse: (state, action) => {
			state.ImportUserSuccess = true
			state.ImportUserData = action.payload
		},
		setRegularUsers: (state, action) => {
			state.RegularUsers = action.payload;
		},
		setSuperAdminUsers: (state, action) => {
			state.SuperAdminUsers = action.payload;
		},
		setSuparAdminUserTotalCount: (state, action) => {
			state.SuperAdminUserTotalCount = action.payload
		},
		setPageValue: (state, action) => {

			state.page = action.payload
		},
		setRowsPerPageValue: (state, action) => {

			state.rowsPerPage = action.payload
		},
		setSuperAdminPageValue: (state, action) => {

			state.superAdminPage = action.payload
		},
		setSuperAdminRowsPerPageValue: (state, action) => {

			state.superAdminRowsPerPage = action.payload
		},
	},
	extraReducers: {},
})

export const {
	setUsers,
	setUserSearchText,
	setSeletedUser,
	resetSeletedUser,
	setUsersDelete,
	setUsersHardDelete,
	setUsersEnable,
	setsearchUser,
	setAllusers,
	setFilterdUsersByAgency,
	setLoading,
	setUserTotalCount,
	setImportUserResponse,
	setRegularUsers,
	setSuperAdminUsers,
	setClearUserSearchText,
	setSuparAdminUserTotalCount,
	setPageValue,
	setRowsPerPageValue,
	setSuperAdminPageValue,
	setSuperAdminRowsPerPageValue
} = userSlice.actions;

export default userSlice.reducer;
