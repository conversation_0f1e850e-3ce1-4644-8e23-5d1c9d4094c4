import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import history from '@history';
import axios from "axios";
import { encrypt, decrypt } from '../../security/crypto';
import { setUserDefaultLocation } from './userSlice';
import { verifyUserSession } from './loginSlice';

export const passwordGenerator = (len) => {
	const length = (len);
	const stringLower = "abcdefghijklmnopqrstuvwxyz";
	const stringUpper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	const numeric = '0123456789';
	const punctuation = '@@@@';
	let password = "";
	let character = "";
	while (password.length < length) {
		character += stringUpper.charAt(Math.ceil(stringUpper.length * Math.random() * Math.random()));
		character += stringLower.charAt(Math.ceil(stringLower.length * Math.random() * Math.random()));
		character += numeric.charAt(Math.ceil(numeric.length * Math.random() * Math.random()));
		character += stringUpper.charAt(Math.ceil(stringUpper.length * Math.random() * Math.random()));
		character += numeric.charAt(Math.ceil(numeric.length * Math.random() * Math.random()));
		character += stringUpper.charAt(Math.ceil(stringUpper.length * Math.random() * Math.random()));
		character += punctuation.charAt(Math.ceil(punctuation.length * Math.random() * Math.random()));
		character += stringLower.charAt(Math.ceil(stringLower.length * Math.random() * Math.random()));
		password = character;
	}
	password = password.split('').sort(function () { return 0.5 - Math.random() }).join('');
	return password.substr(0, len);
}

export const submitRegister = (userObject) => async dispatch => {
	dispatch(setLoader(true))
	userObject.password = passwordGenerator(8);
	return axios.post(`admin/api/users/user`, encrypt(JSON.stringify(userObject)))
		.then(user => {
			dispatch(setLoader(false))
			if (user.status == 200) {
				user = JSON.parse(decrypt(user.data));
				if (user.result.code === "Success") {
					dispatch(
						showMessage({
							message: 'User has been added successfully..!!',
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'success'
						}));
					dispatch(registerSuccess(true));
					return history.push("/admin/users");
				} else if (user.result.code === "Failed") {
					dispatch(
						showMessage({
							message: "User not Created",
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}))
					return dispatch(registerSuccess());
				}
			} else {
				let response = JSON.parse(decrypt(user.response.data));
				console.log('response', response);
				dispatch(showMessage({
					message: response,
					autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'warning'
				}))
				return;
			}
		})
		.catch(error => {
			dispatch(setLoader(false))
			dispatch(
				showMessage({
					message: error,
					autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'error'
				}))
			return dispatch(registerError(error));
		});
};


export const submitInitialUserRegistration = (userObject) => async dispatch => {
	dispatch(setLoader(true))
	userObject.password = passwordGenerator(8);
	return axios.post(`admin/api/users/user`, encrypt(JSON.stringify(userObject)))
		.then(user => {
			dispatch(setLoader(false))
			if (user.status == 200) {
				user = JSON.parse(decrypt(user.data));
				if (user.result.code === "Success") {
					dispatch(registerSuccess(true));
					return history.push("/admin/agencyList");
				} else if (user.result.code === "Failed") {
					dispatch(
						showMessage({
							message: "User not Created",
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}))
					return dispatch(registerSuccess());
				}
			} else {
				let response = JSON.parse(decrypt(user.response.data));
				console.log('response', response);
				dispatch(showMessage({
					message: response,
					autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'warning'
				}))
				return;
			}
		})
		.catch(error => {
			dispatch(setLoader(false))
			dispatch(
				showMessage({
					message: error,
					autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'error'
				}))
			return dispatch(registerError(error));
		});
};

export const updateUser = (user) => async dispatch => {
	dispatch(setLoader(true))
	await axios.post(`admin/api/users/usersEdit`, encrypt(JSON.stringify(user)))
		.then(response => {
			dispatch(setLoader(false))
			response.data = JSON.parse(decrypt(response.data));
			if (response.data.isSuccess) {
				dispatch(
					showMessage({
						message: response.data.message,//text or html
						autoHideDuration: 2000,//ms
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
				dispatch(verifyUserSession());
				history.push("/admin/users");
			}
			return dispatch(registerSuccess(true));
		}).catch(error => {
			dispatch(setLoader(false))
			return dispatch(registerError(error));
		});
};

export const verifyUserRpsCred = (data) => async dispatch => {
	try {
		await axios.post(`dispatch/api/rpsusers/varifyAndLoginRpsUser`, {
			userName: data.rpsUserName,
			password: data.rpsPassword,
			deviceID: "test",
			deviceType: "test"
		})
			.then(async response => {
				if (response.status === 400) {
					dispatch(showMessage({
						message: response.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'error'
					}));
				}
				else {
					data.token = response.data.token;
					await axios.post(`dispatch/api/rpsusers/messageUsersList`, data)
						.then(rpsResponse => {
							if (rpsResponse.status === 400) {
								dispatch(showMessage({
									message: rpsResponse.message, autoHideDuration: 2000,
									anchorOrigin: {
										vertical: 'top',
										horizontal: 'right'
									},
									variant: 'error'
								}));
							}
							else {
								const rpsDetails = {
									rpsUserName: rpsResponse.data[0].rpsUserName,
									rpsPassword: rpsResponse.data[0].rpsPassword,
									userGroupName: rpsResponse.data.length ? "" : rpsResponse.data[0].userAgency + " - " + rpsResponse.data[0].groupName,
									RPSName: rpsResponse.data.length == 0 ? "" : rpsResponse.data[0].userName.trim(),
									RPSUserID: rpsResponse.data.length == 0 ? 0 : rpsResponse.data[0].rpsUserID,
									RMDUserID: rpsResponse.data.length == 0 ? 0 : rpsResponse.data[0].rmdUserID
								}
								return dispatch(showMessage({
									message: 'User verified successfully..!', autoHideDuration: 2000,
									anchorOrigin: {
										vertical: 'top',
										horizontal: 'right'
									},
									variant: 'success'
								})), dispatch(rpsUserDetails(rpsDetails));
							}
						});
				}
			}).catch(error => {
				return dispatch(
					showMessage({
						message: "Username/Password does not match",
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}),
				), dispatch(rpsUserDetails({
					rpsUserName: "",
					rpsPassword: "",
					userGroupName: "",
					RPSName: "",
					RPSUserID: "",
					RMDUserID: "",
				}));
			});
	} catch (e) {
	}
}

export const submitUserProfileUpdate = (data) => async dispatch => {
	try {
		if (data.file !== null) {
			const formData = new FormData();
			formData.append("file", data.file);
			await axios.post(`fileupload/uploadProfilePicture/${data._id}/ProfilePicture`, formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			})
				.then(async response => {
					if (response.data) {
						const userObj = {
							_id: data._id,
							phone: data.phone,
							profilePic: response.data.Location
						}
						await axios.post(`admin/api/users/usersProfileEdit`, encrypt(JSON.stringify(userObj)))
							.then(userResponse => {
								if (userResponse.status == 200) {
									response.data = JSON.parse(decrypt(userResponse.data));
									if (response.data.isSuccess) {
										dispatch(showMessage({
											message: response.data.message, autoHideDuration: 2000,
											anchorOrigin: {
												vertical: 'top',
												horizontal: 'right'
											},
											variant: 'success'
										}));
										dispatch(verifyUserSession());
										return dispatch(profileUpdate(true));
									}
									else {
										dispatch(showMessage({
											message: 'Error Occured!', autoHideDuration: 2000,
											anchorOrigin: {
												vertical: 'top',
												horizontal: 'right'
											},
											variant: 'error'
										}));
										return dispatch(profileUpdate(false));
									}
								}
								else {
									response = JSON.parse(decrypt(response.response.data));
									dispatch(showMessage({
										message: response.data.message,
										autoHideDuration: 2000,
										anchorOrigin: {
											vertical: 'top',
											horizontal: 'right'
										},
										variant: 'warning'
									}))
									return dispatch(profileUpdate(false));
								}
							}).catch(error => {
								return dispatch(
									showMessage({
										message: error.message,//text or html
										autoHideDuration: 2000,//ms
										anchorOrigin: {
											vertical: 'top',
											horizontal: 'right'
										},
										variant: 'warning'
									}),
								);
							});
					}
				})
		}
		else {
			await axios.post(`admin/api/users/usersProfileEdit`, encrypt(JSON.stringify(data)))
				.then(response => {
					if (response.status == 200) {
						response.data = JSON.parse(decrypt(response.data));
						if (response.data.isSuccess) {
							dispatch(showMessage({
								message: response.data.message, autoHideDuration: 2000,
								anchorOrigin: {
									vertical: 'top',
									horizontal: 'right'
								},
								variant: 'success'
							}));
							dispatch(verifyUserSession());
							return dispatch(profileUpdate(true));
						}
						else {
							dispatch(showMessage({
								message: 'Error Occured!', autoHideDuration: 2000,
								anchorOrigin: {
									vertical: 'top',
									horizontal: 'right'
								},
								variant: 'error'
							}));
							return dispatch(profileUpdate(false));
						}
					}
					else {
						response = JSON.parse(decrypt(response.response.data));
						dispatch(showMessage({
							message: response.data.message,
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}))
						return dispatch(profileUpdate(false));
					}
				}).catch(error => {
					return dispatch(
						showMessage({
							message: error.message,//text or html
							autoHideDuration: 2000,//ms
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}),
					);
				});
		}
	} catch (e) {
	}
};

export const GetUserByEmail = (email, users) => async dispatch => {
	let existingUser = users.filter(user => user.email === email.trim());
	dispatch(userExist(false));
	dispatch(setUserIsDisabled(false));
	if (existingUser.length > 0) {
		dispatch(showMessage({
			message: "User already exists. Please try using another email.",
			autoHideDuration: 5000,
			variant: 'warning'
		}))
		return dispatch(userExist(true));
	}
	else {
		await axios.get(`admin/api/users/getUserByEmail/` + email)
			.then(response => {
				if (response.status == 200) {
					response.data = JSON.parse(decrypt(response.data));
					if (response.data) {
						dispatch(showMessage({
							message: "User already exists. Please try using another email.",
							autoHideDuration: 2000,
							variant: 'warning'
						}))
						return dispatch(userExist(true));
					}
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}))
					userEmailVerify([])
					dispatch(setUserIsDisabled(true));
				}
			}).catch(error => {
				return dispatch(userEmailVerify([]));
			});
	}
};

export const ChangeEmail = (data) => async dispatch => {
	data.password = passwordGenerator(8);
	await axios.post(`admin/api/users/changeEmail`, data)
		.then(response => {
			if (response.data.UserExist) {
				dispatch(showMessage({
					message: response.data.message,
					autoHideDuration: 5000,
					variant: 'warning'
				}))
				return dispatch(emailChangeSucess(false));
			}
			else {
				return dispatch(emailChangeSucess(true));
			}
		}).catch(error => {
			return dispatch(emailChangeSucess(false));
		});
}

export const ClearUserData = () => async dispatch => {
	return dispatch(clearUserDataFromStore());
};

export const registerWithFirebase = model => async dispatch => {
};

//To resend verify link to user
export const ResendVerifyEmail = (data) => async dispatch => {
	try {
		await axios.post(`admin/api/auth/resendConfirmationCode`, encrypt(JSON.stringify(data)))
			.then(response => {
				if (response.data.code === "Success") {
					dispatch(showMessage({
						message: response.data.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
				}
				else {
					dispatch(showMessage({
						message: response.data.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}));
				}
			});
	} catch (e) {
		return console.error(e.message);
	}
}

export const GetCountyExtentsData = () => async dispatch => {
	try {
		await axios.get(`admin/api/citycountyextents/getCountyExtents`)
			.then(response => {
				if (response.status == 200) {
					dispatch(countyExtentsData(JSON.parse(decrypt(response.data))));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}))
					return dispatch(countyExtentsData([]));
				}
			}).catch(error => {
				return dispatch(countyExtentsData([]));
			});
	} catch (e) {
	}
};

export const GetCityExtentsData = () => async dispatch => {
	try {
		await axios.get(`admin/api/citycountyextents/getCityExtents`)
			.then(response => {
				if (response.status == 200) {
					dispatch(cityExtentsData(JSON.parse(decrypt(response.data))));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}))
					return dispatch(cityExtentsData([]));
				}
			}).catch(error => {
				return dispatch(cityExtentsData([]));
			});
	} catch (e) {
	}
};

export const UserDefaultLocationUpdate = (data) => async dispatch => {
	try {
		dispatch(setLoader(true));
		await axios.post(`admin/api/users/userDefaultlocationEdit`, encrypt(JSON.stringify(data)))
			.then(response => {
				if (response.status == 200) {
					dispatch(setLoader(false));
					response.data = JSON.parse(decrypt(response.data));
					dispatch(showMessage({
						message: response.data.result.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
					dispatch(setUserDefaultLocation(response.data.result))
					return;
				}
				else {
					dispatch(setLoader(false));
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}))
					return dispatch(profileUpdate(false));
				}
			}).catch(error => {
				dispatch(setLoader(false));
				return dispatch(
					showMessage({
						message: error.message,//text or html
						autoHideDuration: 2000,//ms
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}),
				);
			});
	} catch (e) {
		dispatch(setLoader(false));
	}
};

const initialState = {
	success: false,
	error: {
		username: null,
		password: null
	},
	userExist: false,
	userIsDisabled: false,
	userAvailable: false,
	emailVerify: null,
	emailChangesuccess: false,
	profileUpdate: false,
	countyData: [],
	cityData: [],
	rpsUserDetails: {},
	isloading: false,
};

const registerSlice = createSlice({
	name: 'auth/register',
	initialState,
	reducers: {
		registerSuccess: (state, action) => {
			state.success = true;
		},
		registerError: (state, action) => {
			state.success = false;
			state.error = action.payload;
		},
		userEmailVerify: (state, action) => {
			state.emailVerify = action.payload[0];
			state.userAvailable = action.payload.length > 0 ? true : false;
			state.userExist = false;
		},
		emailChangeSucess: (state, action) => {
			state.emailChangesuccess = action.payload;
		},
		clearUserDataFromStore: (state, action) => {
			state.emailVerify = null;
			state.userAvailable = false;
			state.userExist = false;
			state.userIsDisabled = false;
			state.profileUpdate = false;
		},
		userExist: (state, action) => {
			state.userExist = action.payload;
		},
		setUserIsDisabled: (state, action) => {
			state.userIsDisabled = action.payload;
		},
		profileUpdate: (state, action) => {
			state.profileUpdate = action.payload;
		},
		countyExtentsData: (state, action) => {
			state.countyData = action.payload;
		},
		cityExtentsData: (state, action) => {
			state.cityData = action.payload;
		},
		rpsUserDetails: (state, action) => {
			state.rpsUserDetails = action.payload;
		},
		setLoader: (state, action) => {
			state.isloading = action.payload;
		},
	},
	extraReducers: {}
});

export const { registerSuccess, registerError, userEmailVerify, emailChangeSucess, clearUserDataFromStore, userExist,
	profileUpdate, setUserIsDisabled, countyExtentsData, cityExtentsData, rpsUserDetails, setLoader } = registerSlice.actions;

export default registerSlice.reducer;