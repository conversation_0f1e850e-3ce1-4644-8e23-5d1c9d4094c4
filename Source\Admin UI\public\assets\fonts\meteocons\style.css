@font-face {
  font-family: "meteocons";
  src: url("./fonts/meteocons.ttf?kx31oc") format("truetype"),
    url("./fonts/meteocons.woff?kx31oc") format("woff"),
    url("./fonts/meteocons.svg?kx31oc#meteocons") format("svg");
  font-weight: normal;
  font-style: normal;
}

.meteocons {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "meteocons" !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -moz-font-feature-settings: "liga=1";
  -moz-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.backButton {
  background: none !important;
}

.themeSettings {
  top: -63px !important;
  /* height: 63px !important; */
}

.themeSettingsAfterToolbarHide {
  top: 18px !important;
  /* height: 63px !important; */
}

.profileSection {
  padding-right: 3.8rem !important;
}

.adminControlPadding {
  padding: 12px 0px 12px 0px !important;
}

.igrGridClass {
  padding: 10px;
}

.igx-grid__td,
.igx-grid-thead__title,
.igx-grid-th {
  font-size: 15px !important;
  line-height: 4rem !important;
}

.igx-drop-area__text {
  font-size: 15px !important;
}

.igx-grid-th {
  padding: 10px !important;
}

.igx-icon {
  height: 15px !important;
  width: 15px !important;
  font-size: 15px !important;
}

.igx-drop-area,
.igx-grid-grouparea {
  padding: 15px !important;
}

.igx-chip__item {
  height: 30px !important;
  width: 90px;
}

.igx-chip__content {
  font-size: 14px !important;
}

.uppercase-text {
  text-transform: uppercase;
}