import CardContent from '@mui/material/CardContent';
import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import clsx from 'clsx';
import Typography from '@mui/material/Typography';
import { motion } from 'framer-motion';
import Card from '@mui/material/Card';
import { darken, styled } from '@mui/material/styles';
import { setUserEmailName, confirmChangePassword, GetUserDefaultApp } from '../../../auth/store/userSlice';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import { useParams } from 'react-router-dom';

const Root = styled('div')(({ theme }) => ({
	background: `linear-gradient(to right, ${theme.palette.primary.dark} 0%, ${darken(
		theme.palette.primary.dark,
		0.5
	)} 100%)`,
	color: theme.palette.primary.contrastText,

	'& .Login-leftSection': {},

	'& .Login-rightSection': {
		background: `linear-gradient(to right, ${theme.palette.primary.dark} 0%, ${darken(
			theme.palette.primary.dark,
			0.5
		)} 100%)`,
		color: theme.palette.primary.contrastText,
	},
}));
const schema = yup.object().shape({
	OldPassword: yup.string()
		.required('Please enter your temporary password.')
		.min(8, 'Temporary Password Invalid')
		.max(8, 'Temporary Password Invalid'),
	password: yup
		.string()
		.required('Please enter your password.')
		.matches('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\\$%\\^&\\*])(?=.{8,})',
			"The password must be min. 8 characters & contains at least 1 uppercase,1 lowercase,1 numeric,1 special character"),
	passwordConfirm: yup
		.string()
		.required("Please confirm your password")
		.oneOf([yup.ref('password'), null], "Passwords don't match.")
});
const defaultValues = {
	OldPassword: '',
	password: '',
	passwordConfirm: ''
};

function VerifyUser(props) {
	const dispatch = useDispatch();
	const routeParams = useParams();
	const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
		mode: 'onChange',
		defaultValues,
		resolver: yupResolver(schema),
	});
	const { isValid, dirtyFields, errors } = formState;
	const userName = useSelector(({ auth }) => auth.user.userName);
	const selectedUser = useSelector(({ auth }) => auth.user.selectedUser);
	const passwordChanged = useSelector(({ auth }) => auth.user.passwordChanged);

	const [showOldPassword, setShowOldPassword] = useState(false);


	useEffect(() => {
		//alert("fdfdf")
		dispatch(setUserEmailName(routeParams.id));
		// eslint-disable-next-line
	}, [routeParams]);

	useEffect(() => {
		if (userName !== "") {
			dispatch(GetUserDefaultApp(userName));
		}
		// eslint-disable-next-line
	}, [userName]);

	useEffect(() => {
		if (passwordChanged) {
			if (selectedUser.defaultApp === "call") {
				window.open(`${process.env.REACT_APP_911_API_URL}/login`, '_self');
			}
			else if (selectedUser.defaultApp === "mobile") {
				window.open(`${process.env.REACT_APP_DISPATCH_URL}/auth/login`, '_self');
			}
			else {
				window.open(`${process.env.REACT_APP_ADMIN_URL}/login`, '_self');
			}
		}
		// eslint-disable-next-line
	}, [passwordChanged]);



	function onSubmit(model) {
		dispatch(confirmChangePassword({
			OldPassword: model.OldPassword,
			Password: model.password,
			UserName: userName
		}));
	}




	return (
		<Root className="flex flex-col flex-auto items-center justify-center shrink-0 p-16 md:p-24">
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
			>
				<Card className="w-full min-w-384 max-w-384 ">
					<CardContent className="flex flex-col items-center justify-center p-32">
						<div className="m-16">
							<Icon className="text-96" color="action">
								verified_user
							</Icon>
						</div>

						<Typography variant="h5" className="text-center mb-16">
							Reset Password
						</Typography>
						<form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>
							<Controller
								type="number"
								name="OldPassword"

								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label="Temporary Password"
										type="password"
										error={!!errors.OldPassword}
										helperText={errors?.OldPassword?.message}
										variant="outlined"
										InputProps={{
											className: 'pr-2',
											type: showOldPassword ? 'text' : 'password',
											endAdornment: (
												<InputAdornment position="end">
													<IconButton onClick={() => setShowOldPassword(!showOldPassword)} size="large">
														<Icon className="text-20" color="action">
															{showOldPassword ? 'visibility' : 'visibility_off'}
														</Icon>
													</IconButton>
												</InputAdornment>
											),
										}}
										required
									/>
								)}
							/>

							<Controller
								type="password"
								name="password"

								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label="New Password"
										type="password"
										error={!!errors.password}
										helperText={errors?.password?.message}
										variant="outlined"
										InputProps={{
											className: 'pr-2',
											endAdornment: (
												<InputAdornment position="end">
													<IconButton>
														<Icon className="text-20" color="action">
															vpn_key
														</Icon>
													</IconButton>
												</InputAdornment>
											),
										}}
										required
									/>
								)}
							/>
							<Controller
								type="password"
								name="passwordConfirm"

								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label="Confirm Password"
										type="password"
										error={!!errors.passwordConfirm}
										helperText={errors?.passwordConfirm?.message}
										variant="outlined"
										InputProps={{
											className: 'pr-2',
											endAdornment: (
												<InputAdornment position="end">
													<IconButton>
														<Icon className="text-20" color="action">
															vpn_key
														</Icon>
													</IconButton>
												</InputAdornment>
											),
										}}
										required
									/>
								)}
							/>
							<Button
								type="submit"
								variant="contained"
								color="primary"
								className="w-full mx-auto mt-16"
								aria-label="LOG IN"
								disabled={_.isEmpty(dirtyFields) || !isValid}
								value="legacy"
							>
								Reset Password
							</Button>
						</form>

					</CardContent>
				</Card>
			</motion.div>
		</Root>
	);
	// eslint-disable-next-line no-else-return	
}


export default VerifyUser;
