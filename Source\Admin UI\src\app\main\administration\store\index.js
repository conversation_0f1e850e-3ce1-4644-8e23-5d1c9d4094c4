import { combineReducers } from '@reduxjs/toolkit';
import serverConfigurations from './serverConfigurationsSlice';
import callIconUploadSlice from './callIconSlice'
import callCategorySlice from './callCategorySlice'
import user from './usersSlice';
import callTypes from './callTypeSlice';
import callViolations from './callViolationSlice';
import callResponse from './callResponseSlice';
import deviceLicenses from './devicelicensesSlice';
import personMaster from './personMasterSlice';
import vehicleMaster from './vehicleMasterSlice';
import emailConfiguration from './emailConfigurationSlice';
import supportedUnitType from './supportedUnitTypeSlice';
import twitterConfiguration from './twitterConfigurationSlice';
import callStatusSlice from './callStatusSlice';
import probableCauses from './probableCausesSlice';
import communicationLogsSlice from './communicationLogsSlice';
import intersectionPointSlice from './intersectionPointSlice';
import masterAddressSlice from './masterAddressSlice';
import stateViolationSlice from './stateViolationSlice';
import nibrsCodeSlice from './nibrsCodeSlice';
import violationClassificationSlice from './violationClassificationSlice';

const authReducers = combineReducers({
	user,
	serverConfigurations,
	callIconUploadSlice,
	callCategorySlice,
	callTypes,
	callViolations,
	callResponse,
	deviceLicenses,
	personMaster,
	vehicleMaster,
	emailConfiguration,
	supportedUnitType,
	twitterConfiguration,
	callStatusSlice,
	probableCauses,
	communicationLogsSlice,
	intersectionPointSlice,
	communicationLogsSlice,
	masterAddressSlice,
	stateViolationSlice,
	nibrsCodeSlice,
	violationClassificationSlice
});

export default authReducers;