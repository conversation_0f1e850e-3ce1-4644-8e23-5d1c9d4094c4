import React, { useRef, useEffect, useState, useMemo } from 'react';
import FusePageCarded from '@fuse/core/FusePageCarded';
import UsersHeader from './UserNewHeader';
import UserNewTable from './UserNewTable';
import { ErrorBoundary } from 'react-error-boundary';
import { useSelector, useDispatch } from 'react-redux';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { setFilterAgencyDDlValue } from '../../agencyPage/store/agencySlice';

function userListNewTest() {
	const user = useSelector(({ auth }) => auth.user);
	const dispatch = useDispatch();
	const filterAgencyValue = useSelector(({ agency }) => agency.agency.filterAgencyValue);
	const [selectedAgency, setSelectedAgency] = useState("");
	const [searchText, setSearchText] = useState("");

	const handleSelectedAgecny = (value) => {
		setSelectedAgency(value);
	};
	const [selectedPage, setSelectedPage] = useState("");
	const handlePagination = (value) => {
		setSelectedPage(value);
	}

	useEffect(() => {
		if (user.data.defaultAgency) {
			dispatch(setFilterAgencyDDlValue(filterAgencyValue !== "" ? filterAgencyValue : user.data.agencyAdmin ? user.data.defaultAgency : "ALL"))
		}
	}, [user.data, filterAgencyValue]);

	return (
		<>
			<FusePageCarded
				classes={{
					content: 'flex',
					header: 'min-h-72 h-72 sm:h-136 sm:min-h-136'
				}}
				header={
					<ErrorBoundary
						FallbackComponent={(props) => <ErrorPage {...props} componentName="UsersHeader" />} onReset={() => { }} >
						<UsersHeader
							handleSelectedAgecny={handleSelectedAgecny}
							setSearchText={setSearchText}
							selectedPage={selectedPage} />
					</ErrorBoundary>
				}
				content={
					<ErrorBoundary
						FallbackComponent={(props) => <ErrorPage {...props} componentName="UserNewTable" />} onReset={() => { }} >
						<UserNewTable searchText={searchText} selectedAgency={selectedAgency} handlePagination={handlePagination} />
					</ErrorBoundary>
				}
			/>
		</>
	);


}


export default userListNewTest;