import { createSlice } from '@reduxjs/toolkit';
import history from '@history';
import _ from '@lodash';
import { setInitialSettings, setDefaultSettings } from 'app/store/fuse/settingsSlice';
import { showMessage } from 'app/store/fuse/messageSlice';
import jwtService from '../../services/jwtService/jwtService';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';
import { newUserAudit } from '../../main/userAuditPage/store/userAuditSlice';
import settingsConfig from 'app/configs/settingsConfig';
import JwtService from 'src/app/services/jwtService';
import { verifyUserSession } from './loginSlice';

export const setUserDataAuth0 = tokenData => async dispatch => {
	const user = {
		role: ['admin'],
		from: 'auth0',
		data: {
			displayName: tokenData.username || tokenData.name,
			photoURL: tokenData.picture,
			email: tokenData.email,
			settings:
				tokenData.user_metadata && tokenData.user_metadata.settings ? tokenData.user_metadata.settings : {},
			shortcuts:
				tokenData.user_metadata && tokenData.user_metadata.shortcuts ? tokenData.user_metadata.shortcuts : []
		}
	};

	return dispatch(setUserData(user));
};

export const setUserData = user => async (dispatch, getState) => {
	/*
		You can redirect the logged-in user to a specific route depending on his role
		 */
	//if (user.data.defaultApp !== "mobile") {
	if (user.data.isSuperAdmin) {
		if (user.redirectUrl) {
			settingsConfig.loginRedirectUrl = user.redirectUrl; // for example 'apps/academy'
		}
	}
	else {
		if (user.data.agencyAdmin) {
			settingsConfig.loginRedirectUrl = '/admin/users';
		}
		else {
			settingsConfig.loginRedirectUrl = '/pages/maintenance';
		}
	}

	/*
	Set User Settings
	 */
	dispatch(setDefaultSettings(user.data.settings));

	dispatch(setUser(user));
	const defaltLocationdata = { defaultLocation: user.data.defaultLocation, defaultLocationType: user.data.defaultLocationType }
	dispatch(setUserDefaultLocation(defaltLocationdata));
};

export const updateUserSettings = settings => async (dispatch, getState) => {
	const oldUser = getState().auth.user;
	const user = _.merge({}, oldUser, { data: { settings } });

	dispatch(updateUserData(user));

	return dispatch(setUserData(user));
};

export const updateUserShortcuts = shortcuts => async (dispatch, getState) => {
	const { user } = getState().auth;
	const newUser = {
		...user,
		data: {
			...user.data,
			shortcuts
		}
	};

	dispatch(updateUserData(user));

	return dispatch(setUserData(newUser));
};

export const logoutUser = () => async (dispatch, getState) => {
	const { user } = getState().auth;

	jwtService.logout();

	dispatch(newUserAudit({
		activity: "Logged Out",
		user,
		appName: "Admin,"
	}));

	if (!user.role || user.role.length === 0) {
		// is guest
		return null;
	}

	history.push({
		pathname: '/login'
	});

	dispatch(setInitialSettings());


	return dispatch(userLoggedOut(initialState));
};

export const updateUserLanguage = (languageId) => async dispatch => {
	try {
		let userId = localStorage.getItem("userId");
		let data = { languageId, userId }
		await axios.post(`admin/api/users/updateUserLanguage`, encrypt(JSON.stringify(data)))
			.then(response => {
				if (response.status == 200) {
					let res = JSON.parse(decrypt(response.data));
					localStorage.removeItem('defaultLanguage');
					localStorage.setItem('defaultLanguage', res.languageId);
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			}).catch(error => {
				return console.error(error);
			});
	}
	catch (e) {
		return console.error(e.message);
	}
};

export const updateUserData = user => async (dispatch, getState) => {
	if (!user.role || user.role.length === 0) {
		// is guest
		return;
	}
	switch (user.from) {
		case 'firebase': {
			// firebaseService
			// 	.updateUserData(user)
			// 	.then(() => {
			// 		dispatch(showMessage({ message: 'User data saved to firebase' }));
			// 	})
			// 	.catch(error => {
			// 		dispatch(showMessage({ message: error.message }));
			// 	});
			break;
		}
		case 'auth0': {
			// auth0Service
			// 	.updateUserData({
			// 		settings: user.data.settings,
			// 		shortcuts: user.data.shortcuts
			// 	})
			// 	.then(() => {
			// 		dispatch(showMessage({ message: 'User data saved to auth0' }));
			// 	})
			// 	.catch(error => {
			// 		dispatch(showMessage({ message: error.message }));
			// 	});
			break;
		}
		default: {
			jwtService
				.updateUserData(user)
				.then(() => {
					dispatch(showMessage({
						message: 'User settings saved successfully.', autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
					window.location.reload()
				})
				.catch(error => {
					dispatch(showMessage({ message: error.message }));
				});
			break;
		}
	}
};

export const updateCardView = (user) => async dispatch => {
	try {
		const newUser = {
			...user,
			data: {
				...user.data,
				cardView: !user.data.cardView
			}
		};
		await axios.post(`admin/api/users/updateCardView`, {
			id: user.data.id,
			cardView: newUser.data.cardView
		})
			.then(response => { });
		return dispatch(setUserData(newUser));
	} catch (e) {
		return console.error(e.message);
	}
}

export const sendForgotPasswordCode = (userEmail) => async dispatch => {
	try {
		await axios.get(`admin/api/auth/forgotPassword?enc=${userEmail}`)
			.then(response => {
				response = JSON.parse(decrypt(response.data));
				if (response.code === "SUCCESS") {
					dispatch(showMessage({
						message: 'Please check your email for Verification Code.', autoHideDuration: 5000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
					return dispatch(setUserName(response.userName));
				}
				else {
					dispatch(showMessage({
						message: response.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}));
				}
				return
			});
		return
	} catch (e) {
		return console.error(e.message);
	}
}

//To reset password to cognito side
export const confirmResetPassword = (data) => async dispatch => {
	try {
		await axios.post(`admin/api/auth/confirmForgotPassword`, encrypt(JSON.stringify(data)))
			.then(response => {

				if (response.data.result.code === "Success") {
					return dispatch(showMessage({
						message: response.data.result.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
					history.push("/login");
				}
				else {
					return dispatch(showMessage({
						message: response.data.result.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}));
				}
			});
	} catch (e) {
		return console.error(e.message);
	}
}

//To get user email from mail url
export const setUserEmailName = (userEmail) => async dispatch => {
	try {
		await axios.get(`admin/api/auth/getUserName?enc=` + userEmail)
			.then(response => {
				response = JSON.parse(decrypt(response.data));
				if (response.code === "SUCCESS") {
					dispatch(setPasswordChanged(false));
					return dispatch(setUserName(response.userName));

				}
				else {
					dispatch(showMessage({
						message: response.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}));
				}

			});
	} catch (e) {
		return console.error(e.message);
	}
}

//To confirm Change Password to cognito side
export const confirmChangePassword = (data) => async dispatch => {
	try {
		await axios.post(`admin/api/auth/confirmChangePassword`, encrypt(JSON.stringify(data)))
			.then(response => {
				if (response.data.result.code === "Success") {
					dispatch(showMessage({
						message: response.data.result.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
					return dispatch(setPasswordChanged(true));
				}
				else {
					dispatch(showMessage({
						message: response.data.result.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}));
				}


			});
	} catch (e) {
		return console.error(e.message);
	}
}

//To get user defalt app url
export const GetUserDefaultApp = (userName) => async dispatch => {
	try {
		await axios.get(`admin/api/users/getUserDefaultApp/` + userName)
			.then(response => {
				if (response.status == 200) {
					response.data = JSON.parse(decrypt(response.data));
					return dispatch(setUserDetails(response.data));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			}).catch(error => {
				return dispatch(
					showMessage({
						message: error.message,//text or html
						autoHideDuration: 2000,//ms
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}),
				);
			});
	} catch (e) {
		return console.error(e.message);
	}
}

export const GetUserDefaultMFAType = (userName) => async dispatch => {
	try {
		await axios.get(`admin/api/users/getUserDefaultMFAType/` + userName)
			.then(response => {
				if (response.status == 200) {
					response.data = JSON.parse(decrypt(response.data));
					dispatch(setUserMFADetails(response.data));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			}).catch(error => {
				return dispatch(
					showMessage({
						message: error.message,//text or html
						autoHideDuration: 2000,//ms
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}),
				);
			});
	} catch (e) {
		return console.error(e.message);
	}
}

//Update Default Agency
export const updateDefaultAgency = (data) => async dispatch => {
	try {
		dispatch(setLoading(true));
		await axios.post(`admin/api/users/updateDefaultAgency`, encrypt(JSON.stringify(data)))
			.then(response => {
				if (response.status == 200) {
					dispatch(setLoading(false));
					response.data = JSON.parse(decrypt(response.data));
					dispatch(showMessage({
						message: response.data.message, autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
					return dispatch(getUserDefaultSettings(data.id));
				}
				else {
					dispatch(setLoading(false));
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}))
				}
			}).catch(error => {
				dispatch(setLoading(false));
				return dispatch(
					showMessage({
						message: error.message,//text or html
						autoHideDuration: 2000,//ms
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}),
				);
			});
	} catch (e) {
		dispatch(setLoading(false));
		return console.error(e.message);
	}
}

export const getUserDefaultSettings = (id) => async dispatch => {
	try {
		await axios.get(`admin/api/users/getUserDefaultSettings/` + id)
			.then(response => {
				response.data = JSON.parse(decrypt(response.data));
				dispatch(setDefaultAgency(response.data));
			}).catch(error => { });
	} catch (e) {
		return console.error(e.message);
	}
}

export const UserDefaultMfaTypeUpdate = (data) => async dispatch => {
	try {
		await axios.post(`admin/api/users/updateDefaultMfaType`, encrypt(JSON.stringify(data))).then(response => {
			if (response.status == 200) {
				response.data = JSON.parse(decrypt(response.data));
				dispatch(showMessage({
					message: response.data.message, autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'success'
				}));
				return dispatch(getUserDefaultSettings(data.userId));
			}
			else {
				response = JSON.parse(decrypt(response.response.data));
				dispatch(showMessage({
					message: response.data.message,
					autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'warning'

				}))
			}
		}).catch(error => {
			return dispatch(
				showMessage({
					message: error.message,//text or html
					autoHideDuration: 2000,//ms
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'warning'
				}),
			);
		});

	} catch (e) {
	}
};

export const UserNcicUpdate = (data) => async dispatch => {
	try {
		await axios.post(`admin/api/users/userNcicEdit`, encrypt(JSON.stringify(data)))
			.then(response => {
				if (response.status == 200) {
					response.data = JSON.parse(decrypt(response.data));
					dispatch(verifyUserSession());
					dispatch(showMessage({
						message: response.data.result.message,
						autoHideDuration: 3000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
					return dispatch(setUserNcicCrendentails(response.data.result));
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))
				}
			}).catch(error => {
				return dispatch(
					showMessage({
						message: error.message,//text or html
						autoHideDuration: 2000,//ms
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'
					}),
				);
			});

	} catch (e) {
	}
};

//Changes For Change Password API 07-11-2022 by Dhirubhai Kulkarni 
export const ChangedPassword = (data, isDialog) => async dispatch => {
	try {
		await axios.post(`admin/api/users/changePassword`, encrypt(JSON.stringify(data)))
			.then(response => {
				if (response.status === 200) {
					response.data = JSON.parse(decrypt(response.data));
					if (response.data.code === "Success") {
						if (isDialog === true) {
							dispatch(showMessage({
								message: response.data.message, autoHideDuration: 3000,
								anchorOrigin: {
									vertical: 'top',
									horizontal: 'right'
								},
								variant: 'success'
							}));
						}
						if (isDialog === false) {
							dispatch(setChangePasswordConfirmation(true));
							//JwtService.logout()
						}
						return dispatch(setChangePassword(true))

					}
					else {
						dispatch(showMessage({
							message: response.data.message, autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}));
					}
				}
				else {
					response = JSON.parse(decrypt(response.response.data));
					dispatch(showMessage({
						message: response.data.message,
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))

				}
			});
	} catch (e) {
		return console.error(e.message);
	}
}


//Added 02-02-24 by RD 

export const UpdateRPSAuthentication = (body, agencyCode, userId) => async dispatch => {
	try {
		dispatch(setLoading(true))
		await axios.post(`admin/api/users/VarifyAndLoginRpsUser/${agencyCode}/${userId}`, encrypt(JSON.stringify(body)))
			.then(response => {
				if (response.status === 200) {
					dispatch(setLoading(false))
					response.data = JSON.parse(decrypt(response.data));
					dispatch(showMessage({
						message: response.data.data, autoHideDuration: 3000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'success'
					}));
					return dispatch(setRPSAuthDialogFlag(true));
				}
				else {
					dispatch(setLoading(false))
					dispatch(setRPSAuthDialogFlag(false));
					return dispatch(showMessage({
						message: "RPS credentials failed",
						autoHideDuration: 2000,
						anchorOrigin: {
							vertical: 'top',
							horizontal: 'right'
						},
						variant: 'warning'

					}))

				}
			});
	} catch (e) {
		dispatch(setLoading(false))
		dispatch(setRPSAuthDialogFlag(false));
		return dispatch(showMessage({
			message: e.message,
			autoHideDuration: 2000,
			anchorOrigin: {
				vertical: 'top',
				horizontal: 'right'
			},
			variant: 'warning'

		}))
	}
}


export const updateConfirmationDlg = () => async dispatch => {
	return dispatch(setChangePasswordConfirmation(false));
}

export const updateChangePassword = () => async dispatch => {
	return dispatch(setChangePassword(false));
}

const initialState = {
	role: [], // guest
	loggedIn: false,
	data: {
		id: '',
		displayName: '',
		profilePic: 'assets/images/avatars/profile.jpg',
		email: '',
		shortcuts: ['calendar', 'mail', 'contacts', 'todo'],
		cardView: false
	},
	userName: "",
	selectedUser: [],
	passwordChanged: false,
	setMfaType: [],
	setDefaultAgency: [],
	ChangePassword: false,
	ChangePasswordConfirmationDlg: false,
	defaultlocation: [],
	nciccrendentails: [],
	RPSAuthDialogFlag: false,
	isLoading: false,
};

const userSlice = createSlice({
	name: 'auth/user',
	initialState,
	reducers: {
		setUser: (state, action) => action.payload,
		userLoggedOut: (state, action) => action.payload,
		userVerify: (state, action) => action.payload,
		setUserName: (state, action) => {
			state.userName = action.payload;
		},
		setUserDetails: (state, action) => {
			state.selectedUser = action.payload;
		},
		setUserMFADetails: (state, action) => {
			state.setMfaType = action.payload;
		},
		setPasswordChanged: (state, action) => {
			state.passwordChanged = action.payload;
		},
		setDefaultAgency: (state, action) => {
			state.setDefaultAgency = action.payload;
		},
		setChangePassword: (state, action) => {
			state.ChangePassword = action.payload;
		},
		setChangePasswordConfirmation: (state, action) => {
			state.ChangePasswordConfirmationDlg = action.payload;
		},
		setUserDefaultLocation: (state, action) => {
			state.defaultlocation = action.payload;
		},
		setRPSAuthDialogFlag: (state, action) => {
			state.RPSAuthDialogFlag = action.payload;
		},
		setUserNcicCrendentails: (state, action) => {
			state.nciccrendentails = action.payload;
		},
		setLoading: (state, action) => {
			state.isLoading = action.payload;
		},

	},
	extraReducers: {}
});

export const { setUser, setUserDefaultLocation, setUserNcicCrendentails, userLoggedOut, userVerify, setUserName, setUserDetails, setUserMFADetails, setPasswordChanged, setDefaultAgency, setChangePassword, setChangePasswordConfirmation, setRPSAuthDialogFlag, setLoading } = userSlice.actions;

export default userSlice.reducer;