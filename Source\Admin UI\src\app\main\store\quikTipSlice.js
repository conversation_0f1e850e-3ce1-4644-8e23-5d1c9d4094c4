import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';
import { getStatus } from '../utils/utils';
import moment from "moment";


export const getTipTypes = (sortField, sortDirection, pageIndex, pageLimit, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`quiktipAdmin/api/quiktip/tipTypes/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${code}`)
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(GetTipTypeSuccess(response.data.tipTypesList))
                    dispatch(setTipTypeTotalCount(response.data.totalCount));
                    dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                dispatch(ErrorTipType(error));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );

            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const DeleteTipType = (id, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.delete(`quiktipAdmin/api/quiktip/tipTypes/${id}/${code}`)
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        })
                    );
                    dispatch(getTipTypes("name", "asc", 0, 100, code));
                }
                else {
                    dispatch(getTipTypes("name", "asc", 0, 100, code));
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
};

export const AddUpdateTipType = data => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.post(`quiktipAdmin/api/quiktip/tipTypes/`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        })
                    );
                    dispatch(getTipTypes("name", "asc", 0, 100, data.code));
                    dispatch(setLoading(false));
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(getTipTypes("name", "asc", 1, 100, data.code));

                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
    }
};


export const getViewTips = (sortField, sortDirection, pageIndex, pageLimit, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.get(`quiktipAdmin/api/quiktip/getViewTips/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${code}`)
            .then(response => {
                if (response.status === 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setViewTipsTotalCount(listData.totalCount));
                    dispatch(setLoading(false));
                    return dispatch(GetViewTipSuccess(listData.viewTipsList));
                }
                else {
                    dispatch(GetViewTipSuccess([]));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(GetViewTipSuccess([]));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
        dispatch(GetViewTipSuccess([]));
        return console.error(e.message);
    }
};

export const getSearchViewTips = (data) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.post(`quiktipAdmin/api/quiktip/getSearchViewTips`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status === 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setViewTipsTotalCount(listData.totalCount));
                    dispatch(setLoading(false));
                    return dispatch(GetViewTipSuccess(listData.viewTipsList));
                }
                else {
                    dispatch(GetViewTipSuccess([]));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(GetViewTipSuccess([]));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
        dispatch(GetViewTipSuccess([]));
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};


export const getQuikTipDepartment = (code) => async dispatch => {
    try {
        await axios
            .get(`quiktipAdmin/api/quiktip/getQuickTipDepartment/${code}`)
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setDepartmentData(response.data))
                }
                else {
                    dispatch(setDepartmentData([]))
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
            .catch(error => {
                dispatch(setDepartmentData([]))
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
        dispatch(setDepartmentData([]))
        return console.error(e.message);
    }
};

////////////////-----------Change Staus Ans Department in quiktip details Section----------////////////////////
export const changeStatus = (data) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.post(`quiktipAdmin/api/quiktip/changeStatus/`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    let deptName;
                    let status = getStatus(response.data.quikTipData.Status)
                    let CreatedDate = moment(new Date(response.data.quikTipData.CreatedDate)).format(
                        "MM/DD/YY, HH:mm A"
                    )
                    if (data.departmentValue != "" && response.data.QuikTipsDepartmentData.length > 0) {
                        deptName = response.data.QuikTipsDepartmentData.filter(data => data._id === response.data.quikTipData.DepartmentID)[0].name
                    }
                    else {
                        deptName = ""
                    }
                    response.data.quikTipData.Department = deptName
                    response.data.quikTipData.Status = status
                    response.data.quikTipData.CreatedDate = CreatedDate
                    dispatch(SetQuikTipDetail(response.data.quikTipData))
                    dispatch(
                        showMessage({
                            message: response.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        })
                    );
                    dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
    }
};

////////////////-----------Notification Section----------////////////////////
export const getNotificatios = (sortField, sortDirection, pageIndex, pageLimit, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.get(`quiktipAdmin/api/quiktip/notificationMessage/getNotifications/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${code}`)
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(NotificationSuccess(response.data.notificationData));
                    dispatch(NotificationSuccessTotalCount(response.data.notificationCount));
                    dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );

            });
    } catch (e) {
        dispatch(setLoading(false));
        return dispatch(
            showMessage({
                message: e.message, //text or html
                autoHideDuration: 2000, //ms
                anchorOrigin: {
                    vertical: 'top',
                    horizontal: 'right'
                },
                variant: 'warning'
            })
        );
    }
};

export const sendNotifications = (data, sortField, sortDirection, pageIndex, pageLimit) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.post(`quiktipAdmin/api/quiktip/notificationMessage/sendNotification/`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        })
                    );
                    dispatch(NotificationSuccess(response.data.notificationData));
                    dispatch(NotificationSuccessTotalCount(response.data.notificationCount));
                    dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
    }
};


////////////////-----------Comment Section----------////////////////////
export const addComments = (data) => async dispatch => {
    dispatch(setLoading(true));
    try {
        if (data.fileData === null) {
            data.isAttachment = false;
            data.isInitialAttachment = false;
            await axios.post(`quiktipAdmin/api/quiktip/comments/AddComment/`, encrypt(JSON.stringify(data)))
                .then(response => {
                    if (response.status == 200) {
                        response.data = JSON.parse(decrypt(response.data));
                        dispatch(
                            showMessage({
                                message: response.data.message,
                                autoHideDuration: 2000,
                                anchorOrigin: {
                                    vertical: 'top',
                                    horizontal: 'right'
                                },
                                variant: 'success'
                            })
                        );
                        dispatch(CommentSuccess(response.data.commentsData.reverse()));
                        dispatch(CommentSuccessTotalCount(response.data.commentsCount));
                        dispatch(setLoading(false));
                    }
                    else {
                        response = JSON.parse(decrypt(response.response.data));
                        dispatch(showMessage({
                            message: response.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'warning'

                        }))
                        dispatch(setLoading(false));
                    }
                })
                .catch(error => {
                    dispatch(setLoading(false));
                    return dispatch(
                        showMessage({
                            message: error.message, //text or html
                            autoHideDuration: 2000, //ms
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'warning'
                        })
                    );
                });
        }
        else {
            await axios.post(`fileupload/uploadAttachment/QuikTip/${data.TipID}/${data.code}`, data.fileData, {

            }).then(async response => {
                data.attachmentUrl = response.data.Location
                //data response.data[0].FileKey
                data.attachmentType = response.data.AttachmentType
                data.isAttachment = true;
                data.isInitialAttachment = false;
                await axios.post(`quiktipAdmin/api/quiktip/comments/AddComment/`, encrypt(JSON.stringify(data)))
                    .then(response => {
                        if (response.status == 200) {
                            response.data = JSON.parse(decrypt(response.data));
                            dispatch(
                                showMessage({
                                    message: response.data.message,
                                    autoHideDuration: 2000,
                                    anchorOrigin: {
                                        vertical: 'top',
                                        horizontal: 'right'
                                    },
                                    variant: 'success'
                                })
                            );
                            dispatch(CommentSuccess(response.data.commentsData.reverse()));
                            dispatch(CommentSuccessTotalCount(response.data.commentsCount));
                            dispatch(setLoading(false));
                        }
                        else {
                            response = JSON.parse(decrypt(response.response.data));
                            dispatch(showMessage({
                                message: response.data.message,
                                autoHideDuration: 2000,
                                anchorOrigin: {
                                    vertical: 'top',
                                    horizontal: 'right'
                                },
                                variant: 'warning'

                            }))
                            dispatch(setLoading(false));
                        }
                    })
                    .catch(error => {
                        dispatch(setLoading(false));
                        return dispatch(
                            showMessage({
                                message: error.message, //text or html
                                autoHideDuration: 2000, //ms
                                anchorOrigin: {
                                    vertical: 'top',
                                    horizontal: 'right'
                                },
                                variant: 'warning'
                            })
                        );
                    });
            });
        }

    } catch (e) {
    }
};

export const updateComment = (data) => async dispatch => {
    dispatch(UpdateCommentSuccess(data));
}

export const getComments = (pageIndex, pageLimit, tipID, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.get(`quiktipAdmin/api/quiktip/comments/GetCommentByTipId/${pageIndex}/${pageLimit}/${tipID}/${code}`)
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(CommentSuccess(response.data.commentsData.reverse()));
                    dispatch(CommentSuccessTotalCount(response.data.commentsCount));
                    dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );

            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getTipDetailByTipIdForAdmin = (tipID, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.get(`quiktipAdmin/api/quiktip/getTipDetailByTipIdForAdmin/${tipID}/${code}`)
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(AttchementSuccess(response.data.commentsFiltered));
                    dispatch(setLoading(false));
                }
                else {
                    dispatch(AttchementError());
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(AttchementError());
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );

            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};


////////////////-----------Attachment Section----------////////////////////
export const getAttachments = (tipID, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.get(`quiktipAdmin/api/quiktip/getAttachments/${tipID}/${code}`)
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    response.data.AttachmentsList.map(x => dispatch(AttchementSuccess(x)))
                    dispatch(setLoading(false));
                }
                else {
                    dispatch(AttchementError());
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(AttchementError());
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );

            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};


const initialState = {
    tipTypesdata: [],
    viewTipData: [],
    departmentData: [],
    isloading: false,
    quikTipDetail: [],
    notificationData: [],
    commentData: [],
    attachementData: [],
    commentflag: false
};

const quikTipSlice = createSlice({
    name: 'quiktip',
    initialState,
    reducers: {
        GetViewTipSuccess: (state, action) => {
            state.viewTipData = action.payload;
        },
        setDepartmentData: (state, action) => {
            state.departmentData = action.payload;
        },

        GetTipTypeSuccess: (state, action) => {
            state.tipTypesdata = action.payload;
        },
        setTipTypeTotalCount: (state, action) => {
            state.tipTypesTotalCount = action.payload;
        },
        setViewTipsTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },

        ErrorTipType: (state, action) => {
            state.tipTypesdata = [];
        },

        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        SetQuikTipDetail: (state, action) => {
            state.quikTipDetail = action.payload;
        },
        NotificationSuccess: (state, action) => {
            state.notificationData = action.payload;
        },
        NotificationSuccessTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        NotificationError: (state, action) => {
            state.notificationData = [];
        },
        CommentSuccess: (state, action) => {
            state.commentData = action.payload;
            state.commentflag = true;
        },
        UpdateCommentSuccess: (state, action) => {
            state.commentData = [...state.commentData, action.payload];
            state.commentflag = true;
        },
        CommentSuccessTotalCount: (state, action) => {
            state.commentsTotalCount = action.payload;
        },
        CommentError: (state, action) => {
            state.commentData = [];
        },
        AttchementSuccess: (state, action) => {

            state.attachementData = action.payload;
        },
        AttchementError: (state, action) => {
            state.attachementData = [];
        },
    },
    extraReducers: {}
});

export const {
    GetViewTipSuccess,
    GetTipTypeSuccess,
    setLoading,
    ErrorTipType,
    setTipTypeTotalCount,
    setViewTipsTotalCount,
    setDepartmentData,
    SetQuikTipDetail,
    NotificationSuccess,
    NotificationSuccessTotalCount,
    NotificationError,
    CommentSuccess,
    CommentSuccessTotalCount,
    CommentError,
    AttchementSuccess,
    AttchementError,
    UpdateCommentSuccess
} = quikTipSlice.actions;

export default quikTipSlice.reducer;
