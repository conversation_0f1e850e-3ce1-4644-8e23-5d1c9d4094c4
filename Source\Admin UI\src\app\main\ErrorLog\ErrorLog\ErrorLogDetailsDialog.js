import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import IconButton from "@mui/material/IconButton";
import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import { useDispatch } from 'react-redux';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import IsResolvedDialog from './isResolvedDialog';

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialogContent-root': {
        padding: theme.spacing(2),
    },
    '& .MuiDialogActions-root': {
        padding: theme.spacing(1),
    },
}));

const ErrorLogDetailsDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation('laguageConfig');
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState('');
    const [pagingDetails, setPagingDetails] = React.useState(null);
    const isResolvedRef = useRef();

    const lableStyle = {
        fontSize: "17px",
        fontWeight: "700"
    }

    const valueStyle = {
        fontSize: "15px",
        fontWeight: "400"
    }

    const handleClickOpen = () => {
        setOpen(true);
    };

    useImperativeHandle(ref, () => ({
        openErrorDetails(data, PagingDetails) {
            setData(data);
            setPagingDetails(PagingDetails);
            handleClickOpen();
        }
    }))

    const handleClose = () => {
        setOpen(false);
    };

    const handleResolveIssue = () => {
        let body = [data._id]
        isResolvedRef.current.openErrorDetails(body, pagingDetails)
        handleClose();
    }

    return (
        <div>
            <BootstrapDialog
                onClose={handleClose}
                aria-labelledby="customized-dialog-title"
                open={open}
                maxWidth='md'
            >
                <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
                    {t("errorDetails")}
                </DialogTitle>
                <IconButton
                    aria-label="close"
                    onClick={handleClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: (theme) => theme.palette.grey[500],
                    }}
                >
                    <CloseIcon />
                </IconButton>
                <DialogContent dividers>
                    <Typography gutterBottom>
                        <label style={lableStyle}>{t("agencyCode")}: </label>
                        <label style={valueStyle}>{data.AgencyCode}</label>
                    </Typography>
                    <Typography gutterBottom>
                        <label style={lableStyle}>{t("method")}: </label>
                        <label style={valueStyle}>{data.Method}</label>
                    </Typography>
                    <Typography gutterBottom>
                        <label style={lableStyle}>{t("timestamp")}: </label>
                        <label style={valueStyle}>{data.Timestamp}</label>
                    </Typography>
                    <Typography gutterBottom>
                        <label style={lableStyle}>{t("source")}: </label>
                        <label style={valueStyle}>{data.Source}</label>
                    </Typography>
                    <Typography gutterBottom>
                        <label style={lableStyle}>{t("message")}: </label>
                        <label style={valueStyle}>{data.Message}</label>
                    </Typography>
                    <Typography gutterBottom>
                        <label style={lableStyle}>{t("stackTrace")}: </label>
                        <label style={valueStyle}>{data.StackTrace}</label>
                    </Typography>
                    {data.ResolvedBy != undefined &&
                        <Typography gutterBottom>
                            <label style={lableStyle}>{t("resolvedBy")}: </label>
                            <label style={valueStyle}>{data.ResolvedBy}</label>
                        </Typography>
                    }
                    {data.ResolvedAt != undefined &&
                        <Typography gutterBottom>
                            <label style={lableStyle}>{t("resolvedAt")}: </label>
                            <label style={valueStyle}>{data.ResolvedAt}</label>
                        </Typography>
                    }
                </DialogContent>
                <DialogActions>
                    <div className='flex justify-end'>
                        {!data.isResolved &&
                            < Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                type="button"
                                onClick={handleResolveIssue}
                                size="large"
                            >
                                {t("resolve")}
                            </Button>
                        }
                        <Button
                            variant="contained"
                            color="secondary"
                            className=" w-auto mt-8"
                            aria-label="Register"
                            type="button"
                            size="large"
                            onClick={handleClose}
                        >
                            {t("close")}
                        </Button>
                    </div>
                </DialogActions>
            </BootstrapDialog>
            <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="IsResolvedDialog" />} onReset={() => { }} >
                <IsResolvedDialog ref={isResolvedRef} />
            </ErrorBoundary>
        </div >
    );
});
export default ErrorLogDetailsDialog;

