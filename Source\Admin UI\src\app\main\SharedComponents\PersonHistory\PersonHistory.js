import "./PersonHistory.css";
import { useSelector } from 'react-redux';
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import Divider from '@mui/material/Divider';
import ListItemText from '@mui/material/ListItemText';
import { convertObjectToStringValue } from "../../utils/utils";

function PersonHistory() {
    const personHistoryData = useSelector(
        ({ dispatchCallReducer }) => dispatchCallReducer.personIncident.personHistoryData
    );

    function diplayValue(item) {
        let obj = {}
        for (let i = 0; i < item.fieldChanged.length; i++) {
            obj[item.fieldChanged[i]] = item.oldValue[i];
        }
        let stringData = convertObjectToStringValue(obj);
        return stringData        
    }  

    return (
        <div>
            {personHistoryData.length > 0 &&
                <Card>
                    <CardContent >
                        <List sx={{
                            width: '100%',
                            bgcolor: 'background.paper',
                            position: 'relative',
                            overflow: 'auto',
                            maxHeight: 250,
                            '& ul': { padding: 0 },
                        }}
                        >
                            {personHistoryData && personHistoryData.map((item, i) =>
                                <ListItem className='pointer' display='flex'>
                                    <div>
                                        <ListItemText display="block" primary={new Date(item.ModifiedDate).toUTCString().split('GMT')}
                                        primaryTypographyProps={{
                                            fontWeight: 700,
                                            fontSize: 14,
                                        
                                          }} />
                                        <ListItemText primary={diplayValue(item)} 
                                        primaryTypographyProps={{
                                            fontWeight: 500,
                                            fontSize: 14,
                                           
                                          }}/>
                                        <Divider />
                                    </div>
                                </ListItem>
                            )}
                        </List>
                    </CardContent>
                </Card>
            }
        </div>
    )
}

export default PersonHistory;