import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import Button from '@mui/material/Button';
import { Checkbox, DialogActions, FormControlLabel, Grid, TextField, } from "@mui/material";
import { createViolationClassification } from '../store/violationClassificationSlice';

const defaultValues = {
    name: '',
    minSentence: '',
    maxSentence: '',
    sentenceType: '',
    minFines: '',
    maxFines: '',
};

const AddClassification = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [title, setTitle] = React.useState("");
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [life, setLife] = React.useState(false);
    const [maximum, setMaximum] = React.useState(false);

    const selectedStateCode = useSelector(({ administration }) => administration.stateViolationSlice.selectedStateCode);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues
    });
    const { isValid, dirtyFields, errors } = formState;

    useImperativeHandle(ref, () => ({
        handleOpen(flag, title, data) {
            setIsUpdate(flag);
            setTitle(title);
            setClassificationvalue(data);
            handleClickOpen();
        },
    }));

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
        setIsUpdate(false);
        setLife(false);
        setMaximum(false);
        reset();
        defaultValues;
    };

    const handleCheckboxChange = () => {
        setLife(!life);
    };

    const handleMaximumCheckboxChange = () => {
        setMaximum(!maximum);
    };

    const setClassificationvalue = async (data) => {
        if (data !== undefined) {
            setValue('_id', data._id);
            setValue('classification', data.Classification);
            setValue('name', data.Name);
            setValue('minSentence', data.MinSentence);
            setValue('maxSentence', data.MaxSentence);
            setValue('sentenceType', data.SentenceType);
            setValue('minFines', data.MinFines);
            setValue('maxFines', data.MaxFines);
            setLife(data.Life === 1 ? true : false);
            setMaximum(data.NoMaximumFine === 1 ? true : false);
        }
    };

    function onSubmit(model) {
        let body = {}
        const items = {
            Classification: model.classification,
            Name: model.name,
            MinSentence: model.minSentence,
            MaxSentence: model.maxSentence,
            SentenceType: model.sentenceType,
            MinFines: model.minFines,
            MaxFines: model.maxFines,
            Life: life === true ? 1 : 0,
            NoMaximumFine: maximum === true ? 1 : 0,
        }
        if (isUpdate) {
            body.isUpdate = true;
            items._id = model._id;
        } else {
            body.isUpdate = false;
        }
        body.data = items;
        dispatch(createViolationClassification(body, selectedStateCode));
        handleClose();
    };

    return (
        <div>
            <Dialog
                fullWidth={true} maxWidth='md' open={open} onClose={handleClose} aria-labelledby="responsive-dialog-title" >
                <DialogTitle style={{ display: "flex", justifyContent: "space-between" }} id="responsive-dialog-title">
                    <div>
                        {t(`${title}`)} {t("violationClassification")}
                    </div>
                    <div>
                        <IconButton onClick={handleClose} aria-label="show more">
                            <Icon>close</Icon>
                        </IconButton>
                    </div>
                </DialogTitle>
                <form name="registerForm" noValidate className="flex flex-col justify-center w-full pb-16" onSubmit={handleSubmit(onSubmit)} autoSave={false} >
                    <DialogContent dividers>
                        <Grid container spacing={1} >
                            <Grid item xs={12} sm={5} md={5} lg={5} xl={5}>
                                <Controller
                                    name="classification"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mb-16 w-full"
                                            label={t("classification")}
                                            type="text"
                                            error={!!errors.type}
                                            helperText={errors?.type?.message}
                                            variant="outlined"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} sm={7} md={7} lg={7} xl={7}>
                                <Controller
                                    name="name"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mb-16 w-full"
                                            label={t("name")}
                                            type="text"
                                            error={!!errors.type}
                                            helperText={errors?.type?.message}
                                            variant="outlined"
                                        />
                                    )}
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={1} >
                            <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                <Controller
                                    name="minSentence"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mb-16 w-full"
                                            label={t("minSentence")}
                                            type="number"
                                            error={!!errors.type}
                                            helperText={errors?.type?.message}
                                            variant="outlined"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                <Controller
                                    name="maxSentence"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mb-16 w-full"
                                            label={t("maxSentence")}
                                            type="number"
                                            error={!!errors.name}
                                            helperText={errors?.name?.message}
                                            variant="outlined"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} sm={4} md={4} lg={4} xl={4}>
                                <Controller
                                    name="sentenceType"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mb-16 w-full"
                                            label={t("sentenceType")}
                                            type="text"
                                            error={!!errors.name}
                                            helperText={errors?.name?.message}
                                            variant="outlined"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={life}
                                            onChange={() => handleCheckboxChange()}
                                        />
                                    }
                                    label={t("life")}
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={1} >
                            <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                <Controller
                                    name="minFines"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mb-16 w-full"
                                            label={t("minFines")}
                                            type="number"
                                            error={!!errors.type}
                                            helperText={errors?.type?.message}
                                            variant="outlined"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                <Controller
                                    name="maxFines"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mb-16 w-full"
                                            label={t("maxFines")}
                                            type="number"
                                            error={!!errors.name}
                                            helperText={errors?.name?.message}
                                            variant="outlined"
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} sm={4} md={4} lg={4} xl={4}>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={maximum}
                                            onChange={() => handleMaximumCheckboxChange()}
                                        />
                                    }
                                    label={t("noMaximumFine")}
                                />
                            </Grid>
                        </Grid>
                    </DialogContent>
                    <DialogActions>
                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>
                    </DialogActions>
                </form>
            </Dialog>
        </div>
    );

});

export default AddClassification;