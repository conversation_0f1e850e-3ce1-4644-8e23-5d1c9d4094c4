import React, { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { Controller, useForm } from "react-hook-form";
import FormControl from "@mui/material/FormControl";
import { Grid, TextField } from "@mui/material";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import "./AddPersons.css";
import { checkValueEmptyOrNull, isEmptyOrNull } from "../../utils/utils";
import Button from "@mui/material/Button";
import { useLocation, useParams } from "react-router";
import history from "@history";
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import CommonAddressReactAutoComplete from "../../SharedComponents/SharedFormFields/CommonAddressReactAutoComplete";
import { SavePerson, UpdatePerson } from "../../administration/store/personMasterSlice";
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';
import { DateTimePicker } from "@mui/x-date-pickers";
import { getAgencyDetailsWithCode } from "../../agencyPage/store/agencySlice";

const schema = yup.object().shape({
  Age: yup.string().required('Please enter Age.'),
  PhoneNumber: yup.string().required('Please enter Phone Number.'),
  Email: yup.string().required('Please enter Email.'),
  FName: yup.string().required('Please enter First Name.'),
  MName: yup.string().required('Please enter Middle Name.'),
  LName: yup.string().required('Please enter Last Name.'),
});

const defaultValues = {
  LName: '',
  FName: '',
  MName: '',
  Age: '',
  PhoneNumber: '',
  Email: '',
  ResidenceAdd_Number: '',
  ResidenceAddNum_Suf: '',
  ResidenceSt_PreDir: '',
  ResidenceSt_Name: '',
  ResidenceSt_PosTyp: '',
  ResidenceSt_PosDir: '',
  ResidenceBuilding: '',
  ResidencePlaceType: '',
  ResidenceUnit: '',
  ResidenceFloor: '',
  ResidencePost_Comm: '',
  ResidenceState: '',
  ResidenceCounty: '',
  ResidencePost_Code: '',
  ResidencePostCodeEx: '',
  OfficeAdd_Number: '',
  OfficeAddNum_Suf: '',
  OfficeSt_PreDir: '',
  OfficeSt_Name: '',
  OfficeSt_PosTyp: '',
  OfficeSt_PosDir: '',
  OfficeBuilding: '',
  OfficePlaceType: '',
  OfficeUnit: '',
  OfficeFloor: '',
  OfficePost_Comm: '',
  OfficeState: '',
  OfficeCounty: '',
  OfficePost_Code: '',
  OfficePostCodeEx: '',
};

function PersonData(props) {
  const { t } = useTranslation("laguageConfig");
  const dispatch = useDispatch();
  const routeParams = useParams();
  const location = useLocation();
  const { tempData } = location.state || {};

  const dropdownTypes = useSelector(({ agency }) => agency.agency.dropdownTypes);


  const { control, setValue, formState, handleSubmit, reset, trigger, setError, } = useForm({
    mode: "onChange",
    defaultValues,
    resolver: yupResolver(schema),
  });

  const [personIdValue, setPersonIdValue] = React.useState(0);
  const [date, setDate] = useState(new Date());
  const [idTypeValue, setIdTypeValue] = React.useState("");
  const [emailTypeValue, setEmailTypeValue] = React.useState("");
  const [phoneNumberTypeTypeValue, setPhoneNumTypeValue] = React.useState("");
  const [residenceIdValue, setResidenceIdValue] = React.useState(null);
  const [officeIdValue, setOfficeIdValue] = React.useState(null);
  const lastNameInputRef = useRef(null);

  const handleDate = (event) => {
    setDate(event);
    const calculatedAge = calculateAge(event);
    setValue("Age", calculatedAge);
  }

  const calculateAge = (dob) => {
    if (!dob) return '';
    const diff = new Date() - new Date(dob);
    const ageInYears = Math.floor(diff / (1000 * 60 * 60 * 24 * 365.25));
    return ageInYears.toString();
  };

  useEffect(() => {
    if (routeParams.id !== "0" && tempData?.data !== null) {
      setControlValue(tempData?.data);
    }
    //For Address state and county to search address
    dispatch(getAgencyDetailsWithCode(routeParams.code));
  }, [routeParams, tempData]);

  const onChangePhoneType = (event, newValue) => {
    setPhoneNumTypeValue(newValue);
  };

  const onChangeEmailType = (event, newValue) => {
    setEmailTypeValue(newValue);
  };

  const onChangeIdType = (event, newValue) => {
    setIdTypeValue(newValue);
  };

  function handleClose() {
    ClearAll();
  }

  const setControlValue = (data) => {
    setPersonIdValue(data._id);
    setValue("LName", data.LastName);
    setValue("FName", data.FirstName);
    setValue("MName", data.MiddleName);
    setValue("Suffix", data.Suffix);
    setValue("Age", data.Age);
    setValue("Race", data.Race);
    setValue("Sex", data.Sex);
    setValue("Ethnicity", data.Ethnicity);
    setValue("Height", data.Height);
    setValue("Weight", data.Weight);
    setValue("Hair", data.Hair);
    setValue("Eyes", data.Eyes);
    setValue("PhoneNumber", data.PhoneNumber);
    setValue("IdNumber", data.IdNumber);
    setValue("Email", data.Email);
    setDate(data.DateOfBirth ? new Date(data.DateOfBirth) : null);

    const idTypeValue = dropdownTypes.Iddata && dropdownTypes.Iddata.find(x => x.name === data.IdType);
    setIdTypeValue(idTypeValue);
    const emailType = dropdownTypes.Emaildata && dropdownTypes.Emaildata.find(x => x.name === data.EmailType);
    setEmailTypeValue(emailType);
    const phoneNumberType = dropdownTypes.Phonedata && dropdownTypes.Phonedata.find(x => x.name === data.PhoneNumberType);
    setPhoneNumTypeValue(phoneNumberType);
    const residenceAddress = data.ResidenceAddress;
    setResidentialAddress(residenceAddress);
    const officeAddress = data.OfficeAddress;
    setOfficeAddress(officeAddress);
  };

  const ClearAll = () => {
    setValue("LName", "");
    setValue("FName", "");
    setValue("MName", "");
    setValue("Suffix", "");
    setValue("Age", "");
    setValue("Race", "");
    setValue("Sex", "");
    setValue("Ethnicity", "");
    setValue("Height", "");
    setValue("Weight", "");
    setValue("Hair", "");
    setValue("Eyes", "");
    setValue("PhoneNumber", "");
    setValue("IdNumber", "");
    setValue("Email", "");
    setDate(new Date());
    setIdTypeValue("");
    setEmailTypeValue("");
    setPhoneNumTypeValue("");
    setOfficeAddress();
    setResidentialAddress();
  };

  const setOfficeAddress = (data) => {
    setOfficeIdValue(data?._id ?? "");
    setValue("OfficeAdd_Number", data?.Add_Number ?? '');
    setValue("OfficeAddNum_Suf", data?.AddNum_Suf ?? '');
    setValue("OfficeSt_PreDir", data?.St_PreDir ?? '');
    setValue("OfficeSt_Name", data?.St_Name ?? '');
    setValue("OfficeSt_PosTyp", data?.St_PosTyp ?? '');
    setValue("OfficeSt_PosDir", data?.St_PosDir ?? '');
    setValue("OfficeBuilding", data?.Building ?? '');
    setValue("OfficePlaceType", data?.PlaceType ?? '');
    setValue("OfficeUnit", data?.Unit ?? '');
    setValue("OfficeFloor", data?.Floor ?? '');
    setValue("OfficePost_Comm", data?.Post_Comm ?? '');
    setValue("OfficeState", data?.State ?? '');
    setValue("OfficeCounty", data?.County ?? '');
    setValue("OfficePost_Code", data?.Post_Code ?? '');
    setValue("OfficePostCodeEx", data?.PostCodeEx ?? '');
  };

  const setResidentialAddress = (data) => {
    setResidenceIdValue(data?._id ?? "");
    setValue("ResidenceAdd_Number", data?.Add_Number ?? '');
    setValue("ResidenceAddNum_Suf", data?.AddNum_Suf ?? '');
    setValue("ResidenceSt_PreDir", data?.St_PreDir ?? '');
    setValue("ResidenceSt_Name", data?.St_Name ?? '');
    setValue("ResidenceSt_PosTyp", data?.St_PosTyp ?? '');
    setValue("St_PosDir", data?.St_PosDir ?? '');
    setValue("ResidenceBuilding", data?.Building ?? '');
    setValue("ResidencePlaceType", data?.PlaceType ?? '');
    setValue("ResidenceUnit", data?.Unit ?? '');
    setValue("ResidenceFloor", data?.Floor ?? '');
    setValue("ResidencePost_Comm", data?.Post_Comm ?? '');
    setValue("ResidenceState", data?.State ?? '');
    setValue("ResidenceCounty", data?.County ?? '');
    setValue("ResidencePost_Code", data?.Post_Code ?? '');
    setValue("ResidencePostCodeEx", data?.PostCodeEx ?? '');
  };

  const onSubmit = (model) => {
    const ResidenceAddress = {
      _id: residenceIdValue,
      Add_Number: checkValueEmptyOrNull(model.ResidenceAdd_Number),
      AddNum_Suf: checkValueEmptyOrNull(model.ResidenceAddNum_Suf),
      St_PreDir: checkValueEmptyOrNull(model.ResidenceSt_PreDir),
      St_Name: checkValueEmptyOrNull(model.ResidenceSt_Name),
      St_PosTyp: checkValueEmptyOrNull(model.ResidenceSt_PosTyp),
      St_PosDir: checkValueEmptyOrNull(model.ResidenceSt_PosDir),
      Building: checkValueEmptyOrNull(model.ResidenceBuilding),
      PlaceType: checkValueEmptyOrNull(model.ResidencePlaceType),
      Unit: checkValueEmptyOrNull(model.ResidenceUnit),
      Floor: checkValueEmptyOrNull(model.ResidenceFloor),
      Post_Comm: checkValueEmptyOrNull(model.ResidencePost_Comm),
      State: checkValueEmptyOrNull(model.ResidenceState),
      County: checkValueEmptyOrNull(model.ResidenceCounty),
      Post_Code: checkValueEmptyOrNull(model.ResidencePost_Code),
      PostCodeEx: checkValueEmptyOrNull(model.ResidencePostCodeEx),
    }

    const OfficeAddress = {
      _id: officeIdValue,
      Add_Number: checkValueEmptyOrNull(model.OfficeAdd_Number),
      AddNum_Suf: checkValueEmptyOrNull(model.OfficeAddNum_Suf),
      St_PreDir: checkValueEmptyOrNull(model.OfficeSt_PreDir),
      St_Name: checkValueEmptyOrNull(model.OfficeSt_Name),
      St_PosTyp: checkValueEmptyOrNull(model.OfficeSt_PosTyp),
      St_PosDir: checkValueEmptyOrNull(model.OfficeSt_PosDir),
      Building: checkValueEmptyOrNull(model.OfficeBuilding),
      PlaceType: checkValueEmptyOrNull(model.OfficePlaceType),
      Unit: checkValueEmptyOrNull(model.OfficeUnit),
      Floor: checkValueEmptyOrNull(model.OfficeFloor),
      Post_Comm: checkValueEmptyOrNull(model.OfficePost_Comm),
      State: checkValueEmptyOrNull(model.OfficeState),
      County: checkValueEmptyOrNull(model.OfficeCounty),
      Post_Code: checkValueEmptyOrNull(model.OfficePost_Code),
      PostCodeEx: checkValueEmptyOrNull(model.OfficePostCodeEx),
    }

    const data = {
      PersonId: personIdValue,
      LName: checkValueEmptyOrNull(model.LName),
      FName: checkValueEmptyOrNull(model.FName),
      MName: checkValueEmptyOrNull(model.MName),
      Suffix: checkValueEmptyOrNull(model.Suffix),
      DOB: checkValueEmptyOrNull(date),
      Age: checkValueEmptyOrNull(model.Age),
      Race: checkValueEmptyOrNull(model.Race),
      Sex: checkValueEmptyOrNull(model.Sex),
      Ethnicity: checkValueEmptyOrNull(model.Ethnicity),
      Height: checkValueEmptyOrNull(model.Height),
      Weight: checkValueEmptyOrNull(model.Weight),
      Hair: checkValueEmptyOrNull(model.Hair),
      Eyes: checkValueEmptyOrNull(model.Eyes),
      IdType: isEmptyOrNull(idTypeValue) ? null : idTypeValue?.name,
      EmailType: isEmptyOrNull(emailTypeValue) ? null : emailTypeValue?.name,
      PhoneNumberType: isEmptyOrNull(phoneNumberTypeTypeValue) ? null : phoneNumberTypeTypeValue?.name,
      PhoneNumber: checkValueEmptyOrNull(model.PhoneNumber),
      IdNumber: checkValueEmptyOrNull(model.IdNumber),
      Email: checkValueEmptyOrNull(model.Email),
      code: routeParams.code,
      ResidenceAddress: ResidenceAddress,
      OfficeAddress: OfficeAddress
    };

    if (data.PersonId != 0) {
      dispatch(UpdatePerson(data, tempData));
    } else {
      dispatch(SavePerson(data, tempData));
    }
    history.push(`/admin/person/${routeParams.code}`);
    handleClose();
  };

  useEffect(() => {
    setTimeout(() => {
      if (lastNameInputRef.current) {
        lastNameInputRef.current.focus();
      }
    }, 100);
  }, [lastNameInputRef]);

  const handleOnOfficeSelectAddress = (data) => {
    setOfficeAddress(data);
  };

  const handleOnOfficeClearAddress = () => {
    setOfficeAddress({});
  };

  const handleOnResidenceSelectAddress = (data) => {
    setResidentialAddress(data);
  };

  const handleOnResidenceClearAddress = () => {
    setResidentialAddress({});
  };

  return (
    <div className="w-full p-16 ">
      <form
        className="flex flex-col justify-center w-full"
        onSubmit={handleSubmit(onSubmit)}
        autoSave={false}
        autoComplete={false}
      >
        <div className="flex ">
          <Controller
            name="LName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                inputRef={lastNameInputRef}
                label={t("lastName")}
                variant="outlined"
                required
                tabIndex={0}
              />
            )}
          />

          <Controller
            name="FName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("firstName")}
                variant="outlined"
                required
              />
            )}
          />

          <Controller
            name="MName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("middleName")}
                variant="outlined"
                required
              />
            )}
          />
          <Controller
            name="Suffix"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("suffix")}
                variant="outlined"
              />
            )}
          />
        </div>
        {/* Start Address Section */}
        <span className="mt-8 mb-16 mx-4">
          {t("residentialAddress")} :
        </span>

        <Grid container spacing={1} className="mb-8">
          <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="ReactAutoCompleteSearch" />} onReset={() => { }}>
              <CommonAddressReactAutoComplete
                onSelect={handleOnResidenceSelectAddress}
                handleClear={handleOnResidenceClearAddress}
              />
            </ErrorBoundary>
          </Grid>
        </Grid>
        <div className="flex">
          <Controller
            name="ResidenceAdd_Number"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("addNumber")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidenceAddNum_Suf"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("numSuffix")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidenceSt_PreDir"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("preDir")}
                variant="outlined"
              />
            )}
          />

          <Controller
            name="ResidenceSt_Name"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("streetName")}
                variant="outlined"
              />
            )}
          />

          <Controller
            name="ResidenceSt_PosTyp"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("streetType")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidenceSt_PosDir"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("postDir")}
                variant="outlined"
              />
            )}
          />
        </div>
        <div className="flex ">
          <Controller
            name="ResidenceBuilding"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("building")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidencePlaceType"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("unitType")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidenceUnit"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("unit")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidenceFloor"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("floor")}
                variant="outlined"
              />
            )}
          />
        </div>
        <div className="flex">
          <Controller
            name="ResidencePost_Comm"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("city")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidenceCounty"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("county")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidenceState"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("state")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidencePost_Code"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("zip")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="ResidencePostCodeEx"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("zipPlus4")}
                variant="outlined"
              />
            )}
          />
        </div>

        <span className="mt-8 mb-16 mx-4">
          {t("officeAddress")} :
        </span>
        <Grid container spacing={1} className="mb-8">
          <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="ReactAutoCompleteSearch" />} onReset={() => { }}>
              <CommonAddressReactAutoComplete
                onSelect={handleOnOfficeSelectAddress}
                handleClear={handleOnOfficeClearAddress}
              />
            </ErrorBoundary>
          </Grid>
        </Grid>
        <div className="flex">
          <Controller
            name="OfficeAdd_Number"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("addNumber")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficeAddNum_Suf"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("numSuffix")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficeSt_PreDir"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("preDir")}
                variant="outlined"
              />
            )}
          />

          <Controller
            name="OfficeSt_Name"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("streetName")}
                variant="outlined"
              />
            )}
          />

          <Controller
            name="OfficeSt_PosTyp"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("streetType")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficeSt_PosDir"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("postDir")}
                variant="outlined"
              />
            )}
          />
        </div>
        <div className="flex ">
          <Controller
            name="OfficeBuilding"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("building")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficePlaceType"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("unitType")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficeUnit"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("unit")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficeFloor"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("floor")}
                variant="outlined"
              />
            )}
          />
        </div>

        <div className="flex">
          <Controller
            name="OfficePost_Comm"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("city")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficeCounty"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("county")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficeState"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("state")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficePost_Code"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("zip")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="OfficePostCodeEx"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("zipPlus4")}
                variant="outlined"
              />
            )}
          />
        </div>
        <div className="flex">
          <Controller
            name="Age"
            control={control}
            render={({ field }) => (
              <DateTimePicker
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                autoOk={true}
                size="medium"
                inputVariant="standard"
                format="MM/dd/yyyy HH:mm"
                margin="normal"
                id="date-picker-inline"
                ampm={false}
                value={date}
                onChange={handleDate}
                KeyboardButtonProps={{
                  "aria-label": "change date",
                }}
                label={t('dateOfBirth')}
                renderInput={(params) => (
                  <TextField {...params} className="w-full mt-8 mb-16 mx-4" />
                )}
              />
            )}
          />

          <Controller
            name="Age"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("age")}
                variant="outlined"
                required
              />
            )}
          />
          <Controller
            name="Race"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("race")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="Sex"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("sex")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="Ethnicity"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("ethnicity")}
                variant="outlined"
              />
            )}
          />
        </div>

        <div className="flex">
          <Controller
            name="Height"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("height")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="Weight"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("weight")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="Hair"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("hair")}
                variant="outlined"
              />
            )}
          />
          <Controller
            name="Eyes"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("eyes")}
                variant="outlined"
              />
            )}
          />
        </div>

        <div className="flex">
          <FormControl sx={{ m: 1, ml: .5, minWidth: 200 }}>

            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsWarrantValue" />} onReset={() => { }}>
              <div>
                <CommonAutocomplete
                  parentCallback={onChangePhoneType}
                  options={dropdownTypes.Phonedata || []}
                  value={phoneNumberTypeTypeValue}
                  fieldName={t("phoneType")}
                  optionLabel={"name"}
                  onKeyDown={handleSelectKeyDown}
                />
              </div>

            </ErrorBoundary>
          </FormControl>

          <Controller
            name="PhoneNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="mt-8 mb-16 mx-4"
                type="text"
                label={t("phoneNumber")}
                variant="outlined"
                required
              />
            )}
          />

          <FormControl sx={{ m: 1, ml: 10, minWidth: 200 }}>

            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsWarrantValue" />} onReset={() => { }}>
              <div>
                <CommonAutocomplete
                  parentCallback={onChangeIdType}
                  options={dropdownTypes.Iddata || []}
                  value={idTypeValue}
                  fieldName={t("idType")}
                  optionLabel={"name"}
                  onKeyDown={handleSelectKeyDown}
                />
              </div>

            </ErrorBoundary>
          </FormControl>
          <Controller
            name="IdNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="mt-8 mb-16 mx-4"
                type="text"
                label={t("idNumber")}
                variant="outlined"
              />
            )}
          />

          <FormControl sx={{ m: 1, ml: 10, minWidth: 200 }}>

            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsWarrantValue" />} onReset={() => { }}>
              <div>
                <CommonAutocomplete
                  parentCallback={onChangeEmailType}
                  options={dropdownTypes.Emaildata || []}
                  value={emailTypeValue}
                  fieldName={t("emailType")}
                  optionLabel={"name"}
                  onKeyDown={handleSelectKeyDown}
                />
              </div>

            </ErrorBoundary>
          </FormControl>
          <Controller
            name="Email"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="mt-8 mb-16 mx-4"
                type="text"
                label={t("email")}
                variant="outlined"
                required
              />
            )}
          />
        </div>

        <div className="flex justify-center">
          <Button
            type="submit"
            variant="contained"
            color="primary"
            className="normal-case m-16"
            aria-label="REGISTER"
            value="legacy"
          >
            {t("save")}
          </Button>
          <Button
            type="button"
            variant="contained"
            color="secondary"
            onClick={() => history.push(`/admin/person/${routeParams.code}`)}
            className="normal-case m-16"
            aria-label="UPDATE"
            value="legacy"
          >
            {t("back")}
          </Button>
        </div>
      </form>
    </div>
  );
}
export default PersonData;
