import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import ShowCuurentLocation from "src/app/main/SharedComponents/ShowLocation/ShowCuurentLocation";
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';

const ShowSelectedLocation = forwardRef((props, ref) => {
    const [open, setOpen] = React.useState(false);
    const [dataValue, setData] = React.useState(null);

    const handleClickOpen1 = () => {
        setOpen(true);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(x) {
            setData(x)
            handleClickOpen1();
        },
    }));

    useEffect(() => {

    }, [dataValue]);

    const handleClose = () => {
        setOpen(false);
    };

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">GPS Location</DialogTitle>
                <DialogContent dividers>
                    {dataValue !== null &&
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_showCuurentLocation" />} onReset={() => window.location.reload()} >
                            <ShowCuurentLocation Latitude={dataValue.Latitude} Longitude={dataValue.Longitude}></ShowCuurentLocation>
                        </ErrorBoundary>
                    }
                </DialogContent>
            </Dialog>
        </div>
    );
});

export default ShowSelectedLocation;