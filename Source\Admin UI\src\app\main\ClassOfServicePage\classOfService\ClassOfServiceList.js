
import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ClassOfServiceListHeader from './ClassOfServiceListHeader';
import ClassOfServiceListTable from './ClassOfServiceListTable';
import ClassOfServiceListCards from './ClassOfServiceListCards';
import { newUserAudit } from '../../../main/userAuditPage/store/userAuditSlice';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';

function ClassOfServiceList() {
	const dispatch = useDispatch();
	const user = useSelector(({ auth }) => auth.user);
	useEffect(() => {
		dispatch(newUserAudit({
			activity: "Access Class Of Service",
			user: user,
			appName: "911 Call",
		}));
		// eslint-disable-next-line
	}, []);

	return (
		<FusePageCarded
			classes={{
				content: 'flex',
				header: 'min-h-72 h-72 sm:h-136 sm:min-h-136'
			}}
			header={
				<ErrorBoundary
					FallbackComponent={(props) => <ErrorPage {...props} componentName="ClassOfServiceListHeader" />} onReset={() => { }}>
					<ClassOfServiceListHeader />
				</ErrorBoundary>
			}
			content={user.data.cardView ?
				<ErrorBoundary
					FallbackComponent={(props) => <ErrorPage {...props} componentName="ClassOfServiceListCards" />} onReset={() => { }}>
					<ClassOfServiceListCards />
				</ErrorBoundary>
				:
				<ErrorBoundary
					FallbackComponent={(props) => <ErrorPage {...props} componentName="ClassOfServiceListTable" />} onReset={() => { }}>
					<ClassOfServiceListTable />
				</ErrorBoundary>

			}

			innerScroll
		/>

	);
}

export default ClassOfServiceList;
