import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import Button from '@mui/material/Button';
import { TextField, } from "@mui/material";
import { saveNFIRS } from '../../store/nfirsSlice';
import { IsCheckMongooseObjectId } from '../../utils/utils';

let update = false;

const defaultValues = {
    type: '',
    name: '',
    description: ''
};

const addnfirs = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState([]);
    const [title, setTitle] = React.useState("");
    const [PagingDetails, setPagingDetails] = React.useState();
    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues
    });
    const { isValid, dirtyFields, errors } = formState;

    const typeInputRef = useRef(null);

    useImperativeHandle(ref, () => ({
        handleOpen(data, PagingDetails, flag, title) {
            setData(data)
            setPagingDetails(PagingDetails)
            setTitle(title);
            update = flag;
            setNFIRSvalue(data);
            handleClickOpen();
        },
    }));

    const handleClickOpen = () => {
        setOpen(true);
        setTimeout(() => {
            typeInputRef.current?.focus();
        }, 0);
    };

    const handleClose = () => {
        setOpen(false);
        defaultValues;
    };

    const setNFIRSvalue = async (data) => {
        setValue('type', data.type);
        setValue('name', data.name);
        setValue('description', data.description);
    };

    function onSubmit(model) {
        const items = {
            type: model.type,
            name: model.name,
            description: model.description
        }
        if (data) {
            var checkObjectId = IsCheckMongooseObjectId(data._id === undefined ? "0" : data._id);
            if (checkObjectId) {
                items.isUpdate = true;
                items._id = data._id;
            }
        }

        dispatch(saveNFIRS(items));
        setOpen(false);
        handleClose();
    }

    return (
        <div>
            <Dialog
                fullWidth={true} maxWidth='md' open={open} onClose={handleClose} aria-labelledby="responsive-dialog-title" >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t(`${title}`)} {t("nfirs")}</DialogTitle>
                <DialogContent dividers>
                    <form name="registerForm" noValidate className="flex flex-col justify-center w-full pb-16" onSubmit={handleSubmit(onSubmit)} autoSave={false} >
                        <Controller
                            name="type"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("type")}
                                    type="text"
                                    inputRef={typeInputRef}
                                    error={!!errors.type}
                                    helperText={errors?.type?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="name"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("name")}
                                    type="text"
                                    error={!!errors.name}
                                    helperText={errors?.name?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="description"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("description")}
                                    type="text"
                                    error={!!errors.description}
                                    helperText={errors?.description?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );

});

export default addnfirs;