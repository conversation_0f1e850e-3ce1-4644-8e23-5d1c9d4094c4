import { createSlice } from "@reduxjs/toolkit";
import { showMessage } from "app/store/fuse/messageSlice";
import { setNavigation } from "app/store/fuse/navigationSlice";
import history from "@history";
import { changeLanguage } from "app/store/i18nSlice";
import { newUserAudit } from "../../main/userAuditPage/store/userAuditSlice";
import jwtService from "../../services/jwtService/jwtService";
import { setUserData } from "./userSlice";
import { isEmptyOrNull } from "src/app/main/utils/utils";
import { getCallTypes } from "src/app/main/911Call/store/call911Slice";
import Cookies from 'js-cookie';
import JwtService from "src/app/services/jwtService";
import axios from 'axios';
import { decrypt, encrypt } from "src/app/security";

export const setmfaType = (mfatype) => async (dispatch) => {
  dispatch(SetMFAType(mfatype));
};

export const getOtp = ({ email, password, mfaType }, deviceLicenceId) => async (dispatch) => {
  dispatch(setLoading(true));
  return jwtService.signInWithEmailAndPassword(email.toLowerCase(), password, mfaType, deviceLicenceId, true)
    .then((user) => {

      if (mfaType === "email") {
        dispatch(
          showMessage({
            message: `OTP sent successfully on ${email}`, autoHideDuration: 3000,
            anchorOrigin: { vertical: "top", horizontal: "right", }, variant: "success",
          })
        );
      }
      else {
        dispatch(
          showMessage({
            message: `OTP sent successfully on your mobile number`, autoHideDuration: 3000,
            anchorOrigin: { vertical: "top", horizontal: "right", }, variant: "success",
          })
        );
      }

      dispatch(setLoading(false));
      dispatch(MfaOtp(false));
      dispatch(EmailOtp(false));
      return dispatch(GETOTP_REQUIRED(user));
    })
    .catch((error) => {
      dispatch(
        showMessage({
          message: error.message,
          autoHideDuration: 5500,
          anchorOrigin: {
            vertical: "top",
            horizontal: "right",
          },
          variant: "warning",
        })
      );
      dispatch(setLoading(false));
    });
};


export const submitLogin = ({ email, password }, deviceLicenceId) => async (dispatch) => {
  dispatch(setLoading(true));
  return jwtService.signInWithEmailAndPassword(email.toLowerCase(), password, "", deviceLicenceId, false)
    .then((user) => {
      if (!isEmptyOrNull(user.tenant)) {
        localStorage.setItem('tenant', user.tenant)
        localStorage.setItem('twitterData', JSON.stringify(user.twitterData[0]))
        if (user.isOtpRequired) {
          if (user.mfaType === "email") {
            dispatch(
              showMessage({
                message: `OTP sent successfully on ${email}`, autoHideDuration: 3000,
                anchorOrigin: { vertical: "top", horizontal: "right", }, variant: "success",
              }));
          }
          else if (user.mfaType === "text") {
            dispatch(
              showMessage({
                message: `OTP sent successfully on mobile`, autoHideDuration: 3000,
                anchorOrigin: { vertical: "top", horizontal: "right", }, variant: "success",
              }));
          }
          dispatch(setLoading(false));
          dispatch(MfaOtp(false));
          dispatch(EmailOtp(false));
          return dispatch(OTP_REQUIRED(user));
        }
        else {

          let agencyCode;
          if (user.isSuperAdmin)
            agencyCode = 'System';
          else if (user.UserAgencyAccessList[0])
            agencyCode = user.defaultAgency;
          else
            agencyCode = null;

          dispatch(without_Otp({ email, agencyCode }, deviceLicenceId));

          dispatch(setLoading(false));
          dispatch(MfaOtp(false));
          dispatch(EmailOtp(false));
          return dispatch(OTP_REQUIRED(user));
        }
      }
      else {
        dispatch(setLoading(false));
        dispatch(
          showMessage({
            message: "There is configuration issue with your user, Please contact to Admin!",
            autoHideDuration: 5500,
            anchorOrigin: {
              vertical: "top",
              horizontal: "right",
            },
            variant: "warning",
          })
        );
      }

    })
    .catch((error) => {
      dispatch(
        showMessage({
          message: error.message,
          autoHideDuration: 5500,
          anchorOrigin: {
            vertical: "top",
            horizontal: "right",
          },
          variant: "warning",
        })
      );
      dispatch(setLoading(false));
    });
};

export const submitOTP = ({ email, otp, agencyCode, mfaType }, deviceLicenceId) => async (dispatch) => {
  dispatch(setLoading(true));
  jwtService.verifyOTP(email, otp, agencyCode, mfaType, deviceLicenceId)
    .then((user) => {
      dispatch(setUserData(user));
      jwtService.setUserID(user);
      dispatch(setNavigation(createMenuList(user)));
      const res = user.data.settings && user.data.settings.lang &&
        dispatch(changeLanguage(user.data.defaultLanguage !== undefined ? user.data.defaultLanguage : user.data.settings.lang));
      dispatch(newUserAudit({ activity: "Logged In", user, appName: "Admin", }));
      dispatch(setLoading(false));
      dispatch(EmailOtp(false));
      return dispatch(loginSuccess());
    })
    .catch((error) => {
      dispatch(EmailOtp(true));
      dispatch(setLoading(false));
    });
};

export const submit_MFA_OTP = ({ email, code, agencyCode }, deviceLicenceId) => async (dispatch) => {
  dispatch(setLoading(true));
  jwtService.verify_MFA_OTP(email, code, agencyCode, deviceLicenceId)
    .then((user) => {
      dispatch(setUserData(user));
      jwtService.setUserID(user);
      dispatch(setNavigation(createMenuList(user)));
      const res =
        user.data.settings &&
        user.data.settings.lang &&
        dispatch(changeLanguage(user.data.defaultLanguage !== undefined ? user.data.defaultLanguage : user.data.settings.lang));

      dispatch(
        newUserAudit({
          activity: "Logged In",
          user,
          appName: "Admin",
        })
      );
      dispatch(setLoading(false));
      dispatch(MfaOtp(false));

      return dispatch(loginSuccess());
    })
    .catch((error) => {
      dispatch(setLoading(false));
      dispatch(MfaOtp(true));
      return dispatch(loginError());

    });
};

export const without_Otp = ({ email, agencyCode }, deviceLicenceId) => async (dispatch) => {
  dispatch(setLoading(true));
  jwtService.without_OTP(email, agencyCode, deviceLicenceId)
    .then((user) => {
      dispatch(setUserData(user));
      jwtService.setUserID(user);
      dispatch(setNavigation(createMenuList(user)));
      const res =
        user.data.settings &&
        user.data.settings.lang &&
        dispatch(changeLanguage(user.data.defaultLanguage !== undefined ? user.data.defaultLanguage : user.data.settings.lang));
      dispatch(
        newUserAudit({
          activity: "Logged In",
          user,
          appName: "Admin",
        })
      );
      dispatch(setLoading(false));
      dispatch(MfaOtp(false));
      let data = {
        _id: user.data.id,
        source: "Web Admin",
      }
      dispatch(updateUserLastLoginById(data));
      return dispatch(loginSuccess());
    })
    .catch((error) => {
      dispatch(
        showMessage({
          message: error.message,
          autoHideDuration: 5500,
          anchorOrigin: {
            vertical: "top",
            horizontal: "right",
          },
          variant: "warning",
        })
      );
      // dispatch(setLoading(false));
    });
};

export const updateUserLastLoginById = (body) => async dispatch => {
  try {
    return axios.post(`admin/api/users/updateUserLastLoginById`, (encrypt(JSON.stringify(body))))
      .then(async (response) => {
        if (response.status === 200) {
          response = JSON.parse(decrypt(response.data));
        }
        else {
          response = JSON.parse(decrypt(response.data));
          return dispatch(showMessage({
            message: response.data.message,
            autoHideDuration: 2000,
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right'
            },
            variant: 'warning'

          }))
        }
      })
      .catch(error => {
        return;
      });
  } catch (e) {
    return;
  }
};

export const auto_Login = () => async (dispatch) => {
  dispatch(setLoading(true));
  let data = await getDeviceLicenseCode();
  const agencyCode = localStorage.getItem('agencyCode');
  const refresh_token = Cookies.get('refresh_token')

  jwtService.auto_Login(refresh_token, agencyCode, data)
    .then((user) => {
      dispatch(setUserData(user));
      jwtService.setUserID(user);
      dispatch(setNavigation(createMenuList(user)));
      const res =
        user.data.settings &&
        user.data.settings.lang &&
        dispatch(changeLanguage(user.data.defaultLanguage !== undefined ? user.data.defaultLanguage : user.data.settings.lang));

      dispatch(
        newUserAudit({
          activity: "Logged In",
          user,
          appName: "Admin",
        })
      );
      dispatch(setLoading(false));
      dispatch(MfaOtp(false));
      // let data = {
      //   _id: user.data.id,
      //   source: "Web",
      // }
      // dispatch(updateUserLastLoginById(data));
      return dispatch(loginSuccess());
    })
    .catch((error) => {
      dispatch(
        showMessage({
          message: error.message,
          autoHideDuration: 5500,
          anchorOrigin: {
            vertical: "top",
            horizontal: "right",
          },
          variant: "warning",
        })
      );
    });
};

export const verifyUserSession = () => async (dispatch) => {
  jwtService
    .verifyUserSession()
    .then((user) => {
      dispatch(setUserData(user));
      dispatch(getCallTypes())
      localStorage.setItem('twitterData', JSON.stringify(user.data.twitterData[0]))
      dispatch(setNavigation(createMenuList(user)));
      if (
        user.data.userAccess[0].Users &&
        window.location.pathname === "/admin/users"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].Users &&
        window.location.pathname === "/admin/intersectionPoint"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].Contact &&
        window.location.pathname === "/admin/contactList"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].DeviceLicense &&
        window.location.pathname === "/admin/devicelicenses"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].Units &&
        window.location.pathname === "/admin/unit"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].Server &&
        window.location.pathname === "/admin/serverconfiguration"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].Icons &&
        window.location.pathname === "/admin/callIcons"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].CallType &&
        window.location.pathname === "/admin/callTypes"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].CallCategory &&
        window.location.pathname === "/admin/callCategory"
      ) {
        history.push(window.location.pathname);
      } else if (
        user.data.userAccess[0].CallViolation &&
        window.location.pathname === "/admin/callViolations"
      ) {
        history.push(window.location.pathname);
      } else if (window.location.pathname === "/admin/userProfile") {
        history.push(window.location.pathname);
      } else if (window.location.pathname === "/admin/userAuditList") {
        history.push("/admin/userAuditList");
      } else if (
        user.data.userAccess[0].Call &&
        window.location.pathname === "/911/Call911"
      ) {
        history.push(window.location.pathname);
      }
      else if (window.location.pathname === "/911/userProfile") {
        history.push(window.location.pathname);
      } else if (window.location.pathname.split('/').slice(0, 3).join('/') === "/incident/incident") {
        history.push(window.location.pathname);
      }
      else if (user.data.userAccess[0].ErrorLog && window.location.pathname === "/errorLog") {
        history.push(window.location.pathname);
      }
      else if (window.location.pathname === "/admin/users") {
        history.push("/admin/users");
      }
      else if (window.location.pathname === "/admin/TwitterAutorize") {
        history.push(window.location.href);
      }
      else {
        //if (user.data.defaultApp !== "mobile") {
        if (user.redirectUrl) {
          history.push(user.redirectUrl);
        }
        //}
        else {
          history.push('/pages/maintenance');
        }

      }
      const x =
        user.data.settings &&
        user.data.settings.lang &&
        dispatch(changeLanguage(user.data.defaultLanguage !== undefined ? user.data.defaultLanguage : user.data.settings.lang));
      return dispatch(loginSuccess());
    })
    .catch((error) => {
      // dispatch(
      //   showMessage({
      //     message: "Something went wrong",
      //     autoHideDuration: 5000,
      //     anchorOrigin: {
      //       vertical: "top",
      //       horizontal: "right",
      //     },
      //     variant: "error",
      //   })
      // );
      //JwtService.logout()
    });
};

export const createMenuList = (user) => {
  // Added the tranlastion for the menu items keys in the navigation and common en and sp files

  const menuList = [];
  const childernList = [];

  //Administrator Menu list
  if (user) {
    let childernadmin = [];
    if (user.data.isSuperAdmin) {
      childernadmin.push({
        id: "agency-component",
        title: "Agency",
        translate: "agency",
        type: "item",
        icon: "business",
        url: "/admin/agencyList",
      });
    }

    if ((user.data.userAccess[0].Users && user.data.isSuperAdmin) || (user.data.userAccess[0].Users && user.data.agencyAdmin)) {
      childernadmin.push({
        id: "users1-component",
        title: "Users",
        translate: "users",
        type: "item",
        icon: "people",
        url: "/admin/users",
      });
    }

    if (user.data.agencyAdmin || user.data.isSuperAdmin) {
      if (user.data.userAccess[0].EmailConfiguration) {
        childernadmin.push({
          id: "emailConfiguration",
          title: "Email Configuration",
          translate: "emailConfiguration",
          type: "item",
          icon: "email",
          url: "/admin/emailConfiguration",
        });
      }
      if (user.data.userAccess[0].TwitterConfiguration) {
        childernadmin.push({
          id: "twitterConfiguration",
          title: "TWITTERCONFIGURATION",
          translate: "twitterConfiguration",
          type: "item",
          icon: "settings_ethernet",
          url: "/admin/twitterConfiguration",
        });
      }

      if (user.data.userAccess[0].ArchiveChatHistories) {
        childernadmin.push({
          id: "archiveChatHistories",
          title: "ARCHIVECHATHISTORIES",
          translate: "archiveChatHistories",
          type: "item",
          icon: "archive ",
          url: "/admin/archiveChatHistories",
        });
      }

    }

    if ((user.data.userAccess[0].Nfirs && user.data.isSuperAdmin) || (user.data.userAccess[0].Nfirs && user.data.agencyAdmin)) {
      childernadmin.push({
        id: "nfirs-component",
        title: "Nfirs",
        translate: "nfirs",
        type: "item",
        icon: "api",
        url: "/admin/nfirs",
      })
    }

    //In super admin case 1st show county list
    if (user.data.isSuperAdmin) {
      childernadmin.push({
        id: "counties-component",
        title: "Counties",
        translate: "counties",
        type: "item",
        icon: "dns",
        url: "/admin/counties",
      });
    }


    if (user.data.isSuperAdmin) {
      childernadmin.push({
        id: "counties-component",
        title: "State",
        translate: "states",
        type: "item",
        icon: "corporate_fare",
        url: "/admin/states",
      });
    }

    if (user.data.isSuperAdmin) {
      childernadmin.push({
        id: "counties-component",
        title: "NibrsCode",
        translate: "nibrsCode",
        type: "item",
        icon: "integration_instructions",
        url: "/admin/nibrs",
      });
    }

    if (user.data.isSuperAdmin) {
      childernadmin.push({
        id: "updateRequest-component",
        title: "Update Request",
        translate: "updateRequest",
        type: "item",
        icon: "update",
        url: "/admin/updateRequest",
      });
    }

    //In agency admin show the master address and intersection directly
    if (user.data.agencyAdmin) {
      childernadmin.push({
        id: "masterAddress-component",
        title: "Master Address Point",
        translate: "masterAddressPoint",
        type: "item",
        icon: "dns",
        url: "/admin/masterAddress",
      });
    }

    if (user.data.agencyAdmin) {
      childernadmin.push({
        id: "intersection-component",
        title: "Intersection Point",
        translate: "intersectionPoint",
        type: "item",
        icon: "select_all",
        url: "/admin/intersectionPoint",
      });
    }

    if (user.data.agencyAdmin) {
      if (user.data.userAccess[0].Contact) {
        childernadmin.push({
          id: "contact-component",
          title: "Contact",
          translate: "contact",
          type: "item",
          icon: "contacts",
          url: "/admin/contactList/list",
        });
      }
      if (user.data.userAccess[0].Server) {
        childernadmin.push({
          id: "serverConfiguration-component",
          title: "Server Configuration",
          translate: "serverConfiguration",
          type: "item",
          icon: "settings",
          url: "/admin/serverconfiguration",
        });
      }
      if (user.data.userAccess[0].CallCategory) {
        childernadmin.push({
          id: "callCategory-component",
          title: "Call Category",
          translate: "callCategory",
          type: "item",
          icon: "group_work",
          url: "/admin/callCategory/list",
        });
      }
      if (user.data.userAccess[0].CallViolation) {
        childernadmin.push({
          id: "callViolation-component",
          title: "Violations",
          translate: "callViolation",
          type: "item",
          icon: "call_to_action",
          url: "/admin/callViolations/list",
        });
      }
      if (user.data.userAccess[0].CallResponse) {
        childernadmin.push({
          id: "callResponse-component",
          title: "Dispatch Call Response",
          translate: "callResponse",
          type: "item",
          icon: "phone_callback",
          url: "/admin/callResponse/list",
        });
      }
      if (user.data.userAccess[0].DeviceLicense) {
        childernadmin.push({
          id: "deviceLicenses-component",
          title: "Device Licenses",
          translate: "deviceLicenses",
          type: "item",
          icon: "devices",
          url: "/admin/devicelicenses/list",
        });
      }
      if (user.data.userAccess[0].CallType) {
        childernadmin.push({
          id: "callTypes-component",
          title: "Dispatch Call Types",
          translate: "callTypes",
          type: "item",
          icon: "local_library",
          url: "/admin/callTypes/list",
        });
      }
      if (user.data.userAccess[0].Units) {
        childernadmin.push({
          id: "unit-component",
          title: "Unit",
          translate: "units",
          type: "item",
          icon: "airport_shuttle",
          url: "/admin/unit/list",
        });
      }
      if (user.data.userAccess[0].Shift) {
        childernadmin.push({
          id: "shifts-component",
          title: "Shifts",
          translate: "shifts",
          type: "item",
          icon: "history",
          url: "/admin/ShiftTime/list/0",
        });
      }
      if (user.data.userAccess[0].Team) {
        childernadmin.push({
          id: "team-component",
          title: "Team",
          translate: "team",
          type: "item",
          icon: "group",
          url: "/admin/team/list",
        });
      }
      if (user.data.userAccess[0].ShiftAllocation) {
        childernadmin.push({
          id: "ShiftAllocation-component",
          title: "ShiftAllocation",
          translate: "shiftAllocation",
          type: "item",
          icon: "assignment",
          url: "/admin/ShiftAllocation/list/0",
        });
      }
      if (user.data.userAccess[0].Department) {
        childernadmin.push({
          id: "Department",
          title: "Department",
          translate: "department",
          type: "item",
          icon: "dashboard",
          url: "/admin/Department/list",
        });
      }

      if (user.data.userAccess[0].UserAudit) {

        childernadmin.push({
          id: "userAudit-component",
          title: "User Audit",
          translate: "userAudit",
          type: "item",
          icon: "find_in_page",
          url: "/admin/userAuditList/list",
        });
      }
    }

    if (childernadmin.length > 0) {
      childernList.push({
        id: "Administrator",
        title: "Administrator",
        translate: "administrator",
        type: "collapse",
        icon: "whatshot",
        children: childernadmin
      });
    }
  }

  //Twitter Menu List
  if (!user.data.isSuperAdmin && user.data.agencyAdmin && user.data.defaultApp !== "mobile") {
    if (user.data.userAccess[0].TwitterAccountSettings) {
      childernList.push({
        id: "twitter-component",
        title: "TwitterAccountSettings",
        translate: "twitterAccountSettings",
        type: 'item',
        icon: "settings_ethernet",
        url: "/admin/twitter",
      });
    }
  }

  // 911 Menu List
  if (user.data.userAccess[0].Call) {
    childernList.push({
      id: "911Call-component",
      title: "911Call",
      translate: "calls911",
      type: "item",
      icon: "whatshot",
      url: "/911/Call911",
    });
  }

  //Error Log Menu
  if (user.data.agencyAdmin || user.data.isSuperAdmin) {
    if (user.data.userAccess[0].ErrorLog) {
      childernList.push({
        id: "ErrorLog-component",
        title: "ErrorLog",
        translate: "errorLog",
        type: "item",
        icon: "error_outline",
        url: "/errorLog",
      });
    }
  }

  if (!user.data.isSuperAdmin) {
    if (user.data.userAccess[0].ViewTip) {
      childernList.push({
        id: "tip-component",
        title: "ViewTip",
        translate: "viewTip",
        type: 'item',
        icon: "rate_review",
        url: "/quiktip/viewtips/list",
      });
    }
    if (user.data.userAccess[0].TipType) {
      childernList.push({
        id: "tip-component",
        title: "TipType",
        translate: "tipType",
        type: 'item',
        icon: "lightbulb",
        url: "/quiktip/tiptype/list",
      });
    }
    if (user.data.userAccess[0].Notification) {
      childernList.push({
        id: "notifications",
        title: "NotificationCenter",
        translate: "notificationCenter",
        type: 'item',
        icon: "notifications",
        url: "/quiktip/Notifications/list",
        // end: true,
      });
    }
  }

  if (childernList.length > 0) {
    menuList.push({
      type: "group",
      icon: "apps",
      children: childernList,
    });
  } else {
    menuList.push([]);
  }

  return menuList;
};

const initialState = {
  otpRequired: false,
  userId: "",
  UserAgencyAccessList: [],
  resetPass: false,
  success: false,
  isSuperAdmin: false,
  isloading: false,
  mfaOtpvalid: false,
  emailOtpvalid: false,
  userData: [],
  mfaType: "",
  error: {
    email: null,
    password: null,
    otp: null,
  },
  isOtpRequired: true
};

const loginSlice = createSlice({
  name: "auth/login",
  initialState,
  reducers: {
    loginSuccess: (state, action) => {

      state.success = true;
    },
    loginError: (state, action) => {
      state.success = false;
      state.error = action.payload;
    },
    OTP_REQUIRED: (state, action) => {
      state.userData = action.payload;
      state.otpRequired = action.payload.otpRequired;
      state.UserAgencyAccessList = action.payload.UserAgencyAccessList;
      state.isSuperAdmin = action.payload.isSuperAdmin;
      state.userId = action.payload.userId;
      state.mfaType = action.payload.mfaType;
      state.isOtpRequired = action.payload.isOtpRequired;
    },
    GETOTP_REQUIRED: (state, action) => {
      state.userData = action.payload;
      state.otpRequired = action.payload.otpRequired;
      state.UserAgencyAccessList = action.payload.UserAgencyAccessList;
      state.isSuperAdmin = action.payload.isSuperAdmin;
      state.userId = action.payload.userId;
      state.isOtpRequired = action.payload.isOtpRequired;
    },
    setLoading: (state, action) => {
      state.isloading = action.payload;
    },
    MfaOtp: (state, action) => {
      state.mfaOtpvalid = action.payload;
    },
    EmailOtp: (state, action) => {
      state.emailOtpvalid = action.payload;
    },
    SetMFAType: (state, action) => {
      state.mfaType = action.payload;
    }
  },
  extraReducers: {},
});

export const { loginSuccess, loginError, OTP_REQUIRED, setLoading, MfaOtp, EmailOtp, SetMFAType, GETOTP_REQUIRED } = loginSlice.actions;

export default loginSlice.reducer;