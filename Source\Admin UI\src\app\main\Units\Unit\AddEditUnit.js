import { useParams } from 'react-router';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import _ from '@lodash';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import makeStyles from '@mui/styles/makeStyles';
import history from '@history';
import { useTranslation } from 'react-i18next';
import { CardContent, Card, AppBar, Toolbar, TextField, Typography } from '@mui/material';
import { saveUnit, getNFIRS, clearuploadfileobject, uploadFileObject } from '../../store/unitSlice';
import Button from '@mui/material/Button';
import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker';
import { IsCheckMongooseObjectId, checkValueEmptyOrNull, isEmptyOrNull } from '../../utils/utils';
import FormControl from '@mui/material/FormControl';
import RoleSelector from './RoleSelector';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';
import { DateTimePicker } from "@mui/x-date-pickers";

const useStyles = makeStyles({
    w50: {
        width: '100%',
        marginBottom: "1.6rem"
    }, root: {
        display: 'flex',
    },
    cardImage: {
        width: '305px',
        height: '50px',
        marginTop: '10px',
        paddingLeft: '5px',
    },
});

const schema = yup.object().shape({
    unitName: yup.string().required('Please enter Unit Name.'),
    branch: yup.string().required('Please enter Branch.'),
    station: yup.string().required('Please enter Station.'),
    manufacturer: yup.string().required('Please enter Manufacturer.'),
    unitmodel: yup.string().required('Please enter Model.'),
    style: yup.string().required('Please enter Style.'),
    color: yup.string().required('Please enter Color.'),
    emsLevel: yup.string().required('Please enter EMS Level.'),
    unitPositions: yup.string().required('Please enter Unit Positions.'),
    estimatedEndofLife: yup.string().required('Please enter Estimated End of Life.'),
    latestMileage: yup.string().required('Please enter Latest Mileage.'),
    nextServiceMiles: yup.string().required('Please enter Next Service Miles.'),
    pmcsSchedule: yup.string().required('Please enter PMCS Schedule.'),
    oilType: yup.string().required('Please enter Oil Type.'),
    tireSize: yup.string().required('Please enter Tire Size.'),
    fueltype: yup.string().required('Please enter Fuel Type.'),
    vinNumber: yup.string().required('Please enter VIN Number.'),
    tagState: yup.string().required('Please enter Tag State.'),
    tagNumber: yup.string().required('Please enter Tag Number.'),
    nextMaintenance: yup.string().required('Please enter Next Maintenance.'),
    capabilityTags: yup.string().required('Please enter Capability Tags.'),
    issuedAssignedTo: yup.string().required('Please enter Issued/Assigned To.'),
});

const defaultValues = {
    unitName: '',
    branch: '',
    station: '',
    manufacturer: '',
    unitmodel: '',
    style: '',
    color: '',
    emsLevel: '',
    unitPositions: '',
    estimatedEndofLife: '',
    latestMileage: '',
    nextServiceMiles: '',
    pmcsSchedule: '',
    oilType: '',
    tireSize: '',
    fueltype: '',
    vinNumber: '',
    tagState: '',
    tagNumber: '',
    unittype: '',
    nextMaintenance: '',
    role: [{ role: '' }],
    capabilityTags: '',
    issuedAssignedTo: '',
};

let imageflag = false;

function AddEditUnit() {
    const classes = useStyles();
    const UnitData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.unit.data);
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();
    const selectedUnit = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.unit.selectedUnit);
    const uploadFileObj = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.unit.uploadFileObj);
    const nfirsdata = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.unit.nfirsdata);
    const routeParams = useParams();
    const fileUpload = useRef(null);
    const unitNameInputRef = useRef(null);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });
    const { isValid, dirtyFields, errors } = formState;
    const [inservicedate, setInServiceDate] = useState(new Date());
    const [nextServiceDate, setnextServiceDate] = useState(new Date());
    const [unittype, setunittype] = React.useState('');
    const [category, setcategory] = React.useState('');
    const [nfirs, setnfirs] = React.useState('');
    const [fileurl, setfileurl] = React.useState('');

    const categoryOptions = [{ id: 1, name: t('ncic') }, { id: 2, name: t('neim') }];
    const unitTypeOptions = [{ id: 1, name: t('ems') }, { id: 2, name: t('fireTruck') }, { id: 3, name: t('policecar') }];

    useEffect(() => {
        dispatch(getNFIRS());
    }, [])

    const handledate = (event) => {
        setInServiceDate(event);
    }

    const handlenextServiceDate = (event) => {
        setnextServiceDate(event);
    }

    useEffect(() => {
        selectedUnit
        if (routeParams.id)
            setSelectedUnitById();
    }, [dispatch, routeParams])

    useEffect(() => {
    }, [selectedUnit])

    function uploadMultipleFiles(e) {
        if (e.target.files) {
            let temp = {
                url: URL.createObjectURL(e.target.files[0]),
                type: e.target.files[0].type,
            };
            imageflag = true;
            dispatch(uploadFileObject(null));
            dispatch(uploadFileObject(temp));
        }

    }
    function setSelectedUnitById() {
        if (routeParams.id !== '0') {
            var data = UnitData.filter(x => x._id == routeParams.id)[0]
            setValue('unitName', data.unitName);
            setValue('emsLevel', data.emsLevel);
            setInServiceDate(data.inServiceDate ? new Date(data.inServiceDate) : null);
            setnextServiceDate(data.nextServiceDate ? new Date(data.nextServiceDate) : null);
            setValue('manufacturer', data.manufacturer);
            const nfirs = nfirsdata && nfirsdata.find(x => x.name === data.nfirsEquipmentType);
            setnfirs(nfirs);
            setValue('station', data.station);
            setValue('unitPositions', data.unitPositions);
            setValue('branch', data.branch);
            setValue('style', data.style);
            setValue('color', data.color);
            const category = categoryOptions && categoryOptions.find(x => x.name === data.category);
            setcategory(category);
            setValue('unitmodel', data.unitmodel);
            setValue('estimatedEndofLife', data.estimatedEndofLife);
            setValue('latestMileage', data.latestMileage);
            setValue('nextServiceMiles', data.nextServiceMiles);
            setValue('pmcsSchedule', data.pmcsSchedule);
            setValue('oilType', data.oilType);
            setValue('tireSize', data.tireSize);
            setValue('fueltype', data.fueltype);
            setValue('vinNumber', data.vinNumber);
            setValue('tagNumber', data.tagNumber);
            setValue('nextMaintenance', data.nextMaintenance);
            setValue('role', data.role);
            setValue('capabilityTags', data.capabilityTags);
            setValue('tagState', data.tagState);
            setValue('issuedAssignedTo', data.issuedAssignedTo);
            const unitType = unitTypeOptions && unitTypeOptions.find(x => x.name === data.unitType);
            setunittype(unitType);
            setfileurl(data.fileUrl);
        }
    }

    function onSubmit(model) {
        const data = {
            _id: '0',
            emsLevel: model.emsLevel,
            manufacturer: model.manufacturer,
            nfirsEquipmentType: isEmptyOrNull(nfirs) ? null : nfirs?.name,
            station: model.station,
            inServiceDate: checkValueEmptyOrNull(inservicedate),
            nextServiceDate: checkValueEmptyOrNull(nextServiceDate),
            unitPositions: model.unitPositions,
            branch: model.branch,
            unitName: model.unitName,
            unittype: isEmptyOrNull(unittype) ? null : unittype?.name,
            style: model.style,
            color: model.color,
            category: isEmptyOrNull(category) ? null : category?.name,
            unitmodel: model.unitmodel,
            issuedAssignedTo: model.issuedAssignedTo,
            estimatedEndofLife: model.estimatedEndofLife,
            latestMileage: model.latestMileage,
            nextServiceMiles: model.nextServiceMiles,
            pmcsSchedule: model.pmcsSchedule,
            oilType: model.oilType,
            tagState: model.tagState,
            tireSize: model.tireSize,
            fueltype: model.fueltype,
            vinNumber: model.vinNumber,
            tagNumber: model.tagNumber,
            nextMaintenance: model.nextMaintenance,
            role: model.role,
            capabilityTags: model.capabilityTags,
            isUpdate: false,
            code: routeParams.code
        };

        let file = fileUpload.current.files[0] ? fileUpload.current.files[0] : null;
        var formData = new FormData();
        formData.append("file", file);

        var checkObjectId = IsCheckMongooseObjectId(routeParams.id)
        if (checkObjectId) {
            data.isUpdate = true;
            data._id = routeParams.id;
            data.imageurl = fileurl;
        }
        dispatch(saveUnit(formData, file, data));
        history.push(`/admin/unit/${routeParams.code}`);
        imageflag = false;
    }

    const handleunittype = (event, newValue) => {
        setunittype(newValue);
    };

    const handlecategory = (event, newValue) => {
        setcategory(newValue);
    };

    const handlenfirs = (event, newValue) => {
        setnfirs(newValue);
    };

    const handleclose = () => {
        imageflag = false;
        dispatch(clearuploadfileobject());
        history.push(`/admin/unit/${routeParams.code}`);
    }

    useEffect(() => {
        if (unitNameInputRef.current) {
            unitNameInputRef.current.focus();
        }
    }, [unitNameInputRef]);

    return (
        <div className="p-16">
            <Card className=" m-16 rounded-8 shadow">
                <AppBar position="static" elevation={0}>
                    <Toolbar className="px-8">
                        <Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
                            {t('unit')}
                        </Typography>
                    </Toolbar>
                </AppBar>

                <CardContent style={{ overflowX: "scroll", height: "90%" }}>
                    <div className="w-full p-16 ">
                        <form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)} autoSave={false}
                            autoComplete={false}>
                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="unitName"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('unitName')}
                                            type="text"
                                            inputRef={unitNameInputRef}
                                            error={!!errors.unitName}
                                            helperText={errors?.unitName?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="branch"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('branch')}
                                            type="text"
                                            error={!!errors.branch}
                                            helperText={errors?.branch?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="station"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('station')}
                                            type="text"
                                            error={!!errors.station}
                                            helperText={errors?.station?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="manufacturer"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('manufacturer')}
                                            type="text"
                                            error={!!errors.manufacturer}
                                            helperText={errors?.manufacturer?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="unitmodel"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('model')}
                                            type="text"
                                            error={!!errors.unitmodel}
                                            helperText={errors?.unitmodel?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="style"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('style')}
                                            type="text"
                                            error={!!errors.style}
                                            helperText={errors?.style?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="color"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('color')}
                                            type="text"
                                            error={!!errors.color}
                                            helperText={errors?.color?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="nextServiceDate"
                                    control={control}
                                    render={({ field }) => (
                                        <DateTimePicker
                                            {...field}
                                            className="w-full mt-8 mb-16 mx-4"
                                            autoOk={true}
                                            size="medium"
                                            inputVariant="standard"
                                            format="MM/dd/yyyy HH:mm"
                                            margin="normal"
                                            id="date-picker-inline"
                                            ampm={false}
                                            value={nextServiceDate}
                                            onChange={handlenextServiceDate}
                                            KeyboardButtonProps={{
                                                "aria-label": "change date",
                                            }}
                                            label={t('nextServiceDate')}
                                            renderInput={(params) => (
                                                <TextField {...params} />
                                            )}
                                        />
                                    )}
                                />

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }} required>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsWarrantValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handlenfirs}
                                                options={nfirsdata || []}
                                                value={nfirs}
                                                fieldName={t('nfirsEquipmentType')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="emsLevel"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('emsLevel')}
                                            type="text"
                                            error={!!errors.emsLevel}
                                            helperText={errors?.emsLevel?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="unitPositions"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('unitPositions')}
                                            type="text"
                                            error={!!errors.unitPositions}
                                            helperText={errors?.unitPositions?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="inServiceDate"
                                    control={control}
                                    render={({ field }) => (
                                        <DateTimePicker
                                            {...field}
                                            className="w-full mt-8 mb-16 mx-4"
                                            autoOk={true}
                                            size="medium"
                                            inputVariant="standard"
                                            format="MM/dd/yyyy HH:mm"
                                            margin="normal"
                                            id="date-picker-inline"
                                            ampm={false}
                                            value={inservicedate}
                                            onChange={handledate}
                                            KeyboardButtonProps={{
                                                "aria-label": "change date",
                                            }}
                                            label={t('inServiceDate')}
                                            renderInput={(params) => (
                                                <TextField {...params} />
                                            )}
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="estimatedEndofLife"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('estimatedEndofLife')}
                                            type="text"
                                            error={!!errors.estimatedEndofLife}
                                            helperText={errors?.estimatedEndofLife?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="latestMileage"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('latestMileage')}
                                            type="text"
                                            error={!!errors.latestMileage}
                                            helperText={errors?.latestMileage?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="nextServiceMiles"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('nextServiceMiles')}
                                            type="text"
                                            error={!!errors.nextServiceMiles}
                                            helperText={errors?.nextServiceMiles?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">
                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }} required>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsWarrantValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handlecategory}
                                                options={categoryOptions || []}
                                                value={category}
                                                fieldName={t('category')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                                <Controller
                                    className={classes.w50}
                                    name="pmcsSchedule"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('pmcsSchedule')}
                                            type="text"
                                            error={!!errors.pmcsSchedule}
                                            helperText={errors?.pmcsSchedule?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="capabilityTags"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('capabilityTags')}
                                            type="text"
                                            error={!!errors.capabilityTags}
                                            helperText={errors?.capabilityTags?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="nextMaintenance"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('nextMaintenance')}
                                            type="text"
                                            error={!!errors.nextMaintenance}
                                            helperText={errors?.nextMaintenance?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="issuedAssignedTo"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('issuedAssignedTo')}
                                            type="text"
                                            error={!!errors.issuedAssignedTo}
                                            helperText={errors?.issuedAssignedTo?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="oilType"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('oilType')}
                                            type="text"
                                            error={!!errors.oilType}
                                            helperText={errors?.oilType?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="tireSize"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('tireSize')}
                                            type="text"
                                            error={!!errors.tireSize}
                                            helperText={errors?.tireSize?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="fueltype"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('fueltype')}
                                            type="text"
                                            error={!!errors.fueltype}
                                            helperText={errors?.fueltype?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }} required>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsWarrantValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleunittype}
                                                options={unitTypeOptions || []}
                                                value={unittype}
                                                fieldName={t('unitType')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>
                            </div>

                            <div className="flex -mx-4">

                                <Controller
                                    className={classes.w50}
                                    name="vinNumber"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('vinNumber')}
                                            type="number"
                                            error={!!errors.vinNumber}
                                            helperText={errors?.vinNumber?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="tagState"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('tagState')}
                                            type="text"
                                            error={!!errors.tagState}
                                            helperText={errors?.tagState?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="tagNumber"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('tagNumber')}
                                            type="text"
                                            error={!!errors.tagNumber}
                                            helperText={errors?.tagNumber?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <input
                                    type="file"
                                    className={classes.w50}
                                    ref={fileUpload}
                                    accept="image/*"
                                    style={{
                                        padding: '13px',
                                        border: '1px solid lightgray',
                                        borderRadius: '4px',
                                        // backgroundColor: 'antiquewhite',
                                        cursor: 'pointer',
                                        marginTop: '8px',
                                        height: '53px',
                                    }}
                                    onChange={uploadMultipleFiles}
                                />

                                {(routeParams.id !== '0' && fileurl && !imageflag) ?
                                    <img src={fileurl} className={classes.cardImage} alt="..."></img>
                                    :
                                    ""
                                }

                                {(uploadFileObj !== null && imageflag) &&
                                    <img src={uploadFileObj.url} alt="..." className={classes.cardImage} ></img>
                                }
                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    control={control}
                                    name="role"
                                    render={({ field }) => <RoleSelector className="mt-32" {...field} />}
                                />
                            </div>

                            <div className="flex justify-center">
                                <Button
                                    type="submit"
                                    variant="contained"
                                    color="primary"
                                    className="normal-case m-16"
                                    aria-label="REGISTER"
                                    value="legacy">
                                    {t('save')}
                                </Button>
                                <Button
                                    type="button"
                                    variant="contained"
                                    color="secondary"
                                    onClick={handleclose}
                                    className="normal-case m-16"
                                    aria-label="UPDATE"
                                    value="legacy">
                                    {t('back')}
                                </Button>
                            </div>
                        </form>
                    </div>
                </CardContent >
            </Card >
        </div >
    )
}
export default AddEditUnit 
