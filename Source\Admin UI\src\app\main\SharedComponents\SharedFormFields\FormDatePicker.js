import React from "react";
import { TextField, Grid } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";


const FormDatePicker = ({
    label,
    value,
    onChange,
    gridProps = {},
    disableFuture = false,
    format = "MM/dd/yyyy",
}) => {
    return (
        <Grid item {...gridProps}>
            <DatePicker
                label={label}
                value={value}
                onChange={onChange}
                disableFuture={disableFuture}
                format={format}
                renderInput={(params) => <TextField {...params} fullWidth />}
                ampm={false}
                slotProps={{ field: { clearable: true } }}
            />
        </Grid>
    );
};

export default FormDatePicker;
