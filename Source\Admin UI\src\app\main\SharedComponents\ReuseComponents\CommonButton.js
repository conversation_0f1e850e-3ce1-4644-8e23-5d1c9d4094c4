import makeStyles from "@mui/styles/makeStyles";
import React, { useState, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { motion } from "framer-motion";
import Button from "@mui/material/Button";
import "./CommonButton.css"
import { getHighlightStyle, isEmptyOrNull } from "../../utils/utils";
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";

function CommonButton(props) {
    const dispatch = useDispatch();
    const [isButtonFocused, setIsButtonFocused] = React.useState(false);
    const navbarTheme = useSelector(selectNavbarTheme);
    const BackgroundColor = navbarTheme.palette.secondary.main;
    const highlightStyle = getHighlightStyle(BackgroundColor);


    const btnClick = () => {
        const value = !isEmptyOrNull(props.btnValueType) ? props.btnValueType : "";
        props.parentCallback(value)
    }

    const handleFocus = () => {
        if (props.btnName === "Edit") {
            setIsButtonFocused(true);
        }
    };

    const handleBlur = () => {
        if (props.btnName === "Edit") {
            setIsButtonFocused(false);
        }
    };

    return (
        <div>
            <Button
                component={motion.span}
                initial={{ x: -20 }}
                animate={{ x: 0, transition: { delay: 0.2 } }}
                delay={300}
                onClick={() => btnClick()}
                className={props.styleClass}
                variant="contained"
                color={props.color !== undefined ? props.color : props.btnName == "Archive Calls" ? "primary" : "secondary"}
                style={isButtonFocused ? highlightStyle : {}} // Apply highlight style when focused
                onFocus={handleFocus} // Trigger when button is focused
                onBlur={handleBlur}
            >
                <span className="hidden sm:flex">{props.btnName}</span>
                <span className="flex sm:hidden">New</span>
            </Button>
        </div>
    );
}

export default CommonButton;