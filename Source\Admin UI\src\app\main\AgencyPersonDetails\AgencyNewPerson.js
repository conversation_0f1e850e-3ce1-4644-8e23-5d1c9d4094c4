import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, InputAdornment, } from "@mui/material";
import { useTranslation } from "react-i18next";
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import _ from "@lodash";
import { getPersonDetails, removePerson } from "../administration/store/personMasterSlice";
import history from "@history";
import { newUserAudit } from "../../main/userAuditPage/store/userAuditSlice";
import TablePagination from "@mui/material/TablePagination";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import ConfirmationDialog from "../components/ConfirmationDialog/ConfirmationDialog";
import './AgencyPerson.css'
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../utils/utils";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import CancelIcon from '@mui/icons-material/Cancel';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../SharedComponents/ErrorPage/ErrorPage";
import CommonButton from "../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import CircularProgressLoader from '../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
import { IgrGrid, IgrColumn } from "@infragistics/igniteui-react-grids";
import { getTypes } from "../agencyPage/store/agencySlice";


IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function PersonNewDetails() {
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const gridRef = useRef(null);
    const dispatch = useDispatch();

    const user = useSelector(({ auth }) => auth.user);
    const personMasterData = useSelector(({ administration }) => administration.personMaster.data);
    const personMasterTotalCount = useSelector(({ administration }) => administration.personMaster.totalCount);

    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [searchText, setSearchText] = React.useState("");
    const [data, setData] = React.useState(personMasterData);
    const [countData, setCountData] = React.useState(personMasterTotalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "FullName",
    });
    let colorCode = getNavbarTheme();
    const routeParams = useParams();

    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access Agency person",
                user: user,
                appName: "Admin",
            })
        );
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        dispatch(getTypes());
    }, []);

    function onRowAddChange() {
        history.push(`/admin/person/${routeParams.code}/0`, {
            tempData: {
                orderId: order.id,
                orderDirection: order.direction,
                pageIndex: pageIndex * rowsPerPage,
                rowsPerPage: rowsPerPage,
                searchText: searchText === '' ? null : searchText,
                data: null,
            }
        });
    }

    const editRow = (n) => {
        history.push(`/admin/person/${routeParams.code}/${n._id}`, {
            tempData: {
                orderId: order.id,
                orderDirection: order.direction,
                pageIndex: pageIndex * rowsPerPage,
                rowsPerPage: rowsPerPage,
                searchText: searchText === '' ? null : searchText,
                data: n,
            }
        });
    };

    const deleteRecord = (id) => {
        setOpen(true);
        setRemoveID(id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(
                removePerson(
                    removeID,
                    order.id,
                    order.direction,
                    pageIndex * rowsPerPage,
                    rowsPerPage,
                    searchText == '' ? null : searchText,
                    routeParams.code
                )
            );
        }
    };

    const search = useDebounce((search, pageIndex, rowsPerPage, order) => {
        dispatch(getPersonDetails(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage,
            search === '' ? null : search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, pageIndex, rowsPerPage, order);
        } else {
            dispatch(getPersonDetails(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage,
                searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, searchText, pageIndex, rowsPerPage, order, routeParams.code]);

    useEffect(() => {
        setData(personMasterData);
        setCountData(personMasterTotalCount)
    }, [personMasterData]);

    const isLoadingValue = useSelector(({ administration }) => administration.personMaster.isloading);
    const [loading, setLoading] = useState();

    useEffect(() => {
        setLoading(isLoadingValue)
    }, [isLoadingValue]);

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const ActionIcons = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;
            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    variant="contained"
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => editRow(x)}
                                    size="large"
                                >
                                    <EditIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("delete")}>
                                <IconButton
                                    variant="contained"
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => deleteRecord(x._id)}
                                    size="large"
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                    }
                </>
            );
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1  items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() =>
                                                    history.push(
                                                        `/admin/agencyOptionsList/${routeParams.code}`
                                                    )
                                                }
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 p-32 items-center justify-between"
                            style={{ paddingTop: "24px" }}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    people
                                </Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("persons")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                // defaultValue={searchText}
                                                value={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText("")}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addNewPerson" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="whitespace-no-wrap normal-case" btnName={t("addNewPerson")} parentCallback={onRowAddChange}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={countData}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="FullName"
                                        field="FullName"
                                        header={t("name")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="St_Name"
                                        header={t("streetName")}
                                        field="ResidenceAddress.St_Name"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="St_PosTyp"
                                        header={t("streetType")}
                                        field="ResidenceAddress.St_PosTyp"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="PlaceType"
                                        header={t("unitType")}
                                        field="ResidenceAddress.PlaceType"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Email"
                                        header={t("email")}
                                        field="Email"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="PhoneNumber"
                                        header={t("phoneNumber")}
                                        field="PhoneNumber"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}

                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={t("personDeleteMsg")}
                                onClose={handleClose}
                                value={removeID}
                            ></ConfirmationDialog>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    );
}

export default PersonNewDetails;