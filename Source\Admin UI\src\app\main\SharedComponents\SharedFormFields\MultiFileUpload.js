import React, { useState, useRef } from "react";
import { Grid, Button, IconButton, Typography } from "@mui/material";
import { InsertDriveFile, Delete, UploadFile, Attachment, PictureAsPdf, Download } from "@mui/icons-material";

const MultiFileUpload = ({
    onFilesChange,
    gridProps = {},
    name = "Upload Files",
    attachments = [],
    hideUploadButton = true
}) => {

    const [files, setFiles] = useState(attachments);
    const fileInputRef = useRef(null);

    const handleFileChange = (event) => {
        const newFiles = Array.from(event.target.files);
        const filePreviews = newFiles.map((file) => ({
            file,
            preview: file.type.startsWith("image/") ? URL.createObjectURL(file) : null,
        }));

        setFiles((prev) => [...prev, ...filePreviews]);

        if (onFilesChange) {
            onFilesChange([...files.map((f) => f.file), ...newFiles]);
        }
    };

    const removeFile = (index) => {
        const updatedFiles = [...files];
        updatedFiles.splice(index, 1);
        setFiles(updatedFiles);

        if (onFilesChange) {
            onFilesChange(updatedFiles.map((f) => f.file));
        }
    };

    const downloadFile = (url) => {
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('target', '_blank'); // Open in new tab
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <Grid item {...gridProps}>

            {hideUploadButton &&
                <>
                    <input
                        type="file"
                        ref={fileInputRef}
                        multiple
                        onChange={handleFileChange}
                        style={{ display: "none" }}
                    />
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={() => fileInputRef.current.click()}
                        startIcon={<Attachment />}
                    >
                        {name}
                    </Button>
                </>
            }

            <Grid container spacing={2} sx={{ marginTop: 2 }}>
                {files.map((fileObj, index) => {
                    // Determine source and type

                    const displaySrc = fileObj?.preview ?? fileObj?.fileUrl ?? "";
                    const fileUrl = fileObj?.fileUrl ?? null;
                    const fileName = fileObj?.file?.name ?? fileObj?.fileName ?? "Unknown File";
                    const fileType = fileObj?.file?.type || fileObj?.mimeType || "";
                    const filekey = fileObj?.fileKey || null;

                    // Determine display component based on file type
                    const renderFilePreview = () => {
                        // if (!displaySrc) {
                        //     return (
                        //         <div style={{ display: "flex", alignItems: "center", flexDirection: "column" }}>
                        //             <InsertDriveFile fontSize="large" />
                        //             <Typography variant="caption" style={{ wordBreak: "break-word", width: "100px" }}>
                        //                 {fileName || "No Preview"}
                        //             </Typography>
                        //         </div>
                        //     );
                        // }

                        if (fileType.startsWith("image/")) {
                            return (
                                <img
                                    src={displaySrc}
                                    alt={`file-preview-${index}`}
                                    style={{
                                        width: 100,
                                        height: 100,
                                        objectFit: "cover",
                                        borderRadius: 8,
                                        border: "1px solid lightgray",
                                    }}
                                />
                            );
                        }

                        if (fileType.startsWith("video/")) {
                            return (
                                <video
                                    src={displaySrc}
                                    controls
                                    style={{
                                        width: 100,
                                        height: 100,
                                        objectFit: "cover",
                                        borderRadius: 8,
                                        border: "1px solid lightgray",
                                    }}
                                />
                            );
                        }

                        if (fileType.startsWith("audio/")) {
                            return (
                                <div style={{
                                    width: 100,
                                    height: 100,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderRadius: 8,
                                    border: "1px solid lightgray",
                                    overflow: "hidden"
                                }}>
                                    <audio
                                        src={displaySrc}
                                        controls
                                        style={{
                                            width: "100%",
                                            height: "40px", // Controls height of audio player
                                        }}
                                    />
                                </div>
                            );
                        }


                        if (fileType === "application/pdf") {
                            return (
                                <a
                                    href={displaySrc}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    style={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        textDecoration: "none",
                                    }}
                                >
                                    <PictureAsPdf
                                        fontSize="large"
                                        style={{
                                            width: 100,
                                            height: 100,
                                            objectFit: "cover",
                                            borderRadius: 8,
                                            border: "1px solid lightgray",
                                        }}
                                        color="error"
                                    />
                                    {/* <Typography variant="caption" style={{ wordBreak: "break-word", width: "100px" }}>
                                        {fileName}
                                    </Typography> */}
                                </a>
                            );
                        }

                        if (fileType.includes("excel") || fileName.match(/\.xls|\.xlsx$/)) {
                            return <InsertDriveFile fontSize="large" style={{ width: 100, height: 100, objectFit: "cover", borderRadius: 8, border: "1px solid lightgray" }} color="primary" />;
                        }

                        if (fileType.includes("word") || fileName.match(/\.doc|\.docx$/)) {
                            return <InsertDriveFile fontSize="large" style={{ width: 100, height: 100, objectFit: "cover", borderRadius: 8, border: "1px solid lightgray" }} color="primary" />;
                        }

                        // Default for unsupported types
                        return (
                            <div style={{ display: "flex", alignItems: "center", flexDirection: "column" }}>
                                <InsertDriveFile fontSize="large" />
                            </div>
                        );
                    };

                    return (
                        <Grid item key={index}>
                            <div style={{ position: "relative", display: "inline-block", textAlign: "center" }}>
                                {renderFilePreview()}

                                {/* Show delete icon only if file is locally uploaded */}
                                {filekey === null && (
                                    <IconButton
                                        size="small"
                                        onClick={() => removeFile(index)}
                                        style={{
                                            position: "absolute",
                                            top: 2,
                                            right: 2,
                                            background: "white",
                                        }}
                                    >
                                        <Delete fontSize="small" color="error" />
                                    </IconButton>
                                )}
                                {/* Show download icon only if file is uploaded on server*/}
                                {filekey !== null && (
                                    <IconButton
                                        size="small"
                                        onClick={() => downloadFile(fileUrl)}
                                        style={{
                                            position: "absolute",
                                            top: 2,
                                            right: 2,
                                            background: "white",
                                        }}
                                    >
                                        <Download fontSize="small" color="error" />
                                    </IconButton>
                                )}
                            </div>
                        </Grid>
                    );
                })}
            </Grid>
        </Grid>
    );
};

export default MultiFileUpload;
