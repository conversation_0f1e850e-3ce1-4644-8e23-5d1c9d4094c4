import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useEffect, useRef, useState } from 'react'
import { Icon, Input, Paper, StyledEngineProvider, TablePagination, ThemeProvider, Typography } from '@mui/material';
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useSelector, useDispatch } from 'react-redux'
import { useDebounce } from '@fuse/hooks';
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from '../../utils/utils';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import { deleteNibrs, getNibrsList } from '../store/nibrsCodeSlice';
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import { ErrorBoundary } from "react-error-boundary";
import ConfirmationDialog from "src/app/main/components/ConfirmationDialog/ConfirmationDialog";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import IntegrationInstructionsOutlinedIcon from '@mui/icons-material/IntegrationInstructionsOutlined';
import CommonButton from "src/app/main/SharedComponents/ReuseComponents/CommonButton";
import NibrsCodeAdd from './NibrsCodeAdd';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "code",
        align: "left",
        disablePadding: false,
        label: "code",
        sort: true,
    },
    {
        id: "title",
        align: "left",
        disablePadding: false,
        label: "Title",
        sort: true,
    },
    {
        id: "description",
        align: "left",
        disablePadding: false,
        label: "Description",
        sort: true,
    },
    {
        id: "group",
        align: "left",
        disablePadding: false,
        label: "Group",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function NibrsCode(props) {
    const { t } = useTranslation("laguageConfig");
    const mainTheme = useSelector(selectMainTheme);
    const dispatch = useDispatch();
    const gridRef = useRef(null);
    const nibrsRef = useRef(null);

    const rowsPerPageOptions = getRowsPerPageOptions();
    let colorCode = getNavbarTheme();

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [page, setPage] = useState(0);
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    const [searchText, setSearchText] = React.useState("");
    const [removeData, setRemoveData] = React.useState(null);
    const [open, setOpen] = React.useState(false);

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "code",
    });

    const nibrsList = useSelector(({ administration }) => administration.nibrsCodeSlice.nibrsList)
    const TotalCount = useSelector(({ administration }) => administration.nibrsCodeSlice.nibrsTotalCount)
    const isloading = useSelector(({ administration }) => administration.nibrsCodeSlice.isloading);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [loading, setloading] = useState(false);

    useEffect(() => {
        setloading(isloading);
    }, [isloading]);

    let pagingDetails = {
        sortField: order.id,
        sortDirection: order.direction,
        pageIndex: page * rowsPerPage,
        pageLimit: rowsPerPage,
        searchText: searchText === '' ? null : searchText
    };

    function handleChangePage(event, value) {
        setPage(value);
        setloading(true);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
        setloading(true);
    }

    const ActionIcons = (n) => {
        // let x = checkData(n);

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div className="flex">
                    <Tooltip title={t("edit")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => handleEditClick(x)}
                            size="large"
                        >
                            <EditIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={t("delete")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => handleDeleteClick(x)}
                            size="large"
                        >
                            <DeleteIcon />
                        </IconButton>
                    </Tooltip>
                </div >
            );
        }
    };

    const handleDeleteClick = (data) => {
        setRemoveData(data._id);
        setOpen(true);
    };

    const addNibrsCode = () => {
        nibrsRef.current.handleOpen(false, "add", pagingDetails)
    };

    const handleEditClick = (data) => {
        nibrsRef.current.handleOpen(true, "update", pagingDetails, data);
    };

    const handleConfimationDialogClick = async (newValue) => {
        if (newValue) {
            await dispatch(deleteNibrs(removeData));
            setRemoveData(null);
        }
        setOpen(false);
    };

    const rowData = (data).map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["_id"] = item._id;
            row["code"] = item.code ? item.code : '';
            row["group"] = item.group ? item.group : '';
            row["title"] = item.title ? item.title : '';
            row["description"] = item.description ? item.description : '';
            row["action"] = ActionIcons(item)
        });
        return row;
    });

    useEffect(() => {
        if (nibrsList !== null && nibrsList !== undefined) {
            setData(nibrsList);
            setTotalCount(TotalCount);
        }
    }, [nibrsList, totalCount])

    const search = useDebounce((search, page, rowsPerPage) => {
        dispatch(getNibrsList(order.id, order.direction, page * rowsPerPage, rowsPerPage,
            search === '' ? null : search));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, page, rowsPerPage);
        } else {
            dispatch(getNibrsList(order.id, order.direction, page * rowsPerPage, rowsPerPage,
                searchText === '' ? null : searchText));
        }
    }, [dispatch, searchText, page, rowsPerPage, order, nibrsRef, removeData]);

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 0 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={{ paddingTop: "50px" }}
                        >
                            <div className="flex items-center">
                                <IntegrationInstructionsOutlinedIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("nibrsCode")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addNfirs" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="whitespace-no-wrap normal-case ml-16" btnName={t("addNibrsCode")} parentCallback={addNibrsCode}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="code"
                                        header={t("code")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("group")}
                                        field="group"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("title")}
                                        field="title"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("description")}
                                        field="description"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        width="300px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}

                                    />
                                </IgrGrid>
                            </div>
                        </div>

                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={t("deleteRecord")}
                                onClose={handleConfimationDialogClick}
                                value={removeData}
                            >
                            </ConfirmationDialog>
                        </ErrorBoundary>

                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="NibrsCodeAdd" />} onReset={() => { }} >
                            <NibrsCodeAdd ref={nibrsRef} />
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    );
}

export default NibrsCode;