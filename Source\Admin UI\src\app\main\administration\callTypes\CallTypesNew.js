import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, Checkbox, } from "@mui/material";
import { getCallTypes, removeType, UpdateShowAlertFlag } from "../store/callTypeSlice";
import { useTranslation } from "react-i18next";
import { getCallCategory } from "../store/callCategorySlice";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import _ from "@lodash";
import { newUserAudit } from "../../../main/userAuditPage/store/userAuditSlice";
import { getCallResponse } from "../store/callResponseSlice";
import TablePagination from "@mui/material/TablePagination";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import { checkData, getNavbarTheme, getRowsPerPageOptions, isEmptyOrNull, calculateOptimalMultiplier, calculateColumnWidth, useWindowResizeHeight } from "../../utils/utils";
import CircularProgressLoader from '../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';
import CallTypeDialog from "./CallTypesDialog";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { useDebounce } from '@fuse/hooks';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "CallTypeID",
        align: "left",
        disablePadding: false,
        label: "CALL_TYPE_ID_LBL",
        sort: true,
    },
    {
        id: "Category",
        align: "left",
        disablePadding: false,
        label: "CATEGORY",
        sort: true,
    },
    {
        id: "CallCategoryID",
        align: "left",
        disablePadding: false,
        label: "CALL_CATEGORY_TYPE_ID",
        sort: true,
    },
    {
        id: "ResponseCode",
        align: "left",
        disablePadding: false,
        label: "RESPPONSE_CODE_LBL",
        sort: true,
    },
    {
        id: "Priority",
        align: "left",
        disablePadding: false,
        label: "PRORITY_LBL",
        sort: true,
    },
    {
        id: "PriorityID",
        align: "left",
        disablePadding: false,
        label: "PriorityID",
        sort: true,
    },
    {
        id: "IconURL",
        align: "left",
        disablePadding: false,
        label: "IconURL",
        sort: true,
    },
    {
        id: "CallTypeName",
        align: "left",
        disablePadding: false,
        label: "NAME",
        sort: true,
    },
    {
        id: "CallTypeAbbr",
        align: "left",
        disablePadding: false,
        label: "CALL_TYPEABBREVAITION_LBL",
        sort: true,
    },
    {
        id: "Preview",
        align: "left",
        disablePadding: false,
        label: "PREVIEWLBL",
        sort: true,
    },
    {
        id: "showAlertCheckBox",
        align: "left",
        disablePadding: false,
        label: "showAlert",
        sort: true,
    },
    {
        id: "showAlert",
        align: "left",
        disablePadding: false,
        label: "showAlert",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

let Data = [];

function CallTypesNew() {
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const navbarTheme = useSelector(selectNavbarTheme);
    const gridRef = useRef(null);
    const callRef = useRef();
    const dispatch = useDispatch();

    const user = useSelector(({ auth }) => auth.user);
    const callTypeData = useSelector(({ administration }) => administration.callTypes.data);
    const callTypeTotalCount = useSelector(({ administration }) => administration.callTypes.totalCount);
    const success = useSelector(({ administration }) => administration.callTypes.success);

    const [newCallType, setNewCallType] = React.useState(false);
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [searchText, setSearchText] = React.useState(null);
    const [data, setData] = React.useState(callTypeData);
    const [page, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [totalCount, setTotalCount] = useState(callTypeTotalCount);
    const [gridWidth, setGridWidth] = useState(1200); // Default width
    const routeParams = useParams();
    let colorCode = getNavbarTheme();
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const priorities = [
        {
            PriorityID: 1,
            Priority: "High",
        },
        {
            PriorityID: 2,
            Priority: "Mid",
        },
        {
            PriorityID: 3,
            Priority: "Low",
        },
    ];
    const [callType, setSelectedType] = React.useState("");
    priorities.unshift({ Priority: "Select", PriorityID: 0 });

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "CallTypeID",
    });

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access Dispatch Call Type",
                user: user,
                appName: "Admin",
            })
        );
    }, []);

    const deleteCallType = (n) => {
        setOpen(true);
        setRemoveID(n._id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeType(removeID, routeParams.code, page, rowsPerPage, order.id, "desc", searchText));
        }
    };

    function checkIcon(n) {
        let URL = "";
        if (n !== undefined) {
            URL = n.IconURL;
        } else {
            URL = "";
        }
        return URL;
    }

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const search = useDebounce((search, page, rowsPerPage) => {
        dispatch(getCallTypes(order.id, order.direction, page * rowsPerPage, rowsPerPage, search === '' ? null : search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, page, rowsPerPage);
        } else {
            dispatch(getCallTypes(order.id, order.direction, page * rowsPerPage, rowsPerPage, searchText === '' ? null : searchText, routeParams.code));
        }
    }, [searchText]);

    useEffect(() => {
        setData(callTypeData);
        setTotalCount(callTypeTotalCount)
    }, [callTypeData]);

    useEffect(() => {
        dispatch(getCallTypes(order.id, order.direction, page * rowsPerPage, rowsPerPage, searchText === '' ? null : searchText, routeParams.code));
        dispatch(getCallCategory("CallCategoryName", "asc", 0, 0, routeParams.code));
        dispatch(getCallResponse("ResponseCode", "asc", 0, 0, null, routeParams.code));
    }, [dispatch, success, page, rowsPerPage, order]);

    useEffect(() => { }, [isUpdate, newCallType, rowsPerPage, callType]);

    const isloadingvalue = useSelector(({ administration }) => administration.callTypes.isloading);
    const [loading, setLoading] = useState();

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    let PagingDetails = {
        pageIndex: page,
        rowsPerPage: rowsPerPage,
        id: order.id,
        direction: order.direction,
        searchText: searchText
    };

    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    <div style={{ display: "flex" }}>
                        <Tooltip title={t("edit")}>
                            <IconButton
                                variant="contained"
                                aria-label="Back"
                                color="inherit"
                                onClick={() => callRef.current.handleClickOpen(x, routeParams.code, PagingDetails, true)}
                                size="large"
                            >
                                {/* <Icon>edit</Icon> */}
                                <EditIcon />
                            </IconButton>
                        </Tooltip>

                        <Tooltip title={t("delete")}>
                            <IconButton
                                variant="contained"
                                aria-label="Back"
                                color="inherit"
                                disabled={x.isActive}
                                onClick={() => deleteCallType(x)}
                                size="large"
                            >
                                {/* <Icon>delete</Icon> */}
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>
                    </div>
                </>
            );
        }
    };

    const handleCheckBoxChange = async (event) => {
        var data = {
            _id: event.target.id,
            showAlert: event.target.checked,
            agencyCode: routeParams.code,
        }
        dispatch(UpdateShowAlertFlag(data, order.id, order.direction, page * rowsPerPage, rowsPerPage, searchText));
    };

    const showAlertCheckBox = (n) => {
        // const x = checkData(n);
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            const showAlert = x.showAlert !== undefined && x.showAlert !== null ? x.showAlert : false;
            return (
                <>
                    <Checkbox
                        id={x._id}
                        checked={showAlert}
                        onClick={handleCheckBoxChange}
                        inputProps={{ 'aria-label': 'controlled' }}
                    ></Checkbox>
                </>
            )
        }
    }

    const rowData = data.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["CallTypeName"] = item.CallTypeName
            row["Category"] = item.Category
            row["ResponseCode"] = item.ResponseCode
            row["Priority"] = item.Priority
            row["CallTypeAbbr"] = item.CallTypeAbbr
            row["Preview"] = checkIcon(item.CallCategoryList[0])
            row["showAlertCheckBox"] = showAlertCheckBox(item)
            row["action"] = ActionIcons(item)
        });
        return row;
    });

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            setOrder({ id: sortDesc.field, direction: sortDesc.sortDirection === 0 ? "asc" : "desc" });
        } else {
            console.log("No sorting applied.");
        }
    };
    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    //method for rendering image in grid
    const renderPreview = (ctx) => {
        const item = ctx.dataContext;
        return (
            <img
                src={item.implicit}
                style={{ height: '50px', width: '100px', objectFit: 'contain' }}
            />
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 w-full items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    local_library
                                </Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("dispatchCallTypes")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addCallType" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="whitespace-no-wrap normal-case ml-16" btnName={t("addCallType")} parentCallback={() => callRef.current.handleClickOpen(Data, routeParams.code, PagingDetails, false)}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={totalCount}
                                rowsPerPage={rowsPerPage}
                                page={page}
                                backIconButtonProps={{ "aria-label": "Previous Page", }}
                                nextIconButtonProps={{ "aria-label": "Next Page", }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />
                        </div>
                        <div className="igrGridClass" >

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="CallTypeName"
                                        field="CallTypeName"
                                        header={t("name")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Category"
                                        header={t("category")}
                                        field="Category"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="ResponseCode"
                                        header={t("code")}
                                        field="ResponseCode"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Priority"
                                        header={t("priority")}
                                        field="Priority"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="CallTypeAbbr"
                                        header={t("abbrevaition")}
                                        field="CallTypeAbbr"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Preview"
                                        header={t("preview")}
                                        field="Preview"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        bodyTemplate={renderPreview}
                                    />
                                    <IgrColumn
                                        key="showAlertCheckBox"
                                        header={t("showAlert")}
                                        field="showAlertCheckBox"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        bodyTemplate={showAlertCheckBox}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteMsgCallTypes")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>

                            <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="CallTypeDialog" />} onReset={() => { }} >
                                <CallTypeDialog ref={callRef} />
                            </ErrorBoundary>
                        </div>
                    </div>
                }
            />
        </>
    );
}

export default CallTypesNew;