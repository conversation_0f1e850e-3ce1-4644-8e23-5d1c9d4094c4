const router = require("express").Router();
const dotenv = require("dotenv");
dotenv.config();


const printLogger = (MethodName, String) => {
    if (process.env.IS_LOG == "true") {
        console.log(`MethodName: ${MethodName},  Message: ${String},  ${Date()}`);
    }

}

const printJsonDataLogger = (Data) => {
    if (process.env.IS_LOG == "true") {
        console.log(`${Data}`);
    }

}
module.exports = { printLogger, printJsonDataLogger };