import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useEffect, useRef, useState } from 'react';
import {
    Icon, Input, Paper, StyledEngineProvider, TablePagination, IconButton,
    ThemeProvider, Typography, InputAdornment, Tooltip, Stack, Button,
} from '@mui/material';
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import { useSelector, useDispatch } from 'react-redux';
import history from '@history';
import CancelIcon from '@mui/icons-material/Cancel';
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import './Organization.css';
import Inventory2Icon from '@mui/icons-material/Inventory2';

function Organization(props) {
    const { t } = useTranslation("laguageConfig");
    const mainTheme = useSelector(selectMainTheme);
    const dispatch = useDispatch();

    const user = useSelector(({ auth }) => auth.user);

    const navigateToOptions = () => {
        history.push(`/admin/updateRequest`);
    };

    return (
        <>
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {user.data.isSuperAdmin && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => navigateToOptions()}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={user.data.isSuperAdmin ? {} : { paddingTop: "29px" }}
                        >
                            <div className="flex items-center">
                                <Inventory2Icon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("organization")}
                                </Typography>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                    </div>
                }
            />
        </>
    );
}

export default Organization;