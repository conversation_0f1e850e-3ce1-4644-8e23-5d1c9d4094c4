import Grid from "@mui/material/Grid";
import CloseButton from "./CloseButton";
import { useTranslation } from "react-i18next";
import FormTextField from "../../SharedFormFields/FormTextField";

const AddressForm = ({ namespace = "address", showLatLong, onClose }) => {
  const { t } = useTranslation("laguageConfig");

  return (
    <>
      <Grid container spacing={2} className="mt-10 mb-20">
        <Grid
          container
          item
          xs={12}
          spacing={2}
          sx={{ display: "flex", justifyContent: "space-between" }}
        >
          <Grid item xs={12} sm={10} md={10} container spacing={2}>
            <FormTextField
              name={`${namespace}.Add_Number`}
              label={t("addrNumber")}
              gridProps={{ xs: 12, sm: 12, md: 2 }}
            />
            <FormTextField
              name={`${namespace}.AddNum_Suf`}
              label={t("numberSuffix")}
              gridProps={{ xs: 12, sm: 12, md: 2 }}
            />
            <FormTextField
              name={`${namespace}.St_PreDir`}
              label={t("preDirection")}
              gridProps={{ xs: 12, sm: 12, md: 2 }}
            />
            <FormTextField
              name={`${namespace}.St_PreTyp`}
              label={t("preType")}
              gridProps={{ xs: 12, sm: 12, md: 2 }}
            />
            <FormTextField
              name={`${namespace}.St_Name`}
              label={t("streetName")}
              gridProps={{ xs: 12, sm: 12, md: 4 }}
            />
            <FormTextField
              name={`${namespace}.St_PosTyp`}
              label={t("streetType")}
              gridProps={{ xs: 12, sm: 12, md: 2 }}
            />
            <FormTextField
              name={`${namespace}.St_PosDir`}
              label={t("postDirection")}
              gridProps={{ xs: 12, sm: 12, md: 2 }}
            />
          </Grid>

          <CloseButton onClose={onClose} />
        </Grid>

        <FormTextField
          name={`${namespace}.PlaceType`}
          label={t("unitType")}
          gridProps={{ xs: 12, sm: 12, md: 2 }}
        />
        <FormTextField
          name={`${namespace}.Unit`}
          label={t("unitID")}
          gridProps={{ xs: 12, sm: 12, md: 2 }}
        />
        <FormTextField
          name={`${namespace}.Building`}
          label={t("buildingName")}
          gridProps={{ xs: 12, sm: 12, md: 3 }}
        />
        <FormTextField
          name={`${namespace}.Floor`}
          label={t("floor")}
          gridProps={{ xs: 12, sm: 12, md: 2 }}
        />
        <FormTextField
          name={`${namespace}.Post_Comm`}
          label={t("cityName")}
          gridProps={{ xs: 12, sm: 12, md: 3 }}
        />
        <FormTextField
          name={`${namespace}.State`}
          label={t("state")}
          gridProps={{ xs: 12, sm: 12, md: 2 }}
        />
        <FormTextField
          name={`${namespace}.County`}
          label={t("county")}
          gridProps={{ xs: 12, sm: 12, md: 3 }}
        />
        <FormTextField
          name={`${namespace}.Post_Code`}
          label={t("zip")}
          gridProps={{ xs: 12, sm: 12, md: 2 }}
          inputProps={{ maxLength: 5 }}
        />
        <FormTextField
          name={`${namespace}.PostCodeEx`}
          label={t("zipPlus4")}
          gridProps={{ xs: 12, sm: 12, md: 2 }}
          inputProps={{ maxLength: 4 }}
        />
      </Grid>

      {showLatLong && (
        <Grid container spacing={1} className="mb-20">
          <FormTextField
            name={`${namespace}.Latitude`}
            label={t("latitude")}
            gridProps={{ xs: 12, sm: 12, md: 3 }}
          />
          <FormTextField
            name={`${namespace}.Longitude`}
            label={t("longitude")}
            gridProps={{ xs: 12, sm: 12, md: 3 }}
          />
        </Grid>
      )}
    </>
  );
};

export default AddressForm;
