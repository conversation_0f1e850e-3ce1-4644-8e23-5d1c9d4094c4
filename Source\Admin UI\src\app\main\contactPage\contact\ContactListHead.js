import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Tooltip from '@mui/material/Tooltip';
import React from 'react';
import { useTranslation } from 'react-i18next';


const rows = [

	{
		id: 'title',
		align: 'left',
		disablePadding: false,
		label: 'TITLE',
		sort: true
	},
	{
		id: 'fName',
		align: 'left',
		disablePadding: false,
		label: 'FIRSTNAME',
		sort: true
	},
	{
		id: 'mName',
		align: 'left',
		disablePadding: false,
		label: 'MIDDLENAME',
		sort: true
	},
	{
		id: 'lName',
		align: 'left',
		disablePadding: false,
		label: 'LASTNAME',
		sort: true
	},
	{
		id: 'address',
		align: 'left',
		disablePadding: false,
		label: 'ADDRESS',
		sort: true
	},
	{
		id: 'city',
		align: 'left',
		disablePadding: false,
		label: 'CITY',
		sort: true
	},
	{
		id: 'phone',
		align: 'left',
		disablePadding: false,
		label: 'PHONE',
		sort: true
	},
	{
		id: 'email',
		align: 'left',
		disablePadding: false,
		label: 'EMAIL',
		sort: true
	}
];

// const useStyles = makeStyles(theme => ({
// 	actionsButtonWrapper: {
// 		background: theme.palette.background.paper
// 	}
// }));

function ContactListTableHead(props) {
	const { t } = useTranslation('laguageConfig');
	//const classes = useStyles(props);
	//const [selectedUsersMenu, setSelectedUsersMenu] = useState(null);

	const createSortHandler = property => event => {
		props.onRequestSort(event, property);
	};

	// function openSelectedUsersMenu(event) {
	// 	setSelectedUsersMenu(event.currentTarget);
	// }

	// function closeSelectedUsersMenu() {
	// 	setSelectedUsersMenu(null);
	// }

	return (
		<TableHead>
			<TableRow className="h-64">
				{rows.map(row => {
					return (
						<TableCell
							key={row.id}
							align={row.align}
							padding={row.disablePadding ? 'none' : 'default'}
							sortDirection={props.order.id === row.id ? props.order.direction : false}
						>
							{row.sort && (
								<Tooltip
									title="Sort"
									placement={row.align === 'right' ? 'bottom-end' : 'bottom-start'}
									enterDelay={300}
								>
									<TableSortLabel
										active={props.order.id === row.id}
										direction={props.order.direction}
										onClick={createSortHandler(row.id)}
									>
										{t(row.label)}
									</TableSortLabel>
								</Tooltip>
							)}
						</TableCell>
					);
				}, this)}
			</TableRow>
		</TableHead>
	);
}

export default ContactListTableHead;
