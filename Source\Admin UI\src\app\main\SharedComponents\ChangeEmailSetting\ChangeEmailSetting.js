import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../ErrorPage/ErrorPage';
import * as yup from 'yup';
import TextField from '@mui/material/TextField';
import { updateConfirmationDlg } from 'src/app/auth/store/userSlice';
import { useDispatch, useSelector } from 'react-redux';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import JwtService from 'src/app/services/jwtService';
import ConfirmationMessageDlg from '../ConfirmationMessageDialog/ConfirmationMessageDlg';
import { ChangeEmail } from 'src/app/auth/store/registerSlice';
import { useTranslation } from "react-i18next";

const schema = yup.object().shape({
    email: yup.string().email('You must enter a valid email').required('You must enter a email'),
});

const defaultValues = {
    email: '',
};

function ChangeEmailSetting(props) {
    const { onClose, currentEmail: valueProp, ...other } = props;

    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });
    const { isValid, dirtyFields, errors } = formState;
    const { t } = useTranslation("laguageConfig");
    const [pageType, setPageType] = useState(1);
    const emailChange = useSelector(({ auth }) => auth.register.emailChangesuccess);
    const [message, setMessage] = useState('');
    const [newEmail, setNewEmail] = useState("");
    const user = useSelector(({ auth }) => auth.user);
    const dispatch = useDispatch();

    const [open, setOpen] = useState(false);

    useEffect(() => {
        if (emailChange) {
            setOpen(true)
        }

    }, [emailChange]);

    const onSubmit = (model) => {
        dispatch(ChangeEmail({ email: props.currentEmail, newMail: model.email, password: "" }));
    };

    const handleClose = () => {
        setOpen(false);
        dispatch(updateConfirmationDlg());
        JwtService.logout()
    };

    return (
        <div className="w-full">
            <form
                name="registerForm"
                noValidate
                className="flex flex-col justify-center w-full pb-16"
                onSubmit={handleSubmit(onSubmit)}
                autoSave={false}
            >
                <Controller
                    type="text"
                    name="email"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            className="mb-16"
                            label={t("emailAddress")}
                            type="text"
                            error={!!errors.email}
                            helperText={errors?.email?.message}
                            variant="outlined"
                            InputProps={{
                                className: 'pr-2',
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <Icon className="text-20" color="action">
                                            email
                                        </Icon>
                                    </InputAdornment>
                                ),
                            }}
                            required
                        />
                    )}
                />
                <Button
                    variant="contained"
                    color="primary"
                    className=" w-full mx-auto mt-8"
                    aria-label="Register"
                    disabled={_.isEmpty(dirtyFields) || !isValid}
                    type="submit"
                    size="large"
                >
                    {t("changeemail")}
                </Button>
            </form>
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationMessageDlg" />} onReset={() => { }}>
                <ConfirmationMessageDlg
                    id="ringtone-menu"
                    keepMounted
                    open={open}
                    onClose={handleClose}
                    text={t("emailConfirm")}
                />
            </ErrorBoundary>
        </div>
    )
}


export default ChangeEmailSetting;
