import React from 'react';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import FormGroup from '@mui/material/FormGroup';
import { useTranslation } from "react-i18next";
import { FormLabel, FormControl } from '@mui/material';

function OtpRequired(props) {

    const { t } = useTranslation('laguageConfig');
    const handleIsOtpRequiredChange = (event) => {
        props.otpRequiredValue(event.currentTarget.checked)
    }

    return (
        <div className="w-full">
            <FormControl component="fieldset" sx={{ marginLeft: '32px', marginTop: '12px' }}>
                <FormLabel component="legend">{t("otp")} </FormLabel>
                <FormControlLabel
                    control={<Checkbox checked={props.isOtpRequired}
                        onChange={handleIsOtpRequiredChange} name="IsOtpRequired" />}
                    label={t('isOtpRequired')}
                    className='justify-center z-0'
                />
            </FormControl>
        </div>
    );

}

export default OtpRequired;
