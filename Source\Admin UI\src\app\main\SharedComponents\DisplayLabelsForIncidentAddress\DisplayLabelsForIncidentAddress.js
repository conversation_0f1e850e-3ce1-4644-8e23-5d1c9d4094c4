import Grid from "@mui/material/Grid";
import { useTranslation } from "react-i18next";
import { checkValueEmptyOrNull } from "../../utils/utils";
import Button from "@mui/material/Button";
import EditIcon from '@mui/icons-material/Edit';

function DisplayLabelsForIncidentAddress({item, onEdit}) {
    const { t } = useTranslation("laguageConfig");
    return item ? (
        <div>
            <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ display: 'flex' }}>
                <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px 10px 15px' }}>
                    <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ padding: '6px 0px 6px 0px' }}>
                        <span className="callType">{t("locationPlaceOcurred")}</span>
                    </Grid>
                    <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ padding: '0px' }}>
                        <span className="callAddress">{checkValueEmptyOrNull(item.placeName)}</span>
                    </Grid>
                    <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ padding: '0px' }}>
                        <span className="callCaller">{`${checkValueEmptyOrNull(item.Add_Number)} ${checkValueEmptyOrNull(item.AddNum_Suf)} ${checkValueEmptyOrNull(item.St_PreDir)} ${checkValueEmptyOrNull(item.St_Name)} ${checkValueEmptyOrNull(item.St_PosTyp)}  ${checkValueEmptyOrNull(item.St_PosDir)}`}</span>
                    </Grid>
                    <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ padding: '0px' }}>
                        <span className="callCaller">{`${checkValueEmptyOrNull(item.Post_Comm)} ${checkValueEmptyOrNull(item.State)} ${checkValueEmptyOrNull(item.Post_Code)}`}</span>
                    </Grid>
                    <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ marginTop: '8px'}}>
                        <Button variant="outlined" startIcon={<EditIcon />} onClick={onEdit}>
                            {t('edit')}
                        </Button>
                    </Grid>
                </Grid>
            </Grid>
            
        </div>) : null;
}

export default DisplayLabelsForIncidentAddress;
