import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { newUserAudit } from '../../../main/userAuditPage/store/userAuditSlice';
import { motion } from 'framer-motion';
import Icon from '@mui/material/Icon';
import Input from '@mui/material/Input';
import Paper from '@mui/material/Paper';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';
import history from '@history';
import { selectMainTheme } from 'app/store/fuse/settingsSlice';
import { ErrorBoundary } from 'react-error-boundary';
import CommonButton from 'src/app/main/SharedComponents/ReuseComponents/CommonButton';
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import { IconButton, InputAdornment } from '@mui/material';
import CancelIcon from '@mui/icons-material/Cancel';
import _ from "@lodash";
import TablePagination from "@mui/material/TablePagination";
import withRouter from "@fuse/core/withRouter";
import Tooltip from "@mui/material/Tooltip";
import EditIcon from "@mui/icons-material/Edit";
import {
	getAgencyList, getCallCategories,
	setAgencyID, createSearchIndex, removeAgency, copySystemData, getTenantUrlList
} from "../store/agencySlice";
import CallToActionIcon from "@mui/icons-material/CallToAction";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import { setAgencyName } from "../../agencyOptions/store/agencyOptionsSlice";
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import { useDebounce } from '@fuse/hooks';
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";
import {
	IgrGridModule,
	IgrGridToolbar,
	IgrGridToolbarActions,
	IgrGridToolbarAdvancedFiltering,
	IgrGridToolbarHiding,
	IgrGridToolbarPinning,
	IgrGrid,
	IgrColumn,
	ColumnPinningPosition,
	ColumnPinning
} from "@infragistics/igniteui-react-grids";
import { getCityCountyStateZipCode } from '../../administration/store/masterAddressSlice';

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const AgencyList = () => {
	const mainTheme = useSelector(selectMainTheme);
	const { t } = useTranslation('laguageConfig');

	const user = useSelector(({ auth }) => auth.user);
	const isloadingvalue = useSelector(({ agency }) => agency.agency.isloading);
	const agency = useSelector(({ agency }) => agency.agency.data);
	const agencyTotalCount = useSelector(({ agency }) => agency.agency.totalCount);
	const navbarTheme = useSelector(selectNavbarTheme);

	const dispatch = useDispatch();

	const [loading, setLoading] = useState();
	const [searchText, setSearchText] = useState("");
	const [data, setData] = useState(agency);
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(100);
	const [totalCount, setTotalCount] = useState(agencyTotalCount);
	const [order, setOrder] = useState({
		direction: "asc",
		id: "name",
	});
	const [open, setOpen] = React.useState(false);
	const [value, setValue] = React.useState();

	const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
	document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
	let colorCode = getNavbarTheme();
	const rowsPerPageOptions = getRowsPerPageOptions();
	//.....Call Custom hook to handle window height adjustments dynamically
	const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
	// Runs only once when the component mounts
	const gridKey = navbarTheme.palette.mode;

	const gridRef = useRef(null);

	useEffect(() => {
		dispatch(getCityCountyStateZipCode(null, null));
		dispatch(getCallCategories());
	}, [dispatch]);

	useEffect(() => {
		dispatch(newUserAudit({
			activity: "Access Agency",
			user: user,
			appName: "Admin",
		}));
		// eslint-disable-next-line
	}, []);

	useEffect(() => {
		setLoading(isloadingvalue)
	}, [isloadingvalue]);

	const addAgency = () => {
		dispatch(setAgencyID("0"));
		history.push("/admin/agency");
	};

	const search = useDebounce((search, page, rowsPerPage, order) => {
		dispatch(getAgencyList(order.id, order.direction, page * rowsPerPage, rowsPerPage,
			search === '' ? null : search
		));
	}, 300);

	useEffect(() => {
		if (searchText !== '') {
			search(searchText, page, rowsPerPage, order);
		} else {
			dispatch(getAgencyList(order.id, order.direction, page * rowsPerPage, rowsPerPage,
				searchText === '' ? null : searchText
			));
		}
	}, [dispatch, searchText, page, rowsPerPage, order]);

	// Separate tenant URL fetch
	useEffect(() => {
		dispatch(getTenantUrlList());
	}, [dispatch]);

	useEffect(() => {
		setData(agency);
		setTotalCount(agencyTotalCount);
	}, [agency, agencyTotalCount]);

	// Optimize data setting with memoization
	const memoizedData = useMemo(() => agency, [agency]);
	const memoizedTotalCount = useMemo(() => agencyTotalCount, [agencyTotalCount]);

	useEffect(() => {
		setData(memoizedData);
		setTotalCount(memoizedTotalCount);
	}, [memoizedData, memoizedTotalCount]);

	function handleChangePage(event, value) {
		setPage(value);
	}

	function handleChangeRowsPerPage(event) {
		setRowsPerPage(event.target.value);
		setPage(0);
	}

	const handleClickEdit = (agency) => {
		dispatch(setAgencyID(agency._id));
		history.push("/admin/agency");
	};

	const handleClickUpdate = (agency) => {
		{/* For creating search Index Development purpose only */ }
		dispatch(createSearchIndex(agency.code));
	};

	const handleClickOptions = (data) => {
		dispatch(setAgencyName(data.name));
		history.push(`/admin/agencyOptionsList/${data.code}`);
	};

	const handleClickCopySystemData = (data) => {
		//--call api
		dispatch(copySystemData(data.code));
	};

	const handleClickDelete = (agencyID) => {
		setValue(agencyID._id);
		setOpen(true);
	};

	const handleClose = (newValue) => {
		setOpen(false);
		if (newValue) {
			dispatch(removeAgency(value));
		}
	};

	const ActionIcons = (n) => {
		// let x = checkData(n)

		if (n && n.dataContext) {
			let x = n.dataContext.cell.row.data;

			return (
				<>
					<div style={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
						<Tooltip title={t("edit")}>
							<IconButton
								aria-label="edit"
								color="inherit"
								onClick={() => handleClickEdit(x)}
								size="large"
							>
								<EditIcon />
							</IconButton>
						</Tooltip>

						{/* {x.code !== "System" && (
								<Tooltip title="Copy System Data">
									<IconButton
										aria-label="Copy System Data"
										color="inherit"
										onClick={() => handleClickCopySystemData(x)}
										size="large"
									>
										<FileCopyIcon />
									</IconButton>
								</Tooltip>
							)} */}

						<Tooltip title={t("options")}>
							<IconButton
								aria-label="tty"
								color="inherit"
								onClick={() => handleClickOptions(x)}
								size="large"
							>
								<CallToActionIcon />
							</IconButton>
						</Tooltip>

						{/* For creating search Index Development purpose only */}
						{/* <Tooltip title="Edit">
							<IconButton
								aria-label="edit"
								color="primary"
								onClick={() => handleClickUpdate(x)}
								size="large"
							>
								<UpgradeIcon />
							</IconButton>
						</Tooltip> */}
					</div>
				</>
			);
		}

	};

	const groupByRowTemplate = (ctx) => {
		const groupRow = ctx.dataContext.implicit;
		return (
			<div style={{ padding: '10px', fontSize: '15px' }}>
				<span>{groupRow.value}</span> - {groupRow.records.length} Items
			</div>
		);
	};

	return (
		<>
			{loading && < CircularProgressLoader loading={loading} />}
			<FusePageCarded
				classes={{
					content: 'flex',
					header: 'min-h-72 h-72 sm:h-136 sm:min-h-136'
				}}
				header={
					<div className="flex flex-1 p-32 w-full items-center justify-between">
						<div className="flex items-center">
							<Icon
								component={motion.span}
								initial={{ scale: 0 }}
								animate={{ scale: 1, transition: { delay: 0.2 } }}
								className="text-32">business</Icon>
							<Typography
								component={motion.span}
								initial={{ x: -20 }}
								animate={{ x: 0, transition: { delay: 0.2 } }}
								delay={300}
								className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
								{t('agency')}
							</Typography>
						</div>

						<div className="flex flex-1 items-center justify-center px-12">
							<StyledEngineProvider injectFirst>
								<ThemeProvider theme={mainTheme}>
									<Paper
										sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
										component={motion.div}
										initial={{ y: -20, opacity: 0 }}
										animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
										className="flex items-center w-full max-w-512 px-8 py-4 rounded-8" elevation={1}>
										<Icon color="action">search</Icon>
										<Input
											placeholder={t('search')}
											className="flex flex-1 mx-8"
											disableUnderline
											fullWidth

											value={searchText}
											inputProps={{
												'aria-label': 'Search'
											}}
											onChange={(ev) => setSearchText(ev.target.value)}
											endAdornment={
												<InputAdornment position='end'>
													<IconButton
														onClick={e => setSearchText("")}
													>
														<CancelIcon />
													</IconButton>
												</InputAdornment>
											}
										/>
									</Paper>
								</ThemeProvider>
							</StyledEngineProvider>
						</div>
						<motion.div
							initial={{ opacity: 0, y: 40 }}
							animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
						>
							<ErrorBoundary
								FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addNewAgency" />} onReset={() => window.location.reload()} >
								<CommonButton styleClass="whitespace-no-wrap normal-case" btnName={t("addNewAgency")} parentCallback={addAgency}></CommonButton>
							</ErrorBoundary>
						</motion.div>
					</div>
				}
				content={
					<div className="w-full flex flex-col" key={gridKey}>
						<div className="igrGridClass">
							<div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
								<TablePagination
									className="tablePaging"
									component="div"
									count={totalCount}
									rowsPerPage={rowsPerPage}
									page={page}
									backIconButtonProps={{
										"aria-label": "Previous Page",
									}}
									nextIconButtonProps={{
										"aria-label": "Next Page",
									}}
									onPageChange={handleChangePage}
									onRowsPerPageChange={handleChangeRowsPerPage}
									rowsPerPageOptions={rowsPerPageOptions}
								/>
							</div>

							<div>
								<IgrGrid
									id="grid"
									// ✅ This forces the grid to re-render when theme changes
									autoGenerate="false"
									data={data}
									primaryKey="_id"
									ref={gridRef}
									height={`${gridHeight}px`}
									rowHeight={60}
									groupRowTemplate={groupByRowTemplate}
									filterMode="ExcelStyleFilter"
									allowFiltering={false}
									moving={true}
									allowAdvancedFiltering={true}
									allowPinning={true}
									pinning={pinningConfig}
									virtualization="true"
									virtualizationState={{
										startIndex: page * rowsPerPage,
										chunkSize: rowsPerPage
									}}
								>
									<IgrGridToolbar>
										<IgrGridToolbarActions>
											<IgrGridToolbarAdvancedFiltering />
											<IgrGridToolbarHiding />
											<IgrGridToolbarPinning />
										</IgrGridToolbarActions>
									</IgrGridToolbar>
									<IgrColumn
										field="name"
										header={t("name")}
										width="280px"
										resizable={true}
										groupable={true}
										sortable={true}
									/>
									<IgrColumn
										header={t("parentAgency")}
										field="parentAgency"
										width="280px"
										resizable={true}
										groupable={true}
										sortable={true}
									/>
									<IgrColumn
										header={t("type")}
										field="type"
										width="130px"
										resizable={true}
										groupable={true}
										sortable={true}
									/>
									<IgrColumn
										header={t("address")}
										field="address"
										width="280px"
										resizable={true}
										groupable={true}
										sortable={true}
									/>
									<IgrColumn
										header={t("city")}
										field="city"
										width="150px"
										resizable={true}
										groupable={true}
										sortable={true}
									/>
									<IgrColumn
										header={t("state")}
										field="state"
										width="100px"
										resizable={true}
										groupable={true}
										sortable={true}
									/>
									<IgrColumn
										header={t("county")}
										field="county"
										width="180px"
										resizable={true}
										groupable={true}
										sortable={true}
									/>
									<IgrColumn
										header={t("zip")}
										field="zip"
										width="180px"
										resizable={true}
										groupable={true}
										sortable={true}
									/>
									<IgrColumn
										field="action"
										header={t("action")}
										width="200px"
										resizable={true}
										pinned={true}
										bodyTemplate={ActionIcons}
									/>
								</IgrGrid>
							</div>

							<ErrorBoundary
								FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
								<ConfirmationDialog
									id="ringtone-menu"
									keepMounted
									open={open}
									text={t("Areyousureyouwanttodelete")}
									onClose={handleClose}
									value={value}
								>
								</ConfirmationDialog>
							</ErrorBoundary>
						</div>
					</div >
				}
			/>
		</>
	);
}

export default withRouter(AgencyList)