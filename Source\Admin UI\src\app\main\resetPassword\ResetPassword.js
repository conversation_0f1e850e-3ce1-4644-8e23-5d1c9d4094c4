import { motion } from 'framer-motion';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import React from 'react';
import { Link } from 'react-router-dom';
import { styled, darken } from '@mui/material/styles';
import JWTResetPasswordTab from './tabs/JWTResetPasswordTab';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../SharedComponents/ErrorPage/ErrorPage";

const Root = styled('div')(({ theme }) => ({
	background: `linear-gradient(to right, ${theme.palette.primary.dark} 0%, ${darken(
		theme.palette.primary.dark,
		0.5
	)} 100%)`,
	color: theme.palette.primary.contrastText,

	'& .Login-leftSection': {},

	'& .Login-rightSection': {
		background: `linear-gradient(to right, ${theme.palette.primary.dark} 0%, ${darken(
			theme.palette.primary.dark,
			0.5
		)} 100%)`,
		color: theme.palette.primary.contrastText,
	},
}));

function ResetPassword() {

	return (
		<Root className="flex flex-col flex-auto items-center justify-center shrink-0 p-16 md:p-24">
			<motion.div
				initial={{ opacity: 0, scale: 0.6 }}
				animate={{ opacity: 1, scale: 1 }}
				className="flex w-full max-w-400 md:max-w-3xl rounded-20 shadow-2xl overflow-hidden"
			>
				<div className="Login-rightSection hidden md:flex flex-1 items-center justify-center p-64">
					<div className="max-w-320">
						<motion.div
							initial={{ opacity: 0, y: 40 }}
							animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
						>
							<Typography variant="h3" color="inherit" className="font-semibold leading-tight">
								Welcome <br />
								to {process.env.REACT_APP_TITLE}!
							</Typography>
						</motion.div>

					</div>
				</div>
				<Card
					className="Login-leftSection flex flex-col w-full max-w-sm items-center justify-center shadow-0"
					square
				>
					<CardContent className="flex flex-col items-center justify-center w-full py-96 max-w-320">
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1, transition: { delay: 0.2 } }}
						>
							<div className="flex items-center mb-32">
								<img className="logo-icon w-48" src="assets/images/logo/fuse.svg" alt="logo" />
								<div className="border-l-1 mr-4 w-1 h-40" />
								<div>
									<Typography className="text-24 font-800 logo-text" color="inherit">
										{process.env.REACT_APP_TITLE} Login
									</Typography>
								</div>
							</div>
						</motion.div>

						<ErrorBoundary
							FallbackComponent={(props) => <ErrorPage {...props} componentName="JWTResetPasswordTab" />} onReset={() => { }} >
							<JWTResetPasswordTab />
						</ErrorBoundary>

						<div className="flex flex-col items-center justify-center pt-16">
							<span className="font-medium">Already have an account?</span>
							<Link className="font-medium" to="/login">
								Login
							</Link>
						</div>
					</CardContent>
				</Card>
			</motion.div>
		</Root>
	);
}

export default ResetPassword;
