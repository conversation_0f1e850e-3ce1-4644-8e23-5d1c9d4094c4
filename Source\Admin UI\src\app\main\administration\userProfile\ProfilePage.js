import { motion } from "framer-motion";
import FusePageSimple from "@fuse/core/FusePageSimple";
import Avatar from "@mui/material/Avatar";
import Typography from "@mui/material/Typography";
import React, { useState, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";

import { useTranslation } from "react-i18next";
import { Toolbar, Grid, CircularProgress, IconButton } from "@mui/material";

import { styled } from "@mui/material/styles";
import UserDefaultLocationSetting from "../../SharedComponents/UserDefaultLocationSettings/UserDefaultLocationSetting";
import PersonalInformation from "../components/PersonalInformation";
import RPSAuthentication from "../components/RPSAuthentication";
import AppHeaderBar from "../components/AppHeaderBar";
import UserDefaultMFATypeSetting from "../../SharedComponents/UserDefaultMFATypeSettings/UserDefaultMFATypeSetting";
import UserDefaultAgencySetting from "../../SharedComponents/UserDefaultAgencySettings/UserDefaultAgencySetting";
import { getUserDefaultSettings } from "../../../auth/store/userSlice";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { makeStyles } from "@mui/styles";
import { green } from "@mui/material/colors";
import UserNCICSettings from "../../SharedComponents/UserNCICSettings/UserNCICSettings";

const useStyles = makeStyles((theme) => ({
  layoutHeader: {
    height: 120,
    minHeight: 100,
    paddingTop: 20,
    [theme.breakpoints.down("md")]: {
      height: 240,
      minHeight: 240,
    },
  },
  buttonSuccess: {
    backgroundColor: green[500],
    "&:hover": {
      backgroundColor: green[700],
    },
  },
  wrapper: {
    margin: theme.spacing(1),
    position: "relative",
  },
  buttonProgress: {
    color: green[500],
    position: "absolute",
    top: "5%",
    left: "5%",
    marginTop: 0,
    marginLeft: -12,
  },
}));

function ProfilePage() {
  const classes = useStyles();
  const { t } = useTranslation("laguageConfig");
  const dispatch = useDispatch();
  const user = useSelector(({ auth }) => auth.user);
  const defaultlocation = useSelector(({ auth }) => auth.user.defaultlocation);

  const register = useSelector(({ auth }) => auth.register);
  const setDefaultAgency = useSelector(
    ({ auth }) => auth.user.setDefaultAgency
  );

  const isChanges = setDefaultAgency;

  useEffect(() => {
    let id = localStorage.getItem("userId");
    dispatch(getUserDefaultSettings(id));
  }, []);

  return (
    <FusePageSimple
      classes={{
        header: classes.layoutHeader,
        toolbar: "min-h-56 h-56 items-end",

      }}
      header={
        <img
          className="h-60 lg:h-120 object-cover w-full"
          src="assets/images/profile/morain-lake.jpg"
          alt="Profile Cover"
        />
      }
      contentToolbar={
        <>
          <div className="w-full pt-48 px-24 pb-48 flex flex-col md:flex-row flex-1 items-center">
            <div className="flex flex-1 flex-col items-center justify-center md:flex-row md:items-center md:justify-start">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1, transition: { delay: 0.1 } }}
              >
                <Avatar
                  sx={{
                    borderWidth: 4,
                    borderStyle: "solid",
                    borderColor: "background.default",
                  }}
                  className="-mt-64  w-128 h-128"
                  src={
                    user.data.profilePic === undefined
                      ? "assets/images/avatars/profile.jpg"
                      : user.data.profilePic
                  }
                />
              </motion.div>
              <Typography component={motion.span} initial={{ x: -20 }} animate={{ x: 0, transition: { delay: 0.2 } }} delay={300} className="md:mx-24 text-24 md:text-32 my-8 md:my-0" variant="h4" color="inherit" >
                {user.data.fullName}
              </Typography>
            </div>
          </div>
        </>
      }
      content={
        <div className="flex flex-col flex-1 p-8 md:ltr:pr-32 md:rtl:pl-32">
          <Grid>
            <Grid item className="grid grid-cols-2">
              <Grid item xs={6} className="pl-4">
                <Card className="w-full mb-16 rounded-8 ml-4 shadow" style={{ height: "96%" }}>
                  <AppHeaderBar headerText={t("personalInformation")} />
                  <CardContent>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="PersonalInformation" />} onReset={() => { }} >
                      <PersonalInformation />
                    </ErrorBoundary>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={6} className="pl-4">
                <Card className="w-full mb-16 rounded-8 ml-4 shadow" style={{ height: "96%" }}>
                  <AppHeaderBar headerText={t("rpsAuthentication")} />
                  <CardContent>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="RPSAuthentication" />} onReset={() => { }} >
                      <RPSAuthentication />
                    </ErrorBoundary>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Grid item className="grid grid-cols-3">
              <Grid item xs={6} className="pl-4">
                <Card className="w-full mb-16 rounded-8 ml-4 shadow" style={{ height: "96%" }}>
                  <AppHeaderBar headerText={t("defaultLocation")} />
                  <CardContent>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="UserDefaultLocationSetting" />} onReset={() => { }} >
                      <UserDefaultLocationSetting user={defaultlocation} flag={false} />
                    </ErrorBoundary>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={6} className="pl-4">
                <Card className="w-full mb-16 rounded-8 ml-4 shadow" style={{ height: "96%" }}>
                  <AppHeaderBar headerText={t("defaultMFAType")} />
                  <CardContent>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="UserDefaultMFATypeSetting" />} onReset={() => { }}>
                      <UserDefaultMFATypeSetting mfaType={setDefaultAgency == undefined || setDefaultAgency.length == 0 ? "" : setDefaultAgency.mfaType} registerUser={false} />
                    </ErrorBoundary>
                  </CardContent>
                </Card>
              </Grid>

              {user.data.isSuperAdmin && setDefaultAgency != undefined && (
                <Grid item xs={6} className="pl-4">
                  <Card className="w-full mb-16 rounded-8 ml-4 shadow" style={{ height: "96%" }}>
                    <AppHeaderBar headerText={t("defaultAgency")} />
                    <CardContent>
                      <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="UserDefaultAgencySetting" />} onReset={() => { }}>
                        <UserDefaultAgencySetting user={user.data} registerUser={false} defaultAgencyValue={setDefaultAgency == undefined || setDefaultAgency.length == 0 ? null : setDefaultAgency.defaultAgency} agencies={setDefaultAgency == undefined || setDefaultAgency.length == 0 ? [] : setDefaultAgency.agencies} />
                      </ErrorBoundary>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
            <Grid item className="grid grid-cols-3">
              <Grid item xs={6} className="pl-4">
                <Card className="w-full mb-16 rounded-8 ml-4 shadow" style={{ height: "96%" }}>
                  <AppHeaderBar headerText={t("ncicAuthentication")} />
                  <CardContent>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="UserNCICSettings" />} onReset={() => { }} >
                      <UserNCICSettings />
                    </ErrorBoundary>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </div>
      }
    />
  );
}

export default ProfilePage;


