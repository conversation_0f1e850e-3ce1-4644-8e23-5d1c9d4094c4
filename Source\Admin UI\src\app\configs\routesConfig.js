import FuseUtils from '@fuse/utils';
import { Navigate } from 'react-router-dom';
import settingsConfig from 'app/configs/settingsConfig';
import LoginConfig from '../main/login/LoginConfig';
import ResetPasswordConfig from '../main/resetPassword/ResetPasswordConfig';
import AdministrationAppConfig from '../main/administration/AdministrationAppConfig';
import AgencyAppConfig from '../main/agencyPage/AgencyAppConfig';
import AgencyAddConfig from '../main/agencyAdd/AgencyAddConfig';
import ContactAppConfig from '../main/contactPage/ContactAppConfig';
import ContactAddConfig from '../main/contactAdd/ContactAddConfig';
import MaintenancePageConfig from '../main/maintenance/MaintenancePageConfig';
import UserAuditAppConfig from '../main/userAuditPage/UserAuditAppConfig';
import UnitAppConfig from '../main/Units/UnitsAppConfig';
import TeamMasterAppConfig from '../main/TeamMasters/TeamMasterAppConfig';
import NfirsAppConfig from '../main/NFIRS/NfirsAppConfig';
import ErrorLogConfig from '../main/ErrorLog/ErrorLogConfig';
import Call911Config from '../main/911Call/Call911Config';
import classOfServiceAppConfig from '../main/ClassOfServicePage/ClassOfServiceAppConfig';
import classOfServiceAddConfig from '../main/ClassOfServiceAdd/ClassOfServiceAddConfig';
import FileUploadConfig from '../main/FileUpload/FileUploadConfig';
import AgencyOption from '../main/agencyOptions/AgencyOptionsConfig';
import ComponentConfig from '../main/components/ComponentConfig';
import ChangedPasswordConfig from '../main/ChangedPassword/ChangedPasswordConfig'
import InvalidSession from '../main/invalidSession/invalidSessionConfig';
import AgencyShiftConfig from '../main/AgencyShiftTime/AgencyShiftConfig';
import AgencyShiftAllocationConfig from '../main/agencyShiftAllocation/AgencyShiftAllocationConfig';
import AgencyDepartmentConfig from '../main/AgencyDepartmentDetails/AgencyDepartmentConfig';
import SettingsConfig from '../main/administration/Settings/settingsConfig';
import TwitterAutorizeConfig from '../main/TwitterAutorize/TwitterAutorizeConfig';
import QuikTipConfig from '../main/QuikTip/QuikTipConfig';
import UpdateRequestAppConfig from '../main/UpdateRequest/UpdateRequestAppConfig';

const routeConfigs = [
	LoginConfig,
	ResetPasswordConfig,
	AdministrationAppConfig,
	AgencyAddConfig,
	AgencyAppConfig,
	ContactAddConfig,
	ContactAppConfig,
	MaintenancePageConfig,
	UserAuditAppConfig,
	UnitAppConfig,
	NfirsAppConfig,
	ErrorLogConfig,
	TeamMasterAppConfig,
	AgencyShiftConfig,
	AgencyShiftAllocationConfig,
	AgencyDepartmentConfig,
	Call911Config,
	classOfServiceAppConfig,
	classOfServiceAddConfig,
	FileUploadConfig,
	AgencyOption,
	ComponentConfig,
	ChangedPasswordConfig,
	InvalidSession,
	SettingsConfig,
	TwitterAutorizeConfig,
	QuikTipConfig,
	UpdateRequestAppConfig
];

const routes = [
	...FuseUtils.generateRoutesFromConfigs(routeConfigs, settingsConfig.defaultAuth),
	{
		path: '/',
		component: () => <Navigate to="/login" />
	}
];

export default routes;
