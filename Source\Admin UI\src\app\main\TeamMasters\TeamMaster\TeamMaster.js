import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { useParams } from "react-router";
import TablePagination from "@mui/material/TablePagination";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import { newUserAudit } from "../../../main/userAuditPage/store/userAuditSlice";
import { removeTeam, getTeams, searchTeam } from "../../store/teamSlice";
import history from '@history';
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import CircularProgressLoader from "../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import AddTeamMaster from "../AddTeamMaster/AddTeamMaster";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import GroupsIcon from '@mui/icons-material/Groups';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils"
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rowsConst = [
    {
        id: '_id',
        align: 'left',
        disablePadding: false,
        label: 'ID',
        sort: true
    },
    {
        id: "name",
        align: "left",
        disablePadding: false,
        label: "unitName",
        sort: true,
    },
    {
        id: "teamLeader",
        align: "left",
        disablePadding: false,
        label: "teamlead",
        sort: true,
    },
    {
        id: "users",
        align: "left",
        disablePadding: false,
        label: "users",
        sort: true,
    },
    {
        id: "department",
        align: "left",
        disablePadding: false,
        label: "Department",
        sort: true,
    },
    {
        id: "departmentid",
        align: "left",
        disablePadding: false,
        label: "departmentid",
        sort: true,
    },
    {
        id: "color",
        align: "left",
        disablePadding: false,
        label: "color",
        sort: true,
    },
    {
        id: "count",
        align: "left",
        disablePadding: false,
        label: "count",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

let Data = [];

function TeamMaster() {
    const navbarTheme = useSelector(selectNavbarTheme);
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();
    const routeParams = useParams();
    const gridRef = useRef(null);
    const teamRef = useRef(null);

    const TeamData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.teammaster.data);
    const user = useSelector(({ auth }) => auth.user);
    const teamsTotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.teammaster.totalCount);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.teammaster.isloading);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [msg, setmsg] = React.useState("");
    const [loading, setLoading] = useState();
    const [searchText, setSearchText] = React.useState("");
    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [data, setData] = React.useState(TeamData);
    const [countData, setCountData] = React.useState(teamsTotalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "name",
    });
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    let colorCode = getNavbarTheme();

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access Teams",
                user: user,
                appName: "Admin",
            })
        );
    }, []);

    const deleteUnit = (n, msg) => {
        setOpen(true);
        setRemoveID(n._id);
        setmsg(msg);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeTeam(removeID, routeParams.code, pageIndex, rowsPerPage, order.id, order.direction, searchText === '' ? null : searchText));
            setCountData(countData - 1);
        }
    };

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const search = useDebounce((search, pageIndex, rowsPerPage) => {
        dispatch(getTeams(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, search === '' ? null : search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, pageIndex, rowsPerPage);
        } else {
            dispatch(getTeams(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, pageIndex, order, rowsPerPage, searchText, routeParams.code]);

    useEffect(() => {
        setData(TeamData);
        setCountData(teamsTotalCount);
    }, [TeamData, teamsTotalCount]);


    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    const ActionIcons = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div style={{ display: "flex" }}>
                    <Tooltip title={t("edit")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => teamRef.current.handleOpen(x, PagingDetails, true, "Update")}
                            size="large"
                        >
                            <EditIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={t("delete")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            disabled={x.isActive}
                            onClick={() => deleteUnit(x)}
                            size="large"
                        >
                            <DeleteIcon />
                        </IconButton>
                    </Tooltip>
                </div>
            )
        }
    };

    const ColorCode = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div style={{ display: "flex", paddingLeft: "7.2rem", margin: "1.2rem" }}>
                    <FiberManualRecordIcon style={{ color: x.color }} />
                </div>
            )
        }
    };

    function getTeamUsers(n) {
        let data;
        if (n.users === undefined) {
            return ("");
        }
        else {
            if (n.users.length > 0) {
                return (
                    data = n.users.map(number => number.fname + ' ' + number.lname).join(", ")
                );
            }
            else {
                return ("");
            }
        }
    }

    function getTeamLeaders(n) {
        let data;
        if (n.teamLeader === undefined) {
            return ("");
        }
        else {
            if (n.teamLeader.length > 0) {
                return (
                    data = n.teamLeader.map(number => number.fname + ' ' + number.lname).join(", ")
                );
            }
            else {
                return ("");
            }
        }
    }

    const rowData = data.map(item => {
        const row = {};
        let teamusers = getTeamUsers(item);
        let teamLeaders = getTeamLeaders(item)
        rowsConst.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["name"] = item.name
            row["users"] = teamusers;
            row["department"] = item.department[0] ? item.department[0].name : "";
            row["departmentid"] = item.department[0] ? item.department[0]._id : "";
            row["teamLeader"] = teamLeaders;
        });
        return row;
    });

    let PagingDetails = {
        sortField: order.id,
        sortDirection: order.direction,
        pageIndex: pageIndex * rowsPerPage,
        pageLimit: rowsPerPage
    }

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <GroupsIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("team")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addTeam" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addTeam")} parentCallback={() => teamRef.current.handleOpen(Data, PagingDetails, false, "Add")}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={countData}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{ "aria-label": "Previous Page", }}
                                nextIconButtonProps={{ "aria-label": "Next Page", }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />
                        </div>

                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="name"
                                        field="name"
                                        header={t("name")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="teamLeader"
                                        header={t("teamlead")}
                                        field="teamLeader"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="users"
                                        header={t("users")}
                                        field="users"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="department"
                                        header={t("department")}
                                        field="department"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="color"
                                        header={t("color")}
                                        field="color"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        bodyTemplate={ColorCode}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("teamDeleteMsg")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="AddTeamMaster" />} onReset={() => { }} >
                                <AddTeamMaster ref={teamRef} />
                            </ErrorBoundary>
                        </div>
                    </div>
                }
            />
        </>
    );
}

export default TeamMaster;