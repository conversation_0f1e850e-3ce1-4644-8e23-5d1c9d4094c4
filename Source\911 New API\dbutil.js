const mongoose = require("mongoose");
const dotenv = require('dotenv').config();
const MongoClient = require('mongodb').MongoClient;

global.mongo = global.mongo || {};
const initClientDbConnection = async () => {
  if (!global.mongo.client) {
    global.mongo.client = new MongoClient(process.env.ConnectionString)
  }
  await global.mongo.client.connect();
  return global.mongo.client;
};


module.exports = {
  initClientDbConnection
};
