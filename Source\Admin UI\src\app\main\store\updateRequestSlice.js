import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const addMasterAddressRequest = (data) => async dispatch => {
    try {
        await axios.post(`admin/api/updateRequest/addMasterAddressRequest`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                }
            }).catch(error => {
                console.log(error);
            });
    } catch (e) {
        return console.error(e.message);
    }
}

export const updateMasterAddressRequest = (data) => async dispatch => {
    dispatch(setLoading(true));
    try {
        await axios.post(`admin/api/updateRequest/updateMasterAddressRequest`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(setUpdateSuccess(true));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                console.log(error);
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const getMasterAddressRequest = (sortField, sortDirection, pageIndex, pageLimit, searchText, state, county) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`admin/api/updateRequest/getMasterAddressRequest/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${state}/${county}`)
            .then((response) => {
                if (response.status === 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setAddressUpdateList(listData.list));
                    dispatch(setTotalCount(listData.totalCount));
                    //To prevent the loading spinner from showing after the data is loaded
                    dispatch(setUpdateSuccess(false));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

const initialState = {
    addressUpdateList: [],
    totalCount: 0,
    loading: false,
    updateSuccess: true,
}

const updateRequestSlice = createSlice({
    name: 'updateRequest',
    initialState,
    reducers: {
        setAddressUpdateList: (state, action) => {
            state.addressUpdateList = action.payload;
        },
        setTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        },
        setUpdateSuccess: (state, action) => {
            state.updateSuccess = action.payload;
        },
    },
    extraReducers: {}
});

export const {
    setAddressUpdateList,
    setTotalCount,
    setLoading,
    setUpdateSuccess,
} = updateRequestSlice.actions;

export default updateRequestSlice.reducer;