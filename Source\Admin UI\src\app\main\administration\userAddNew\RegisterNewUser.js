import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import './RegisterNewUser.css';
import InputAdornment from '@mui/material/InputAdornment';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import _ from '@lodash';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import history from '@history';
import { Grid, CardContent, Card, InputLabel, useTheme, Autocomplete, TextField, Typography } from '@mui/material';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import makeStyles from '@mui/styles/makeStyles';
import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import FormGroup from '@mui/material/FormGroup';
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import { getAgencyData } from '../../../main/agencyPage/store/agencySlice';
import { getAllUsers, resetEditUser } from '../store/usersSlice';
import { GetUserByEmail, submitRegister, updateUser, ClearUserData, setLoader } from '../../../auth/store/registerSlice';
import { showMessage } from 'app/store/fuse/messageSlice';
import { isEmptyOrNull } from '../../utils/utils'
import UserDefaultLocationSetting from '../../SharedComponents/UserDefaultLocationSettings/UserDefaultLocationSetting';
import AppHeaderBar from '../components/AppHeaderBar';
import UserDefaultMFATypeSetting from '../../SharedComponents/UserDefaultMFATypeSettings/UserDefaultMFATypeSetting';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import AccessRightsSettings from '../../SharedComponents/AccessRightsSettings/AccessRightsSettings';
import ChangeEmailSettingDlg from '../../SharedComponents/ChangeEmailSetting/ChangeEmailSettingDlg';
import { clearDepartment, getMasterDepartmentDetails } from '../../store/departmentSlice';
import PhoneInput from 'react-phone-input-2'
import OtpRequired from '../../SharedComponents/OtpRequired/OtpRequired';
import UserStatusPermission from '../../SharedComponents/UserStatusPermission/UserStatusPermission';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { Select, MenuItem } from '@mui/material';

const useStyles = makeStyles({
	w50: {
		width: '100%',
		marginBottom: "1.6rem"
	}, root: {
		display: 'flex',
	},

});

const schema = yup.object().shape({
	fname: yup.string().required('Please enter First Name.').min(2, 'Please enter valid First Name'),
	lname: yup.string().required('Please enter Last Name.').min(2, 'Please enter valid last name'),
	email: yup.string().email('You must enter a valid email').required('You must enter a email'),
});

const defaultValues = {
	lname: '',
	fname: '',
	email: '',
	phone: '',
	agencies: [],
	mapZoomLevel: 'CITY',
	mfatype: 'email',
};

function RegisterUser() {
	const { t } = useTranslation('laguageConfig');
	const classes = useStyles();
	const ChangeEmailRef = useRef();
	const dispatch = useDispatch();
	const register = useSelector(({ auth }) => auth.register);
	const userExist = useSelector(({ auth }) => auth.register.userExist);
	const userIsDisabled = useSelector(({ auth }) => auth.register.userIsDisabled);
	const isloading = useSelector(({ auth }) => auth.register.isloading);
	const users = useSelector(({ administration }) => administration.user.allusers);
	const agencyusers = users.filter(y => y.isActive === true).map((x) => (x.userAgencies[0].agencyCode));
	const selectedUser = useSelector(({ administration }) => administration.user.selectedUser);
	const agencyList = useSelector(({ agency }) => agency.agency.data);
	const departmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
	const [ncicPassword, setNcicPassword] = useState('');
	const [ncicUserName, setNcicUserName] = useState('');
	const [terminalId, setTerminalID] = useState('');
	const [departmentValue, setDepartmentValue] = React.useState("");
	const [defaultApp, setDefaultAppValue] = React.useState("");
	const [isNCICITokenRequired, setisNCICITokenRequired] = React.useState(true);
	const [isPermanentLogin, setIsPermanentLogin] = React.useState(false);
	const [isVideoCallRecieve, setIsVideoCallRecieve] = React.useState(false);
	const [showTwitterIcon, setShowTwitterIcon] = React.useState(false);
	const [isFireAlert, setIsFireAlert] = React.useState(false);
	const formRef = useRef(null);
	const emailInputRef = useRef(null);
	const nameInputRef = useRef(null);
	const theme = useTheme();

	const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
		mode: 'onChange',
		defaultValues,
		resolver: yupResolver(schema),
	});
	const { isValid, dirtyFields, errors } = formState;
	const user = useSelector(({ auth }) => auth.user);
	const [agencies, setAgencies] = useState([]);
	const [isAceesRightShow, setAccessRightShow] = React.useState(false);
	const [showAccessRight, setShowAccessRight] = React.useState([]);
	const [defaultLocationValue, setdefaultLocationValue] = React.useState('CITY');
	const [userAccountType, setUserAccountType] = React.useState('FullTime');
	const [agencyAdminSetting, setAgencyAdminSetting] = React.useState('');
	const [cityValue, setCityValue] = React.useState(null);
	const [countyValue, setCountyValue] = React.useState(null);
	const [value, setValue1] = useState('us')
	const [countryCode, setCountryCode] = useState('us');
	const [phoneNumber, setPhoneNumber] = useState();
	const [dialCode, setDialCode] = useState();
	const [userAccessRights, setUserAccessRights] = React.useState(
		{
			Users: true,
			Contact: true,
			Server: true,
			Icons: true,
			CallCategory: true,
			CallViolation: true,
			CallType: true,
			CallResponse: true,
			Call: true,
			FileUpload: true,
			ClassofService: true,
			Incident: true,
			Dispatch: true,
			Chat: true,
			Current: true,
			Replay: true,
			Watches: true,
			SearchFeature: true,
			BroadcastMessages: true,
			MySchedule: true,
			DeviceLicense: true,
			Citation: true,
			Units: true,
			Nfirs: true,
			Shift: true,
			Team: true,
			ShiftAllocation: true,
			Department: true,
			Mugshot: true,
			TipType: true,
			ViewTip: true,
			Notification: true,
			QuikTip: true,
			DispatchWebsite: true,
			DispatchApp: true,
			DispatchCitation: true,
			TrafficStop: true,
			TransportCall: true,
			TransportMedicalCall: true,
			SecurityCheck: true,
			PersonSearch: true,
			VehicleSearch: true,
			GunSearch: true,
			ArticleSearch: true,
			BoatSearch: true,
			IncidentReport: true,
			AccidentReport: true,
			UserAudit: true,
			EmailConfiguration: true,
			TwitterConfiguration: true,
			TwitterAccountSettings: true,
			ErrorLog: true,
			ArchiveChatHistories: true,
			IncidentApp: true,
			IncidentWebsite: true
		}
	);
	const [agencyDropdownValue, setAgencyDropdownValue] = React.useState([]);
	const [superAdmin, setSuperAdmin] = React.useState(false);
	const [agencyAdmin, setAgencyAdmin] = React.useState(false);
	const [zoomLevelValue, setZoomLevelValue] = React.useState('CITY');
	const [mfaTypeValue, setMfaTypeValue] = React.useState('email');
	const [defaultAgencyValue, setdefaultAgencyValue] = React.useState('');
	const [isOtpRequired, setIsOtpRequired] = React.useState(false);
	const [userStatusPermission, setUserStatusPermission] = React.useState(false);
	const [showAdminDefaultApp, setShowAdminDefaultApp] = useState(false);

	const [doesNotUseOldRps, setDoesNotUseOldRps] = useState(false)

	useEffect(() => {
		if (register.error && (register.error.username || register.error.email)) {
			formRef.current.updateInputsWithError({
				...register.error
			});
			dispatch(ClearUserData());
		}
	}, [dispatch]);


	useEffect(() => {

	}, []);

	useEffect(() => {
		dispatch(ClearUserData());
		dispatch(clearDepartment());
		dispatch(getAllUsers());
	}, []);

	useEffect(() => {
	}, [departmentData]);

	const getDepartment = async () => {
		if (agencyDropdownValue.length > 0) {
			dispatch(clearDepartment());
			dispatch(setLoader(true));
			await dispatch(getMasterDepartmentDetails("name", "asc", 0, 10000, null, agencyDropdownValue[0].agencyCode));
			dispatch(setLoader(false));
		}
	}

	useEffect(() => {
		getDepartment();
	}, [defaultAgencyValue, agencyDropdownValue]);

	const [loading, setLoading] = useState();

	useEffect(() => {
		setLoading(isloading)
	}, [isloading]);

	function ShowErrorMessage(message) {
		dispatch(showMessage({
			message: message,
			autoHideDuration: 4000,
			anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
		}));
	}

	async function onSubmit(model) {

		await dispatch(clearDepartment());
		await dispatch(getAgencyData());

		const { agencies } = model;

		// Validate agency selection
		if (agencies.length === 0) {
			return ShowErrorMessage(t("oneAgencySelectMsg"));
		}

		if (!superAdmin) {
			if (agencies.length > 1) {
				return ShowErrorMessage(t("cannotSelectMoreThanOneAgencyMSg"));
			}
		} else if (!agencies.some(x => x.agencyCode === "System")) {
			return ShowErrorMessage(t("systemAgencySelectMsg"));
		}

		// Validate at least one access right
		if (Object.values(userAccessRights).every(v => v === false)) {
			return ShowErrorMessage(t("oneAccessRightsSelectMsg"));
		}

		// If Incident is selected, IncidentApp or IncidentWebsite must be selected
		if (
			userAccessRights["Incident"] &&
			!userAccessRights["IncidentApp"] &&
			!userAccessRights["IncidentWebsite"]
		) {
			return ShowErrorMessage(t("selectIncidentAppOrWebsiteAccessRight"));
		}

		// If Dispatch is selected, DispatchApp or DispatchWebsite must be selected
		if (
			userAccessRights["Dispatch"] &&
			!userAccessRights["DispatchApp"] &&
			!userAccessRights["DispatchWebsite"]
		) {
			return ShowErrorMessage(t("selectDispatchAppOrWebsiteAccessRight"));
		}

		// Validate default app specific access
		const defaultAccessMap = {
			incident: {
				keys: ["Incident"],
				message: t("selectIncidentAccessRight"),
			},
			mobile: {
				keys: ["Dispatch"],
				message: t("selectDispatchAccessRight"),
			},
			admin: {
				keys: ["Users"],
				message: t("selectAdminAccessRight"),
			},
		};

		const requiredAccess = defaultAccessMap[defaultApp];
		if (requiredAccess) {
			const missing = requiredAccess.keys.some(key => !userAccessRights[key]);
			if (missing) {
				return ShowErrorMessage(requiredAccess.message);
			}
		}

		UserAddEdit(model);
	}

	function UserAddEdit(model) {
		let defaultAgencyCode = user.data.isSuperAdmin ? defaultAgencyValue : model.agencies[0].agencyCode;
		model.agencies[0].ncicPassword = ncicPassword;
		model.agencies[0].ncicUserName = ncicUserName;

		const agencyTypes = [...new Set(agencyusers)];
		const counts = agencyTypes.map(type => ({ type, count: agencyusers.filter(item => item === type).length }));
		const useragency = model.agencies.map(x => x.agencyCode)[0];
		const UserLicensesCount = agencyList.find(y => y.code === useragency)?.noOfUserLicenses || 0;
		const countuser = counts.find(y => y.type === useragency)?.count || 0;



		let defaultLocation = !cityValue ? countyValue?.COUNTY : cityValue?.CITYNAME;
		let department = departmentValue ? departmentData.find(x => x._id == departmentValue) || null : null;
		if (!department) setDepartmentValue(null);

		const userObject = {
			fname: model.fname,
			lname: model.lname,
			email: model.email,
			phone: phoneNumber,
			defaultApp: defaultApp,
			isSuperAdmin: superAdmin,
			mapZoomLevel: model.mapZoomLevel,
			mfaType: mfaTypeValue,
			defaultAgency: defaultAgencyCode,
			agencyAdmin: agencyAdmin,
			userAccessRights: userAccessRights,
			agencies: model.agencies,
			defaultLocation: defaultLocation,
			defaultLocationType: defaultLocationValue,
			departmentValue: department,
			countryCode: countryCode,
			dialCode: dialCode,
			isOtpRequired: isOtpRequired,
			ncicUserName: ncicUserName,
			ncicPassword: ncicPassword,
			isNCICTokenRequired: isNCICITokenRequired,
			isPermanentLogin: isPermanentLogin,
			isDispatchUser: (!superAdmin && !agencyAdmin) ?? false,
			//	RPS Related
			userStatusPermission: doesNotUseOldRps === true ? false : userStatusPermission,
			isFireAlert: doesNotUseOldRps === true ? false : isFireAlert,
			isVideoCallRecieve: doesNotUseOldRps === true ? false : isVideoCallRecieve,
			showTwitterIcon: doesNotUseOldRps === true ? false : showTwitterIcon,

			userAccountType: userAccountType,
			terminalId: terminalId,
			agencyID: model.agencies[0].value,
		};

		if (selectedUser) {
			if (selectedUser.userAgencies[0].agencyCode === model.agencies[0].agencyCode) {
				userObject._id = selectedUser._id;
				dispatch(updateUser(userObject));
				dispatch(resetEditUser());
			} else {
				if (countuser >= UserLicensesCount && !superAdmin) {
					ShowErrorMessage(t("userLimitReachedUpdateMsg"));
				} else {
					userObject._id = selectedUser._id;
					dispatch(updateUser(userObject));
					dispatch(resetEditUser());
				}
			}
		} else {
			if (countuser >= UserLicensesCount && !superAdmin) {
				ShowErrorMessage(t("userLimitReachedMsg"));
			} else {
				dispatch(submitRegister(userObject));
			}
		}
	}

	function onEmailChange(e) {
		let email = e.target.value;
		if (selectedUser === null) {
			setValue('email', email);
			dispatch(GetUserByEmail(email, users));
		}
		else {
			dispatch(ClearUserData());
		}
	}

	const navigateBack = () => {
		dispatch(resetEditUser());
		history.push("/admin/users");
	};

	useEffect(() => {
		if (selectedUser !== null) {
			getDepartmentForUserEdit();
		}
	}, [selectedUser]);

	const getDepartmentForUserEdit = async () => {
		if (agencyDropdownValue.length > 0) {
			dispatch(clearDepartment());
			dispatch(setLoader(true));
			await dispatch(getMasterDepartmentDetails("name", "asc", 0, 10000, null, selectedUser.defaultAgency));
			dispatch(setLoader(false));
		}
	}

	useEffect(() => {
		if (selectedUser !== null) {
			setValue('email', selectedUser.email);
			setValue('fname', selectedUser.fname);
			setValue('lname', selectedUser.lname);
			setNcicUserName(selectedUser.ncicUserName !== undefined ? selectedUser.ncicUserName : "");
			setNcicPassword(selectedUser.ncicPassword !== undefined ? selectedUser.ncicPassword : "");
			setTerminalID(selectedUser.terminalId !== undefined ? selectedUser.terminalId : "");

			setZoomLevelValue(selectedUser.mapZoomLevel);
			setMfaTypeValue(selectedUser.mfaType);
			setdefaultAgencyValue(selectedUser.defaultAgency);
			setValue('mapZoomLevel', selectedUser.mapZoomLevel);
			setCountryCode(isEmptyOrNull(selectedUser.countryCode) ? "us" : selectedUser.countryCode);

			setPhoneNumber(!isEmptyOrNull(selectedUser.phone) ? selectedUser.phone : '');
			setValue1(selectedUser.phone);
			setDialCode(selectedUser.dialCode);
			setIsOtpRequired(selectedUser.isOtpRequired);
			setUserStatusPermission(selectedUser.userStatusPermission);
			setisNCICITokenRequired(selectedUser.isNCICTokenRequired)
			setIsPermanentLogin(selectedUser.isPermanentLogin)
			setIsVideoCallRecieve(selectedUser.isVideoCallRecieve)
			setShowTwitterIcon(selectedUser.showTwitterIcon)
			setIsFireAlert(selectedUser.isFireAlert)
			setUserAccountType(selectedUser.userAccountType)


			if (selectedUser.userAgencies !== undefined) {
				setValue('agencies', selectedUser.userAgencies.map((x) => ({
					value: x.value,
					label: x.label,
					agencyCode: x.agencyCode,
					RPSUserName: x.RPSUserName,
					RPSPassword: x.RPSPassword,
					RPSName: x.RPSName,
					RPSUserGroupName: x.RPSUserGroupName,
					RPSUserID: x.RPSUserID,
					RMDUserID: x.RMDUserID,
					ncicUserName: x.ncicUserName,
					ncicPassword: x.ncicPassword,
					doesNotUseOldRps: x.doesNotUseOldRps
				})));
				setAgencyDropdownValue(selectedUser.userAgencies.map((x) => ({
					value: x.value,
					label: x.label,
					agencyCode: x.agencyCode,
					RPSUserName: x.RPSUserName,
					RPSPassword: x.RPSPassword,
					RPSName: x.RPSName,
					RPSUserGroupName: x.RPSUserGroupName,
					RPSUserID: x.RPSUserID,
					RMDUserID: x.RMDUserID,
					ncicUserName: x.ncicUserName,
					ncicPassword: x.ncicPassword,
					doesNotUseOldRps: x.doesNotUseOldRps
				})))

				const doesNotUseOldRpsData = selectedUser?.userAgencies?.filter(x => x.agencyCode === selectedUser.defaultAgency)
				if (doesNotUseOldRpsData.length > 0) { setDoesNotUseOldRps(doesNotUseOldRpsData[0].doesNotUseOldRps) }
			}
			setDefaultAppValue(selectedUser.defaultApp ? selectedUser.defaultApp : "admin");
			setShowAdminDefaultApp(selectedUser.isSuperAdmin ? true : selectedUser.agencyAdmin ? true : false);
			if (selectedUser.userAccessRights) {
				if (selectedUser.userAccessRights.length > 0) {
					let selectedAgency = agencyList.filter(x => x.code === selectedUser.defaultAgency)[0];
					setUserAccessRights({
						...userAccessRights,
						Users: selectedAgency.agencyLicenses[0].Users ? selectedUser.userAccessRights[0].Users !== undefined ? selectedUser.userAccessRights[0].Users : false : false,
						Contact: selectedAgency.agencyLicenses[0].Contact ? selectedUser.userAccessRights[0].Contact !== undefined ? selectedUser.userAccessRights[0].Contact : false : false,
						Server: selectedAgency.agencyLicenses[0].Server ? selectedUser.userAccessRights[0].Server !== undefined ? selectedUser.userAccessRights[0].Server : false : false,
						Icons: selectedAgency.agencyLicenses[0].Icons ? selectedUser.userAccessRights[0].Icons !== undefined ? selectedUser.userAccessRights[0].Icons : false : false,
						CallCategory: selectedAgency.agencyLicenses[0].CallCategory ? selectedUser.userAccessRights[0].CallCategory !== undefined ? selectedUser.userAccessRights[0].CallCategory : false : false,
						CallViolation: selectedAgency.agencyLicenses[0].CallViolation ? selectedUser.userAccessRights[0].CallViolation !== undefined ? selectedUser.userAccessRights[0].CallViolation : false : false,
						CallType: selectedAgency.agencyLicenses[0].CallType ? selectedUser.userAccessRights[0].CallType !== undefined ? selectedUser.userAccessRights[0].CallType : false : false,
						CallResponse: selectedAgency.agencyLicenses[0].CallResponse ? selectedUser.userAccessRights[0].CallResponse !== undefined ? selectedUser.userAccessRights[0].CallResponse : false : false,
						Call: selectedAgency.agencyLicenses[0].Call ? selectedUser.userAccessRights[0].Call !== undefined ? selectedUser.userAccessRights[0].Call : false : false,
						FileUpload: selectedAgency.agencyLicenses[0].FileUpload ? selectedUser.userAccessRights[0].FileUpload !== undefined ? selectedUser.userAccessRights[0].FileUpload : false : false,
						ClassofService: selectedAgency.agencyLicenses[0].ClassofService ? selectedUser.userAccessRights[0].ClassofService !== undefined ? selectedUser.userAccessRights[0].ClassofService : false : false,
						Incident: selectedAgency.agencyLicenses[0].Incident ? selectedUser.userAccessRights[0].Incident !== undefined ? selectedUser.userAccessRights[0].Incident : false : false,
						Dispatch: selectedAgency.agencyLicenses[0].Dispatch ? selectedUser.userAccessRights[0].Dispatch !== undefined ? selectedUser.userAccessRights[0].Dispatch : false : false,
						Chat: selectedAgency.agencyLicenses[0].Chat ? selectedUser.userAccessRights[0].Chat !== undefined ? selectedUser.userAccessRights[0].Chat : false : false,
						Current: selectedAgency.agencyLicenses[0].Current ? selectedUser.userAccessRights[0].Current !== undefined ? selectedUser.userAccessRights[0].Current : false : false,
						Replay: selectedAgency.agencyLicenses[0].Replay ? selectedUser.userAccessRights[0].Replay !== undefined ? selectedUser.userAccessRights[0].Replay : false : false,
						SearchFeature: selectedAgency.agencyLicenses[0].SearchFeature ? selectedUser.userAccessRights[0].SearchFeature !== undefined ? selectedUser.userAccessRights[0].SearchFeature : false : false,
						Watches: selectedAgency.agencyLicenses[0].Watches ? selectedUser.userAccessRights[0].Watches !== undefined ? selectedUser.userAccessRights[0].Watches : false : false,
						BroadcastMessages: selectedAgency.agencyLicenses[0].BroadcastMessages ? selectedUser.userAccessRights[0].BroadcastMessages !== undefined ? selectedUser.userAccessRights[0].BroadcastMessages : false : false,
						MySchedule: selectedAgency.agencyLicenses[0].MySchedule ? selectedUser.userAccessRights[0].MySchedule !== undefined ? selectedUser.userAccessRights[0].MySchedule : false : false,
						DeviceLicense: selectedAgency.agencyLicenses[0].DeviceLicense ? selectedUser.userAccessRights[0].DeviceLicense !== undefined ? selectedUser.userAccessRights[0].DeviceLicense : false : false,
						Citation: selectedAgency.agencyLicenses[0].Citation ? selectedUser.userAccessRights[0].Citation !== undefined ? selectedUser.userAccessRights[0].Citation : false : false,
						Units: selectedAgency.agencyLicenses[0].Units ? selectedUser.userAccessRights[0].Units !== undefined ? selectedUser.userAccessRights[0].Units : false : false,
						Nfirs: selectedAgency.agencyLicenses[0].Nfirs ? selectedUser.userAccessRights[0].Nfirs !== undefined ? selectedUser.userAccessRights[0].Nfirs : false : false,
						Shift: selectedAgency.agencyLicenses[0].Shift ? selectedUser.userAccessRights[0].Shift !== undefined ? selectedUser.userAccessRights[0].Shift : false : false,
						Team: selectedAgency.agencyLicenses[0].Team ? selectedUser.userAccessRights[0].Team !== undefined ? selectedUser.userAccessRights[0].Team : false : false,
						ShiftAllocation: selectedAgency.agencyLicenses[0].ShiftAllocation ? selectedUser.userAccessRights[0].ShiftAllocation !== undefined ? selectedUser.userAccessRights[0].ShiftAllocation : false : false,
						Department: selectedAgency.agencyLicenses[0].Department ? selectedUser.userAccessRights[0].Department !== undefined ? selectedUser.userAccessRights[0].Department : false : false,
						Mugshot: selectedAgency.agencyLicenses[0].Mugshot ? selectedUser.userAccessRights[0].Mugshot !== undefined ? selectedUser.userAccessRights[0].Mugshot : false : false,
						TipType: selectedAgency.agencyLicenses[0].TipType ? selectedUser.userAccessRights[0].TipType !== undefined ? selectedUser.userAccessRights[0].TipType : false : false,
						ViewTip: selectedAgency.agencyLicenses[0].ViewTip ? selectedUser.userAccessRights[0].ViewTip !== undefined ? selectedUser.userAccessRights[0].ViewTip : false : false,
						DispatchApp: selectedAgency.agencyLicenses[0].DispatchApp ? selectedUser.userAccessRights[0].DispatchApp !== undefined ? selectedUser.userAccessRights[0].DispatchApp : false : false,
						DispatchWebsite: selectedAgency.agencyLicenses[0].DispatchWebsite ? selectedUser.userAccessRights[0].DispatchWebsite !== undefined ? selectedUser.userAccessRights[0].DispatchWebsite : false : false,
						TrafficStop: selectedAgency.agencyLicenses[0].TrafficStop ? selectedUser.userAccessRights[0].TrafficStop !== undefined ? selectedUser.userAccessRights[0].TrafficStop : false : false,
						TransportCall: selectedAgency.agencyLicenses[0].TransportCall ? selectedUser.userAccessRights[0].TransportCall !== undefined ? selectedUser.userAccessRights[0].TransportCall : false : false,
						TransportMedicalCall: selectedAgency.agencyLicenses[0].TransportMedicalCall ? selectedUser.userAccessRights[0].TransportMedicalCall !== undefined ? selectedUser.userAccessRights[0].TransportMedicalCall : false : false,
						SecurityCheck: selectedAgency.agencyLicenses[0].SecurityCheck ? selectedUser.userAccessRights[0].SecurityCheck !== undefined ? selectedUser.userAccessRights[0].SecurityCheck : false : false,
						PersonSearch: selectedAgency.agencyLicenses[0].PersonSearch ? selectedUser.userAccessRights[0].PersonSearch !== undefined ? selectedUser.userAccessRights[0].PersonSearch : false : false,
						VehicleSearch: selectedAgency.agencyLicenses[0].VehicleSearch ? selectedUser.userAccessRights[0].VehicleSearch !== undefined ? selectedUser.userAccessRights[0].VehicleSearch : false : false,
						GunSearch: selectedAgency.agencyLicenses[0].GunSearch ? selectedUser.userAccessRights[0].GunSearch !== undefined ? selectedUser.userAccessRights[0].GunSearch : false : false,
						ArticleSearch: selectedAgency.agencyLicenses[0].ArticleSearch ? selectedUser.userAccessRights[0].ArticleSearch !== undefined ? selectedUser.userAccessRights[0].ArticleSearch : false : false,
						BoatSearch: selectedAgency.agencyLicenses[0].BoatSearch ? selectedUser.userAccessRights[0].BoatSearch !== undefined ? selectedUser.userAccessRights[0].BoatSearch : false : false,
						IncidentReport: selectedAgency.agencyLicenses[0].IncidentReport ? selectedUser.userAccessRights[0].IncidentReport !== undefined ? selectedUser.userAccessRights[0].IncidentReport : false : false,
						AccidentReport: selectedAgency.agencyLicenses[0].AccidentReport ? selectedUser.userAccessRights[0].AccidentReport !== undefined ? selectedUser.userAccessRights[0].AccidentReport : false : false,
						UserAudit: selectedAgency.agencyLicenses[0].UserAudit ? selectedUser.userAccessRights[0].UserAudit !== undefined ? selectedUser.userAccessRights[0].UserAudit : false : false,
						EmailConfiguration: selectedAgency.agencyLicenses[0].EmailConfiguration ? selectedUser.userAccessRights[0].EmailConfiguration !== undefined ? selectedUser.userAccessRights[0].EmailConfiguration : false : false,
						TwitterConfiguration: selectedAgency.agencyLicenses[0].TwitterConfiguration ? selectedUser.userAccessRights[0].TwitterConfiguration !== undefined ? selectedUser.userAccessRights[0].TwitterConfiguration : false : false,
						TwitterAccountSettings: selectedAgency.agencyLicenses[0].TwitterAccountSettings ? selectedUser.userAccessRights[0].TwitterAccountSettings !== undefined ? selectedUser.userAccessRights[0].TwitterAccountSettings : false : false,
						ErrorLog: selectedAgency.agencyLicenses[0].ErrorLog ? selectedUser.userAccessRights[0].ErrorLog !== undefined ? selectedUser.userAccessRights[0].ErrorLog : false : false,
						ArchiveChatHistories: selectedAgency.agencyLicenses[0].ArchiveChatHistories ? selectedUser.userAccessRights[0].ArchiveChatHistories !== undefined ? selectedUser.userAccessRights[0].ArchiveChatHistories : false : false,
						IncidentWebsite: selectedAgency.agencyLicenses[0].IncidentWebsite ? selectedUser.userAccessRights[0].IncidentWebsite !== undefined ? selectedUser.userAccessRights[0].IncidentWebsite : false : false,
						IncidentApp: selectedAgency.agencyLicenses[0].IncidentApp ? selectedUser.userAccessRights[0].IncidentApp !== undefined ? selectedUser.userAccessRights[0].IncidentApp : false : false,
					});
					setAgencyAdminSetting(selectedUser.isSuperAdmin === true ? "superAdmin" : selectedUser.agencyAdmin === true ? "agencyAdmin" : "user")
					setSuperAdmin((selectedUser.isSuperAdmin === "" || selectedUser.isSuperAdmin === undefined) ? false : selectedUser.isSuperAdmin);
					setAgencyAdmin(selectedUser.agencyAdmin);
					if (selectedUser.isSuperAdmin) {
						setAccessRightShow(true);
					}
					if (!isEmptyOrNull(selectedUser.defaultAgency)) {
						FilterAccessRightByAgencyCode(selectedUser.defaultAgency);
					}
					setAccessRightShow(true);
				}
			}
			setDepartmentValue(selectedUser.department !== undefined ? selectedUser.department !== null ? selectedUser.department[0]._id : null : null)
			setSelectedDefaultValue();
		}
	}, [selectedUser])

	useEffect(() => {
		if (userExist && !isEmptyOrNull(register.emailVerify)) {
			setValue('email', register.emailVerify.email);
			setValue('fname', register.emailVerify.fname);
			setValue('phone', register.emailVerify.phone);
			setValue('lname', register.emailVerify.lname);
			setValue('agencies', register.emailVerify.agencies.map((x) => ({
				value: x._id,
				label: x.name,
				agencyCode: x.code,
				RPSUserName: "",
				RPSPassword: "",
				RPSName: "",
				RPSUserGroupName: "",
				RPSUserID: 0,
				RMDUserID: 0,
				ncicUserName: "",
				ncicPassword: "",
				doesNotUseOldRps: x.doesNotUseOldRps

			})));
		}
		if (agencyList.length > 0) {
			if (user.data.isSuperAdmin) {
				let data = agencyList.filter(x => x.code != 'System');
				if (superAdmin == false) {
					setAgencies(data.map((x) => ({
						value: x._id,
						label: x.name,
						agencyCode: x.code,
						RPSUserName: "",
						RPSPassword: "",
						RPSName: "",
						RPSUserGroupName: "",
						RPSUserID: 0,
						RMDUserID: 0,
						ncicUserName: "",
						ncicPassword: "",
						doesNotUseOldRps: x.doesNotUseOldRps
					})))
				}
				else {
					setAgencies(agencyList.map((x) => ({
						value: x._id,
						label: x.name,
						agencyCode: x.code,
						RPSUserName: "",
						RPSPassword: "",
						RPSName: "",
						RPSUserGroupName: "",
						RPSUserID: 0,
						RMDUserID: 0,
						ncicUserName: "",
						ncicPassword: "",
						doesNotUseOldRps: x.doesNotUseOldRps
					})))
				}
			}
			else {
				let filteredAgencyList = agencyList.filter(x => x.code.toLowerCase() === localStorage.getItem('agencyCode').toLowerCase())
				setValue('agencies', filteredAgencyList.map((x) =>
				({
					value: x._id,
					label: x.name,
					agencyCode: x.code,
					RPSUserName: "",
					RPSPassword: "",
					RPSName: "",
					RPSUserGroupName: "",
					RPSUserID: 0,
					RMDUserID: 0,
					ncicUserName: x.ncicUserName,
					ncicPassword: x.ncicPassword,
					doesNotUseOldRps: x.doesNotUseOldRps
				})));
				setAgencyDropdownValue(filteredAgencyList.map((x) => ({
					value: x._id,
					label: x.name,
					agencyCode: x.code,
					RPSUserName: "",
					RPSPassword: "",
					RPSName: "",
					RPSUserGroupName: "",
					RPSUserID: 0,
					RMDUserID: 0,
					ncicUserName: x.ncicUserName,
					ncicPassword: x.ncicPassword,
					doesNotUseOldRps: x.doesNotUseOldRps
				})))
				let code = localStorage.getItem('agencyCode');
				FilterAccessRightByAgencyCode(code);
				setDoesNotUseOldRps(filteredAgencyList[0]?.doesNotUseOldRps ?? false);
			}
		}
	}, [userExist, agencyList, superAdmin]);

	const handleDefaultAppChange = (event) => {

		if (agencyDropdownValue.length === 0) {
			ShowErrorMessage(t("oneAgencySelectMsg"));
			return;
		}

		setDefaultAppValue(event.target.value);

		const selectedAgency = agencyList?.find(x => x.code === agencyDropdownValue[0]?.agencyCode);
		const selectedAgencyRights = selectedAgency?.agencyLicenses?.[0] || {};
		const value = event.target.value;
		let newRights = {};

		if (value === "call") {
			newRights = {
				Call: selectedAgencyRights.Call ?? false,
				FileUpload: selectedAgencyRights.FileUpload ?? false,
				ClassofService: selectedAgencyRights.ClassofService ?? false,
			};
		} else if (value === "mobile") {
			newRights = {
				Dispatch: selectedAgencyRights.Dispatch ?? false,
				Chat: selectedAgencyRights.Chat ?? false,
				Current: selectedAgencyRights.Current ?? false,
				Replay: selectedAgencyRights.Replay ?? false,
				SearchFeature: selectedAgencyRights.SearchFeature ?? false,
				Watches: selectedAgencyRights.Watches ?? false,
				BroadcastMessages: selectedAgencyRights.BroadcastMessages ?? false,
				MySchedule: selectedAgencyRights.MySchedule ?? false,
				DispatchWebsite: selectedAgencyRights.DispatchWebsite ?? false,
				DispatchApp: selectedAgencyRights.DispatchApp ?? false,
				TrafficStop: selectedAgencyRights.TrafficStop ?? false,
				PersonSearch: selectedAgencyRights.PersonSearch ?? false,
				VehicleSearch: selectedAgencyRights.VehicleSearch ?? false,
				GunSearch: selectedAgencyRights.GunSearch ?? false,
				ArticleSearch: selectedAgencyRights.ArticleSearch ?? false,
				BoatSearch: selectedAgencyRights.BoatSearch ?? false,

				IncidentReport: selectedAgencyRights.IncidentReport ?? false,
				AccidentReport: selectedAgencyRights.AccidentReport ?? false,
				Incident: selectedAgencyRights.Incident ?? false,
				Citation: selectedAgencyRights.Citation ?? false,
			};

		}

		else if (value === "incident") {
			if (doesNotUseOldRps) {
				newRights = {
					Dispatch: false,
					Chat: false,
					Current: false,
					Replay: false,
					SearchFeature: false,
					Watches: false,
					BroadcastMessages: false,
					MySchedule: false,
					DispatchWebsite: true,
					DispatchApp: true,
					TrafficStop: false,
					PersonSearch: false,
					VehicleSearch: false,
					GunSearch: false,
					ArticleSearch: false,
					BoatSearch: false,

					IncidentReport: selectedAgencyRights.IncidentReport ?? false,
					AccidentReport: selectedAgencyRights.AccidentReport ?? false,
					Incident: selectedAgencyRights.Incident ?? false,
					Citation: selectedAgencyRights.Citation ?? false,
					IncidentApp: selectedAgencyRights.IncidentApp ?? false,
					IncidentWebsite: selectedAgencyRights.IncidentWebsite ?? false,
				}
			}
			else {
				newRights = {
					Dispatch: selectedAgencyRights.Dispatch ?? false,
					Chat: selectedAgencyRights.Chat ?? false,
					Current: selectedAgencyRights.Current ?? false,
					Replay: selectedAgencyRights.Replay ?? false,
					SearchFeature: selectedAgencyRights.SearchFeature ?? false,
					Watches: selectedAgencyRights.Watches ?? false,
					BroadcastMessages: selectedAgencyRights.BroadcastMessages ?? false,
					MySchedule: selectedAgencyRights.MySchedule ?? false,
					DispatchWebsite: selectedAgencyRights.DispatchWebsite ?? false,
					DispatchApp: selectedAgencyRights.DispatchApp ?? false,
					DispatchCitation: selectedAgencyRights.DispatchCitation ?? false,
					TrafficStop: selectedAgencyRights.TrafficStop ?? false,
					PersonSearch: selectedAgencyRights.PersonSearch ?? false,
					VehicleSearch: selectedAgencyRights.VehicleSearch ?? false,
					GunSearch: selectedAgencyRights.GunSearch ?? false,
					ArticleSearch: selectedAgencyRights.ArticleSearch ?? false,
					BoatSearch: selectedAgencyRights.BoatSearch ?? false,

					IncidentReport: selectedAgencyRights.IncidentReport ?? false,
					AccidentReport: selectedAgencyRights.AccidentReport ?? false,
					Incident: selectedAgencyRights.Incident ?? false,
					Citation: selectedAgencyRights.Citation ?? false,
					IncidentApp: selectedAgencyRights.IncidentApp ?? false,
					IncidentWebsite: selectedAgencyRights.IncidentWebsite ?? false,
				};

			}
		}
		else if (value === "quiktip") {
			newRights = {
				ViewTip: selectedAgencyRights.ViewTip ?? false,
				TipType: selectedAgencyRights.TipType ?? false,
				Notification: selectedAgencyRights.Notification ?? false,
				QuikTip: selectedAgencyRights.QuikTip ?? false,
			};
		} else {
			if (superAdmin) {
				newRights = {
					...userAccessRights,
					Users: true,
					Contact: true,
					Server: true,
					Icons: true,
					CallCategory: true,
					CallViolation: true,
					CallType: true,
					CallResponse: true,
					Call: true,
					FileUpload: true,
					ClassofService: true,
					Incident: true,
					Dispatch: true,
					Chat: true,
					Current: true,
					Replay: true,
					SearchFeature: true,
					Watches: true,
					BroadcastMessages: true,
					MySchedule: true,
					DeviceLicense: true,
					Citation: true,
					Units: true,
					Nfirs: true,
					Shift: true,
					Team: true,
					ShiftAllocation: true,
					Department: true,
					Mugshot: true,
					TipType: true,
					ViewTip: true,
					DispatchApp: true,
					DispatchWebsite: true,
					DispatchCitation: true,
					TrafficStop: true,
					TransportCall: true,
					TransportMedicalCall: true,
					SecurityCheck: true,
					PersonSearch: true,
					VehicleSearch: true,
					GunSearch: true,
					ArticleSearch: true,
					BoatSearch: true,
					IncidentReport: true,
					AccidentReport: true,
					UserAudit: true,
					EmailConfiguration: true,
					TwitterConfiguration: true,
					TwitterAccountSettings: true,
					ErrorLog: true,
					ArchiveChatHistories: true,
					IncidentApp: true,
					IncidentWebsite: true
				};
			} else {
				newRights = {
					Users: selectedAgencyRights.Users ?? false,
					Contact: selectedAgencyRights.Contact ?? false,
					Server: selectedAgencyRights.Server ?? false,
					Icons: selectedAgencyRights.Icons ?? false,
					CallCategory: selectedAgencyRights.CallCategory ?? false,
					CallViolation: selectedAgencyRights.CallViolation ?? false,
					CallType: selectedAgencyRights.CallType ?? false,
					CallResponse: selectedAgencyRights.CallResponse ?? false,
					DeviceLicense: selectedAgencyRights.DeviceLicense ?? false,
					Units: selectedAgencyRights.Units ?? false,
					Nfirs: selectedAgencyRights.Nfirs ?? false,
					Shift: selectedAgencyRights.Shift ?? false,
					Team: selectedAgencyRights.Team ?? false,
					ShiftAllocation: selectedAgencyRights.ShiftAllocation ?? false,
					Department: selectedAgencyRights.Department ?? false,
					DispatchWebsite: selectedAgencyRights.DispatchWebsite ?? false,
					UserAudit: selectedAgencyRights.UserAudit ?? false,
					EmailConfiguration: selectedAgencyRights.EmailConfiguration ?? false,
					TwitterConfiguration: selectedAgencyRights.TwitterConfiguration ?? false,
					TwitterAccountSettings: selectedAgencyRights.TwitterAccountSettings ?? false,
					ErrorLog: selectedAgencyRights.ErrorLog ?? false,
					ArchiveChatHistories: selectedAgencyRights.ArchiveChatHistories ?? false
				};
			}
		}


		setUserAccessRights(newRights);
	};



	const handleZoomLevelChange = (event) => {
		setZoomLevelValue(event.target.value);
		setValue('mapZoomLevel', event.target.value);
	};

	const handleMfaTypeChange = (value) => { setMfaTypeValue(value); };

	const FilterAccessRightByAgencyCode = (agencyCode) => {
		if (!isEmptyOrNull(agencyCode)) {
			let filterData = {}
			_(agencyList.filter(x => x.code == agencyCode)[0].agencyLicenses[0]).forEach(function (a, k) {
				if (a) {
					filterData[k] = k
				}
			})
			setAccessRightShow(true);
			setShowAccessRight(filterData);
		}
	};

	const handleAgencyAceessRightCallback = (data) => {

		setUserAccessRights({ ...userAccessRights, [data.name]: data.cheked });
		if (ncicPassword == "" && ncicUserName == "") {
			if ((data.name == "PersonSearch" || data.name == "VehicleSearch" || data.name == "GunSearch" || data.name == "BoatSearch") && data.cheked == true)
				ShowErrorMessage("You have not filled NCIC Username And Password")
		}
	}

	function setSelectedDefaultValue() {
		if (selectedUser !== null) {
			if (!isEmptyOrNull(selectedUser.defaultLocation)) {
				setdefaultLocationValue(selectedUser.defaultLocationType)
				if (selectedUser.defaultLocationType === "CITY") {
					setCountyValue(null);
					const defaultLocationCityObj = register && register.cityData && register.cityData.find(x => x.CITYNAME === selectedUser.defaultLocation);
					setCityValue(defaultLocationCityObj ?? null);
				} else {
					setCityValue(null);
					const defaultLocationCountyObj = register && register.countyData && register.countyData.find(x => x.COUNTY === selectedUser.defaultLocation);
					setCountyValue(defaultLocationCountyObj ?? null);
				}
			}
		}
	}

	const handleDefaultLocation = (value) => {
		setdefaultLocationValue(value);
		setCountyValue(null);
		setCityValue(null);
	};

	const handleCity = (value) => {
		setCityValue(value);
		setCountyValue(null);
	};

	const handleCounty = (value) => {
		setCountyValue(value);
		setCityValue(null);
	};

	const handleShiftTypeChange = (event) => { setDepartmentValue(event.target.value); };
	const handleNcicUserNameChange = (event) => { setNcicUserName(event.target.value); };
	const handleNcicPasswordChange = (event) => { setNcicPassword(event.target.value); };
	const handleTerminalIDChange = (event) => { setTerminalID(event.target.value); };
	const otpRequiredValueChange = (value) => { setIsOtpRequired(value) }
	const handleNCICITokenRequired = (event) => { setisNCICITokenRequired(event.currentTarget.checked) }
	const handleIsPermanentLogin = (event) => { setIsPermanentLogin(event.currentTarget.checked) }
	const handleIsVideoCallRecieve = (event) => { setIsVideoCallRecieve(event.currentTarget.checked) }
	const handleIsShowTwitterIcon = (event) => { setShowTwitterIcon(event.currentTarget.checked) }
	const handleIsFireAlertChange = (event) => { setIsFireAlert(event.currentTarget.checked) }
	const handleChangeUserAccountType = (event) => { setUserAccountType(event.target.value); }

	function handleOnChange(phoneMumber, data, event, formattedValue) {
		setCountryCode(data.countryCode);
		setPhoneNumber(formattedValue);
		setDialCode(formattedValue.split(' ')[0]);
	}

	const handleAgencyAdminSetting = (event) => {

		setAgencyAdminSetting(event.target.value);
		if (event.target.value === 'superAdmin') {
			if (!isEmptyOrNull(defaultAgencyValue)) {
				FilterAccessRightByAgencyCode(defaultAgencyValue);
			}
			setAccessRightShow(true);
			setShowAdminDefaultApp(true);

			if (agencyDropdownValue.find(o => o.agencyCode === 'System') == null) {
				agencyDropdownValue.push(agencyList.filter(x => x.code.toLowerCase() === 'System'.toLowerCase()).map((x) => ({
					value: x._id, label: x.name, agencyCode: x.code,
					RPSUserName: "",
					RPSPassword: "",
					RPSName: "",
					RPSUserGroupName: "",
					RPSUserID: 0,
					RMDUserID: 0,
					ncicUserName: "",
					ncicPassword: "",
					doesNotUseOldRps: x.doesNotUseOldRps
				}))[0]);
				setDoesNotUseOldRps(false)
			}
			setValue('agencies', agencyDropdownValue);
			//Set Default Agency---------------
			if (agencyDropdownValue.length > 0) {
				setdefaultAgencyValue(agencyDropdownValue[0].agencyCode);
			}
			setSuperAdmin(true);
			setAgencyAdmin(false);
			setUserAccessRights({
				...userAccessRights,
				Users: true,
				Contact: true,
				Server: true,
				Icons: true,
				CallCategory: true,
				CallViolation: true,
				CallType: true,
				CallResponse: true,
				Call: true,
				FileUpload: true,
				ClassofService: true,
				Incident: true,
				Dispatch: true,
				Chat: true,
				Current: true,
				Replay: true,
				SearchFeature: true,
				Watches: true,
				BroadcastMessages: true,
				MySchedule: true,
				DeviceLicense: true,
				Citation: true,
				Units: true,
				Nfirs: true,
				Shift: true,
				Team: true,
				ShiftAllocation: true,
				Department: true,
				Mugshot: true,
				TipType: true,
				ViewTip: true,
				DispatchApp: true,
				DispatchWebsite: true,
				DispatchCitation: true,
				TrafficStop: true,
				TransportCall: true,
				TransportMedicalCall: true,
				SecurityCheck: true,
				PersonSearch: true,
				VehicleSearch: true,
				GunSearch: true,
				ArticleSearch: true,
				BoatSearch: true,
				IncidentReport: true,
				AccidentReport: true,
				UserAudit: true,
				EmailConfiguration: true,
				TwitterConfiguration: true,
				TwitterAccountSettings: true,
				ErrorLog: true,
				ArchiveChatHistories: true,
				IncidentApp: true,
				IncidentWebsite: true
			});
		} else if (event.target.value === 'agencyAdmin') {
			if (agencyDropdownValue !== null && agencyDropdownValue.length > 0) {
				let filterAgencyData = agencyDropdownValue.filter(x => x.agencyCode.toLowerCase() !== 'System'.toLowerCase());
				setAgencyDropdownValue(filterAgencyData);
				setValue('agencies', filterAgencyData);
				setAgencies(agencyList.map((x) => ({
					value: x._id,
					label: x.name,
					agencyCode: x.code,
					RPSUserName: "",
					RPSPassword: "",
					RPSName: "",
					RPSUserGroupName: "",
					RPSUserID: 0,
					RMDUserID: 0,
					ncicUserName: "",
					ncicPassword: "",
					doesNotUseOldRps: x.doesNotUseOldRps
				})).filter(x => x.agencyCode != 'System'));
				setdefaultAgencyValue(filterAgencyData.length > 0 ? filterAgencyData[0].agencyCode : '');
				setDoesNotUseOldRps(filterAgencyData[0]?.doesNotUseOldRps)
			}
			setSuperAdmin(false);
			setAgencyAdmin(true);
			setShowAdminDefaultApp(true);
			setUserAccessRights({
				...userAccessRights,
				Users: true,
				Contact: true,
				Server: true,
				Icons: true,
				CallCategory: true,
				CallViolation: true,
				CallType: true,
				CallResponse: true,
				Call: true,
				FileUpload: true,
				ClassofService: true,
				Incident: true,
				Dispatch: true,
				Chat: true,
				Current: true,
				Replay: true,
				SearchFeature: true,
				Watches: true,
				BroadcastMessages: true,
				MySchedule: true,
				DeviceLicense: true,
				Citation: true,
				Units: true,
				Nfirs: true,
				Shift: true,
				Team: true,
				ShiftAllocation: true,
				Department: true,
				Mugshot: true,
				TipType: true,
				ViewTip: true,
				Notification: true,
				QuikTip: true,
				DispatchApp: true,
				DispatchWebsite: true,
				DispatchCitation: true,
				TrafficStop: true,
				TransportCall: true,
				TransportMedicalCall: true,
				SecurityCheck: true,
				PersonSearch: true,
				VehicleSearch: true,
				GunSearch: true,
				ArticleSearch: true,
				BoatSearch: true,
				IncidentReport: true,
				AccidentReport: true,
				UserAudit: false,
				EmailConfiguration: false,
				TwitterConfiguration: false,
				TwitterAccountSettings: false,
				ErrorLog: false,
				ArchiveChatHistories: false,
				IncidentApp: true,
				IncidentWebsite: true
			});
		} else {
			if (event.target.value === 'user') {
				if (agencyDropdownValue !== null && agencyDropdownValue.length > 0) {
					let filterAgencyData = agencyDropdownValue.filter(x => x.agencyCode.toLowerCase() !== 'System'.toLowerCase());
					setAgencyDropdownValue(filterAgencyData);
					setValue('agencies', filterAgencyData);
					setAgencies(agencyList.map((x) => ({
						value: x._id,
						label: x.name,
						agencyCode: x.code,
						RPSUserName: "",
						RPSPassword: "",
						RPSName: "",
						RPSUserGroupName: "",
						RPSUserID: 0,
						RMDUserID: 0,
						ncicUserName: "",
						ncicPassword: "",
						doesNotUseOldRps: x.doesNotUseOldRps
					})).filter(x => x.agencyCode != 'System'));
					setdefaultAgencyValue(filterAgencyData.length > 0 ? filterAgencyData[0].agencyCode : '');
					setDoesNotUseOldRps(filterAgencyData[0].doesNotUseOldRps)
				}
				setSuperAdmin(false);
				setAgencyAdmin(false);
				setShowAdminDefaultApp(false); //For regular user set admin option hidden
				setUserAccessRights({
					...userAccessRights,
					Users: false,
					Contact: false,
					Server: false,
					Icons: false,
					CallCategory: false,
					CallViolation: false,
					CallType: false,
					CallResponse: false,
					Call: false,
					FileUpload: false,
					ClassofService: false,
					Incident: false,
					Dispatch: false,
					Chat: false,
					Current: false,
					Replay: false,
					SearchFeature: false,
					Watches: false,
					BroadcastMessages: false,
					MySchedule: false,
					DeviceLicense: false,
					Citation: false,
					Units: false,
					Nfirs: false,
					Shift: false,
					Team: false,
					ShiftAllocation: false,
					Department: false,
					Mugshot: false,
					TipType: false,
					ViewTip: false,
					Notification: false,
					QuikTip: false,
					DispatchApp: false,
					DispatchWebsite: false,
					DispatchCitation: false,
					TrafficStop: false,
					TransportCall: false,
					TransportMedicalCall: false,
					SecurityCheck: false,
					PersonSearch: false,
					VehicleSearch: false,
					GunSearch: false,
					ArticleSearch: false,
					BoatSearch: false,
					IncidentReport: false,
					AccidentReport: false,
					UserAudit: false,
					EmailConfiguration: false,
					TwitterConfiguration: false,
					TwitterAccountSettings: false,
					ErrorLog: false,
					ArchiveChatHistories: false,
					IncidentApp: false,
					IncidentWebsite: false
				});
			}
		}
	}

	useEffect(() => {
		if (selectedUser) {
			nameInputRef.current.focus();
		} else {
			emailInputRef.current.focus();
		}

	}, [emailInputRef, nameInputRef]);

	const backButton = (
		<Button
			type="button"
			variant="contained"
			color='secondary'
			className="normal-case m-16"
			aria-label="Back"
			value="legacy"
			onClick={navigateBack}>
			{t("back")}
		</Button>
	)

	const submitButton = (
		<Button
			type="submit"
			variant="contained"
			color="secondary"
			className="normal-case m-16"
			aria-label="REGISTER"
			onClick={handleSubmit(onSubmit)}
			value="legacy">
			{selectedUser ? t("update") : t("createUser")}
		</Button>
	)

	const userStatusRequiredValueChange = (value) => {
		setUserStatusPermission(value)
	}

	const handleAgencyChange = (event, newValue) => {

		if (!superAdmin) {
			if (newValue.length > 1) {
				ShowErrorMessage(t('cannotSelectMoreThanOneAgencyMSg'));
			} else {
				setValue('agencies', newValue); // if you're not using `useForm`, use `onChange` prop instead
				setAgencyDropdownValue(newValue);
				if (newValue.length > 0) {
					setDoesNotUseOldRps(newValue[0].doesNotUseOldRps)

					setdefaultAgencyValue(newValue[0].agencyCode);
					FilterAccessRightByAgencyCode(newValue[0].agencyCode);
				}
			}
		} else {
			if (newValue.filter(element => element.agencyCode === 'System').length > 1) {
				ShowErrorMessage(t('systemAgencySelectMsg'));
			} else {
				setValue('agencies', newValue);
				setDoesNotUseOldRps(false)
				setAgencyDropdownValue(newValue);
				if (newValue.length > 0) {
					setdefaultAgencyValue(newValue[0].agencyCode);
				}
			}
		}
	};


	useEffect(() => {
		if (doesNotUseOldRps) {
			setUserAccessRights(prev => ({
				...prev,
				Dispatch: false,
				Chat: false,
				Current: false,
				Replay: false,
				SearchFeature: false,
				Watches: false,
				BroadcastMessages: false,
				MySchedule: false,
			}));

		}
	}, [doesNotUseOldRps])

	return (
		<>
			{loading && < CircularProgressLoader loading={loading} />}
			<div className="p-16">
				<Card className="w-full mb-0 rounded-8 ml-4 shadow ">
					<AppHeaderBar headerText={t("newUser")} backButton={backButton} submitButton={submitButton} />
					<CardContent>
						<div className="w-full p-16 ">
							<form autocomplete="off" className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>
								<div class="">
									<div>
										<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
											<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("personalDetails")}</legend>
											<Grid item xs={12}>
												<Controller
													type="text"
													className={classes.w50}
													name="email"
													control={control}
													render={({ field }) => (
														<TextField
															{...field}
															className="mb-16 w-full"
															disabled={selectedUser ? true : false}
															label={t("email")}
															onChange={onEmailChange}
															type="text"
															inputRef={emailInputRef}
															error={!!errors.email}
															helperText={errors?.email?.message}
															variant="outlined"
															InputProps={{
																className: 'pr-2',
																endAdornment: (
																	<InputAdornment position="end">
																		<Icon className="text-20" color="action">
																			email
																		</Icon>
																	</InputAdornment>

																),
															}}
															required
														/>
													)}
												/>
											</Grid>

											<Grid container spacing={2}>
												<Grid item xs={6}>
													<Controller
														type="text"
														className={classes.w50}
														name="fname"
														control={control}
														render={({ field }) => (
															<TextField
																{...field}
																className={classes.w50}
																disabled={(!selectedUser && register.userAvailable || register.userExist || userIsDisabled) ? true : false}
																label={t("firstName")}
																type="text"
																error={!!errors.fname}
																helperText={errors?.fname?.message}
																variant="outlined"
																inputRef={nameInputRef}
																InputProps={{
																	className: 'pr-2',
																	endAdornment: (
																		<InputAdornment position="end">
																			<Icon className="text-20" color="action">
																				person
																			</Icon>
																		</InputAdornment>
																	),
																}}
																required
															/>
														)}
													/>
												</Grid>
												<Grid item xs={6}>
													<Controller
														type="text"
														name="lname"
														className={classes.w50}
														control={control}
														render={({ field }) => (
															<TextField
																{...field}
																className={classes.w50}
																disabled={(!selectedUser && register.userAvailable || register.userExist || userIsDisabled) ? true : false}
																label={t("lastName")}
																type="text"
																error={!!errors.lname}
																helperText={errors?.lname?.message}
																variant="outlined"
																InputProps={{
																	className: 'pr-2',
																	endAdornment: (
																		<InputAdornment position="end">
																			<Icon className="text-20" color="action">
																				person
																			</Icon>
																		</InputAdornment>
																	),
																}}
																required
															/>
														)}
													/>
												</Grid>
											</Grid>
											<Grid container spacing={2} columnSpacing={4}>
												<Grid item xs={4}>
													<FormControl fullWidth>
														<PhoneInput
															value={value}
															country={'US'}
															onChange={handleOnChange}
															placeholder={t("phone")}
															specialLabel=''
														/>
													</FormControl>
												</Grid>
												<Grid item xs={4}>
													<FormControl component="fieldset">
														<FormLabel component="legend">{t("userAccountType")}</FormLabel>
														<RadioGroup
															row aria-label="gender"
															name="defaultlocationtype"
															value={userAccountType}
															onChange={handleChangeUserAccountType}
														>
															<FormControlLabel value="FullTime" control={<Radio />} label={t("fulltTime")} />
															<FormControlLabel value="PartTime" control={<Radio />} label={t("partTime")} />
															<FormControlLabel value="UnitAccount" control={<Radio />} label={t("unitAccount")} />
														</RadioGroup>
													</FormControl>
												</Grid>
											</Grid>
										</Box>
										<Grid container spacing={2}>
											<Grid item xs={7}>
												<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: '8px 8px 0 8px', borderRadius: '5px', marginBottom: 2 }}>
													<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("otpDetails")}</legend>
													<Grid container spacing={0} justifyContent="center">
														<Grid item xs={3} >
															<ErrorBoundary
																FallbackComponent={(props) => <ErrorPage {...props} componentName="OtpRequired" />} onReset={() => { }}>
																<OtpRequired
																	isOtpRequired={isOtpRequired}
																	otpRequiredValue={otpRequiredValueChange}
																>
																</OtpRequired>
															</ErrorBoundary>
														</Grid>
														<Grid item xs={5} >
															<FormControl component="fieldset" className="adminControlPadding items-center h-full">
																<ErrorBoundary
																	FallbackComponent={(props) => <ErrorPage {...props} componentName="UserDefaultMFATypeSetting" />} onReset={() => { }}>
																	<UserDefaultMFATypeSetting selectedUser={selectedUser} mfaValue={handleMfaTypeChange} mfaType={mfaTypeValue} registerUser={true} />
																</ErrorBoundary>
															</FormControl>
														</Grid>
														<Grid item xs={4}>
															<FormLabel component="legend" sx={{ marginTop: '12px' }}>{t("permanentLogin")}</FormLabel>
															<FormGroup className='col-span-1'>
																<FormControlLabel
																	control={<Checkbox checked={isPermanentLogin}
																		onChange={handleIsPermanentLogin}
																		name="isPermanentLogin"

																	/>}
																	label={t('allowPermanantLogin')}
																/>
															</FormGroup>
														</Grid>
													</Grid>
												</Box>

												<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
													<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("ncicDetails")}</legend>
													<Grid container spacing={2} justifyContent="center">
														<Grid item xs={3}>
															<TextField
																name='NCICUser'
																className={classes.w50}
																label={t('ncicUserName')}
																type="text"
																variant="outlined"
																onChange={handleNcicUserNameChange}
																autoComplete="new-password"
																value={ncicUserName || ""}
																InputProps={{
																	className: 'pr-2',
																	endAdornment: (
																		<InputAdornment position="end">
																			<Icon className="text-20" color="action">
																				person
																			</Icon>
																		</InputAdornment>
																	),
																}}
															/>
														</Grid>
														<Grid item xs={3}>
															<TextField
																name='NCICPass'
																className={classes.w50}
																label={t('ncicPassword')}
																type="text"
																disabled={true}
																variant="outlined"
																autoComplete="new-password"
																value={ncicPassword || ""}
																onChange={handleNcicPasswordChange}
																InputProps={{
																	className: 'pr-2',
																	type: 'password',
																}}
															/>
														</Grid>
														<Grid item xs={3}>
															<TextField
																name='TerminalID'
																className={classes.w50}
																label={t('terminalId')}
																type="text"
																variant="outlined"
																onChange={handleTerminalIDChange}
																autoComplete="new-password"
																value={terminalId || ""}
															/>
														</Grid>
														<Grid item xs={3} className=''>
															<FormGroup className='justify-center items-center mt-6'>
																<FormControlLabel
																	control={
																		<Checkbox
																			checked={isNCICITokenRequired}
																			onChange={handleNCICITokenRequired}
																			name="IsTokenRequired"
																		/>
																	}
																	label={t('tokenRequired')}
																/>
															</FormGroup>
														</Grid>
													</Grid>
													{
														selectedUser?.lastNcicChange &&
														<Typography className="font-semibold text-16 mb-6">{t("lastNcicChange")}: {(new Date(selectedUser?.lastNcicChange).toLocaleString()) ?? null}</Typography>
													}
												</Box>

												{!doesNotUseOldRps &&
													<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
														<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("locationDetails")}</legend>
														<ErrorBoundary
															FallbackComponent={(props) => <ErrorPage {...props} componentName="UserDefaultLocationSetting" />} onReset={() => { }}>
															<UserDefaultLocationSetting user={selectedUser} handleDefaultLocation={handleDefaultLocation}
																handleCity={handleCity} handleCounty={handleCounty} flag={true} zoomLevelValue={zoomLevelValue}
																handleZoomLevelChange={handleZoomLevelChange}
															/>
														</ErrorBoundary>
													</Box>
												}
											</Grid>
											<Grid item xs={5}>
												{!doesNotUseOldRps &&
													<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
														<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("permissionDetails")}</legend>
														<Grid container spacing={1}>
															<Grid item xs={4}>
																<FormGroup className='col-span-1'>
																	<FormControlLabel
																		control={<Checkbox checked={isVideoCallRecieve}
																			onChange={handleIsVideoCallRecieve}
																			name="isVideoCallRecieve"
																		/>}
																		label={t('receiveVideoCall')}
																	/>
																</FormGroup>
															</Grid>
															<Grid item xs={4}>
																<FormGroup className='col-span-1'>
																	<FormControlLabel
																		control={<Checkbox checked={showTwitterIcon}
																			onChange={handleIsShowTwitterIcon}
																			name="showTwitterIcon"
																		/>}
																		label={t('showTwitterIcon')}
																	/>
																</FormGroup>
															</Grid>
															<Grid item xs={4}>
																<ErrorBoundary
																	FallbackComponent={(props) => <ErrorPage {...props} componentName="UserStatusPermission" />} onReset={() => { }}>
																	<UserStatusPermission
																		userStatusPermission={userStatusPermission}
																		userStatusRequiredValueChange={userStatusRequiredValueChange}
																	>
																	</UserStatusPermission>
																</ErrorBoundary>
															</Grid>
															<Grid item xs={4}>
																<FormGroup className='col-span-1'>
																	<FormControlLabel
																		control={<Checkbox checked={isFireAlert}
																			onChange={handleIsFireAlertChange} name="IsFireAlert" />}
																		label={t('receiveCallAlert')}
																	/>
																</FormGroup>
															</Grid>
														</Grid>
													</Box>
												}

												<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 0 }}>
													<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("userAgencyDetails")}</legend>
													<Grid container spacing={2}>
														<Grid item xs={12}>
															<RadioGroup
																row
																aria-label="agency_admin_setting"
																name="agencyAdminSetting"
																value={agencyAdminSetting}
																onChange={handleAgencyAdminSetting}
															>
																{user.data.isSuperAdmin && <FormControlLabel value="superAdmin" control={<Radio />} label={t('superAdmin')} />}
																<FormControlLabel value="agencyAdmin" control={<Radio />} label={t('agencyAdmin')} />
																<FormControlLabel value="user" control={<Radio />} label={t('user')} />
															</RadioGroup>
														</Grid>
													</Grid>

													<Grid container spacing={2} className='mt-0'>
														<Grid item xs={12}>
															<FormControl className='' fullWidth='true'>
																<Controller
																	name='agencies'
																	control={control}
																	defaultValue={[]}
																	render={({ field: { onChange, value } }) => (
																		<Autocomplete
																			multiple
																			freeSolo
																			options={agencies}
																			value={value}
																			onChange={(e, val) => {
																				handleAgencyChange(e, val);
																				onChange(val); // update react-hook-form value
																			}}
																			disabled={!user.data.isSuperAdmin}
																			renderInput={(params) => (
																				<TextField
																					{...params}
																					placeholder={t("selectAgencies")}
																					label={t("selectAgencies")}
																					variant="outlined"
																					InputLabelProps={{ shrink: true }}
																				/>
																			)}
																		/>
																	)}
																/>
															</FormControl>
														</Grid>
														<Grid item xs={12}>
															<FormControl fullWidth sx={{ mb: 0, minWidth: 120 }}>
																<InputLabel id="department-select-label">{t("department")}</InputLabel>
																<Select
																	name="Department"
																	label={t("department")}
																	value={departmentValue}
																	onChange={handleShiftTypeChange}
																	displayEmpty
																	inputProps={{ 'aria-label': 'Without label' }}
																>
																	{departmentData.map((element) => (
																		<MenuItem key={element.name} value={element._id}>
																			{element.name}
																		</MenuItem>
																	))}
																</Select>
															</FormControl>
														</Grid>
														<Grid item xs={12} >
															<FormControl fullWidth sx={{ mb: 0, minWidth: 120 }}>
																<InputLabel id="demo-simple-select-label">{t("defaultApplication")}</InputLabel>
																<Select
																	labelId="demo-simple-select-label"
																	id="demo-simple-select"
																	value={defaultApp}
																	label={t("defaultApplication")}
																	onChange={handleDefaultAppChange}

																>
																	<MenuItem value="call">{t('call911')}</MenuItem>
																	<MenuItem value="incident">{t('incident')}</MenuItem>
																	{!doesNotUseOldRps &&
																		<MenuItem value="mobile">{t('dispatch')}</MenuItem>
																	}
																	<MenuItem value="quiktip">{t('quikTip')}</MenuItem>
																	{showAdminDefaultApp &&
																		<MenuItem value="admin">{t('relativityAdmin')}</MenuItem>
																	}
																</Select>
															</FormControl>
														</Grid>

													</Grid>
												</Box>
											</Grid>
										</Grid>
									</div>
									<div>
										{isAceesRightShow &&
											<Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 1, borderRadius: '5px', marginBottom: 2 }}>
												<legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t('userAccessRights')}:</legend>
												{isAceesRightShow &&
													<ErrorBoundary
														FallbackComponent={(props) => <ErrorPage {...props} componentName="AccessRightsSettings" />} onReset={() => { }}>
														<AccessRightsSettings
															showAccessRight={showAccessRight}
															accessRights={userAccessRights}
															parentAgencyAceessRightCallback={handleAgencyAceessRightCallback}
															defaultApp={defaultApp}
															isSuperAdmin={superAdmin}
															isAgencyAdmin={agencyAdmin}
															doesNotUseOldRps={doesNotUseOldRps}
														>
														</AccessRightsSettings>
													</ErrorBoundary>
												}
											</Box>
										}


									</div>
								</div>

								<div className="flex justify-center">
									{submitButton}
									{backButton}
								</div>
							</form>
						</div>
						<ErrorBoundary
							FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangeEmailSettingDlg" />} onReset={() => { }}>
							<ChangeEmailSettingDlg ref={ChangeEmailRef} value={selectedUser && selectedUser.email} />
						</ErrorBoundary>
					</CardContent>
				</Card>
			</div>
		</>
	);
}
export default RegisterUser;

