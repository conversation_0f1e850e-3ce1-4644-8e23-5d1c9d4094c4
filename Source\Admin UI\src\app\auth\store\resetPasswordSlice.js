import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';

import jwtService from '../../services/jwtService/jwtService';

export const submitResetPassword = (email) => async dispatch => {
    return jwtService.resetPassword(email).then(user => {
        if (user.code === "Success") {
            dispatch(
                showMessage({
                    message: user.message,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'success'
                }))
        }
        else if (user.code === "Error") {
            dispatch(
                showMessage({
                    message: user.data.message,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'
                }))
        }
        else {
            dispatch(
                showMessage({
                    message: user.message,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'
                }))
        }
    })
        .catch(error => {
            dispatch(
                showMessage({
                    message: error,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'
                }))
        });
};

const initialState = {
    success: false
};

const resetPasswordSlice = createSlice({
    name: 'auth/resetPass',
    initialState,
    reducers: {
    },
    extraReducers: {}
});

export default resetPasswordSlice.reducer;
