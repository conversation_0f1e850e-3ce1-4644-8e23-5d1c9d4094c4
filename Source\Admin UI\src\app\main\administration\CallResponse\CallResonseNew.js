import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import TablePagination from "@mui/material/TablePagination";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import FuseScrollbars from "@fuse/core/FuseScrollbars";
import { getCallResponse, removeResponse, } from "../store/callResponseSlice";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import { newUserAudit } from "../../../main/userAuditPage/store/userAuditSlice";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import CircularProgressLoader from '../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';
import CallResponseDialog from "./CallResponseDialog";
import { calculateColumnWidth, calculateOptimalMultiplier, checkData, getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "ResponseID",
        align: "left",
        disablePadding: false,
        label: "ResponseID",
        sort: true,
    },
    {
        id: "ResponseCode",
        align: "left",
        disablePadding: false,
        label: "RESPPONSE_CODE_LBL",
        sort: true,
    },
    {
        id: "ResponseCodeDescription",
        align: "left",
        disablePadding: false,
        label: "ResponseCodeDescription",
        sort: true,
    },
    {
        id: "ResponseCodeProQA",
        align: "left",
        disablePadding: false,
        label: "ResponseCodeProQA",
        sort: true,
    },
    {
        id: "ResponseCodeWestNet",
        align: "left",
        disablePadding: false,
        label: "ResponseCodeWestNet",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

let Data = []
function CallResponseNew() {
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);
    const gridRef = useRef(null);
    const callResponseRef = useRef();
    const routeParams = useParams();
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const user = useSelector(({ auth }) => auth.user);
    const CallReponses = useSelector(({ administration }) => administration.callResponse.callResponses);
    const callReponsesTotalCount = useSelector(({ administration }) => administration.callResponse.totalCount);

    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [searchText, setSearchText] = React.useState("");
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "ResponseCode",
    });
    let colorCode = getNavbarTheme();

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access Call Response",
                user: user,
                appName: "Admin",
            })
        );
    }, []);

    const deleteCallResponse = (n) => {
        setOpen(true);
        setRemoveID(n._id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeResponse(removeID, routeParams.code, pageIndex, rowsPerPage, order.id, searchText === '' ? null : searchText, order.direction),);
        }
    };

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const search = useDebounce((search, pageIndex, rowsPerPage) => {
        dispatch(getCallResponse(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, search === '' ? null : search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, pageIndex, rowsPerPage);
        } else {
            dispatch(getCallResponse(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, searchText === '' ? null : searchText, routeParams.code));
        }

    }, [dispatch, pageIndex, rowsPerPage, searchText, routeParams.code, order]);

    const isloadingvalue = useSelector(({ administration }) => administration.callResponse.isloading);
    const [loading, setLoading] = useState();

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    let PagingDetails = {
        pageIndex: pageIndex,
        rowsPerPage: rowsPerPage,
        id: order.id,
        direction: order.direction,
        searchText: searchText === '' ? null : searchText,
    }

    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;
            return (
                <>
                    <div>
                        <Tooltip title={t("edit")}>
                            <IconButton
                                variant="contained"
                                aria-label="Back"
                                color="inherit"
                                onClick={() => callResponseRef.current.handleClickOpen(x, routeParams.code, PagingDetails, true)}
                                size="large"
                            >
                                {/* <Icon>edit</Icon> */}
                                <EditIcon />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title={t("delete")}>
                            <IconButton
                                variant="contained"
                                aria-label="Back"
                                color="inherit"
                                onClick={() => deleteCallResponse(x)}
                                size="large"
                            >
                                {/* <Icon>delete</Icon> */}
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>
                    </div>
                </>
            );
        }

    };

    const rowData = CallReponses.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["action"] = ActionIcons(item)

        });
        return row;
    });

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();


    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{ content: "flex", header: "min-h-72 h-72 sm:h-136 sm:min-h-136", }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 w-full items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    phone_callback
                                </Icon>

                                <Typography className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
                                    {t("callResponseCode")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            component={motion.div}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addCallResponse" />} onReset={() => window.location.reload()} >
                                <CommonButton styleClass="whitespace-no-wrap normal-case ml-16" btnName={t("addCallResponse")} parentCallback={() => callResponseRef.current.handleClickOpen(Data, routeParams.code, PagingDetails, false)}></CommonButton>
                            </ErrorBoundary>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={callReponsesTotalCount}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{ "aria-label": "Previous Page", }}
                                nextIconButtonProps={{ "aria-label": "Next Page", }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />
                        </div>
                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={CallReponses}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="ResponseCode"
                                        header={t("code")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("codeDescription")}
                                        field="ResponseCodeDescription"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("codeProQA")}
                                        field="ResponseCodeProQA"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("codeWestNet")}
                                        field="ResponseCodeWestNet"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteMsgCallResponse")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>
                            <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="CallResponseDialog" />} onReset={() => { }} >
                                <CallResponseDialog ref={callResponseRef} />
                            </ErrorBoundary>
                        </div>
                    </div>
                }
            />
        </>
    );
}

export default CallResponseNew;