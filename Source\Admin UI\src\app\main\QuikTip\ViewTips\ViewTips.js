import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import TablePagination from "@mui/material/TablePagination";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import CircularProgressLoader from '../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';
import "./ViewTips.css";
import { getStatus, getNavbarTheme, isEmptyOrNull, getRowsPerPageOptions, useWindowResizeHeight, } from "../../utils/utils";
import ReviewsIcon from '@mui/icons-material/Reviews';
import { SetQuikTipDetail, getSearchViewTips, getViewTips } from "../../store/quikTipSlice";
import moment from "moment";
import SerachViewTips from "../componets/SearchViewTips/serachViewTips";
import DetailsIcon from '@mui/icons-material/Details';
import PlaceIcon from '@mui/icons-material/Place';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import ShowSelectedLocation from "../componets/ShowSelectedLocation";
import SocketIoInitialization from "../../SharedComponents/SocketIoInitialization/SocketIoInitialization";
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid, IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "FullName",
        align: "left",
        disablePadding: false,
        label: "FullName",
        sort: true,
    },
    {
        id: "Status",
        align: "left",
        disablePadding: false,
        label: "Status",
        sort: true,
    },
    {
        id: "TipText",
        align: "left",
        disablePadding: false,
        label: "TipText",
        sort: true,
    },
    {
        id: "DepartmentID",
        align: "left",
        disablePadding: false,
        label: "DepartmentID",
        sort: true,
    },
    {
        id: "StatusKey",
        align: "left",
        disablePadding: false,
        label: "StatusKey",
        sort: true,
    },
    {
        id: "Department",
        align: "left",
        disablePadding: false,
        label: "Department",
        sort: true,
    },
    {
        id: "DeviceToken",
        align: "left",
        disablePadding: false,
        label: "DeviceToken",
        sort: true,
    },
    {
        id: "CreatedDate",
        align: "left",
        disablePadding: false,
        label: "CreatedDate",
        sort: true,
    },
    {
        id: "Where",
        align: "left",
        disablePadding: false,
        label: "IncidentLocation",
        sort: true,
    },
    {
        id: "Latitude",
        align: "left",
        disablePadding: false,
        label: "Latitude",
        sort: true,
    },
    {
        id: "Longitude",
        align: "left",
        disablePadding: false,
        label: "Longitude",
        sort: true,
    },
    {
        id: "PhoneNumber",
        align: "left",
        disablePadding: false,
        label: "PhoneNumber",
        sort: true,
    },
    {
        id: "Email",
        align: "left",
        disablePadding: false,
        label: "Email",
        sort: true,
    },
    {
        id: "Comments",
        align: "left",
        disablePadding: false,
        label: "Comments",
        sort: true,
    },
    {
        id: "Attachments",
        align: "left",
        disablePadding: false,
        label: "Attachments",
        sort: true,
    },

    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

function ViewTips() {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const gridRef = useRef(null);
    const tipTypeRef = useRef(null);
    const ViewTipData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.viewTipData);
    const ViewTipTotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.totalCount);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.isloading);
    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.departmentData);
    const [searchText, setSearchText] = React.useState("");
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);

    const [data, setData] = React.useState(ViewTipData);
    const [countData, setCountData] = React.useState(ViewTipTotalCount);
    const routeParams = useParams();
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [loading, setLoading] = useState();

    const [order, setOrder] = React.useState({
        direction: "desc",
        id: "_id",
    });
    let colorCode = getNavbarTheme();

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }
    useEffect(() => {
        setData(ViewTipData);
        setCountData(ViewTipTotalCount);
    }, [ViewTipData]);

    useEffect(() => {
        dispatch(getViewTips(order.id, order.direction, pageIndex, rowsPerPage, routeParams.code));
    }, [pageIndex, rowsPerPage, order]);

    const QuickTipDetail = (value) => {
        dispatch(SetQuikTipDetail([]))
        dispatch(SetQuikTipDetail(value))
        history.push(`/quiktip/QuikTipDetail/${routeParams.code}`);
    }

    const ActionIcons = (n) => {
        // let x = checkData(n)
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    <div>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => QuickTipDetail(x)}
                            size="large"
                        >
                            <DetailsIcon />
                        </IconButton>

                        {x.Latitude !== 0 && <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => tipTypeRef.current.handleClickOpen(x)}
                            size="large"
                            title="View location"
                        >
                            <PlaceIcon />
                        </IconButton>}

                    </div>
                </>
            );
        }
    };

    const getDeparrtmentName = (item) => {
        let departmentName = DepartmentData.filter(x => x._id == item.DepartmentID)
        if (!isEmptyOrNull(departmentName)) {
            return departmentName.length > 0 ? !isEmptyOrNull(departmentName[0].name) ? departmentName[0].name : "" : ""
        }
        else {
            return ""
        }
    };

    const rowData = data.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["DepartmentID"] = item.DepartmentID
            row["StatusKey"] = item.Status
            row["Department"] = getDeparrtmentName(item)
            row["Status"] = getStatus(item.Status)
            row["TipText"] = item.TipText
            row["Where"] = item.Address
            row["Latitude"] = item.Latitude
            row["Longitude"] = item.Longitude
            row["PhoneNumber"] = item.PhoneNumber
            row["Email"] = item.Email
            row["tipTypes"] = item.tipTypes !== undefined && item.tipTypes !== null ? item.tipTypes[0] : ""
            row["CreatedDate"] = moment(new Date(item.CreatedDate)).format(
                "MM/DD/YY, HH:mm A"
            )
            row["Action"] = ActionIcons(item)
        });
        return row;
    });

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    function handleSearchData(departmentValue, selectedStartDate, selectedEndDate, status) {
        let code = routeParams.code
        let data = {
            departmentValue,
            selectedStartDate,
            selectedEndDate,
            status,
            order,
            code,
            pageIndex,
            rowsPerPage
        }
        dispatch(getSearchViewTips(data));
    }

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            setOrder({ id: sortDesc.field, direction: sortDesc.sortDirection === 0 ? "asc" : "desc" });
        } else {
            console.log("No sorting applied.");
        }
    };
    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 w-full items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                    style={{ alignItems: 'center', display: "flex" }}
                                >
                                    <ReviewsIcon style={{ fontSize: '40px' }} />
                                </Icon>

                                <Typography className="item hidden sm:flex mx-0 sm:mx-12" variant="h6">
                                    {t("viewTips")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="SerachViewTips" />} onReset={() => { }} >
                                    <SerachViewTips code={routeParams.code} handleSearchData={handleSearchData} />
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div
                            className="sticky"
                            style={{
                                backgroundColor: colorCode.backgroundColor, color: colorCode.color,
                                zIndex: 1250
                            }}
                        >

                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={countData}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{
                                    "aria-label": "Previous Page",
                                }}
                                nextIconButtonProps={{
                                    "aria-label": "Next Page",
                                }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            /></div>

                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="FullName"
                                        field="FullName"
                                        header={t("name")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Status"
                                        header={t("status")}
                                        field="Status"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="TipText"
                                        header={t("description")}
                                        field="TipText"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Department"
                                        header={t("department")}
                                        field="Department"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="CreatedDate"
                                        header={t("date")}
                                        field="CreatedDate"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Where"
                                        header={t("where")}
                                        field="Where"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Latitude"
                                        header={t("latitude")}
                                        field="Latitude"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Longitude"
                                        header={t("longitude")}
                                        field="Longitude"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="PhoneNumber"
                                        header={t("phoneNumber")}
                                        field="PhoneNumber"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Email"
                                        header={t("email")}
                                        field="Email"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ShowSelectedLocation" />} onReset={() => { }}>
                                <ShowSelectedLocation ref={tipTypeRef} />
                            </ErrorBoundary>
                        </div>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="SocketIoInitialization" />} onReset={() => { }}>
                            <SocketIoInitialization tipID={0} viewTipAlert="viewtipAlert" code={routeParams.code}></SocketIoInitialization>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    )
}

export default ViewTips;