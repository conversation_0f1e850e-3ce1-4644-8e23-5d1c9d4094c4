import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';


export const getBoatDetails = (data) => async dispatch => {
    
    await axios.post(process.env.REACT_APP_GateWayAPI_URL_URL.concat(`admin/api/boatSearch/getboatbysearch`), { data })
        .then(response => {
            
            if (response.data.length > 0) {
                return dispatch(boatListSuccess(response.data))

            }
        }).catch(error => {
            
            //return dispatch(countyExtentsData([]));

        });

};

export const clearBoatSearchData= () => dispatch=>{
    return dispatch(boatSearchListSuccess([]))  
}

export const getBoatDetailsCopy = (data) => async dispatch => {
    
    await axios.post(process.env.REACT_APP_GateWayAPI_URL_URL.concat(`admin/api/boatSearch/getboatbysearchCopy`), { data })
        .then(response => {
            
            if (response.data.length > 0) {
                return dispatch(boatListSuccessCopy(response.data))

            }
        }).catch(error => {
            
            //return dispatch(countyExtentsData([]));

        });

};

export const clearBoatSearchDataCopy= () => dispatch=>{
    return dispatch(boatSearchListSuccessCopy([]))  
}

const initialState = {
    boatsuccess: false,
    boatdata: [],

    boatsuccessCopy: false,
    boatdataCopy: [],
    
};


const boatSlice = createSlice({
    name: 'boat',
    initialState,
    reducers: {
        
        boatListSuccess: (state, action) => {
            
            state.boatsuccess = true;
            state.boatdata = action.payload;
        },
        boatSearchListSuccess: (state, action) => {
            state.boatsuccess = true;
            state.boatdata = [];
        },
         
        boatListSuccessCopy: (state, action) => {
            
            state.boatsuccessCopy = true;
            state.boatdataCopy = action.payload;
        },
        boatSearchListSuccessCopy: (state, action) => {
            state.boatsuccessCopy = true;
            state.boatdataCopy = [];
        },
       
    },
    extraReducers: {}
});

export const {
    boatListSuccess,
    boatSearchListSuccess,
    boatListSuccessCopy,
    boatSearchListSuccessCopy,

} = boatSlice.actions;

export default boatSlice.reducer;
