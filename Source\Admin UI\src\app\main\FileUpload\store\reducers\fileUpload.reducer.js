import * as Actions from '../actions';

const initialState = {
	success: false,
	error: {
		contact: {}
	},
	uploadFileObj: [],
	savedFileObj: [],
	savedFileView: []
};

const fileUpload = (state = initialState, action) => {
	switch (action.type) {

		case Actions.GET_SAVEDFILEOBJLIST: {
			return {
				...state,
				savedFileObj: action.payload
			};
		}

		case Actions.GET_SAVEDFILEOBJ: {
			return {
				...state,
				savedFileView: action.payload
			};
		}

		case Actions.UPLOAD_SUCCESS: {
			return {
				...initialState,
				success: true
			};
		}

		case Actions.UPLOAD_FILEOBJ: {
			return {
				...state,
				uploadFileObj: action.uploadFileObj
			};
		}

		default: {
			return state;
		}
	}
};

export default fileUpload;
