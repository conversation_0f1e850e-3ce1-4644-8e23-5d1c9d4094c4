import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import React, { useState, useEffect, useRef } from "react";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import { deleteSupportedUnitType, getSupportedUnitType, searchSupportedUnitType } from "../store/supportedUnitTypeSlice";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import CircularProgressLoader from '../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';
import SupportIcon from '@mui/icons-material/Support';
import { ErrorBoundary } from "react-error-boundary";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import AddNewSupportedUnitTypeDlg from "./AddNewSupportedUnitTypeDlg";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid, IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import { useWindowResizeHeight } from "../../utils/utils";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "name",
        align: "left",
        disablePadding: false,
        label: "Name",
        sort: true,
    },
    {
        id: "rpsCode",
        align: "left",
        disablePadding: false,
        label: "RpsCode",
        sort: true,
    },
    {
        id: "availability",
        align: "left",
        disablePadding: false,
        label: "availability",
        sort: true,
    },
    {
        id: "iconUrl",
        align: "left",
        disablePadding: false,
        label: "Icon",
        sort: true,
    },
    {
        id: "iconKey",
        align: "left",
        disablePadding: false,
        label: "Icon",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

function SupportedUnitType(props) {
    const navbarTheme = useSelector(selectNavbarTheme);
    const gridRef = useRef(null);
    const SupportUnitTypeRef = useRef();
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");

    const routeParams = useParams();
    const SupportedUnitType = useSelector(({ administration }) => administration.supportedUnitType.supportedUnitType);
    const searchSupportedUnitTypes = useSelector(({ administration }) => administration.supportedUnitType.searchSupportedUnitTypes);
    const isloadingvalue = useSelector(({ administration }) => administration.supportedUnitType.isloading);
    const [removeID, setRemoveID] = React.useState(0);
    const [iconKey, setIconKey] = React.useState("");
    const [open, setOpen] = React.useState(false);
    const [searchText, setSearchText] = React.useState("");
    const [data, setData] = React.useState(SupportedUnitType);
    const [loading, setLoading] = useState();
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "Srno",
    });

    useEffect(() => {
        dispatch(getSupportedUnitType(routeParams.code));
    }, []);

    useEffect(() => {
        if (searchText.length > 0) {
            search(searchText);
        } else {
            setData(SupportedUnitType);
        }
    }, [searchText, SupportedUnitType]);

    useEffect(() => {
        if (searchText.length !== 0) {
            setData(searchSupportedUnitTypes);
        } else {
            setData(SupportedUnitType);
        }
    }, [SupportedUnitType, searchText]);

    const search = useDebounce(search => {
        dispatch(searchSupportedUnitType(search, routeParams.code));
    }, 500);

    const deleteRecord = (n) => {
        setOpen(true);
        setRemoveID(n._id);
        setIconKey(n.iconKey);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(deleteSupportedUnitType(removeID, routeParams.code, iconKey));
        }
    };

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => SupportUnitTypeRef.current.handleClickOpen(x, routeParams.code, true)}
                                    size="large"
                                >
                                    <EditIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("delete")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => deleteRecord(x)}
                                    size="large"
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                    }
                </>
            );
        }
    };

    const rowData = data.map(item => {
        const row = {};
        let UnitAvailability = item.availability === true ? "Available" : "UnAvailable";
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["_id"] = item._id
            row["name"] = item.name
            row["rpsCode"] = item.rpsCode
            row["availability"] = UnitAvailability
            row["iconUrl"] = item.iconUrl
            row["iconKey"] = item.iconKey
            row["action"] = ActionIcons(item)
        });
        return row;
    });

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    const renderIcon = (ctx) => {
        const item = ctx.dataContext;
        return (
            <img
                src={item.implicit}
                alt={item.name}
                style={{ height: '50px', width: '100px', objectFit: 'contain' }}
            />
        );
    };

    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <SupportIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("supportedUnitType")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addSupportTypeUnit" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addSupportTypeUnit")} parentCallback={() => SupportUnitTypeRef.current.handleClickOpen("", routeParams.code, false)}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="name"
                                        field="name"
                                        header={t("name")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="rpsCode"
                                        header={t("rpsCode")}
                                        field="rpsCode"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="availability"
                                        header={t("availability")}
                                        field="availability"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="iconUrl"
                                        header={t("icon")}
                                        field="iconUrl"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        bodyTemplate={renderIcon}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteRecord")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="AddNewSupportedUnitTypeDlg" />} onReset={() => { }} >
                                <AddNewSupportedUnitTypeDlg ref={SupportUnitTypeRef} />
                            </ErrorBoundary>
                        </div>

                    </div>
                }
            />
        </>
    );
}

export default SupportedUnitType;