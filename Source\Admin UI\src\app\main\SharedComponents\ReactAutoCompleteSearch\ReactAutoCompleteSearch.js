import { ReactSearchAutocomplete } from 'react-search-autocomplete'
import { useDispatch } from 'react-redux';
import "./ReactAutoCompleteSearch.css"
import { decrypt } from "src/app/security";
import React, { forwardRef, useRef, useImperativeHandle, useEffect, useState, } from "react";
import { EnumForAutoSearch } from '../../utils/utils';
import { setGlobalPersonDetails } from '../../store/personSlice';
import { setSearchData } from '../../store/incidentSlice';
import { useSelector } from "react-redux";
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import Cookies from 'js-cookie';
import axios from 'axios';

function ReactAutoCompleteSearch(props) {
    const dispatch = useDispatch();
    const searchData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.incident.serachData);
    const EnumForSearch = EnumForAutoSearch();
    const navbarTheme = useSelector(selectNavbarTheme);

    const getDataFromApi = async (search, url) => {
        let searchUrl = await getSearchUrl(url, search)
        const data = await axios.get(searchUrl, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${Cookies.get('jwt_access_token')}`
            }
        })
        let finalData = await JSON.parse(decrypt(data.data));
        if (props.global == true) {
            let globalData = await axios.get(`admin/api/personMaster/globalPersonSearchList/${search}`, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${localStorage.getItem('jwt_access_token')}`
                }
            })
            globalData = await JSON.parse(decrypt(data.data));
            dispatch(setGlobalPersonDetails(globalData))
        }
        return finalData;
    }

    const getSearchUrl = async (url, search) => {
        let searchUrl;
        switch (props.type) {
            case EnumForSearch.veh:
                return searchUrl = `admin/api/${url}/${search}/${props.code}`
            case EnumForSearch.PER: case EnumForSearch.per: case EnumForSearch.Per:
                return searchUrl = `admin/api/${url}/${search}/${props.code}`
            case EnumForSearch.inctype:
                return searchUrl = `incident/api/${url}/${search}/${props.code}`
            case EnumForSearch.place:
                return searchUrl = `admin/api/${url}/${search}`
            case EnumForSearch.driverPlace:
                return searchUrl = `admin/api/${url}/${search}`
        }
    }

    const handleOnSearch = async (value) => {
        if (value !== "") {
            const data = await getDataFromApi(value, props.url);
            let newArray = await data.map(obj => {
                return { ...obj, [props.keys]: getOptionLabelString(obj) };
            });
            dispatch(setSearchData(newArray));
        }
    };

    const handleOnHover = (result) => {
    };

    const handleOnSelect = (item) => {
        props.passChildSelectData(item);
    };

    const handleOnFocus = () => {
    };

    const handleOnClear = () => {
        dispatch(setSearchData([]));
        props.clear();
    };

    const getOptionLabelString = (option) => {
        let stringValue = "";
        switch (props.type) {
            case EnumForSearch.PER: case EnumForSearch.per: case EnumForSearch.Per:
                stringValue = `${option.FullName}, ${new Date(option.DateOfBirth).toDateString()}`
                break;
            case EnumForSearch.veh:
                stringValue = `${option.tagNumber}, ${option.tagState !== "NULL" ? option.tagState : ""}, ${option.tagYear !== "NULL" && option.tagYear !== undefined ? option.tagYear : option.modelYear}, ${option.makeDesc !== "NULL" ? option.makeDesc : ""}`
                break;
            case EnumForSearch.place: case EnumForSearch.driverPlace:
                stringValue =
                    `${option.Add_Number !== undefined ? option.Add_Number : ""} ${option.StNam_Full !== undefined ? option.StNam_Full : ""}, ${option.Post_City !== undefined ? option.Post_City : ""}, ${option.State !== undefined ? option.State : ""}, ${option.Zip_Code !== undefined ? option.Zip_Code : ""}`
                break;
            case EnumForSearch.inctype:
                stringValue = Object.values(option).join(" ")
                break;
            case EnumForSearch.callType:
                stringValue = `${option.CallTypeName}`
                break;
            default:
                stringValue = "";
        }
        return stringValue;
    }

    const formatResult = (option) => {
        const offenseDescription = option.OffenseDescription !== undefined
            ? option.OffenseDescription
            : '';

        return (
            <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                <div style={{ display: "grid", gridTemplateColumns: "150px 1fr", gap: "10px" }}>
                    <span style={{ fontWeight: "bold", textAlign: "left" }}>{option.StatuteCitation}</span>
                    <span>{offenseDescription}</span>
                </div>
            </div>
        );
    }

    return (
        <ReactSearchAutocomplete
            items={searchData}
            fuseOptions={{ keys: [props.keys] }} // Search on both fields
            resultStringKeyName={props.resultStringKeyName} // String to display in the results
            onSearch={handleOnSearch}
            onHover={handleOnHover}
            onSelect={handleOnSelect}
            onFocus={handleOnFocus}
            onClear={handleOnClear}
            placeholder={props.placeholder}
            showNoResultsText="Gathering Results"
            maxResults={200}
            autoFocus={true}
            formatResult={props.type === EnumForSearch.inctype ? formatResult : undefined}
            styling={{
                border: "1px solid #ccc",
                borderRadius: "4px",
                backgroundColor: navbarTheme.palette.mode === 'light' ? 'white' : 'black',
                boxShadow: "none",
                hoverBackgroundColor: navbarTheme.palette.secondary.main,
                color: navbarTheme.palette.mode === 'light' ? 'black' : 'white',
                fontSize: "15px",
                iconColor: "grey",
                lineColor: "grey",
                placeholderColor: navbarTheme.palette.primary.light,
                clearIconMargin: "3px 8px 0 0",
                zIndex: 4,
                cursor: 'text',
                height: "40px"
            }}
        />
    );
}

export default ReactAutoCompleteSearch;
