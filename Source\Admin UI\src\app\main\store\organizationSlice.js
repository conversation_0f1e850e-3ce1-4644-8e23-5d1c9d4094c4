import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';


export const getOrganizationDetails = (searchText) => async dispatch => {

    try {
        await axios.post(process.env.REACT_APP_API_URL.concat('dispatch/api/organisation/organisationDetails'), { searchText })
            .then(response => {
                dispatch(organizationListSuccess(response.data));
                if (response.data.length > 0) {
                    dispatch(showMessage({
                        message: 'Organization details found.', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                }
                else {
                    dispatch(showMessage({
                        message: 'Organization details not found.', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }));
                }

            })
            .catch(error => {
                dispatch(organizationListError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
};



const initialState = {
    organizationsuccess: false,
    organizationdata: [],
    organizationerror: {
        username: null,
        password: null
    }
};


const organizationSlice = createSlice({
    name: 'organization',
    initialState,
    reducers: {
        organizationListSuccess: (state, action) => {
            state.organizationsuccess = true;
            state.organizationdata = action.payload;
        },
        organizationListError: (state, action) => {
            state.organizationsuccess = false;
            state.organizationdata = [];
        }
    },
    extraReducers: {}
});

export const {
    organizationListSuccess,
    organizationListError
} = organizationSlice.actions;

export default organizationSlice.reducer;
