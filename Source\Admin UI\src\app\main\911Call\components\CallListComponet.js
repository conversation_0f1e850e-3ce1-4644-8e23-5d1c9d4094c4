import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import React, { useState, useEffect, useRef } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import { ErrorBoundary } from "react-error-boundary";
import CallInfo from "./CallInfo";
import Tooltip from "@mui/material/Tooltip";
import { useTranslation } from "react-i18next";
import {
    setSelectedCallData, getRapidSoSData, getCountyList, getPacket911CallList, getCityList, getZoneList, getPDZoneList, SetCurrrentData, clearUpdatedData
} from "../store/call911Slice";
import { useDispatch, useSelector } from "react-redux";
import "../911Call.css";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { isMobile } from "react-device-detect";
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { getClassOfServiceList } from "src/app/main/ClassOfServicePage/store/classOfServiceSlice";
import jsonp from 'jsonp';
import SelectDropDown from "../../SharedComponents/ReuseComponents/SelectDropDown";
import { getUniqueArray, isEmptyOrNull, useWindowResizeHeight } from "../../utils/utils";
import SocketIoInitialization from "../../SharedComponents/SocketIoInitialization/SocketIoInitialization";
import Badge from '@mui/material/Badge';
import { newUserAudit } from "../../userAuditPage/store/userAuditSlice";

function CallListComponent(props) {
    const classes = props.classes;
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [expanded, setExpanded] = React.useState("currentCall");
    const selectedCallData = useSelector(({ call911 }) => call911.call911.selectedCall);
    const packet911Calls = useSelector(({ call911 }) => call911.call911.data);
    const updatedData = useSelector(({ call911 }) => call911.call911.updatedData);
    const update = useSelector(({ call911 }) => call911.call911.update);
    const countyList = useSelector(({ call911 }) => call911.call911.countyList);
    const cityList = useSelector(({ call911 }) => call911.call911.cityList);
    const zoneList = useSelector(({ call911 }) => call911.call911.zoneList);
    const pdZoneList = useSelector(({ call911 }) => call911.call911.pdZoneList);
    const codeList = useSelector(({ classOfService }) => classOfService.classOfService.data);
    const packet911CALLTotalCount = useSelector(({ call911 }) => call911.call911.packet911calltotalCount);
    const user = useSelector(({ auth }) => auth.user);

    //const [dispatchWorkerList] = useWorker(getDataUsingWorker, { autoTerminate: true, });
    const autoRefresh = localStorage.getItem('AutoRefreshForCall911');
    //const [intervalEnabled, setIntervalEnabled] = React.useState((autoRefresh !== null && autoRefresh !== undefined) ? JSON.parse(autoRefresh) : false);
    //const [toggleRefreshValue, setToggleRefreshValue] = React.useState((autoRefresh !== null && autoRefresh !== undefined) ? JSON.parse(autoRefresh) : false)

    var timeoutID;
    const navbarTheme = useSelector(selectNavbarTheme);
    const [showCityDropdown, setShowCityDropdown] = React.useState(false);
    const [cityValue, setCity] = React.useState(0);
    const [showZoneDropdown, setShowZoneDropdown] = React.useState(false);
    const [zoneValue, setZone] = React.useState(0);
    const [pdZoneValue, setPDZone] = React.useState(0);
    const [countCall, setCountCall] = useState(0);
    const [page, setpage] = useState(0);
    const [rowPerPage, setrowPerPage] = useState(50);
    const [listCount, setListCount] = useState({ prev: 0, next: 50 });
    const handlePanelChange = (panel) => (event, newExpanded) => { setExpanded(newExpanded ? panel : false); };

    const [dbUpdate, setDbUpdate] = React.useState(false);

    useEffect(() => {
        if (dbUpdate) {
            dispatch(getPacket911CallList(props.countyValue, page, rowPerPage, cityValue, zoneValue, pdZoneValue));
            setDbUpdate(false);
        }
    }, [dbUpdate, props.newCallOpen]);



    const userActive = () => {
        setCountCall(countCall + 1);
    }

    useEffect(() => {
        let latestDate = props.current
            .map(function (e) {
                return e.PacketCallReceivedDT;
            })
            .sort()
            .reverse()[0];

        localStorage.setItem("latestDate", latestDate)

    }, [props.current])

    const windowHeight = useWindowResizeHeight(window.innerHeight, 130);

    const divstyle = {
        overflow: "auto",
        height: windowHeight
    }

    // useEffect(() => {
    //     if (countCall !== -1 && intervalEnabled) {
    //         const timer = () => setTimeout(() => {
    //             const latestDate = props.current
    //                 .map(function (e) {
    //                     return e.PacketCallReceivedDT;
    //                 })
    //                 .sort()
    //                 .reverse()[0];
    //             dispatchCallListBGT(
    //                 `911/api/call/refreshcallList?countyID=${props.countyValue}&selectedCity=${cityValue}&selectedZone=${zoneValue}&selectedPDZone=${pdZoneValue}&maxdate=${latestDate}`
    //             );
    //             userActive();
    //         }, 5000);
    //         timeoutID = timer();
    //     }
    //     else {
    //         setCountCall(0);
    //     }
    //     return () => {
    //         clearTimeout(timeoutID);
    //     };
    // }, [countCall, intervalEnabled]);




    useEffect(() => {
        if (update) {
            let x = updatedData
            let data = [...x].sort((a, b) => b.PacketCallingPhone - a.CAD911CallID);
            props.setNewCurrentValue(data);

            // props.setData(updatedData);
        }
        else {
            dispatch(clearUpdatedData([]))
            props.setNewCurrentValue(packet911Calls);

        }
    }, [update]);



    // const dispatchCallListBGT = async (url) => {
    //     try {
    //         let data = await dispatchWorkerList({
    //             APIUrl: localStorage.getItem('tenant').concat(url),
    //             Token: localStorage.getItem("jwt_access_token"),
    //         });
    //         if (data.length > 0) {
    //             let uniqueArray = getUniqueArray([...data, ...props.current])
    //             // Update the state with the unique array
    //             props.setCurrent(uniqueArray);
    //             props.setflag(true);
    //         }
    //     } catch (e) {
    //     }
    // };

    useEffect(() => {
        dispatch(getClassOfServiceList());
        var lCountID =
            localStorage.getItem("call911_selectedCounty") !== null
                ? localStorage.getItem("call911_selectedCounty")
                : 0;
        handleChangeCounty(parseInt(lCountID), true);

        dispatch(
            newUserAudit({
                activity: "Access 911 Call",
                user: user,
                appName: "911 Call",
            })
        );

        dispatch(getRapidSoSData());
        dispatch(getCountyList());
    }, []);

    useEffect(() => {
        if (props.packet911Calls.length > 0 && codeList.length > 0) {
            props.setData(props.packet911Calls);
        }
    }, [props.packet911Calls, codeList]);

    const setScrollingToStart = () => {
        props.setHasMore(true);
        setListCount({ prev: 0, next: 50 });
        props.setCurrent([]);
        dispatch(SetCurrrentData([]))
        props.setNewCurrentValue([]);
    };

    const getMoreData = () => {
        if (props.current.length >= packet911CALLTotalCount) {
            props.setHasMore(false);
            return;
        }
        setTimeout(() => {
            dispatch(getPacket911CallList(props.countyValue, page + 1 * rowPerPage, rowPerPage, cityValue, zoneValue, pdZoneValue));
            setpage(page + 1 * rowPerPage);
        }, 1000);

        dispatch(SetCurrrentData(props.current))

    };

    const handleChangeCounty = (value, localSettings) => {
        dispatch(SetCurrrentData([]))
        setpage(0);
        setScrollingToStart();
        props.setCounty(value);
        if (!localSettings) {
            setCity(0);
            localStorage.setItem("call911_selectedCity", 0);
        }
        setShowZoneDropdown(false);
        dispatch(getPacket911CallList(value, 0, rowPerPage, cityValue, zoneValue, pdZoneValue, 0));
        if (value === 0) {
            setShowCityDropdown(false);
        } else {
            dispatch(getCityList(value));
            setShowCityDropdown(true);
        }
        localStorage.setItem("call911_selectedCounty", value);
        if (localSettings) {
            var lCityID =
                localStorage.getItem("call911_selectedCity") !== null
                    ? localStorage.getItem("call911_selectedCity")
                    : 0;
            if (parseInt(lCityID) !== 0) handleChangeCity(lCityID);
        }
    };

    const handleChangeCity = (value) => {
        dispatch(SetCurrrentData([]))
        setpage(0);
        setScrollingToStart();
        setCity(value);
        setZone(0);
        localStorage.setItem("call911_selectedZone", 0);
        setPDZone(0);
        localStorage.setItem("call911_selectedPDZone", 0);

        if (value === 0) {
            setShowZoneDropdown(false);
            dispatch(getPacket911CallList(props.countyValue, 0, rowPerPage, value, zoneValue, pdZoneValue, 0));
            setpage(0);
        } else {
            dispatch(getZoneList(props.countyValue, value));
            dispatch(getPDZoneList(props.countyValue, value));
            dispatch(getPacket911CallList(props.countyValue, page, rowPerPage, value, zoneValue, pdZoneValue, 0));
            setShowZoneDropdown(true);
        }
        localStorage.setItem("call911_selectedCity", value);
    };

    const handleChangeZone = (event) => {
        dispatch(SetCurrrentData([]))
        setpage(0);
        setScrollingToStart();
        setZone(event.target.value);
        localStorage.setItem("call911_selectedZone", event.target.value);
        getPacket911CallList(props.countyValue, 0, rowPerPage, cityValue, event.target.value, pdZoneValue, 0);
    };

    const handleChangePDZone = (event) => {
        dispatch(SetCurrrentData([]))
        setpage(0);
        setScrollingToStart();
        setPDZone(event.target.value);
        localStorage.setItem("call911_selectedPDZone", event.target.value);
        getPacket911CallList(props.countyValue, 0, rowPerPage, cityValue, zoneValue, event.target.value, 0);
    };

    const handleKeyPressAllCalls = (e) => {
        var callIndex = props.current.indexOf(selectedCallData);
        if (callIndex !== -1) {
            if (e.keyCode === 40 && callIndex !== props.current.length - 1) {
                props.setSelectedCard(props.current[callIndex + 1]._id);
                dispatch(setSelectedCallData(props.current[callIndex + 1]));
            }
            if (e.keyCode === 38 && callIndex !== 0) {
                props.setSelectedCard(props.current[callIndex - 1]._id);
                dispatch(setSelectedCallData(props.current[callIndex - 1]));
            }
            if (
                (e.keyCode === 35 || e.keyCode === 34) &&
                callIndex !== props.current.length - 1
            ) {
                props.setSelectedCard(props.current[props.current.length - 1]._id);
                dispatch(setSelectedCallData(props.current[props.current.length - 1]));
            }
            if ((e.keyCode === 36 || e.keyCode === 33) && callIndex !== 0) {
                props.setSelectedCard(props.current[0]._id);
                dispatch(setSelectedCallData(props.current[0]));
            }
        }
    };

    const nav_css = {
        backgroundColor: navbarTheme.palette.primary.main,
        color: navbarTheme.palette.primary.contrastText,

    };

    // const handleToggleRefreshChange = (event) => {
    //     setToggleRefreshValue(event.target.checked);
    //     localStorage.setItem('AutoRefreshForCall911', event.target.checked);
    //     if (intervalEnabled) {
    //         setIntervalEnabled(false);
    //         setCountCall(-1);
    //     } else {
    //         setIntervalEnabled(true);
    //         setCountCall(0);
    //     }
    // };

    const setSelectedAddress = (value) => {
        props.setSelectedCard(selectedCallData._id)
        props.setNearestAddress(value);
    };

    return (
        <>
            {isMobile ? (
                <Accordion onChange={handlePanelChange("other")}>
                    <AccordionSummary
                        expandIcon={<ExpandMoreIcon style={nav_css} />}
                        aria-controls="panel1a-content"
                        id="panel1a-header"
                        style={nav_css}
                        className="cardborderclass"
                    >
                        <Typography className={classes.title} variant="h3">
                            {t("incomingCallAndMessage")}
                        </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                        <Grid item xs={12}>
                            <div >
                                <Card className="cardborderclass" >
                                    <CardContent>
                                        <Grid container spacing={1}>
                                            {!showCityDropdown && (
                                                <Grid item xs={12}>
                                                    <ErrorBoundary
                                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />}
                                                        onReset={() => { }} >
                                                        <SelectDropDown value="id" label="county" inputLable={t("county")} data={countyList} parentHandleChange={handleChangeCounty} defaultValue={props.countyValue} localSettings={false}></SelectDropDown>
                                                    </ErrorBoundary>
                                                </Grid>
                                            )}
                                            {showCityDropdown && (
                                                <Grid item xs={6}>
                                                    <ErrorBoundary
                                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                        <SelectDropDown value="id" label="county" inputLable={t("county")} data={countyList} parentHandleChange={handleChangeCounty} defaultValue={props.countyValue} localSettings={false}></SelectDropDown>
                                                    </ErrorBoundary>
                                                </Grid>
                                            )}
                                            {showCityDropdown && (
                                                <Grid item xs={6}>
                                                    <ErrorBoundary
                                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                        <SelectDropDown value="id" label="city" inputLable={t("city")} data={cityList} parentHandleChange={handleChangeCity} defaultValue={cityValue}></SelectDropDown>
                                                    </ErrorBoundary>
                                                </Grid>
                                            )}
                                            {showZoneDropdown && (
                                                <Grid item xs={6}>
                                                    <ErrorBoundary
                                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                        <SelectDropDown value="id" label="LABEL" inputLable={t("zone")} data={zoneList} parentHandleChange={handleChangeZone} defaultValue={zoneValue}></SelectDropDown>
                                                    </ErrorBoundary>
                                                </Grid>
                                            )}
                                            {showZoneDropdown && (
                                                <Grid item xs={6}>
                                                    <ErrorBoundary
                                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                        <SelectDropDown value="id" label="LABEL" inputLable={t("pdZone")} data={pdZoneList} parentHandleChange={handleChangePDZone} defaultValue={pdZoneValue}></SelectDropDown>
                                                    </ErrorBoundary>
                                                </Grid>
                                            )}
                                        </Grid>
                                    </CardContent>
                                </Card>

                                <div id="scrollableDiv" style={divstyle}>
                                    <InfiniteScroll
                                        dataLength={props.current.length}
                                        next={getMoreData}
                                        hasMore={props.hasMore}
                                        loader={
                                            <p style={{ textAlign: "center" }}>
                                                <b> {t("loading")}</b>
                                            </p>
                                        }
                                        endMessage={
                                            <p style={{ textAlign: "center" }}>
                                                <b>{t("seenAll")}</b>
                                            </p>
                                        }
                                        scrollableTarget="scrollableDiv"
                                    >
                                        <div>
                                            {props.current &&
                                                props.current.map((name, index) => (
                                                    <div
                                                        key={index}
                                                        onClick={() =>
                                                            dispatch(
                                                                setSelectedCallData(name),
                                                                (props.setSelectedCard(name._id))
                                                            )
                                                        }
                                                    >
                                                        {(!isEmptyOrNull(name.Packety) || !isEmptyOrNull(name.Packetx)) &&
                                                            <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="CallInfo" />} onReset={() => { }} >
                                                                <CallInfo
                                                                    value={name}
                                                                    id={name._id}
                                                                    count={name.count}
                                                                    backColor={codeList
                                                                        .filter(
                                                                            (type) =>
                                                                                type.code ===
                                                                                name.PacketClassofService
                                                                        )
                                                                        .map((code) => code.color)}
                                                                ></CallInfo>
                                                            </ErrorBoundary>
                                                        }
                                                    </div>
                                                ))}
                                        </div>
                                    </InfiniteScroll>
                                </div>
                            </div>
                        </Grid>
                    </AccordionDetails>
                </Accordion>
            ) : (
                <>
                    <Card className={classes.cardRoot} variant="outlined">
                        <CardContent style={nav_css} className="p-13 flex flex-row">
                            <Typography className={classes.title} variant="h3">
                                {t("incomingCallAndMessage")} {"   "}
                                {/* <ErrorBoundary 
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonSwitch" />} onReset={() => window.location.reload()} >
                                    <CommonSwitch switchName={t("autoRefresh")} parentCallback={handleToggleRefreshChange} toggleValue={toggleRefreshValue}
                                    ></CommonSwitch>
                                </ErrorBoundary> */}
                            </Typography>
                            <div className="float-right px-16 -right-4 -top-8 relative">
                                <Badge badgeContent={props.current.length == 0 ? "0" : props.current.length} color="secondary" max={999}>
                                </Badge>
                            </div>
                        </CardContent>
                    </Card>
                    <div tabIndex="1" onKeyDown={handleKeyPressAllCalls}>
                        <Card className='cardborderclass'>
                            <CardContent>
                                <Grid container spacing={1}>
                                    {!showCityDropdown && (
                                        <Grid item xs={12}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                <SelectDropDown value="id" label="county" inputLable={t("county")} data={countyList} parentHandleChange={handleChangeCounty} defaultValue={props.countyValue} localSettings={false}></SelectDropDown>
                                            </ErrorBoundary>
                                        </Grid>
                                    )}
                                    {showCityDropdown && (
                                        <Grid item xs={6}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                <SelectDropDown value="id" label="county" inputLable={t("county")} data={countyList} parentHandleChange={handleChangeCounty} defaultValue={props.countyValue} localSettings={false}></SelectDropDown>
                                            </ErrorBoundary>
                                        </Grid>

                                    )}
                                    {showCityDropdown && (
                                        <Grid item xs={6}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                <SelectDropDown value="id" label="city" inputLable={t("city")} data={cityList} parentHandleChange={handleChangeCity} defaultValue={cityValue}></SelectDropDown>
                                            </ErrorBoundary>
                                        </Grid>

                                    )}

                                    {showZoneDropdown && (
                                        <Grid item xs={6}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                <SelectDropDown value="id" label="LABEL" inputLable={t("zone")} data={zoneList} parentHandleChange={handleChangeZone} defaultValue={zoneValue}></SelectDropDown>
                                            </ErrorBoundary>
                                        </Grid>
                                    )}

                                    {showZoneDropdown && (
                                        <Grid item xs={6}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectDropDown" />} onReset={() => { }} >
                                                <SelectDropDown value="id" label="LABEL" inputLable={t("pdZone")} data={pdZoneList} parentHandleChange={handleChangePDZone} defaultValue={pdZoneValue}></SelectDropDown>
                                            </ErrorBoundary>
                                        </Grid>
                                    )}
                                </Grid>
                            </CardContent>
                        </Card>

                        <div
                            id="scrollableDiv"
                            style={{
                                overflow: "auto",
                                height: showZoneDropdown
                                    ? window.innerHeight - 265
                                    : window.innerHeight - 200,
                            }}
                        >
                            {props.packet911Calls.length > 0 ? (
                                <InfiniteScroll
                                    dataLength={props.current.length}
                                    next={getMoreData}
                                    hasMore={props.hasMore}
                                    loader={
                                        <p style={{ textAlign: "center" }}>
                                            <b>{t("loading")}</b>
                                        </p>
                                    }
                                    endMessage={
                                        <p style={{ textAlign: "center" }}>
                                            <b>{t("YayYouhaveseenitall")}</b>
                                        </p>
                                    }
                                    scrollableTarget="scrollableDiv"
                                >
                                    <div>
                                        {props.current &&
                                            props.current.map((name, index) => (
                                                <Grid
                                                    item
                                                    xs={12}
                                                    key={index}
                                                    onClick={() =>
                                                        dispatch(
                                                            setSelectedCallData(name),
                                                            (props.setSelectedCard(name._id))
                                                        )
                                                    } >
                                                    <ErrorBoundary
                                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="CallInfo" />} onReset={() => { }} >
                                                        <CallInfo
                                                            value={name}
                                                            id={name._id}
                                                            selectedCard={props.selectedCard}
                                                            count={name.count}
                                                            backColor={codeList
                                                                .filter(
                                                                    (type) =>
                                                                        type.code ===
                                                                        name.PacketClassofService
                                                                )
                                                                .map((code) => code.color)}
                                                        >
                                                            <NearAddresses
                                                                value={name}
                                                                onClick={(e) => setSelectedAddress(e)}
                                                            />
                                                        </CallInfo>
                                                    </ErrorBoundary>
                                                </Grid>
                                            ))}
                                    </div>
                                </InfiniteScroll>
                            ) : (
                                <Card variant="outlined" style={{ width: "100%" }}>
                                    <CardContent>
                                        <Typography variant="body2" component="p">
                                            {t("noCallFound")}
                                        </Typography>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="SocketIoInitialization" />} onReset={() => window.location.reload()} >
                            <SocketIoInitialization countyValue={props.countyValue} cityValue={cityValue} zoneValue={zoneValue} pdZoneValue={pdZoneValue} call911app="911appalert"></SocketIoInitialization>
                        </ErrorBoundary>
                    </div>
                </>
            )}
        </>

    );
}

const imageArray = [
    require("../../../asset/img/blue-white.png"),
    require("../../../asset/img/green-white.png"),
    require("../../../asset/img/orange-white.png"),
    require("../../../asset/img/red-white.png"),
    require("../../../asset/img/teal-white.png"),
];

const NearAddresses = ({ onClick, value }) => {
    const [nearAddresses, setNearAddresses] = React.useState([]);

    useEffect(() => {
        jsonp(`https://geocode.arcgis.com/arcgis/rest/services/World/GeocodeServer/findAddressCandidates?singleLine=&category=Residence,Shops%20and%20Service,Food,Education,POI,Professional%20and%20Other%20Places,Nightlife%20Spot&outFields=LongLabel,Distance&maxLocations=5&location=${value.Packetx.trim()},${value.Packety.trim()}&f=pjson`,
            null,
            (err, result) => {
                if (err) {
                    console.log(err.message);
                } else {
                    setNearAddresses(result.candidates);
                }
            }
        );
    }, []);

    let nearAddressLabel = [];

    nearAddresses.forEach((data) => {
        nearAddressLabel.push(data.attributes.LongLabel);
    });

    return (
        <div style={{ float: "right" }}>
            <Tooltip
                title={
                    <div>
                        1.{nearAddressLabel[0]}
                        <br />
                        <br />
                        2.{nearAddressLabel[1]}
                        <br />
                        <br />
                        3.{nearAddressLabel[2]}
                        <br />
                        <br />
                        4.{nearAddressLabel[3]}
                        <br />
                        <br />
                        5.{nearAddressLabel[4]}
                    </div>
                }
                placement="right"
            >
                <div style={{ width: "15px" }}>
                    <img
                        style={{ cursor: "pointer" }}
                        src={imageArray[3]}
                        alt={"logo"}
                        onClick={() => onClick(nearAddresses)}
                    />
                </div>
            </Tooltip>
        </div>
    );
};

export default CallListComponent;