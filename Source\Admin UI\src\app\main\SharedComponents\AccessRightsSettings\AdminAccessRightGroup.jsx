import React from 'react';
import {
    FormControl,
    FormLabel,
    FormGroup,
    FormControlLabel,
    Checkbox
} from '@mui/material';

const AdminAccessRightGroup = ({
    t,
    accessRights,
    handleChange,
    showAccessRight,
    isSuperAdmin,
    defaultApp
}) => {

    const accessOptions = [
        { key: 'Users', label: t('users') },
        { key: 'Contact', label: t('contact') },
        { key: 'Server', label: t('serverConfiguration') },
        { key: 'CallCategory', label: t('callCategory') },
        { key: 'CallViolation', label: t('violations') },
        { key: 'CallType', label: t('dispatchCallTypes') },
        { key: 'CallResponse', label: t('callResponse') },
        { key: 'DeviceLicense', label: t('deviceLicense') },
        { key: 'Units', label: t('unit') },
        { key: 'Nfirs', label: t('nfirs') },
        { key: 'Shift', label: t('shift') },
        { key: 'Team', label: t('team') },
        { key: 'ShiftAllocation', label: t('shiftAllocation') },
        { key: 'Department', label: t('department') },
        { key: 'UserAudit', label: t('userAudit') },
        { key: 'EmailConfiguration', label: t('emailConfiguration') },
        { key: 'TwitterConfiguration', label: t('twitterconfiguration') },
        { key: 'TwitterAccountSettings', label: t('twitterAccountSettings') },
        { key: 'ErrorLog', label: t('errorLog') },
        { key: 'ArchiveChatHistories', label: t('archivechatHistories') },
    ];

    return (
        <div className="col-span-1 w-full">
            <FormControl component="fieldset">
                <FormLabel component="legend">{t('relativityAdmin')}</FormLabel>
                <FormGroup>
                    {accessOptions.map(({ key, label }) => {
                        const canShow = showAccessRight[key] === key || isSuperAdmin;
                        const isDisabled = defaultApp !== 'admin' && !isSuperAdmin;

                        return canShow && (
                            <FormControlLabel
                                key={key}
                                control={
                                    <Checkbox
                                        checked={accessRights[key] || false}
                                        onChange={handleChange}
                                        name={key}
                                    />
                                }
                                label={label}
                                disabled={isDisabled}
                            />
                        );
                    })}
                </FormGroup>
            </FormControl>
        </div>
    );
};

export default AdminAccessRightGroup;
