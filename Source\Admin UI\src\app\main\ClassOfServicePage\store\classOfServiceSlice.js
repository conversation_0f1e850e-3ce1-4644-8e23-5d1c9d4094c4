import { createSlice } from '@reduxjs/toolkit';
import history from '@history';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from "axios";
import { encrypt, decrypt } from '../../../security/crypto';

const initialState = {
    success: false,
    error: {
        contact: {}
    },
    data: [],
    classOfServiceData: [],
    searchText: '',
    classOfServiceID: '0',
    classOfServiceColor: '',
    delSuccess: false,
};

const classOfServiceSlice = createSlice({
    name: 'classOfService',
    initialState,
    reducers: {
        setCLASSOFSERVICELIST: (state, action) => {
            state.delSuccess = false;
            state.data = action.payload;
        },
        setSEARCHTEXT: (state, action) => {
            state.searchText = action.payload;
        },
        setCLASSOFSERVICEID: (state, action) => {
            state.classOfServiceID = action.payload;
            state.classOfServiceData = [];
        },
        setCLASSOFSERVICEBYID: (state, action) => {
            state.classOfServiceData = action.payload;
            state.classOfServiceColor = action.payload.color;
        },
        setREMOVECLASSOFSERVICEID: (state, action) => {
            state.delSuccess = true;
        }
    },
    extraReducers: {}
});

export default classOfServiceSlice.reducer;

// Actions
export const {
    setCLASSOFSERVICELIST,
    setSEARCHTEXT,
    setCLASSOFSERVICEID,
    setCLASSOFSERVICEBYID,
    setREMOVECLASSOFSERVICEID
} = classOfServiceSlice.actions;

// To get Class Of Service List
export const getClassOfServiceList = () => async dispatch => {
    try {

        await axios.get(`911/api/classOfService/classOfServiceList`)
            .then(response => {
                return dispatch(setCLASSOFSERVICELIST(JSON.parse(decrypt(response.data))));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

//To set Users Search Text
export const setUsersSearchText = (event) => async dispatch => {
    try {
        return dispatch(setSEARCHTEXT(event.target.value));
    } catch (e) {
        return console.error(e.message);
    }
}

//To set Class Of Service ID
export const setClassOfServiceID = (classOfServiceID) => async dispatch => {
    try {
        return dispatch(setCLASSOFSERVICEID(classOfServiceID));
    } catch (e) {
        return console.error(e.message);
    }
}

// To add Class Of Service
export const newClassOfService = (data) => async dispatch => {
    try {
        await axios.post(`911/api/classOfService/classOfServiceAdd`, encrypt(JSON.stringify(data)))
            .then(async response => {
                var res = JSON.parse(decrypt(response.data));
                if (res.result.code === "Error") {
                    dispatch(showMessage({
                        message: res.result.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }));
                }
                else {
                    if (data.file !== null) {
                        var formData = new FormData();
                        formData.append("file", data.file);
                        await axios.post('fileupload/upload911CallIcon/' + data.code, formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                                //Authorization: `Bearer ${localStorage.getItem('jwt_access_token')}`
                            }
                        })
                            .then(async response => {
                                if (response.data) {
                                    var resObj = {
                                        _id: res.result.message._id,
                                        iconPic: response.data.Location
                                    }
                                    await axios.post(`911/api/classOfService/classOfServiceEdit`, encrypt(JSON.stringify(resObj)))
                                        .then(response => {
                                            if (response.status === 400) {
                                                dispatch(showMessage({
                                                    message: 'Error Occured!', autoHideDuration: 2000,
                                                    anchorOrigin: {
                                                        vertical: 'top',
                                                        horizontal: 'right'
                                                    },
                                                    variant: 'error'
                                                }));
                                            }
                                            else {
                                                dispatch(showMessage({
                                                    message: 'Class Of Service Code added sucessfully..', autoHideDuration: 2000,
                                                    anchorOrigin: {
                                                        vertical: 'top',
                                                        horizontal: 'right'
                                                    },
                                                    variant: 'success'
                                                }));
                                                history.push("/911/classOfServiceList");
                                            }
                                        }).catch(error => {
                                            return dispatch(
                                                showMessage({
                                                    message: error.message,//text or html
                                                    autoHideDuration: 2000,//ms
                                                    anchorOrigin: {
                                                        vertical: 'top',
                                                        horizontal: 'right'
                                                    },
                                                    variant: 'warning'
                                                }),
                                            );
                                        });
                                }
                            })
                    }
                    else {
                        history.push("/911/classOfServiceList");
                        dispatch(showMessage({
                            message: 'Class Of Service Code added sucessfully..', autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }));
                    }
                }
            }).catch(error => {
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
}

// To update Class Of Service
export const updateClassOfService = (data) => async dispatch => {

    try {
        if (data.file !== null) {
            var formData = new FormData();
            formData.append("file", data.file);
            await axios.post('fileupload/upload911CallIcon/' + data.code, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                    //Authorization: `Bearer ${localStorage.getItem('jwt_access_token')}`
                }
            })
                .then(async response => {
                    if (response.data) {
                        var resObj = {
                            _id: data._id,
                            description: data.description,
                            color: data.color,
                            iconPic: response.data.Location
                        }
                        await axios.post(`911/api/classOfService/classOfServiceEdit`, encrypt(JSON.stringify(resObj)))
                            .then(response => {
                                if (response.status === 400) {
                                    dispatch(showMessage({
                                        message: 'Error Occured!', autoHideDuration: 2000,
                                        anchorOrigin: {
                                            vertical: 'top',
                                            horizontal: 'right'
                                        },
                                        variant: 'error'
                                    }));
                                }
                                else {
                                    dispatch(showMessage({
                                        message: 'Class Of Service Code updated sucessfully..', autoHideDuration: 2000,
                                        anchorOrigin: {
                                            vertical: 'top',
                                            horizontal: 'right'
                                        },
                                        variant: 'success'
                                    }));
                                    history.push("/911/classOfServiceList");
                                }
                            }).catch(error => {
                                return dispatch(
                                    showMessage({
                                        message: error.message,//text or html
                                        autoHideDuration: 2000,//ms
                                        anchorOrigin: {
                                            vertical: 'top',
                                            horizontal: 'right'
                                        },
                                        variant: 'warning'
                                    }),
                                );
                            });
                    }
                })
        }
        else {
            await axios.post(`911/api/classOfService/classOfServiceEdit`, encrypt(JSON.stringify(data))).then(response => {
                if (response.status === 400) {
                    dispatch(showMessage({
                        message: 'Error Occured!', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    dispatch(showMessage({
                        message: 'Class Of Service Code updated successfully..!', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));

                    history.push("/911/classOfServiceList");
                }
            }).catch(error => {
                return dispatch(
                    showMessage({
                        message: error.message,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
        }
    } catch (e) {
    }
}


//Remove Class Of Service
export const removeClassOfService = (_id) => async dispatch => {
    try {
        await axios.delete(`911/api/classOfService/classOfServiceDelete/` + _id)
            .then(response => {
                dispatch(showMessage({
                    message: 'Class Of Service Code Removed sucessfully..', autoHideDuration: 1500,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'success'
                }))
                return dispatch(setREMOVECLASSOFSERVICEID(_id));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}


//Get Class Of Service By ID
export const getClassOfServiceByID = (_id) => async dispatch => {
    try {
        await axios.get(`911/api/classOfService/classOfServiceGetByID/` + _id)
            .then(response => {
                return dispatch(setCLASSOFSERVICEBYID(JSON.parse(decrypt(response.data))));
            });
    } catch (e) {
        return console.error(e.message);
    }
}
