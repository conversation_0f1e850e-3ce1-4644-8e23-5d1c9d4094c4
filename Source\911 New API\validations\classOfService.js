const Joi = require('@hapi/joi')
const errorProcess=require('../utils/processErrors.js')

const querySchema = Joi.object({  
        code:Joi.string().required() 
},)


exports.ClassOfServiceValidate = (req, res, next) => {
  const joiError = querySchema.validate(req.body, { abortEarly: false });
  if (joiError.error) {
    return res.status(400).send(errorProcess.translateMessages(joiError.error.details, req))
  }
  else {
    return true;
  }
}


