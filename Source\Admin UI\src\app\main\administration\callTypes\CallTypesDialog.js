import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import Button from '@mui/material/Button';
import { TextField, FormControl, InputLabel, Select, MenuItem, Autocomplete, } from "@mui/material";
import { saveCallType } from '../store/callTypeSlice';
import { showMessage } from 'app/store/fuse/messageSlice';
import CommonAutocomplete, { handleSelectKeyDown } from "src/app/main/SharedComponents/ReuseComponents/CommonAutocomplete";

let update = false;

const defaultValues = {
    CallTypeName: '',
    CallAbbrivation: ''
};

const CallTypeDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [code, setCode] = React.useState("");

    const CallTypeData = useSelector(({ administration }) => administration.callTypes.data);
    const CallCategories = useSelector(({ administration }) => administration.callCategorySlice.callCategories);
    const CallResponse = useSelector(({ administration }) => administration.callResponse.callResponses);

    const [isUpdate, setIsUpdate] = React.useState(false);
    const [PagingDetails, setPagingDetails] = React.useState();
    const [newCategoryID, setNewCategoryID] = React.useState("");
    const [category, setCategory] = React.useState("");
    const [newResponseCode, setNewResponseCode] = React.useState("");
    const [newPriorityID, setNewPriorityID] = React.useState("");
    const [priority, setPriority] = React.useState("");
    const [id, setId] = React.useState("");
    const priorities = [
        {
            PriorityID: 1,
            Priority: "High",
        },
        {
            PriorityID: 2,
            Priority: "Mid",
        },
        {
            PriorityID: 3,
            Priority: "Low",
        },
    ];

    priorities.sort((a, b) => a.Priority.localeCompare(b.Priority))

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues
    });

    const { isValid, dirtyFields, errors } = formState;
    const callTypeIDRef = useRef(null);
    const callCategoryRef = useRef(null);
    const [dropdownOpen, setDropdownOpen] = React.useState(false);

    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            if (update) {
                // Focus on CallCategory in Edit mode
                callCategoryRef.current?.focus();
                setDropdownOpen(true);
            } else {
                // Focus on CallTypeID in Add mode
                callTypeIDRef.current?.focus();
            }
        }, 0);
    };

    const handleCategory = (event, newValue) => {
        if (newValue) {
            setNewCategoryID(newValue.CallCategoryID);
            setCategory(newValue.CallCategoryName);
        } else {
            setNewCategoryID(null);
            setCategory("");
        }
    };


    const handlePriority = (ev) => {
        setNewPriorityID(ev.target.value);
        var pri = priorities.filter((v) => v.PriorityID === ev.target.value);
        setPriority(pri[0].Priority);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, PagingDetails, flag) {
            setCode(code)
            setPagingDetails(PagingDetails)
            update = flag;
            setCallResponseValue(data, code)
            handleClickOpen1();
        },
    }));

    const setCallResponseValue = async (data, code) => {
        if (data._id !== null && data._id !== undefined) {
            setId(data._id);
            setValue('CallTypeID', data.CallTypeID);
            setValue('CallTypeName', data.CallTypeName);
            setValue('CallAbbrivation', data.CallTypeAbbr);
            setNewCategoryID(data.CallCategoryID);
            setNewResponseCode(data.ResponseCode);
            setNewPriorityID(data.PriorityID);
            setCategory(data.Category);
            setPriority(data.Priority);
        }
    };

    const clear = () => {
        setId("");
        setValue('CallTypeID', "");
        setValue('CallTypeName', "");
        setValue('CallAbbrivation', "");
        setNewCategoryID("");
        setNewResponseCode("");
        setNewPriorityID("");
        setCategory("");
        setPriority("");
    };

    const handleClose = () => {
        setOpen(false);
        clear();
    };

    function handleCallTypeID(e) {
        let CallTypeID = e.target.value;
        let filteredData = CallTypeData.filter((x) => x.CallTypeID === parseInt(CallTypeID));
        setValue('CallTypeID', CallTypeID);
        if (filteredData.length > 0) {
            ShowErroMessage(t(`The ID ${CallTypeID} is already associated with another call type. Please enter a different call ID and proceed.`));
        }
    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    };

    const handleCallResponseChange = (event, newValue) => setNewResponseCode(newValue ? newValue.ResponseCode : "")

    const handlePriorityChange = (event, newValue) => {
        if (newValue) {
            setNewPriorityID(newValue.PriorityID);
            setPriority(newValue.Priority);
        }
    }

    function onSubmit(model) {
        if (newCategoryID !== "" && newCategoryID !== null) {
            const data = {
                id: id,
                CallTypeID: model.CallTypeID === "" ? 0 : parseInt(model.CallTypeID),
                CallCategoryID: newCategoryID,
                Category: category,
                Name: model.CallTypeName,
                CallAbbr: model.CallAbbrivation,
                ResponseCode: newResponseCode,
                PriorityID: newPriorityID,
                Priority: priority,
                isUpdate: update,
                code: code,
            }
            dispatch(
                saveCallType(
                    data,
                    PagingDetails.pageIndex,
                    PagingDetails.rowsPerPage,
                    PagingDetails.id,
                    PagingDetails.direction,
                    PagingDetails.searchText,
                )
            );
            setIsUpdate(false);
            handleClose();
        }
        else {
            ShowErroMessage(t("CategoryWarning"));
        }
    };

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("callType")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        name="registerForm"
                        noValidate
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        autoSave={false}
                    >
                        <Controller
                            name="CallTypeID"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("callTypeID")}
                                    type="number"
                                    onChange={handleCallTypeID}
                                    error={!!errors.CallTypeID}
                                    helperText={errors?.CallTypeID?.message}
                                    disabled={update ? true : false}
                                    variant="outlined"
                                    inputRef={callTypeIDRef}
                                />
                            )}
                        />
                        <FormControl className="mb-16 w-full">
                            <CommonAutocomplete
                                value={CallCategories.find((cat) => cat.CallCategoryID === newCategoryID) || null}
                                parentCallback={handleCategory}
                                options={CallCategories || []}
                                fieldName={t("callCategory")}
                                optionLabel={"CallCategoryName"}
                                onKeyDown={handleSelectKeyDown}
                            />
                        </FormControl>

                        <FormControl className="mb-16 w-full">
                            <CommonAutocomplete
                                value={CallResponse.find((resp) => resp.ResponseCode === newResponseCode) || null}
                                parentCallback={handleCallResponseChange}
                                options={CallResponse || []}
                                fieldName={t("callCategory")}
                                optionLabel={"ResponseCode"}
                                onKeyDown={handleSelectKeyDown}
                            />
                        </FormControl>

                        <FormControl className="mb-16 w-full">
                            <CommonAutocomplete
                                value={priorities.find((resp) => resp.PriorityID === newPriorityID) || null}
                                parentCallback={handlePriorityChange}
                                options={priorities || []}
                                fieldName={t("priority")}
                                optionLabel={"Priority"}
                                onKeyDown={handleSelectKeyDown}
                            />
                        </FormControl>

                        <Controller
                            name="CallTypeName"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("callTypeName")}
                                    type="text"
                                    error={!!errors.CallTypeName}
                                    helperText={errors?.CallTypeName?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="CallAbbrivation"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("abbrevaition")}
                                    type="text"
                                    error={!!errors.CallAbbrivation}
                                    helperText={errors?.CallAbbrivation?.message}
                                    variant="outlined"
                                />
                            )}
                        />
                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );

});

export default CallTypeDialog;