import Button from '@mui/material/Button';
import React, { useState, useRef, useEffect } from 'react';
import InputAdornment from '@mui/material/InputAdornment';
import { useSelector, useDispatch } from 'react-redux';
import Icon from '@mui/material/Icon';
import { submitUserProfileUpdate, ClearUserData, } from '../../../auth/store/registerSlice';
import { verifyUserSession } from '../../../auth/store/loginSlice';
import { useTranslation } from 'react-i18next';
import { newUserAudit } from '../../../main/userAuditPage/store/userAuditSlice';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

const schema = yup.object().shape({
    email: yup.string()
        .required('Please enter email.')
        .email('Enter valid email id'),
    phone: yup
        .string()
        .required('Please enter phone number.')
});
const defaultValues = {
    email: '',
    phone: '',
};

function PersonalInformation(props) {
    const fileUpload = useRef(null);
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();
    const [isFormValid, setIsFormValid] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [loading, setLoading] = React.useState(false);
    const [success, setSuccess] = React.useState(false);
    const timer = React.useRef();
    const user = useSelector(({ auth }) => auth.user);
    const register = useSelector(({ auth }) => auth.register);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });
    const { isValid, dirtyFields, errors } = formState;

    useEffect(() => {
        dispatch(newUserAudit({
            activity: "Access User Profile",
            user: user,
            appName: "Admin",
        }));
    }, []);

    function onSubmit(model) {
        const data = {
            _id: user.data.id,
            phone: model.phone,
            file: fileUpload.current.files[0] ? fileUpload.current.files[0] : null,
            profilePic: ""
        };
        dispatch(submitUserProfileUpdate(data));
    }

    useEffect(() => {
        if (register.profileUpdate || success) {
            dispatch(verifyUserSession());
            dispatch(ClearUserData());
        }
        // eslint-disable-next-line
    }, [register.profileUpdate, success])

    useEffect(() => {
        setValue('email', user.data.email);
        setValue('phone', user.data.phone);
        // eslint-disable-next-line
    }, [user])

    return <>
        <form key={0} className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>
            <Controller
                name="email"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        className="mb-16"
                        label={t('email')}
                        type="text"
                        error={!!errors.email}
                        helperText={errors?.email?.message}
                        variant="outlined"
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <Icon className="text-20" color="action">
                                        email
                                    </Icon>
                                </InputAdornment>
                            ),
                        }}
                        required
                    />
                )}
            />
            <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        className="mb-16"
                        label={t('phone')}
                        type="text"
                        error={!!errors.phone}
                        helperText={errors?.phone?.message}
                        variant="outlined"
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <Icon className="text-20" color="action">
                                        phone
                                    </Icon>
                                </InputAdornment>
                            ),
                        }}
                        required
                    />
                )}
            />


            {t('profilePicture')}
            <input
                type="file"
                className="mb-16"
                ref={fileUpload}
                accept="image/*"
                style={{
                    padding: '13px',
                    border: '1px solid lightgray',
                    borderRadius: '4px',
                    // backgroundColor: 'antiquewhite',
                    cursor: 'pointer'
                }}
            />
            <div className="mx-auto">
                <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    className="normal-case m-16"
                    aria-label="UPDATE"
                    // disabled={_.isEmpty(dirtyFields) || !isValid}
                    value="legacy">
                    {t('update')}
                </Button>
            </div>
        </form>
    </>
}


export default PersonalInformation;