import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { clearPersonDetails, setPersonAlerts } from '../store/personSlice';
import { makeStyles } from '@mui/styles';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Box, Typography, IconButton, Backdrop } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import BannedPersonAlert from './PersonAlerts/BannedPersonsAlert';
import WarrantsAlert from './PersonAlerts/WarrantsAlert';
import CivilPapersAlert from './PersonAlerts/CivilPapersAlert';
import InmatesAlert from './PersonAlerts/InmatesAlert';
import ProtectionOrdersAlert from './PersonAlerts/ProtectionOrdersAlert';
import TaggedPersonsAlert from './PersonAlerts/TaggedPersonsAlert';
import FuseLoading from '@fuse/core/FuseLoading';

const useStyles = makeStyles((theme) => ({
    backdrop: { zIndex: theme.zIndex.drawer + 1, color: '#fff' },
}));

const PersonAlertsNew = forwardRef((props, ref) => {
    const classes = useStyles(props);
    const { t } = useTranslation('languageConfig');
    const dispatch = useDispatch();
    const [open, setOpen] = React.useState(false);
    const [loading, setLoading] = React.useState(false);

    const isloading = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.person.isloading);
    const personLoader = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.dispatchCall.personLoader);
    const personAlerts = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.person.personAlerts);

    useEffect(() => {
        if (personLoader) {
            setLoading(personLoader);
        } else {
            setLoading(isloading);
        }
    }, [personLoader, isloading]);

    useImperativeHandle(ref, () => ({
        openPersonAlerts() {
            setOpen(true);
        }
    }));

    const handleClose = () => {
        setOpen(false);
        dispatch(setPersonAlerts(null));
        if (props.isLocalSearch) {
            dispatch(clearPersonDetails());
        }
    };

    const warrants = personAlerts?.warrants || [];
    const civilPapers = personAlerts?.civilPapers || [];
    const inmates = personAlerts?.inmates || [];
    const protectionOrders = personAlerts?.protectionOrders || [];
    const bannedPersons = personAlerts?.bannedPersons || [];
    const taggedPersons = personAlerts?.taggedPersons || [];


    return (
        <Dialog open={open} onClose={handleClose} maxWidth="xl" fullWidth>
            <DialogTitle>
                <Box className="flex justify-between w-full ">
                    <Typography variant="h5" color="textPrimary" className="font-bold">
                        {t("personAlert")}
                    </Typography>
                    <IconButton
                        aria-label="close"
                        onClick={handleClose}
                        sx={{ position: 'absolute', right: 8, top: 8 }}
                    >
                        <CloseIcon />
                    </IconButton>
                </Box>
            </DialogTitle>

            <DialogContent className="max-h-[80vh] overflow-y-auto space-y-6">
                <>
                    <Backdrop className={classes.backdrop} open={loading}>
                        <FuseLoading variant="determinate" />
                    </Backdrop>

                    {warrants.length > 0 && (
                        <>
                            <WarrantsAlert warrants={warrants} />
                            <hr />
                        </>
                    )}
                    {bannedPersons.length > 0 && (
                        <>
                            <BannedPersonAlert bannedPersons={bannedPersons} />
                            <hr />
                        </>
                    )}
                    {civilPapers.length > 0 && (
                        <>
                            <CivilPapersAlert civilPapers={civilPapers} />
                            <hr />
                        </>
                    )}
                    {inmates.length > 0 && (
                        <>
                            <InmatesAlert inmates={inmates} />
                            <hr />
                        </>
                    )}
                    {protectionOrders.length > 0 && (
                        <>
                            <ProtectionOrdersAlert protectionOrders={protectionOrders} />
                            <hr />
                        </>
                    )}
                    {taggedPersons.length > 0 && (
                        <>
                            <TaggedPersonsAlert taggedPersons={taggedPersons} />
                        </>
                    )}
                </>
            </DialogContent>
        </Dialog>
    );
});

export default PersonAlertsNew;
