import React from "react";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    Icon,
} from "@mui/material";

const CommonDialog = ({
    open,
    onClose,
    title,
    children,
    actions,
    formProps = {},
    dialogProps = {},
    onKeyPress,
    maxWidth = "md",
    width = "500px"
}) => {
    return (
        <Dialog
            fullWidth
            maxWidth={maxWidth}
            open={open}
            onClose={onClose}
            aria-labelledby="common-dialog-title"
            onKeyPress={onKeyPress}
            {...dialogProps}
        >
            <DialogTitle style={{ display: "flex", justifyContent: "space-between", width: width }} id="common-dialog-title">
                <div>{title}</div>
                <div>
                    <IconButton onClick={onClose} aria-label="close">
                        <Icon>close</Icon>
                    </IconButton>
                </div>
            </DialogTitle>
            <form {...formProps}>
                <DialogContent dividers style={{ height: "400px", overflow: "auto" }}>
                    {children}
                </DialogContent>
                {actions && (
                    <DialogActions
                        style={{
                            position: "sticky",
                            bottom: 0,
                            zIndex: 1,
                            padding: "8px 24px",
                            borderTop: "1px solid rgba(0, 0, 0, 0.12)",
                        }}
                    >
                        {actions}
                    </DialogActions>
                )}
            </form>
        </Dialog>
    );
};


export default CommonDialog;
