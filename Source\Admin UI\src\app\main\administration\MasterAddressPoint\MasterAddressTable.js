import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useEffect, useRef, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import {
    Button, Icon, IconButton, Input, Paper, Tooltip, Stack, StyledEngineProvider, TablePagination,
    ThemeProvider, Typography,
    InputAdornment
} from '@mui/material';
import CommonButton from '../../SharedComponents/ReuseComponents/CommonButton';
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useSelector, useDispatch } from 'react-redux';
import history from '@history';
import ConfirmationDialog from '../../components/ConfirmationDialog/ConfirmationDialog';
import { useDebounce } from '@fuse/hooks';
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from '../../utils/utils';
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import CancelIcon from '@mui/icons-material/Cancel';
import {
    deleteMasterAddress,
    getMasterAddress,
    setMasterAddressByID,
    setSelectedCounty,
    setSelectedCountyStateCode,
} from '../store/masterAddressSlice';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import DnsIcon from '@mui/icons-material/Dns';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import { getAgencyByCode } from '../../agencyPage/store/agencySlice';
import { getMasterAddressDetailsLogs } from '../../store/importFromExcelSlice';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
import moment from "moment"
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function MasterAddressTable() {
    const { t } = useTranslation("laguageConfig");
    const mainTheme = useSelector(selectMainTheme);
    const dispatch = useDispatch();

    const gridRef = useRef(null);

    const [open, setOpen] = useState(false);
    const [data, setData] = useState([]);
    const [removeData, setRemoveData] = useState(null);
    const [totalCount, setTotalCount] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [page, setPage] = useState(0);
    const [searchText, setSearchText] = React.useState("");
    const [county, setCounty] = React.useState(null);
    const [countyState, setCountyState] = React.useState(null);
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "AddNum_Pre",
    });

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const user = useSelector(({ auth }) => auth.user);
    const MasterAddress = useSelector(({ administration }) => administration.masterAddressSlice.MasterAddress)
    const TotalCount = useSelector(({ administration }) => administration.masterAddressSlice.totalCount)
    const isloading = useSelector(({ administration }) => administration.masterAddressSlice.isloading)
    const selectedCounty = useSelector(({ administration }) => administration.masterAddressSlice.selectedCounty)
    const selectedCountyStateCode = useSelector(({ administration }) => administration.masterAddressSlice.selectedCountyStateCode)
    const masterAddressSuccess = useSelector(({ administration }) => administration.masterAddressSlice.masterAddressSuccess)
    const agencyByCode = useSelector(({ agency }) => agency.agency.agencyByCode);
    let colorCode = getNavbarTheme();

    const [loading, setLoading] = useState(false);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    useEffect(() => {
        setLoading(isloading);
    }, [isloading]);

    useEffect(() => {
        if (selectedCounty !== null && selectedCountyStateCode !== null) {
            setCounty(selectedCounty);
            setCountyState(selectedCountyStateCode);
            dispatch(getMasterAddressDetailsLogs(selectedCounty, selectedCountyStateCode));
        }
    }, [selectedCounty, selectedCountyStateCode]);

    const getCounty = async () => {
        await dispatch(getAgencyByCode(user.data.defaultAgency));
    }

    useEffect(() => {
        if (user.data.agencyAdmin) {
            getCounty();
        }
    }, [user]);

    useEffect(() => {
        if (agencyByCode !== undefined && agencyByCode !== null &&
            agencyByCode.county !== undefined && agencyByCode.county !== null &&
            agencyByCode.county !== "") {
            setCounty(agencyByCode.county);
        }
    }, [agencyByCode]);

    const addMasterAddress = () => {
        dispatch(setMasterAddressByID(null));
        history.push(`/admin/addMasterAddress`);
    };

    const navigateToCounty = () => {
        history.push(`/admin/counties`);
        dispatch(setSelectedCounty(null));
        dispatch(setSelectedCountyStateCode(null));
    };

    function handleChangePage(event, value) {
        setPage(value);
        setLoading(true);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
        setLoading(true);
    }

    const handleClose = async (newValue) => {
        if (newValue) {
            await dispatch(deleteMasterAddress(removeData));
            setRemoveData(null);
        }
        setOpen(false);
    };

    const deleteRecord = (n) => {
        let data = {
            id: n._id,
            county: n.County,
            stateCode: n.State,
        }
        setRemoveData(data);
        setOpen(true);
    }

    const editRow = (n) => {
        dispatch(setMasterAddressByID(n));
        history.push("/admin/addMasterAddress");
    };

    const ActionIcons = (n) => {
        // let x = checkData(n)
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;
            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => editRow(x)}
                                    size="large"
                                >
                                    <EditIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("delete")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => deleteRecord(x)}
                                    size="large"
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                    }
                </>
            );
        }

    };

    const formatDate = (value) => {
        if (!value) return "";

        let dateValue = value;

        // Convert date to number
        if (typeof value === "string" && /^\d+(\.\d+)?e[\+\-]?\d+$/i.test(value)) {
            dateValue = Number(value);
        }

        // Handle numeric values (Excel serials or timestamps - 1000000000000)
        if (!isNaN(dateValue)) {
            const numericDate = moment(Number(dateValue));
            if (numericDate.isValid()) {
                return numericDate.format("MM/DD/YYYY");
            } else {
                return ""
            }
        }

        // Fallback to parsing known date formats
        const parsedDate = moment(value, [
            moment.ISO_8601,
            "M/D/YYYY",
            "M/DD/YYYY",
            "MM/DD/YYYY",
            "D/M/YYYY",
            "DD/MM/YYYY",
            "YYYY/MM/DD",
            "YYYY-MM-DD",
            "DD-MM-YYYY",
            "MM-DD-YYYY",
            "MMM D, YYYY",
            "MMM DD, YYYY"
        ], true);

        return parsedDate.isValid() ? parsedDate.format("MM/DD/YYYY") : "";
    };


    useEffect(() => {
        if (MasterAddress !== null && MasterAddress !== undefined) {
            setData(MasterAddress);
            setTotalCount(TotalCount)
        }
    }, [MasterAddress, TotalCount])

    const search = useDebounce((search, page, rowsPerPage, county, order, countyState) => {
        dispatch(getMasterAddress(order.id, order.direction, page * rowsPerPage, rowsPerPage,
            search === '' ? null : search, county, countyState));
    }, 500);

    useEffect(() => {
        if (county !== null && countyState !== null) {
            if (searchText !== '') {
                search(searchText, page, rowsPerPage, county, order, countyState);
            } else {
                dispatch(getMasterAddress(order.id, order.direction, page * rowsPerPage, rowsPerPage,
                    searchText === '' ? null : searchText, county, countyState));
            }
        }
    }, [dispatch, searchText, page, rowsPerPage, order, county, removeData, countyState, masterAddressSuccess]);

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;
        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 0 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            // Call the sort handler with the field name and direction
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const viewMasterLogs = () => {
        history.push('/admin/masterImportLogs');
    }

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {user.data.isSuperAdmin && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => navigateToCounty()}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={user.data.isSuperAdmin ? {} : { paddingTop: "29px" }}
                        >
                            <div className="flex items-center">
                                <DnsIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {county}, {countyState} - {t("masterAddressPointDetail")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                // defaultValue={searchText}
                                                value={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText("")}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div className="flex items-center justify-between">
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addMasterAddress" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("viewImportLogs")} parentCallback={viewMasterLogs}></CommonButton>
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addMasterAddress")} parentCallback={addMasterAddress}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging "
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>


                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="OBJECTID"
                                        header={t("objectId")}
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("discrepancyAgencyID")}
                                        field="DiscrpAgID"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("dateUpdated")}
                                        field="DateUpdate"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        formatter={(value) => formatDate(value)}
                                    />
                                    <IgrColumn
                                        header={t("effectiveDate")}
                                        field="Effective"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        formatter={(value) => formatDate(value)}
                                    />
                                    <IgrColumn
                                        header={t("expirationDate")}
                                        field="Expire"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        formatter={(value) => formatDate(value)}
                                    />
                                    <IgrColumn
                                        header={t("nguid")}
                                        field="NGUID"
                                        width="120px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("country")}
                                        field="Country"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("state")}
                                        field="State"
                                        width="110px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("county")}
                                        field="County"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("addCode")}
                                        field="AddCode"
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("addressDataURL")}
                                        field="AddDataURI"
                                        width="200px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("incorporatedMuni")}
                                        field="Inc_Muni"
                                        width="200px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("unincorporatedMuni")}
                                        field="Uninc_Comm"
                                        width="230px"
                                        rresizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("neighborhoodCommunity")}
                                        field="Nbrhd_Comm"
                                        width="260px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("cityPostalComm")}
                                        field="Post_Comm"
                                        width="210px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("zipPostCode")}
                                        field="Post_Code"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("zip")}
                                        field="PostCodeEx"
                                        width="120px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("municipalityType")}
                                        field="MunicipalityType"
                                        width="210px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("preMod")}
                                        field="St_PreMod"
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("direction")}
                                        field="St_PreDir"
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("preType")}
                                        field="St_PreTyp"
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("preSep")}
                                        field="St_PreSep"
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("streetName")}
                                        field="St_Name"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("streetType")}
                                        field="St_PosTyp"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("postDir")}
                                        field="St_PosDir"
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("postMod")}
                                        field="St_PosMod"
                                        width="150px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("streetDirOfTravel")}
                                        field="St_DirOfTravel"
                                        width="230px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("streetNameFull")}
                                        field="StNam_Full"
                                        width="200px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("addressNoPrefix")}
                                        field="AddNum_Pre"
                                        width="220px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("addressNumber")}
                                        field="Add_Number"
                                        width="210px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("numberSuffix")}
                                        field="AddNum_Suf"
                                        width="190px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("addressNumberComp")}
                                        field="Adr_Num_Comp"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("milePost")}
                                        field="MilePost"
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("site")}
                                        field="Site"
                                        width="110px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("subStie")}
                                        field="SubSite"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("addressBuilding")}
                                        field="AddressBuilding"
                                        width="200px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("wing")}
                                        field="Wing"
                                        width="120px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("floor")}
                                        field="Floor"
                                        width="120px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("unitPreType")}
                                        field="UnitPreType"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("unitValue")}
                                        field="UnitValue"
                                        width="150px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("unit")}
                                        field="Unit"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("room")}
                                        field="Room"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("row")}
                                        field="Row"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("seat")}
                                        field="Seat"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("subAddress")}
                                        field="SubAddress"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("additionalLocationInfo")}
                                        field="Addtl_Loc"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("nerisAdditionalAttributes")}
                                        field="AdditionalAttributes"
                                        width="280px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("streetNumberOrMileMarker")}
                                        field="Marker"
                                        width="300px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("placeType")}
                                        field="PlaceType"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("preDir")}
                                        field="lst_predir"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("streetName")}
                                        field="lst_name"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("legacyStreetType")}
                                        field="lst_type"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("postDir")}
                                        field="lst_posdir"
                                        width="150px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("legacyAddress")}
                                        field="LgcyAdd"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("localName")}
                                        field="LocalName"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("longitude")}
                                        field="Longitude"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("latitude")}
                                        field="Latitude"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("elevation")}
                                        field="Elevation"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("mapPage")}
                                        field="MapPage"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("policeZone")}
                                        field="PoliceZone"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("fireZone")}
                                        field="FireZone"
                                        width="180px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("fireMutualAssist")}
                                        field="FireMutualAssist"
                                        width="220px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("fireAutoAssist")}
                                        field="FireAutoAssist"
                                        width="210px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("emsZone")}
                                        field="EMSZone"
                                        width="160px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("emsAutoAssist")}
                                        field="EMSAutoAssist"
                                        width="210px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("emsMutualAssist")}
                                        field="EMSMutualAssist"
                                        width="230px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("psap")}
                                        field="PSAP"
                                        width="130px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("nationalGrid")}
                                        field="NationalGrid"
                                        width="200px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("nearestCrossStreet")}
                                        field="NearestXSt"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("nxtNearestCross")}
                                        field="SecondNearestXSt"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("nearestXStDist")}
                                        field="NearestXStDist"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("secondNearestXStDist")}
                                        field="SecondNearestXStDist"
                                        width="270px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("structure")}
                                        field="Structure"
                                        width="150px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("esn")}
                                        field="ESN"
                                        width="140px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("msagCommunity")}
                                        field="MSAGComm"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("addressLabel")}
                                        field="adr_label"
                                        width="200px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("placement")}
                                        field="Placement"
                                        width="170px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("wreckerServiceZone")}
                                        field="wreckerServiceZone"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>

                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={t("deleteRecord")}
                                onClose={handleClose}
                                value={removeData}
                            >
                            </ConfirmationDialog>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    );
}

export default MasterAddressTable;