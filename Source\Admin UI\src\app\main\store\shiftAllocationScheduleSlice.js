import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const saveShiftAllocationSchedule = (data, code) => async (dispatch) => {
    try {
        let datas = {
            data,
            code
        }
        dispatch(setLoading(true));
        await axios.post(`admin/api/shiftAllocationScheduleRoute/saveshiftAllocationSchedule`, encrypt(JSON.stringify(datas)))
            .then(async (res) => {
                if (res.status == 200) {
                    res.data = await JSON.parse(decrypt(res.data));
                    dispatch(
                        showMessage({
                            message: res.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "right",
                            },
                            variant: "success",
                        })
                    );
                    dispatch(setLoading(false));
                }
                else {
                    res = JSON.parse(decrypt(res.response.data));
                    dispatch(showMessage({
                        message: res.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const saveUserShiftAllocationSchedule = (data, code) => async (dispatch) => {
    try {
        let datas = {
            data,
            code
        }
        dispatch(setLoading(true));
        data.map(x => dispatch(createShiftAllocationData(x)))
        await axios.post(`admin/api/shiftAllocationScheduleRoute/saveUserShiftAllocationSchedule`, encrypt(JSON.stringify(datas)))
            .then(async (res) => {
                if (res.status == 200) {
                    res.data = await JSON.parse(decrypt(res.data));
                    dispatch(
                        showMessage({
                            message: res.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "right",
                            },
                            variant: "success",
                        })
                    );
                    dispatch(setLoading(false));
                }
                else {
                    res = JSON.parse(decrypt(res.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: res.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const DeleteShiftAllocationSchedule = (code, id) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/shiftAllocationScheduleRoute/DeleteShiftAllocationSchedule/${id}/${code}`)
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                    dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    setLoading(false);
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const removeShiftAllocationScheduleUser = (id, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/shiftAllocationScheduleRoute/removeShiftAllocationScheduleUser/${id}/${code}`)
            .then(async response => {
                if (response.status == 200) {
                    dispatch(removeShiftAllocationData(id));
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getShiftAllocationSchedule = (code, id) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/shiftAllocationScheduleRoute/getShiftAllocationScheduleDetails/${id}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(
                        showMessage({
                            message: listData.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        })
                    );
                    return dispatch(setShiftAllocationData(listData.data));
                } else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                    return dispatch(setShiftAllocationData([]));
                }
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const updateShiftAllocationSchedule = (data, id, code, shiftAllocatedID) => async (dispatch) => {
    try {
        let datas = {
            data,
            id,
            code
        }
        dispatch(setLoading(true));
        await axios.post(`admin/api/shiftAllocationScheduleRoute/updateshiftAllocationSchedule`, encrypt(JSON.stringify(datas)))
            .then(async (res) => {
                if (res.status == 200) {
                    dispatch(updateShiftAllocationData(datas));
                    res.data = await JSON.parse(decrypt(res.data));
                    dispatch(
                        showMessage({
                            message: res.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "right",
                            },
                            variant: "success",
                        })
                    );
                    dispatch(setLoading(false));
                }
                else {
                    res = JSON.parse(decrypt(res.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: res.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const clearData = () => async (dispatch) => {
    clearShiftAllocationData([])
}




const initialState = {
    data: [],
    isloading: false,
};

const shiftAllocationScheduleSlice = createSlice({
    name: 'schedule',
    initialState,
    reducers: {
        setShiftAllocationData: (state, action) => {
            state.data = action.payload;
        },
        updateShiftAllocationData: (state, action) => {

            //let x = state.data.filter(x => x._id === action.payload.id)

            const index = state.data.findIndex(obj => obj._id === action.payload.id);

            if (index !== -1) {
                // Update the object's date property
                state.data[index].startDateTime = new Date(action.payload.data.StartDate);
                state.data[index].endDateTime = new Date(action.payload.data.EndDate);
            }


        },
        createShiftAllocationData: (state, action) => {
            state.data = [...state.data, action.payload]
        },
        removeShiftAllocationData: (state, action) => {

            state.data = state.data.filter(x => x._id !== action.payload)
        },
        clearShiftAllocationData: (state, action) => {
            state.data = action.payload;
        },

        setLoading: (state, action) => {
            state.isloading = action.payload;
        }

    },
    extraReducers: {}
});

export const {
    setShiftAllocationData,
    setLoading,
    clearShiftAllocationData,
    updateShiftAllocationData,
    removeShiftAllocationData,
    createShiftAllocationData

} = shiftAllocationScheduleSlice.actions;

export default shiftAllocationScheduleSlice.reducer;
