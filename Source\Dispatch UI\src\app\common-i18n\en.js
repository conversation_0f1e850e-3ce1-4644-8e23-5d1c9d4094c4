const locale = {
	accept: 'Accept',
	accident: 'Accident',
	acquiredBy: 'Acquired By',
	acquiredDate: 'Acquired Date',
	acquiredLocation: 'Acquired Location',
	activity: 'Activity',
	activityType: 'ActivityType',
	actualStopLocation: 'Actual Stop Location',
	add: 'Add',
	addActivity: 'Add Activity',
	addAttachment: 'Add Attachment',
	editAttachment: 'Edit Attachment',
	addCommunication: 'Add Communication Log',
	addDisposition: 'Add Disposition',
	addIncidentCommLog: 'Add Incident Communication Logs',
	addIncidentLocation: 'Add Incident Location',
	addIncidentNote: 'Add Incident Note',
	addIncidentOrg: 'Add Incident Organization',
	addIncidentPerson: 'Add Incident Person',
	addIncidentProperty: 'Add Incident Property',
	addIncidentSecurity: 'Add Incident Security',
	addIncidentVehicle: 'Add Incident Vehicle',
	addIncidentViolation: 'Add Incident Violation',
	addNarrative: 'Add Narrative',
	addNote: 'Add Note',
	addNotes: 'Add Notes',
	addOrganization: 'Add Organization',
	addPerson: 'Add Person',
	addProperty: 'Add Property',
	addSecurity: 'Add Security',
	addVehicle: 'Add Vehicle',
	addViolation: 'Add Violation',
	addrNum: 'Addr Num',
	address: 'Address',
	addressDetails: 'Address Details',
	addressType: 'Address Type',
	addressValidated: 'Address Validated',
	age: 'Age',
	ageFrom: 'Age From',
	ageTo: 'Age To',
	ageType: 'Age Type',
	agency: 'Agency',
	agencyName: 'Agency name',
	agencyOrCaseExhiNo: 'Agency/case Exhi No.',
	alert: 'Alert',
	allCalls: 'All Calls',
	apt: 'Apt',
	areYouSure: 'Are you sure?',
	arrestDateTime: 'Arrest Date Time',
	arrestLocation: 'Arrest Location',
	arrestType: 'Arrest Type',
	arrested: 'Arrested',
	arrestedDetails: 'Arrested Details',
	arrestingOfficer: 'Arresting Officer',
	article: 'Article',
	articleType: 'Article Type',
	assignment: 'Assignment',
	associatedPersons: 'Associated Persons',
	associatedVehicle: 'Associated Vehicles',
	association: 'Association',
	associationPersonDetails: 'Association Person Details',
	associationType: 'Association Type',
	attachment: 'Attachment',
	attemptedOrCompleted: 'Attempted/Completed',
	audioFileSizeLbl: 'Please select audio file less than 1MB.',
	automatic: 'Automatic',
	availableForAssignment: 'AVAILABLE FOR ASSIGNMENT',
	availableInDateOrTimeRange: 'Available In Date/Time Range',
	bandMakeManufacturer: 'Brand/Make/Manufacturer',
	birthCity: 'Birth City',
	birthState: 'Birth State',
	boat: 'Boat',
	boatDecalNumber: 'Boat Decal Number',
	boatHullNumber: 'Boat Hull Number',
	branch: 'Branch',
	broadcast: 'Broadcast',
	building: 'Building',
	buildingName: 'Building Name',
	buildingNbr: 'Building Nbr',
	businessNameLbl: 'Business Name',
	caliber: 'Caliber',
	callAssigned: 'Call Assigned',
	callDisconnectWarning: 'By joining this call, the previous call will be disconnected.',
	callDisposition: 'Call Disposition ',
	callFound: 'Call Found',
	callHistorySearch: 'Call History Search',
	callIdLbl: 'Call number',
	callNumber: 'Call number',
	callPath: 'Call Path',
	callPersonArrestedLbl: 'Arrested',
	callPersonDobLbl: 'DOB',
	callPersonGenderLbl: 'Gender',
	callPersonRaceLbl: 'Race',
	callPersonRightSideArrestedLbl: 'Arrested',
	callPersonRightSideDobLbl: 'DOB',
	callPersonRightSideGenderLbl: 'Gender',
	callPersonRightSideRaceLbl: 'Race',
	callStatus: 'Call status',
	callType: 'Call Type',
	callUnitRightSideArrivedLbl: 'Arrived',
	callUnitRightSideClearedLbl: 'Cleared',
	callUnitRightSideDispatchedLbl: 'Dispatched',
	callUnitRightSideEnRouteLbl: 'En Route',
	callUnitRightSideInStationLbl: 'In Station',
	callUnitRightSideTransportingDestLbl: 'Transporting Destination',
	callUnitRightSideTransportingLbl: 'Transporting',
	callWreckersPhone1Lbl: 'Phone 1',
	callWreckersPhone2Lbl: 'Phone 2',
	callerInfo: 'Caller Info',
	callerInformation: 'Caller Information',
	calls: 'Calls',
	callsFound: 'Calls Found',
	callsMap: 'Calls Map',
	cancel: 'Cancel',
	category: 'Category',
	change: 'Change',
	checkDoor: 'Checked Door',
	chooseEnvironment: 'Choose Environment',
	chooseFile: 'Choose File',
	chooseLanguage: 'Choose Language',
	citation: 'Citation',
	city: 'City',
	cityName: 'City Name',
	clear: 'Clear',
	clearedOrInService: 'Cleared / In-Service',
	closestLocation: 'Closest Locations',
	color: 'Color',
	color1Lbl: 'Color 1',
	color2Lbl: 'Color 2',
	commLogAddedSuccessfully: 'Communication Log Added Successfully',
	commLogUpdatedSuccessfully: 'Communication Log Updated Successfully',
	communicationLogDeletedSuccessfully: 'Communication Log Deleted Successfully',
	communicationLogFailedToDelete: 'Communication Log Failed to Delete',
	communicationLogs: 'Communication Logs',
	communicationNote: 'Communication note',
	communicationWith: 'Communication With',
	condition: 'Condition',
	confimDeletion: 'Confirm Deletion',
	contactNameLbl: 'Contact Name',
	contrabandFound: 'Contraband Found',
	count: 'Count',
	county: 'County',
	current: 'Current',
	currentDispatches: 'Current Dispatches',
	custodyHistory: 'Custody History',
	darkMode: 'Dark',
	date: 'Date',
	definedLocation: 'Defined Location',
	deleteWarning: 'Are you sure you want to delete?',
	description: 'Description',
	didntReceiveOtp: "Didn't receive an OTP?",
	dir: 'Dir',
	direction: 'Direction',
	directionsLbl: 'Directions',
	dispatchCalls: 'Dispatch Calls',
	dispatchChat: 'Dispatch Chat',
	dispatchIncidentAgencyLbl: 'Agency',
	dispatchIncidentReportingOfficerLbl: 'Reporting Officer',
	dispatchIncidents: 'Dispatch Incidents',
	dispatchNumber: 'Dispatch number',
	dispositionLbl: 'Disposition',
	dispositionReason: 'Disposition Reason',
	dl: 'DL',
	dlId: 'DLID',
	dlNumber: 'DL Number',
	dlState: 'DL State',
	doNotHaveAccessToThisApp: 'Do not have access to this app',
	dob: 'Date Of Birth',
	dobAbbreviation: 'DOB',
	driveByThrough: 'Drive by/through',
	driverCategory: 'Driver Category',
	driverDetails: 'Driver Details',
	driverInformation: 'Driver Information',
	drivingLicense: 'Driving License',
	drugName: 'Drug Name',
	drugType: 'Drug Type',
	driver: 'Driver',
	editIncidentCommLog: 'Edit Incident Communication Logs',
	editIncidentLocation: 'Edit Incident Location',
	editIncidentNote: 'Edit Incident Note',
	editIncidentOrg: 'Edit Incident Organization',
	editIncidentPerson: 'Edit Incident Person',
	editIncidentProperty: 'Edit Incident Property',
	editIncidentVehicle: 'Edit Incident Vehicle',
	editIncidentViolation: 'Edit Incident Violation',
	email: 'Email',
	emailType: 'Email type',
	employer: 'Employer',
	ems: 'EMS',
	enRoute: 'En Route',
	endMessageLbl: 'Yay! You have seen it all',
	engaged: 'ENGAGED',
	enterLogType: 'Enter Log Type',
	enterLoginPin: 'Enter 4 digit Login PIN',
	enterNcicToken: 'Enter NCIC Token',
	enterPassword: 'Enter Password',
	enterVerificationCode: 'Please enter the verification code sent to your email',
	enterYourToken: 'Enter your token',
	environment: 'Environment',
	environmentPointedToDev: 'Environment Pointed To Dev',
	environmentPointedToLive: 'Environment Pointed To Live',
	estimatedDrugQuantity: 'Estimated Drug Quantity',
	estimatedValue: 'Estimated Value',
	ethnicity: 'Ethnicity',
	everything10To4: 'Everything 10-4',
	evidence: 'Evidence',
	evidenceInformation: 'Evidence Information',
	evidenceLocation: 'Evidence Location',
	evidenceNo: 'Evidence No.',
	evidenceStatus: 'Evidence Status',
	ext: 'Ext',
	extension: 'Extension',
	eyes: 'Eyes',
	failed: 'Failed!',
	failedToAddNote: 'Failed to add note',
	failedToAddPerson: 'Failed to add Person',
	failedToAddVehicle: 'Failed to add vehicle',
	failedToScan: 'Failed to Scan! Please scan again',
	female: 'Female',
	fileUploadFailed: 'File upload Failed',
	fileUploadedSuccessfully: 'File uploaded Successfully',
	fillRequiredFields: 'Please fill out required fields',
	filterByBranch: 'Filter by Branch',
	fire: 'Fire',
	firePresetsLbl: 'Fire Presets',
	firstName: 'First Name',
	floor: 'Floor',
	fps: 'FPS',
	freeform: 'Freeform',
	from: 'From',
	fromDate: 'From Date',
	gender: 'Gender',
	goToLogin: 'Go to Login',
	gun: 'Gun',
	hair: 'Hair',
	height: 'Height',
	history: 'History',
	idNumber: 'ID number',
	idType: 'ID type',
	immigDocNumber: 'Immig Doc Number',
	immigDocType: 'Immig Doc Type',
	impounded: 'Impounded',
	incident: 'Incident',
	incidentAddedSuccessfully: 'Incident added successfully',
	incidentDeletedSuccessfully: 'Incident Deleted Successfully',
	incidentDetails: 'Incident Details',
	incidentFailedToDelete: 'Incident Failed to Delete',
	incidentInformation: 'Incident Information',
	incidentNumber: 'Incident Number',
	incidentUpdatedSuccessfully: 'Incident Updated Successfully',
	incidentViolationToAdd: 'Incident Violation to add',
	incidents: 'Incidents',
	incomingCall: 'Incoming call',
	incomingVideoCall: 'Incoming Video Call',
	incorrectPassword: 'Incorrect password',
	individualType: 'Individual Type',
	initialLocation: 'Initial Location',
	intersection: 'Intersection',
	invalidCredentials: 'Invalid Credentials',
	invalidDeviceLogin: 'Invalid Device Login',
	involmentTags: 'Involvement Tags',
	involvementLossType: 'Involvement(Loss Type)',
	involvements: 'Involvements',
	isRecovered: 'Is Recovered',
	isSuspectedVehicle: 'Is Suspected Vehicle',
	labResult: 'Lab Result',
	languages: 'Languages',
	lastLocationDate: 'Last location date',
	lastName: 'Last Name',
	lat: 'Lat',
	latitude: 'Latitude',
	latitudeOrLongitude: 'Latitude / Longitude',
	legalAlien: 'Legal Alien',
	licenseNo: 'License No',
	licenseNumber: 'License Number',
	licenseState: 'License State',
	licenseType: 'License Type',
	licenseYear: 'License Year',
	loading: 'Loading',
	location: 'Location',
	locationAlert: 'Location Alert',
	locationHistory: 'Location History',
	locationHistoryNotFound: 'Location History Not Found',
	locationNotAvailable: 'Location not available',
	locationPlace: 'LOCATIONPLACE',
	locationType: 'Location Type',
	logType: 'Log Type',
	login: 'Login',
	logout: 'Logout',
	lon: 'Lon',
	longitude: 'Longitude',
	make: 'Make',
	makeDesc: 'Make Desc',
	makeLbl: 'Make',
	male: 'Male',
	manual: 'Manual',
	mapUnavailable: 'Map Unavailable',
	maritalStatus: 'Marital Status',
	markAsLostOrFound: 'Mark as Lost/Found',
	markStop: 'Mark Stop',
	measure: 'Measure',
	middleName: 'Middle Name',
	misdOrFelony: 'Misd/Felony',
	model: 'Model',
	modelLbl: 'Model',
	modelYearLbl: 'Model Year',
	myCall: 'My Call',
	myCalls: 'My Calls',
	myStatus: 'My Status',
	name: 'Name',
	narrative: 'Narrative',
	narratives: 'Narratives',
	nationality: 'Nationality',
	ncic: 'NCIC',
	ncicInformation: 'NCIC Information',
	ncicLogin: 'NCIC Login',
	ncicSearch: 'NCIC Search',
	ncicSearchHistory: 'NCIC Search History',
	ncicStatus: 'NCIC Status',
	nciciNo: 'NCIC No.',
	newCall: 'New Call',
	nextAction: 'Next Action',
	no: 'No',
	noActivities: 'No Activities',
	noBranches: 'No Branches',
	noCallAssigned: 'No Call Assigned',
	noCalls: 'No Calls',
	noCallsFound: 'No calls found.',
	noContactsFound: 'No Contacts Found',
	noOfCallsToDisplay: 'No. of calls to display',
	noRecordFound: 'No Records Found',
	noUnit: 'No Unit',
	noUnits: 'No Units',
	notOnDuty: 'NOT ON DUTY',
	note: 'Note',
	noteAdded: 'Note added successfully',
	noteDeletedSuccessfully: 'Note Deleted Successfully',
	noteFailedToDelete: 'Note Failed to Delete',
	noteUpdatedSuccessfully: 'Note updated successfully',
	notes: 'Notes',
	noticeLbl: 'Notice',
	numSuffix: 'Num Suffix',
	number: 'Number',
	offDuty: 'Off Duty',
	officer: 'Officer',
	officerName: 'Officer Name',
	officers: 'Officers',
	offline: 'Offline',
	ok: 'OK',
	onDuty: 'On Duty',
	onScene: 'On-Scene',
	online: 'Online',
	options: 'Options',
	orgAddedSuccessfully: 'Organization added successfully',
	orgType: 'Org Type',
	orgUpdatedSuccessfully: 'Organization updated successfully',
	organization: 'Organization',
	organizationDeletedSuccessfully: 'Organization Deleted Successfully',
	organizationFailedToDelete: 'Organization Failed to Delete',
	organizationName: 'Organization Name',
	organizations: 'Organizations',
	otpSentSuccessfully: 'OTP sent successfully on',
	ownedAppliedNumber: 'Owned Applied Number',
	owner: 'Owner',
	ownerDetails: 'Owner Details',
	ownerInformation: 'Owner Information',
	password: 'Password',
	passwordHint: 'Password',
	permanent: 'Permanent',
	person: 'Person',
	personAddedSuccessFully: 'Person added successfully',
	personData: 'Person Data',
	personDeletedSuccessfully: 'Person Deleted Successfully',
	personDetails: 'Person Details',
	personEntry: 'Person Entry',
	personFailedToDelete: 'Person Failed to Delete',
	personGlobalSearch: 'Person Global Search',
	personUpdatedSuccessfully: 'Person Updated Successfully',
	persons: 'Persons',
	pharmacy: 'Pharmacy',
	phone: 'Phone',
	phoneLbl: 'Phone',
	phoneNumber: 'Phone Number',
	phoneType: 'Phone Type',
	placeName: 'Place Name',
	pleaseClickOnSaveButtonToSaveChanges: 'Please click on save button to save changes.',
	pleaseEnterPassword: 'Please enter your password',
	pleaseEnterValid5DigitZipCode: 'Please enter a valid 5-digit Zip Code',
	pleaseEnterYourUserName: 'Please enter your Username',
	pleaseFillRequiredField: 'Please fill required fields',
	pleaseSelectState: 'Please select a state',
	pleaseSelectType: 'Please select type',
	pleaseTryAgain: 'Please try again',
	pleaseWait: 'Please wait...',
	police: 'Police',
	postDir: 'Post Dir',
	postDirection: 'Post Direction',
	preDir: 'PreDir',
	preDirection: 'Pre Direction',
	prescriptionNo: 'Prescription No',
	primaryColor: 'Primary Color',
	primaryContact: 'PRIMARY CONTACT',
	primaryLocation: "PRIMARY LOCATION",
	priorityCall: "Priority Call",
	prioritySort: "Priority",
	privacyPolicy: "Privacy Policy",
	probableCauseFor: "Probable Cause For",
	probableCauseOther: "Probable Cause Other",
	properties: "Properties",
	property: "Property",
	propertyAddedSuccessfully: "Property Added Successfully",
	propertyDeletedSuccessfully: "Property Deleted Successfully",
	propertyFailedToDelete: "Property Failed to Delete",
	propertyUpdatedSuccessfully: "Property Updated Successfully",
	quantity: "Quantity",
	race: "Race",
	receivedOn: "Received on",
	recent: "Recent",
	reconciliationHistory: "Reconciliation History",
	recovered: "Recovered",
	recoveredBy: "Recovered By",
	recoveredDate: "Recovered Date",
	recoveredValue: "Recovered Value",
	recoveryInformation: "Recovery Information",
	recoveryLocation: "Recovery Location",
	registrationNumber: "Registration Number",
	reject: "Reject",
	replayHistory: "Replay History",
	report: "Report",
	reportError: "Report Error",
	residentStatus: "Resident Status",
	reviewDate: "Review Date",
	run: "Run",
	runOrRecordPerson: "Run/Record Person",
	runOrRecordTag: "Run/Record Tag",
	sameAsOwner: "Same As Owner",
	save: "Save",
	saved: "Saved!",
	scanDl: "Scan DL",
	search: "Search",
	searchBy: "Search By",
	searchByNameDlSsnPhoneAr: "Search by Name,DL,SSN,Phone AR",
	searchDriverAddress: "Search Driver Address",
	searchForDriver: "Search For Driver",
	searchForOwner: "Search For Owner",
	searchHistory: "Search History",
	searchLoad: "Search...",
	searchOwnerAddress: "Search Owner Address",
	searchQuerySubmitted: "Search query submitted",
	searchString: "Search String:",
	searchType: "Search Type:",
	searchVehicle: "Search Vehicle",
	searchingQuerySubmitted: "Searching Query Submitted",
	secondaryColor: "Secondary Color",
	security: "Security",
	securityAddedSuccessfully: "Security Added Successfully",
	securityCheck: "Security Check",
	securityDeletedSuccessfully: "Security Deleted Successfully",
	securityFailedToDelete: "Security Failed to Delete",
	securityPersonNotAvailable: "Security person not available",
	selectState: "Select State",
	selectType: "Select Type",
	selected: "Selected",
	sendAnAttachment: "Send an Attachment",
	serialNumber: "Serial Number",
	sessionInvalidWarning: "You have logged in from another device, so this session is no longer valid.",
	setStatus: "Set Status",
	sex: "Sex",
	showHydrant: "Show Hydrant",
	somethingWentWrong: "Something went wrong,Please try after sometime",
	sourceType: "Source type",
	specialInstructionsLbl: "Special Instructions",
	ssn: "SSN",
	st: "ST",
	state: "State",
	status: "Status",
	statusChange: "Status Change",
	statusWarning: "Currently, you are off duty. Please switch to on-duty status and then proceed.",
	statuteCitation: "Statute Citation",
	statuteCode: "Statute Code",
	stopCompletion: "Stop Completion",
	stopMarkedSuccessfully: "Stop Marked Successfully",
	street: "Street",
	streetName: "Street Name",
	streetType: "Street Type",
	styleLbl: "Style",
	submit: "Submit",
	submittedToLab: "Submitted to Lab",
	suffix: "Suffix",
	tag: "Tag",
	tagNumberLbl: "Tag Number",
	tagStateLbl: "Tag State",
	tagType: "Tag Type",
	tagYear: "Tag Year",
	testButton: "Test Button",
	testedVia: "Tested Via",
	themes: "Themes",
	timeline: "Timeline",
	title: "Title",
	titleLbl: "Title",
	to: "To",
	toDate: "To Date",
	tokenExpiredWarning: "Your token has expired and you will need to Sign in again. You will be redirected to the login page in 5 seconds.",
	tokenRequired: "Token Required",
	trackMovement: "Track Movement",
	trafficStop: "Traffic Stop",
	trafficStopReasons: "Traffic Stop Reasons",
	transportCall: "Transport Call",
	transportDetails: "Transport Details",
	transportMedical: "Medical Transport",
	type: "Type",
	unableToValidate: "Unable To Validate",
	unit: "Unit",
	unitArrived: "Arrived",
	unitBegin: "Begin",
	unitBranchName: "Branch Name",
	unitCleared: "Cleared",
	unitDest: "Dest",
	unitDispatch: "Dispatch#",
	unitDispatched: "Dispatched",
	unitEnRoute: "En Route",
	unitEnd: "End",
	unitId: "Unit ID",
	unitName: "Unit Name",
	unitStatus: "Status",
	unitTransporting: "Transporting",
	unitType: "Unit type",
	units: "Units",
	unitsAssigned: "Units Assigned",
	unitsLocation: "Units Location",
	usCitizen: "US Citizen",
	useAuthenticator: "Use Authenticator",
	userName: "Username",
	vehicle: "Vehicle",
	vehicleAddedSuccessfully: "Vehicle added successfully",
	vehicleData: "Vehicle Data",
	vehicleDeletedSuccessfully: "Vehicle Deleted Successfully",
	vehicleEntry: "Vehicle Entry",
	vehicleFailedToDelete: "Vehicle Failed to Delete",
	vehicleSearched: "Vehicle Searched",
	vehicleUpdatedSuccessfully: "Vehicle Updated Successfully",
	vehicles: "Vehicles",
	viewCheckHistory: "View Check History",
	vinLbl: "Vin",
	vinNo: "VIN No",
	vinNumber: "VIN Number",
	violationAddedSuccessfully: "Violation Added Successfully",
	violationDeletedSuccessfully: "Violation Deleted Successfully",
	violationFailedToDelete: "Violation Failed to Delete",
	violationUpdatedSuccessfully: "Violation Updated Successfully",
	violations: "Violations",
	walkAround: "Walk Around",
	warningLbl: "Warning",
	warrantyNumber: "Warrant Number",
	watches: "Watches",
	weight: "Weight",
	welcome: "Welcome",
	welcomeToMobileDispatch: "Welcome to Mobile Dispatch!",
	welcomeToSaber: "Welcome to Saber !",
	wreckers: "Wreckers",
	year: "Year",
	yes: "Yes",
	yourLocation: "Your Location",
	zip: "Zip",
	zipCode: "Zip Code",
	zipPlus4: "ZIP Plus 4",
	zoneOrPrecinct: "Zone / Precinct",
	zoomLevel: "Zoom Level",
	noteAddded: "Note added successfully",
	files: "Files",
	fileDeletedSuccessfully: "File Deleted Successfully",
	fileFailedToDelete: "File Failed to Delete",
	fileAddedSuccessfully: "File Added Successfully",
	fileFailedToAdd: "File Failed to Add",
	fileUpdatedSuccessfully: "File Updated Successfully",
	fileFailedToUpdate: "File Failed to Update",
	applications: "Applications",
	mobileDispatch: "Mobile Dispatch",
	mobileDispatchSocket: "Mobile Dispatch Socket",
	chat: "Chat",
	currentMap: "Current Map",
	cureentMapNew: "Current Map New",
	relativityAdmin: "Relativity Admin",
	call911: "911 Call",
	mySchedule: "My Schedule",
	onDutyCaps: "ON DUTY",
	enroute: "Enroute",
	priority: "Priority",
	timeLine: "Timeline",
	noCallFound: "No call found.",
	associatedVehicles: "Associated Vehicles",
	dark: "Dark",
	form: "From",
	branchName: "Branch Name",
	dispatchHashTag: "Dispatch#",
	unitHashTag: "Unit#",
	dispatched: "Dispatched",
	arrived: "Arrived",
	transporting: "Transporting",
	begin: "Begin",
	end: "End",
	dest: "Dest",
	cleared: "Cleared",
	endMsg: "Yay! You have seen it all",
	disposition: "Disposition",
	tagNumber: "Tag Number",
	style: "Style",
	tagState: "Tag State",
	modelYear: "Model Year",
	color1: "Color 1",
	color2: "Color 2",
	vin: "Vin",
	businessName: "Business Name",
	contactName: "Contact Name",
	warnings: "Warnings",
	specialInstructions: "Special Instructions",
	notice: "Notice",
	firePresets: "Fire Presets",
	directions: "Directions",
	phone1: "Phone 1",
	phone2: "Phone 2",
	reportingOfficer: "Reporting Officer",
	transportingDestination: "Transporting Destination",
	inStation: "In Station",
	changePassword: "Change Password",
	currentPassword: "Current Password",
	confirmPassword: "Confirm Password",
	passwordConfirmMsg: "Your password has been changed successfully. You will need to login again to continue.",
	confirm: "Confirm",
	profilePicture: "Profile Picture",
	update: "Update",
	noResultFound: "No results found",
	code: "Code",
	ownerAppliedNumber: "Owner Applied Number",
	driverLicesenceNumber: "Driver Licesence Number",
	dateOfBirth: "Date Of Birth",
	verify: "Verify",
	personalInformation: "Personal Information",
	rpsAuthentication: "RPS Authentication",
	alertSoundSetting: "Alert Sound Setting",
	defaultlocationsetting: "Default location setting",
	defaultMFAType: "Default MFA Type",
	defaultAgency: "Default Agency",
	relativityAdminMsg: "Relativity Admin opened in new Tab.",
	sendEmail: "Send Email",
	selectDefaultAgency: "Select Default Agency",
	defaultLocationType: "Default location type",
	defaultLocation: "Default location",
	selectLocation: "Select location",
	authenticator: "Authenticator",
	text: "Text",
	notifications: "Notifications",
	dismissAll: "Dismiss All",
	notificationsMsg: "There are no notifications for now.",
	today: "Today",
	events: "Events",
	quickSettings: "Quick Settings",
	fontSize: "Font Size",
	alsAvail: "ALS AVAIL",
	blsAvail: "BLS AVAIL",
	notAvail: "NOT AVAIL",
	available: "Available",
	documentation: "Documentation",
	viewSettingsMsg: "View settings as json/query params",
	fuseSettingViewer: "Fuse Settings Viewer",
	json: "JSON",
	queryParams: "Query Params",
	close: "Close",
	themeSettings: "Theme Settings",
	themeColorSchemes: "Theme Color Schemes",
	themeRuleMsg: "* Selected color scheme will be applied to all theme layout elements (navbar, toolbar,etc.). You can also select a different color scheme for each layout element at theme settings.",
	startChatMsg: "Start a conversation by typing your message below.",
	chatApp: "Chat App",
	selectChatMsg: "Select a contact to start a conversation!..",
	broadcastMessages: "Broadcast Messages",
	unreadMessages: "Unread Messages",
	away: "Away",
	doNotDisturb: "Do not disturb",
	noMessage: "No message.",
	ncicAuthentication: "NCIC Authentication",
	ncicUsername: "NCIC UserName",
	ncicPassword: "NCIC Password",
	showOnlineUsers: "Show Online Users",
	license: "License",
	expDate: "ExpDate",
	lit: "LIT",
	vinCaps: "VIN",
	cylinders: "Cylinders",
	fuel: "Fuel",
	issueDate: "IssueDate",
	stateZip: "StateZip",
	residenceCounty: "Residence County",
	coverage: "Coverage",
	reason: "Reason",
	policy: "Policy",
	effectiveDate: "EffectiveDate",
	naic: "Naic",
	company: "Company",
	holder: "Holder",
	lienDate: "LienDate",
	cdl: "CDL",
	csn: "CSN",
	dln: "DLN",
	endorsements: "Endorsements",
	expires: "Expires",
	eyeColor: "EyeColor",
	hairColor: "HairColor",
	issued: "Issued",
	medicalCertification: "Medical Certification",
	organDonor: "Organ Donor",
	restrictions: "Restrictions",
	totalPoints: "Total Points",
	voluntaryEnhancedSecurity: "Voluntary Enhanced Security",
	ori: "ORI",
	crimeInfoCenter: "Crime Info Center",
	oca: "OCA",
	doi: "DOI",
	towingService: "Towing Service",
	mis: "MIS",
	srn: "SRN",
	doe: "DOE",
	toe: "TOE",
	noUnitsFound: "No units found.",
	unitsFilter: "Units Filter",
	selectAll: "Select All",
	deselectAll: "Deselect All",
	applyFilter: "Apply Filter",
	unitsFilterApplied: "Units filter applied",
	zoomlevel: "Zoom level",
	allCallsAndUnits: "All Calls and Units",
	locationEntryCaps: "LOCATION ENTRY",
	searchForLocation: "Search for location (Full Address)",
	intersectionFreeform: "Intersection/ Freeform/ Common Place Name",
	streetNumber: "Street Number",
	zipExt: "ZipExt",
	locationTags: "Location Tags",
	organizationEntryCaps: "ORGANIZATION ENTRY",
	searchForOrganization: "Search for organisation",
	organizationType: "Organization Type",
	descriptionNotes: "Description/Notes",
	titleRole: "Title/Role",
	individualEntryCaps: "Individual Entry",
	arrestDetailsCaps: "ARREST DETAILS",
	arrestCircumstances: "Arrest Circumstances",
	arrestDate: "Arrest Date",
	arrestTime: "Arrest Time",
	warrantNumber: "Warrant Number",
	zone: "Zone / Precinct",
	latitudeLongitude: "Latitude / Longitude",
	searchForPerson: "Search for person",
	hispanic: "Hispanic",
	issuedBy: "Issued By",
	associatedPersonDetail: "Associated Person Detail",
	vehicleEntryCaps: "VEHICLE ENTRY",
	searchForVehicle: "Search for vehicle",
	involment: "Involvement",
	wreckerEntryCaps: "WRECKER ENTRY",
	searchWrecker: "Search Wrecker (Company Name)",
	towingCompany: "Towing Company",
	speeding: "Speeding",
	driving: "Driving",
	xLine: "X-Line",
	cellPhone: "Cell Phone",
	defEquip: "Def Equip",
	otherViol: "Other Viol",
	onwer: "Owner",
	isDriver: "Is Driver",
	ownerAddress: "Owner Address",
	citationDetailsHere: "Citation Details Here",
	citationCaps: "CITATION",
	attachDocument: "ATTACH DOCUMENT/PHOTO/VIDEO/AUDIO",
	selectFiles: "Select Files",
	additionalInformation: "Additional Information",
	location1: "Location 1",
	location2: "Location 2",
	location3: "Location 3",
	type1: "Type 1",
	type2: "Type 3",
	type3: "Type 3",
	queryArticle: "QUERY ARTICLE",
	reset: "Reset",
	agencyResults: "Agency Results",
	ncicResults: "NCIC Results",
	queryBoat: "QUERY BOAT",
	queryGun: "QUERY GUN",
	ncicPersonSearch: "NCIC",
	personSearch: "Person Search",
	localPersonSearch: "Local",
	youDoNotHavePermissionForNCICSearch: "You do not have permission for NCIC Search",
	youDoNotHavePermissionForLocalSearch: "You do not have permission for Local Search",
	driversLicenseNo: "Drivers License No",
	nameLastFirstMiddle: "Name (last,first,middle)",
	destination: "Destination",
	driverSystemNumber: "Driver System Number",
	socialSecurityNumber: "Social Security Number",
	miscellaneousNumber: "Miscellaneous Number",
	driverHistory: "Driver History",
	y: "Y",
	n: "N",
	vehicleSearchCaps: "VEHICLE SEARCH",
	emsUnitName: "EMS Unit Name",
	unitNumber: "Unit Number",
	officerStatus: "Officer Status",
	jobName: "Job Name",
	availableALS: "Available - ALS",
	unavailable: "Unavailable",
	imageReturn: "Image Return",
	relatedHit: "Related Hit",
	ncicCheck: "NCIC Check",
	acknowlegde: "ACKNOWLEDGE",
	enrouteCaps: "ENROUTE",
	tweet: "Tweet",
	post: "Post",
	ncicResult: "NCIC Result",
	okay: "Okay",
	licenseNum: "License Number",
	messageType: "Message Type",
	messageResponse: "Message Response",
	responseDate: "Response Date",
	scanLicense: "Scan Drivers License",
	immigDocNum: "Immig Doc Num",
	nationalityType: "Nationality Type",
	makeDescription: "Make Description",
	isSuspectVehicle: "Is Suspect Vehicle",
	mileage: "Mileage",
	startMileage: "Start Mileage",
	transportingDetails: "Transporting Details",
	addCallDisposition: "Add Call Disposition",
	ncicToken: "NCIC Token",
	token: "Enter NCIC Token",
	searchResponse: "Search Response",
	putOnDuty: "Put On Duty",
	shiftJob: "Shift Job",
	nonCallStatus: "Non Call Status",
	setCallActiveToOffDutyMsg: "You are currently active on a dispatch call, do you want to clear/remove yourself from Call?",
	setCallInActiveToOffDutyMsg: "Your current status is “On Duty”. Do you want to take yourself Off Duty with this Log Out?",
	dateTime: "Date/Time",
	locationName: "Location Name",
	checkMethod: "Check Method",
	startingMileage: "Starting Mileage",
	endingMileage: "Ending Mileage",
	closestLocations: "CLOSEST LOCATIONS",
	pleaseSelectAudioFileLessThan1MBMsg: "Please select audio file less than 1MB.",
	pleaseClickOnSaveButtonToSaveChangesMsg: "Please click on save button to save changes.",
	agencyname: "Agency name",
	dispatchNumber: "Dispatch number",
	clearedInService: "Cleared / In-Service",
	notesInformation: "Notes/Information",
	zone: "Zone",
	newUser: "New User",
	department: "Department",
	shift: "Shift",
	team: "Team",
	users: "Users",
	userExist: "This user is already exist in same day, same shift. Please select different user or different shift.",
	scheduleIDNotDound: "Please select a date which is in between create Schedule.",
	deleteText: "Are you sure you want to remove this user?",
	map: "Map",
	replay: "Replay",
	clearSelection: "Clear Selection",
	selectedUnits: "Selected Units",
	unitsAvailableDateTime: "Units Available In Date/Time Range",
	officerAvailableDateTime: "Officer's Available In Date/Time Range",
	high: "High",
	medium: "Medium",
	low: "Low",
	showAll: "Show all",
	playAll: "Play all",
	maxSpeed: "Max speed",
	playAllSingleUnit: "Play all/single unit",
	apiResponseForUnitnumber: "API response for Unit number",
	coordinates: "Co-ordinates",
	dateTimeWithLocalOffSet: "DateTime With local offset",
	callID: "CallID",
	darkMode: "DarkMode",
	watchFilter: "Watch Filter",
	all: "All",
	checkHistory: "Check History",
	timeFrame: "TimeFrame",
	parsedResponse: "Parsed Response",
	messageId: "Message ID",
	chatAudibleTone: "Chat Audible Tone",
	probableCause: "Probable Cause",
	rawResponse: "Raw Response",
	searchByLicenseNumber: "Search by License Number",
	searchByVINNumber: "Search by VIN Number",
	queryVehicle: 'Vehicle search',
	startChat: 'Start a conversation by typing your message below.',
	searchByName: "Search by Name",
	searchByDL: "Search by DL",
	freeForm: "FREE FORM",
	markStop: "Mark Stop",
	start: "Start",
	stop: "Stop",
	time: "Time",
	searchByLicenseNumber: "Search By License Number",
	searchByVINNumber: "Search By VIN Number",
	pinCall: "Pin Call",
	unpinCall: "Unpin Call",
	includeOffDuty: "Include OFF Duty",
	fullScreenToggle: "Full Screen Toggle",
	onOffDuty: "On/Off Duty",
	speedByTime: "Speed By Time",
	legend: "Legend",
	sanitation: "Sanitation",
	scrollLeft: "Scroll Left",
	scrollRight: "Scroll Right",
	defaultNCICSettings: "Default NCIC Settings",
	audibleToneForChatMessage: "Audible tone for chat message",
	upload: "Upload",
	punctuateText: "Punctuate Text",
	searchForAddress: "Search for address",
	areYouSure: "Are you sure you want to delete this record ?",
	callInformation: "Call Information",
	lastRpsChange: "Last RPS Change",
	lastNcicChange: "Last NCIC Change",
	addIncidentPerson: "Add Incident Person",
	recentPersonsUsed: "Recent Persons Used",
	recentAddressesUsed: "Recent Addresses Used",
	recentVehicleUsed: "Recent Vehicle Used",
	tokenExpireMsg: "Your token has expired and you will need to Sign in again. You will be redirected to the login page in 5 seconds.",
	invalidSessionMsg: "You have logged in from another device, so this session is no longer valid.",
	heightFeet: "{{feet}} ft",
	heightFeetInches: "{{feet}} ft {{inches}} in",
	heightInches: "{{inches}} in",
	invalidHeightEntry: "Invalid height entry",
	redirecting: "Redirecting in 5 seconds",
	goto: "Go to",
	signIn: "Sign in",
	personDeleteMsg: "Are you sure you want to delete this person ?",
	citationLocation: "Citation Location",
	citationInformation: "Citation Information",
	pleaseFillNote: "Please fill the note",
	pleaseSelectFile: "Please select file",
	communicationWith: "Communication With",
	communicationNote: "Communication Note",
	involvementLossType: "Involvement (LossType)",
	quantity: "Quantity",
	weight: "Weight",
	brand: "Brand/Make/Manufacturer",
	caliber: "Caliber/Gauge",
	estimatedValue: "Estimated Value",
	freeForm: "Free-Form",
	serialNumber: "Serial Number",
	ownedAppliedNumber: "Owner Applied Number",
	ncicRefNumber: "NCIC Ref Number",
	recovered: "Recovered",
	recoveredDate: "Recovered Date",
	recoveryLocation: "Recovery Location",
	recoveryCondition: "Recovery Condition",
	recoveredValue: "Recovered Value",
	pleaseSelectTitle: "Please Select Title",
	changeFrom: "Change From",
	changedBy: "Changed By",
	ncicNumber: "NCIC Number",
	ncicStatus: "NCIC Status",
	recoveryInformation: "Recovery Information",
	recoveredBy: "Recovered By",
	ncicInformation: "NCIC Information",
	owner: "Owner",
	automatic: "Automatic",
	unknown: "Unknown",
	drugType: "Drug Type",
	drugName: "Drug Name",
	measure: "Measure",
	drugQuantity: "Estimated Drug Quantity",
	pharmacy: "Pharmacy",
	prescriptionNumber: "Prescription Number",
	testedVia: "Tested Via",
	submittedToLab: "Submitted To Lab",
	evidence: "Evidence",
	evidenceNumber: "Evidence Number",
	evidenceStatus: "Evidence Status",
	agencyExhibitNumber: "Agency/Case Exhibit Number",
	evidenceLocation: "Evidence Location",
	acquiredDateTime: "Acquired Date Time",
	acquiredBy: "Acquired By",
	acquiredLocation: "Acquired Location",
	nextAction: "Next Action",
	markLostFound: "Mark as Lost/Found Item",
	permanent: "Permanent",
	reviewDate: "Review Date",
	dispositionReason: "Disposition Reason",
	reconciliationHistory: "Reconciliation History",
	custodyHistory: "Custody History",
	labResults: "Lab Results",
	evidenceInformation: "Evidence Information",
	organizationName: "Organization Name",
	organizationType: "Organization Type",
	primaryLocation: "PRIMARY LOCATION",
	primaryContact: "PRIMARY CONTACT",
	addNewIncident: "Add New Incident",
	violationToAdd: "Incident Violation to Add",
	scanDriversLicense: "Scan Drivers License",
	searchbyName: "Search by Name",
	addressValidated: "Address Validated",
	unableToValidate: "Unable To Validate",
	offense: "Offense",
	dob: "DOB",
	hgt: "Hgt",
	wgt: "Wgt",
	file: "File",
	note: "Note",
	doesNotSupportMsg: "Browser doesn't support speech recognition",
	searchForOwner: "Search For Owner",
	searchForDriver: "Search For Driver",
	searchForDriverAddress: "Search Driver Address",
	searchForOwnerAddress: "Search Owner Address",
	add: "Add",
	update: "Update",
	contact: "Contact",
	addNewContact: "Add New Contact",
	title: "Title",
	firstName: "First Name",
	middleName: "Middle Name",
	lastName: "Last Name",
	city: "City",
	state: "State",
	phone: "Phone",
	contactsDeleteMsg: "Are you sure you want to delete Contacts ?",
	errorLogs: "Error Logs",
	user: "User",
	relativityAPI: "Relativity API",
	incidentAPI: "Incident API",
	dispatchUI: "Dispatch UI",
	adminUI: "Admin UI",
	all: "ALL",
	resolved: "Resolved",
	unResolved: "Unresolved",
	resolveError: "Error Resolved",
	confirmMsgErrorResolved: "Are you sure you want to mark this error as resolved?",
	errorDetails: "Error Details",
	method: "Method",
	timeStamp: "Timestamp",
	source: "Source",
	stackTrace: "StackTrace",
	resolvedBy: "Resolved By",
	resolvedAt: "Resolved At",
	resolve: "Resolve",
	dateWarningMsg: "Ensure To date is not greater than From date.",
	dispatchAPI: "Dispatch API",
	socketAPI: "Socket API",
	fileUpload: "File Upload",
	noFileSelectMsg: "No File Selected",
	upload: "Upload",
	fileSelectMsg: "1 File is Selected",
	filesSelectedMsg: "Files Selected",
	uploadedFiles: "Uploaded Files",
	fileViewer: "File Viewer",
	incidentInfo: "Incident Info",
	incidentLocation: "Incident Location",
	incidentFile: "Incident File",
	close: "Close",
	incidentNotes: "Incident Notes",
	incidentPerson: "Incident Person",
	vehicleEntry: "Vehicle Entry",
	searchForVehicle: "Search For Vehicle",
	incidentViolation: "Incident Violation",
	person: "Person",
	ownerInformation: "Owner Information",
	driverInformation: "Driver Information",
	incidentInformation: "Incident Information",
	incidentPDF: "Incident PDF",
	narrative: "Narrative",
	addNewVehicle: "Add New Vehicle",
	incidents: "Incidents",
	addIncident: "Add Incident",
	incidentEndMsg: "No more Incidents",
	incidentDeleteMsg: "Are you sure you want to delete this incident ?",
	incidentPersonDeleteMsg: "Are you sure you want to delete this incident person ?",
	incidentVehicleDeleteMsg: "Are you sure you want to delete this incident vehicle ?",
	edit: "Edit",
	individualType: "Individual Type",
	businessName: "Business Name",
	contactName: "Contact Name",
	ext: "Ext",
	placeName: "Place name",
	address: "Address",
	reportedBy: "Reported By",
	incidentNumber: "Incident Number",
	zipCode: "Zip Code",
	violation: "Violation",
	reportedDate: "Reported Date",
	persons: "Persons",
	vehicles: "Vehicles",
	organization: "Organization",
	property: "Property",
	narrative: "Narrative",
	files: "Files",
	communicationLogs: "Communication Logs",
	security: "Security",
	locationPlaceOcurred: "Location/Place Ocurred",
	nfirsEquipmentType: "NFIRS Equipment Type",
	addNFIRS: "Add Nfirs",
	type: "Type",
	date: "Date",
	gpsLocation: "GPS Location",
	phoneNumber: "Phone Number",
	email: "Email",
	comments: "Comments",
	details: "Details",
	delete: "Delete",
	customer: "Customer",
	status: "Status",
	dateValidation: "Start date must less than end date!",
	dateTime: "Date Time",
	from: "From",
	tipComment: "Tip Comment",
	commentType: "Comment Type",
	answered: "Answered",
	quikTipDetails: "QuikTip Details",
	tipTypes: "Tip Types",
	addTipType: "Add Tip Type",
	notificationCenter: "Notification Center",
	sendMessage: "Send Message",
	tipType: "Tip Type",
	description: "Description",
	deleteMsg: "Are you sure you want to delete ?",
	sender: "Sender",
	pushedMessage: "Pushed Message",
	createDateTime: "Created Date Time",
	message: "Message",
	tipDetail: "Tip Details",
	attachments: "Attachments",
	tipComments: "Tip Comments",
	comment: "Comment",
	addInternalComment: "Add Internal Comment",
	addPublicComment: "Add Public Comment",
	addQuestion: "Add Question",
	messageMustHavevalue: "Message Must Have Value!",
	commentShouldNotBeEmptyOrNull: "Comment Should Not Be Empty!",
	changeStatus: "Change Status",
	name: "Name",
	users: "Users",
	teamDeleteMsg: "Are you sure you want to delete team ?",
	confirmationmsg: "This user is present in other team. Are you sure you want this user ?",
	department: "Department",
	departmentRequired: "You must choose department...",
	teamLimit: "No of teams in this department is Exceeds..",
	teamlead: "Team Leader",
	colorCode: "Color Code",
	cancel: "Cancel",
	addTeam: "Add Team",
	twitterAuthorize: "Twitter Authorize",
	unit: "Units",
	unitName: "Unit Name",
	agency: "Agency",
	branch: "Branch",
	station: "Station",
	manufacturer: "Manufacturer",
	style: "Style",
	"color1/color2": "Color 1 / Color 2",
	category: "Category",
	emsLevel: "EMS Level",
	unitPositions: "Unit Positions",
	oilType: "Oil Type",
	pmcsSchedule: "PMCS Schedule",
	nextServiceDate: "Next Service Date",
	nextServiceMiles: "Next Service Miles",
	latestMileage: "Latest Mileage",
	estimatedEndofLife: "Estimated End of Life",
	inServiceDate: "In Service Date",
	tireSize: "Tire Size",
	fueltype: "Fuel Type",
	vinNumber: "VIN Number",
	tagState: "Tag State",
	tagNumber: " Tag Number",
	nextMaintenance: "Next Maintenance",
	role: "Role",
	capabilityTags: "Capability Tags",
	issuedAssignedTo: "Issued/Assigned To",
	confirm: "Confirm",
	unitDeleteMsg: "Are you sure you want to delete Units ?",
	no: "No",
	yes: "Yes",
	save: "Save",
	back: "Back",
	selectFile: "Select File",
	preview: "Preview",
	policecar: "Police Car",
	fireTruck: "Fire Truck",
	ems: "EMS",
	unitType: "Unit Type",
	addRole: "Add Role",
	acicNcic: "ACIC/NCIC",
	neim: "NEIM",
	action: "Action",
	pleaseSelectImage: "Please select image",
	addUnits: "Add Unit",
	userAudit: "User Audit",
	search: "Search",
	appName: "Application Name",
	activity: "Activity",
	activityDateTime: "Activity date time",
	incident: "Incident",
	viewDetails: "View Details",
	intersectionErrorLogs: "Intersection Error Logs",
	importLogsIntersectionPoint: "Import Logs - Intersection Point",
	filePath: "File Path",
	fileName: "File Name",
	updatedCount: "Updated Count",
	insertedCount: "Inserted Count",
	matchCount: "Match Count",
	failedCount: "Failed Count",
	totalCount: "Total Count",
	viewImportLogs: "View Import Logs",
	masterAddressList: "Master Address List",
	masterIntersectionList: "Master Intersection List",
	municipalityType: "Municipality Type",
	addressNumber: "Address Number",
	addressNumberComp: "Address Number Comp",
	addressBuilding: "Address Building",
	legacyAddress: "Legacy Address",
	localName: "Local Name",
	nearestXStDist: "Nearest Distance",
	secondNearestXStDist: "SecondNearest Distance",
	masterAddressErrorLogs: "Master Address Error Logs",
	importLogsMasterAddressPoint: "Import Logs - Master Address Point",
	importDate: "Import Date",
	deleteWarning: "Are you sure you want to delete?",
	others: "Others",
	soundAlertSetting: "Sound Alert Setting",
	deleteShiftText: "Are you sure you want to remove the shift?",
	addNumber: "Add Number",
	deletethisVehicleMsg: "Are you sure you want to delete this vehicle ?",
	areYouSureYouWantDelete: "Are You Sure You Want Delete",
	selectFiles: "Select Files",
	noFileSelectedMsg: "No file is selected.",
	fileIsSelected: "1 file is selected.",
	accidentalCall: "Accidental Call",
	callBackDeadCall: "Call Back Dead Call",
	callBackNoAnswer: "Call Back No Answer",
	nonAccidentalCallNeedOfficer: "Non Accidental Call Need Officer",
	callForService: "Call For Service - Initial Entry From 911",
	showRectangle: "Show Rectangle",
	archiveChatOlderThan: "Archive chat older than",
	permanentLogin: "Permanent Login",
	userAgencyDetails: "User Agency Details:",
	clear: "Clear",
	lastLoginDate: "Last Login Date",
	objectId: "ObjectId",
	subAddress: "SubAddress",
	selectAll: "Select All",
	deleteDepartmentMsg: "Are you sure you want to remove this department?",
	available: "Available",
	unAvailable: "UnAvailable",
	where: "Where",
	downloadTemplate: "Download Template",
	nibrsCode: "Nibrs Code",
	addNibrsCode: "Add Nibrs Code",
	stateViolation: "State Violation",
	violationTypes: "Violation Types",
	linkedClassification: "Linked Classification",
	defaultClassification: "Default Classification",
	jailTypes: "Jail Types",
	fingerPrintRequired: "FingerPrint Required",
	citationViolation: "Citation Violation",
	criminalViolation: "Criminal Violation",
	warrentViolation: "Warrent Violation",
	trafficViolation: "Traffic Violation",
	localOrdinance: "Local Ordinance",
	violationClassification: "Violation Classification",
	weaponType: "Weapon Types",
	warningMsgForCriminalActivity: " You can only select up to 3 criminal activities.",
	criminalActivity: "Criminal Activity",
	gangType: "Gang Type",
	warningMsgForGangType: "Can select up to 2 gangs",
	biasMotivation: "Bias Motivations",
	community: "Community",
	addClassification: "Add Classification",
	classificationMapping: "Classification Mapping",
	reset: "Reset",
	oneDefaultClassificationNeedsToBeSelected: "One Default Classification Needs To Be Selected.",
	locationTypes: 'Location Types',
	earliestDate: "Earliest Date/Time",
	latestDate: "Latest Date/Time",
	reportDate: "Report Date/Time",
	updateRequest: "Update Request",
	sendUpdateRequest: "Send Update Request",
	updatedFields: "Updated Fields",
	updatedOn: "Updated On",
	updatedBy: "Updated By",
	updateAddress: "Update Address",
	approve: "Approve",
	reject: "Reject",
	acceptedRejectedBy: "Accepted/Rejected By",
	acceptedRejectedByOn: "Accepted/Rejected On",
	masterAddressUpdateRequest: "Master Address Update Request",
	oldFields: "Old",
	newFields: "New",
	dispatchVersion: "Dispatch Version",
	socketVersion: "Socket Version",
	pollingVersion: "Polling Version",
	importViolation: "Import Violation",
	TextStatuteTitle: "Text Statute Title",
	OffenseDescriptionAbbreviated: "Offense Description Abbreviated",
	Type: "Type",
	Class: "Class",
	NIBRSCode: "NIBRS Code",
	BeginDate: "Begin Date",
	EndDate: "End Date",
	JailTypes: "Jail Types",
	ViolationTypes: "Violation Types",
	resetPassword: "Reset Password",
	rpsCredentials: "RPS Credentials",
	mugshotCredentials: "Mugshot Credentials",
	disableUser: "Disable User",
	enableUser: "Enable User",
	options: "Options",
	sourceFields: "Source Fields",
	destinationFields: "Destination Fields",
	selectField: "Select Field",
	death: "Death",
	createdDate: "Created Date",
	lastModifiedDate: "Last Modified Date",
	rpsCredentialsFor: "RPS Credentails For",
	pleaseEnterUsernameAndPassword: "Please Enter Your Username And Password",
	mugshotCredentialsFor: "Mugshot Credentials For",
	backtoAgency: "Back to agency",
	printPDF: "Print PDF",
	audit: "Audit",
	generateShift: "Generate Shift",
	viewAndEdit: "View & Edit",
	reSchedule: "Re-Schedule",
	generateShiftAllocation: "Generate Shift Allocation",
	changePasswordFor: "Change Password For",
	sendEmailToUser: "Send Email to User",
	deselectAll: "Deselect All",
	displayFields: "Display Fields",
	statute: "Statute",
	searchFields: "Search Fields",
	sortColumns: "Sort Columns",
	userNotFound: "User not found",
	otherJurisdition: "Other Jurisdiction",
	outOfJurisdiction: "Out Of Jurisdiction",
	ARStatuteCode: "AR Statute Code",
	userDisabled: "User disabled successfully",
	selectAgencyToBeChanged: "Select agency to be changed",
	addressValidateMsg: "You need to validate the address.",
	stateMsg: "State should same as agency",
	searchForAddress: "Search for address",
	incidentOrganization: "Incident Organization",
	selectUsers: "Select users",
	citationTypeToAdd: "Citation Type to Add",
	formatted: "Formatted",
	callBackAnswer: "Call Back Answer",
	shiftLimitExceedsForDepartment: "The shift limit exceeds for this department please choose other department.",
	shiftAlreadyExists: "This shift is already exists, please select different shift name.",
	ageFrom: "Age From",
	ageTo: "Age To",
	ageType: "Age Type",
	issuingAuthority: "Issuing Authority",
	dateIssued: "Date Issued",
	dateExpires: "Date Expires",
	revoked: "Revoked",
	revokedDate: "Revoked Date",
	idInfo: "Id Info",
	phoneNumberType: "Phone Number Type",
	extension: "Extension",
	extendedDescription: "Extended Description",
	skinTone: "Skin Tone",
	eyeColor: "Eye Color",
	hairLength: "Hair Length",
	eyeWear: "Eyewear",
	hairStyle: "Hair Style",
	demeanor: "Demeanor",
	facialHairColor: "Facial Hair Color",
	dexterity: "Dexterity",
	facialHairStyle: "Facial Hair Style",
	speech: "Speech",
	build: "Build",
	dental: "Dental/Teeth",
	clothing: "Clothing and General Appearance",
	addAddress: "Please add address to send update request",
	suspectedOfUsing: "Suspected of Using",
	methodOfEntry: "Method of Entry",
	EarliestLessLatestDate: "Earliest Date should be less or equal to Latest Date",
	EarliestLessReportDate: "Earliest Date should less or equal to Report Date",
	LatestLessEarliestDate: "Latest Date should be equal or greater than Earliest Date",
	LatestLessReportDate: "Latest Date should be less or equal to Report Date",
	ReportDateGreater: "Report Date should be greater or equal to Earliest and Latest Date",
	primaryColor: "Primary Color",
	secondaryColor: "Secondary Color",
	vin: "Vin",
	recent: "Recent",
	lastNcicChange: "Last Ncic Change Date",
	lastRpsChange: "Last Rps Change Date",
	recentPerson: "Person(s)",
	recentAddress: "Address(es)",
	recentVehicle: "Vehicle(s)",
	recentOrganisation: "Organisation(s)",
	fireArmType: "Fire Arm Type",
	gunType: "Gun Type",
	gunDescription: "Gun Description",
	noReportDate: "No Report Date",
	reportedPerson: "Reported Person",
	brandLabel: "Brand",
	baby: "Baby",
	newborn: "Newborn",
	neonatal: "Neonatal",
	pleaseSelectLogType: "Please select log type",
	gatheringResults: "Gathering Results",
	dLScan: "DL SCAN",
	nCICSearch: "NCIC SEARCH",
	localSearch: "LOCAL SEARCH",
	globalSearch: "GLOBAL SEARCH",
	manualEntry: "MANUAL ENTRY",
	recentList: "RECENT LIST",
	searchByVehicle: "Search by Vehicle",
	residence: "Residence",
	USCitizen: "US Citizen",
	legalAlien: "Legal Alien",
	nationality: "Nationality",
	arrestInformation: "Arrest Information",
	arrestDate: "Arrest Date",
	arrestNumber: "Arrest Number",
	arrestSequence: "Arrest Sequence",
	arrestType: "Arrest Type",
	arresteeArmed: "Weapons At Arrest",
	multipleArresteeSegmentIndicator: "Multiple Arrestee Segment Indicator",
	juvenileDisposition: "Juvenile Disposition",
	searchByVehicle: "Search by Vehicle",
	reportingOfficer: "Reporting officer",
	exceptionalClearance: "Exceptional clearance",
	exceptionalClearanceDate: "Exceptional clearance date",
	cargoTheft: "Cargo theft",
	domesticViolence: "Domestic violence",
	sealed: "Sealed",
	active: "Active",
	awaitingInformation: "Awaiting information",
	closed: "Closed",
	inactive: "Inactive",
	unfounded: "Unfounded",
	LEOKAInformation: "LEOKA Information",
	circumstance: "Circumstance",
	addrNumber: "Addr Number",
	stateMsg: "State should same as agency",
	involvementNotes: "Involvement / Notes",
	involvement: "Involvement",
	notes: "Notes",
	st: "ST",
	tag: "Tag",
	year: "Year",
	make: "Make",
	model: "Model",
	primaryColor: "Primary Color",
	secondaryColor: "Secondary Color",
	vehicleChangeHistory: "Vehicle change history",
	vin: "Vin",
	registrationScan: "REGISTRATION SCAN",
	nCICSearch: "NCIC SEARCH",
	localSearch: "LOCAL SEARCH",
	globalSearch: "GLOBAL SEARCH",
	manualEntry: "MANUAL ENTRY",
	recentList: "RECENT LIST",
	country: "Country",
	unitID: "Unit ID",
	attemptedCompleted: "Attempted/Completed",
	misdFelony: "Misd/Felony",
	searchbyName: "Search by Name",
	involvementTags: "Involvement Tags",
	victimConnectedToOffense: "Victim Connected To Offense",
	offenseConnectedToVictim: "Offense Connected To Victim",
	victim: "Victim",
	victims: "Victim(s)",
	selectPerson: "Select Person",
	selectViolation: "Select Violation",
	checkAllVictims: "Check all victims against whom this offense was attempted or committed",
	personVictimDeleteMsg: "Are you sure you want to delete this person victim ?",
	warningMsgForDelete: "This person is associated with multiple violations. Please remove the person from the violation before deleting.",
	noViolation: "No offense is currently available to link. You need to create an offense first.",
	invalidHeightEntry: "Invalid height entry",
	heightFeet: "{{feet}} ft",
	heightFeetInches: "{{feet}} ft {{inches}} in",
	heightInches: "{{inches}} in",
	brand: "Brand",
	recoveryCondition: "Recovery Condition",
	prescriptionNumber: "Prescription Number",
	evidenceNumber: "Evidence Number",
	agencyExhibitNumber: "Agency/Case Exhibit Number",
	markLostFound: "Mark as Lost/Found Item",
	victimSuspectInfo: "The Victim's relationship to each suspect/offender is reported when the victim was the object of a Crime Against Person, i.e. Assault Offense, Homicide Offense, Kidnaping/Abduction, Forcible Sex Offense, or Nonforcible Sex Offense",
	victimRelationship: "Victim Relationship",
	victimSuspectRelationship: "Victim To Suspect Relationship(s)",
	relationship: "Relationship",
	suspect: "Suspect",
	addressNoPrefix: "Address No Prefix",
	streetNumberOrMileMarker: "Street Number Or Mile Marker",
	addressInfo: "Address Info",
	addrNoPrefix: "Addr No Prefix",
	numberSuffix: "Number Suffix",
	preMod: "Pre-Mod",
	direction: "Direction",
	preType: "Pre-Type",
	preSep: "Pre-Sep",
	streetNameFull: "Street Name Full",
	postMod: "Post Mod",
	milePost: "Mile Post",
	site: "Site",
	subStie: "Sub Site",
	structure: "Structure",
	wing: "Wing",
	streetDirOfTravel: "Street Dir of Travel",
	unitPreType: "Unit Pre Type",
	unitValue: "Unit Value",
	room: "Room",
	section: "Section",
	row: "Row",
	seat: "Seat",
	personChangeHistory: "Person Change History",
	personInvolmentTagsChangeHistory: "Person Involvement Tags History",
	vehicleChangeHistory: "Vehicle Change History",
	victimCircumstances: "Victim Circumstances",
	noCircumstancesRequired: "No circumstances are required for this offense",
	homicideAssaultCircumstances: "Homicide Assault Circumstances",
	negligentManslaughterCircumstances: "Negligent Manslaughter Circumstances",
	justifiableHomicideCircumstances: "Justifiable Homicide Circumstances",
	circumstances: "Circumstances",
	additionalJustifiableHomicideCircumstances: "Additional Justifiable Homicide Circumstances",
	justifiableHomicideFactor: "Justifiable Homicide Factor",
	cityPostalComm: "City (PostalComm)",
	postalCommunity: "Postal Community",
	postalCode: "Postal Code",
	zipPostCode: "Zip (PostCode)",
	zipPlus: "Zip+",
	country: "Country",
	injuryType: "Injury Type",
	injury: "Injury",
	socialMedia: "Social Media",
	userName: "User Name/ ID",
	platform: "Platform",
	customPlatform: "Enter Custom Platform Name",
	customUrl: "Custom URL",
	suspectVictimRelationship: "Suspect To Victim Relationship(s)",
	createMasterOrganizationSaveSuccess: "Master Organization created successfully!",
	createMasterOrganizationSaveFailed: "Error while creating Master Organization!",
	masterOrganizationUpdateSuccess: "Master Organization updated successfully!",
	masterOrganizationUpdateFailed: "Error while updating Master Organization!",
	searchOrganizations: "Search Organizations",
	businessName: "Business Name",

	incidentFileDeleteMsg: "Are you sure you want to delete this incident file ?",
	incidentNoteDeleteMsg: "Are you sure you want to delete this incident narrative?",
	incidentPropertyDeleteMsg: "Are you sure you want to delete this incident Property ?",
	incidentCommunicationDeleteMsg: "Are you sure you want to delete this incident Communication Logs ?",
	incidentSecurityDeleteMsg: "Are you sure you want to delete this incident Security ?",
	incidentViolationDeleteMsg: "Are you sure you want to delete this incident Violation ?",
	incidentOrganizationDeleteMsg: "Are you sure you want to delete this incident Organization ?",
	deleteIncidentDetail: "Delete Incident Detail",
	personChangeHistory: "Person Change History",
	addCitation: "Add Citation",
	citiationEndMsg: "No more Citations",
	citation: "Citation",
	citationDeleteMsg: "Are you sure you want to delete this citation ?",
	citationViolationDeleteMsg: "Are you sure you want to delete this citation violation ?",
	citationPersonDeleteMsg: "Are you sure you want to delete this citation person ?",
	citationVehicleDeleteMsg: "Are you sure you want to delete this citation vehicle ?",
	citationLocation: "Citation Location",
	citationInformation: "Citation Information",
	addNewCitation: "Add New Citation",
	counts: "Counts",
	vehicleChangeHistory: "Vehicle Change History",
	streetAddress: "Street Address",
	address2: "Address 2",
	smtType: "Type",
	smtLocation: "Location",
	smtDescription: "Description",
	smt: "SMT",
	stateProvince: "State / Province",
	incidentReport: "INCIDENT REPORT",
	reportDate: "Report Date",
	reportNumber: "Report Number",
	noReportNumber: "No Report Number",
	drugs: "Drugs",
	alcohol: "Alcohol",
	computer: "Computer",
	officerAssignment: "Officer Assignment",
	leoka: "LEOKA",
	basicDescription: "Basic Description",
	changeNcicPassword: "Change NCIC Password",
	ncicPasswordExpired: "NCIC Password Expired",
	currentPassword: "Current Password",
	newPassword: "New Password",
	confirmPassword: "Confirm Password",
	newPasswordRequired: "New Password is required",
	passwordsDoNotMatch: "Passwords do not match",
	info: "Info",
	personAlert: "Person	Alerts",
	bannedPersonRegistry: "Banned Persons Registry", 
	securityManagementDashboard: "Security Management Dashboard", 
	nobannedPersonFound: "No banned person found.", 
	genderRace: "Gender / Race",
	banStartDate: "Ban Start Date",
	banExpires: "Ban Expires",
	bannedBy: "Banned By",
	bannedLocation: "Banned Location",
	noAddressOnFile: "No address on file",
	remoteSource: "Remote Source",
	lifetimeBan: "Lifetime Ban",
	civilPapersAlert: "Civil Papers Alert",
	noCivilPapersFound: "No civil papers found.",
	tracking: "Tracking",
	docket: "Docket",
	paperType: "Paper Type",
	courtDate: "Court Date",
	requester: "Requester",
	plaintiff: "Plaintiff",
	defendant: "Defendant",
	targetDOB: "Target DOB",
	targetRaceSex: "Target Sex / Race",
	inmatesRegistry: "Inmates Registry",
	noInmatesFound: "No inmates found.",
	booking: "Booking",
	bookingDate: "Booking Date",
	charges: "Charges",
	protectionOrdersRegistry: "Protection Orders Registry",
	noProtetionOrdersFound: "No protection orders found.",
	order: "Order",
	orderDate: "Order Date",
	dobSexRace: "DOB / Sex / Race",
	nextCourtDate: "Next Court Date",
	dateServed: "Date Served",
	home: "Home",
	work: "Work",
	taggedPersons: "Tagged Persons",
	homeAddress: "Home Address",
	workAddress: "Work Address",
	contactRace: "Contact Race",
	contactSex: "Contact Sex",
	contactDL: "Contact DL",
	activeWarrantRegistry: "Active Warrants Registry",
	warrantStatus: "Warrant Status",
	issuedDate: "Issued Date",
	activeWarrantRegistry: "Active Warrants Registry",
	dlState: "DL / State",
	genderRace: "Gender / Race",
}

export default locale;
