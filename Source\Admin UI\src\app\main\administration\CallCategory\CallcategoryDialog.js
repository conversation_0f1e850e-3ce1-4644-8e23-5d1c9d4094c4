import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { Autocomplete, FormControl, Grid, TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { showMessage } from 'app/store/fuse/messageSlice';
import { saveCallCategory } from '../store/callCategorySlice';

let update = false;

const CallCategoryDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const fileUpload = useRef(null);
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [agencyCode, setAgencyCode] = React.useState(null);
    const isloadingvalue = useSelector(({ administration }) => administration.callCategorySlice.isloading);
    const callCategories = useSelector(({ agency }) => agency.agency.callCategories);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
    });

    const { isValid, dirtyFields, errors } = formState;
    const [callCategoryValue, setCallCategoryValue] = React.useState([]);
    const [callCategoryType, setCallCategoryType] = React.useState([]);
    const handleClickOpen1 = () => {
        setOpen(true);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code) {
            setAgencyCode(code)
            removeDuplicates(data, callCategories)
            handleClickOpen1();
        },
    }));

    const handleClose = () => {
        setOpen(false);
        setCallCategoryValue([])
    };

    function onSubmit(model) {
        debugger;
        dispatch(saveCallCategory(callCategoryValue, agencyCode))
    }

    //For FIRE and EMS Call Type
    // useEffect(() => {
    //     if (callCategories.length > 0) {


    //         removeDuplicates(existCallCategories, callCategories)
    //     }
    // }, [existCallCategories]);


    useEffect(() => {
        handleClose()

    }, [isloadingvalue]);



    function removeDuplicates(existCallCategories, callCategories) {
        debugger
        // Create a Set of IDs from the first array
        const firstIds = new Set(existCallCategories.map(item => item.CallCategoryName));
        callCategories = callCategories.filter(item => !firstIds.has(item.CallCategoryName));

        setCallCategoryType(callCategories.map((x) => ({
            value: x._id,
            label: x.CallCategoryName,
            CallCategoryName: x.CallCategoryName,
            CallCategoryID: x.CallCategoryID,
            CallCategoryBranch: x.CallCategoryBranch,
            IconURL: x.IconURL,
        })));
    }

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("callCategory")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        name="registerForm"
                        noValidate
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        // autoComplete="off"
                        autoSave={false}
                    >

                        <Grid container spacing={1} style={{ paddingBottom: '10px' }}>
                            <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ paddingBottom: '10px' }}>
                                <FormControl className="mr-8" fullWidth>
                                    <Controller
                                        control={control}
                                        name='callCategories'
                                        defaultValue={[]}
                                        render={({ field: { onChange, value } }) => (
                                            <Autocomplete
                                                multiple
                                                freeSolo
                                                options={callCategoryType}
                                                value={value}
                                                onChange={(event, newValue) => {
                                                    onChange(newValue);
                                                    setCallCategoryValue(newValue);
                                                }}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        placeholder={t("selectCallCategories")}
                                                        label={t("selectCallCategories")}
                                                        variant="outlined"
                                                        InputLabelProps={{
                                                            shrink: true,
                                                        }}
                                                    />
                                                )}
                                            />)
                                        }
                                    />
                                </FormControl>
                            </Grid>
                        </Grid>
                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );

});

export default CallCategoryDialog;