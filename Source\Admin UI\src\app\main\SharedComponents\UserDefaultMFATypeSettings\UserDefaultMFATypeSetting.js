import React, { useState, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import FormControl from "@mui/material/FormControl";
import FormLabel from "@mui/material/FormLabel";
import FormControlLabel from "@mui/material/FormControlLabel";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import { UserDefaultMfaTypeUpdate } from '../../../auth/store/userSlice';
import "./UserDefaultMFATypeSettings.css"


let mfaTypeValue = ""
function UserDefaultMFATypeSetting(props) {
  const { t } = useTranslation("laguageConfig");
  const dispatch = useDispatch();


  mfaTypeValue = props.mfaType;
  const handleMfaTypeChange = (event) => {
    let userId = localStorage.getItem('userId');
    if (!props.registerUser) {
      let mfaType = event.target.value;
      dispatch(UserDefaultMfaTypeUpdate({ mfaType, userId }));
      mfaTypeValue = mfaType;
    }
    else {
      props.mfaValue(event.target.value);
    }
  };

  return (
    <div>
      <FormLabel component="legend" >{t("defaultMFAType")}</FormLabel>
      <FormControl component="fieldset" sx={{zIndex: 0}}>
        <RadioGroup
          row
          aria-label="gender"
          name="mfaType"
          value={mfaTypeValue}
          onChange={handleMfaTypeChange}
        >
          <FormControlLabel
            value="email"
            control={<Radio />}
            label={t("email")}

          />

          <FormControlLabel
            value="authenticator"
            control={<Radio />}
            label={t("authenticator")}
          />

          <FormControlLabel
            value="text"
            control={<Radio />}
            label={t("text")}
          />
        </RadioGroup>
      </FormControl>
    </div>
  );
}

export default UserDefaultMFATypeSetting;
