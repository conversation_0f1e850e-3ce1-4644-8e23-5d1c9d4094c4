import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogTitle, Dialog, DialogActions, Switch } from '@mui/material';
import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import Icon from "@mui/material/Icon";
import { Controller, useForm } from 'react-hook-form';
import { TextField, Autocomplete } from "@mui/material";
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import Checkbox from '@mui/material/Checkbox';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import ListItemText from '@mui/material/ListItemText';
import { getUsers } from '../../administration/store/usersSlice';
import { saveUserShiftAllocationSchedule } from '../../store/shiftAllocationScheduleSlice';
import { showMessage } from 'app/store/fuse/messageSlice';

const defaultValues = {

};

const schema = yup.object().shape({

});

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
    PaperProps: {
        style: {
            maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
            width: 250,
        },
    },
};

function AddUserDialog(props) {
    const { onClose, open, ...other } = props;
    const dispatch = useDispatch();
    const user = useSelector(({ administration }) => administration.user.allusers);
    const [personName, setPersonName] = React.useState([]);
    const [teamValue, setTeamValue] = React.useState([]);
    const [shiftValue, setShiftValue] = React.useState([]);
    const [departmentName, setDepartmentName] = React.useState(props.filterdata[0].department[0].name);
    
    let UserListDeptData = user.filter(y => y.isSuperAdmin === false && y.department !== undefined && y.department !== null && y.department.length !== 0);
    let userlist = UserListDeptData.filter(y => y.department[0]._id === props.filterdata[0].department[0]._id);
    let teamsData = props.TeamData.filter(x => x.department[0]._id === props.filterdata[0].department[0]._id && x.code == props.agencyCode)
    let shiftData = props.shiftsArray.filter(x => x.department[0]._id === props.filterdata[0].department[0]._id)
    
    const [usersList, setUsersList] = React.useState(userlist);
    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues
    });

    const { isValid, dirtyFields, errors } = formState;
    const { t } = useTranslation("laguageConfig");

    useEffect(() => {
        dispatch(getUsers("fname", "asc", 0, 10000, false, "ALL"));
    }, []);

    const handleCancel = () => {
        onClose(false);
        setPersonName([])
        setTeamValue([])
        setShiftValue([])
        setUsersList(userlist)
    };

    const handleOk = () => {
        onClose(true);
        setPersonName([])
        setTeamValue([])
        setShiftValue([])
        setUsersList(userlist)
    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const getTime = (startDate, endDate, startTime, endTime, shiftDuration) => {
        const newStartDate = new Date(startDate);
        let x = startTime.split(':')
        let y = endTime.split(':')
        newStartDate.setHours(x[0]);
        newStartDate.setMinutes(x[1]);
        const newEndDate = new Date(endDate);
        newEndDate.setHours(new Date(newStartDate).getHours() + parseInt(shiftDuration));
        newEndDate.setMinutes(y[1]);
        let data = {
            newStartDate,
            newEndDate
        }
        return data
    }

    function onSubmit(model) {
        let shift = shiftData.filter(x => x._id == shiftValue);
        let team = teamsData.filter(x => x._id == teamValue);
        let users = personName.map((item) => userlist.filter((x => x.fname + ' ' + x.lname === item))[0])
        let scheduleType = props.DepartmentData.filter(x => x._id == props.filterdata[0].department[0]._id)[0].shiftType[0].code
        let dates = getTime(props.dates[0].startDate, props.dates[0].endDate, shift[0].StartTime, shift[0].EndTime, shift[0].ShiftDuration.split(' ')[0])
        let mainData = []
        let filterArray = []
        let dragData1 = props.ScheduleAllocationData.filter(x => new Date(x.startDateTime).toLocaleDateString() == new Date(props.dates[0].startDate).toLocaleDateString() && new Date(x.endDateTime).toLocaleDateString() == new Date(props.dates[0].endDate).toLocaleDateString())
        let flag = personName.map((item) => dragData1.filter((x => x.userName === item && x.shiftId == shiftValue))[0])
        flag.filter(x => x !== undefined ? filterArray.push(x) : [])
        if (filterArray.length == 0) {
            if (users.length > 0) {
                users.forEach(x => {
                    let Data = {
                        startDateTime: new Date(dates.newStartDate),
                        endDateTime: new Date(dates.newEndDate),
                        userID: x._id,
                        userName: x.fname + ' ' + x.lname,
                        userEmail: x.email,
                        scheduleType: scheduleType,
                        scheduleAllocationID: props.scheduleAllocationID,
                        teamName: team[0].name,
                        teamId: team[0]._id,
                        shiftcode: shift[0].shiftcode,
                        shiftDuration: shift[0].ShiftDuration.split(' ')[0],
                        shiftId: shift[0]._id,
                        ShiftName: shift[0].ShiftName,
                        shiftStartTime: shift[0].StartTime,
                        shiftEndTime: shift[0].EndTime,
                        department: props.filterdata[0].department[0],
                        shiftColor: shift[0].color,
                        CreatedBy: new Date()
                    }
                    mainData.push(Data)
                })
            }
            dispatch(saveUserShiftAllocationSchedule(mainData, props.code));
            handleOk(false)
        }
        else {
            setPersonName([]);
            ShowErroMessage(t("UserExist"));
        }
    }

    const handleChange = (event) => {
        let dragData1 = props.ScheduleAllocationData.filter(x => new Date(x.startDateTime).toLocaleDateString() == new Date(props.dates[0].startDate).toLocaleDateString() && new Date(x.endDateTime).toLocaleDateString() == new Date(props.dates[0].endDate).toLocaleDateString())
        let flag = dragData1.filter(x => x.userName == event.target.value[0] && x.shiftId == shiftValue)
        if (flag.length == 0) {
            setPersonName(event.target.value);
        }
        else {
            setPersonName([]);
            ShowErroMessage(t("UserExist"));
        }
    };

    const handleTeamChange = (event) => {
        setTeamValue(event.target.value)
        if (event.target.value) {
            let x = teamsData.filter(x => x._id == event.target.value)
            let foundUser = [];
            if (x[0].users.length > 0) {
                for (let user of x[0].users) {
                    let a = userlist.filter(u => u._id === user.value)
                    if (a.length > 0)
                        foundUser.push(userlist.filter(u => u._id === user.value)[0])
                    user = null
                    setPersonName([]);
                }
                setUsersList(foundUser)
            }
        }
    };

    const handleShiftChange = (event) => {
        setShiftValue(event.target.value)
    };

    return (
        <Dialog style={{ marginBottom: props.marginBottom }}
            fullWidth={true}
            maxWidth='md'
            aria-labelledby="responsive-dialog-title"
            open={open}
            {...other}>
            <IconButton
                onClick={handleCancel}
                aria-label="show more"
                className='flex justify-end mr-14 ml-[94%]'
            >
                <Icon>close</Icon>
            </IconButton>
            <DialogTitle id="responsive-dialog-title">{t("newUser")}</DialogTitle>
            <DialogContent dividers>
                <form
                    name="registerForm"
                    className="flex flex-col justify-center w-full pb-16"
                    onSubmit={handleSubmit(onSubmit)}
                    autoSave={false} >
                    <Controller
                        name="Department"
                        control={control}
                        render={({ field }) => (
                            <TextField
                                {...field}
                                className="mb-16 w-full"
                                label={t("department")}
                                type="text"
                                value={departmentName}
                                // error={!!errors.type}
                                // helperText={errors?.type?.message}
                                variant="outlined"
                                disabled={true}
                            />
                        )}
                    />

                    <FormControl fullWidth sx={{ mb: 2, minWidth: 120 }} required>
                        <InputLabel id="demo-simple-select-label"> {t("shift")}</InputLabel>
                        <Select
                            name="Shift"
                            label={t("shift")}
                            inputProps={{ 'aria-label': 'Without label' }}
                            value={shiftValue}
                            onChange={handleShiftChange}
                        >

                            {shiftData.sort((a, b) => a.ShiftName.localeCompare(b.ShiftName)).map((element) => (
                                <MenuItem key={element.ShiftName} value={element._id}>{element.ShiftName}</MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <FormControl fullWidth sx={{ mb: 2, minWidth: 120 }} required>
                        <InputLabel id="demo-simple-select-label"> {t("team")}</InputLabel>
                        <Select
                            name="Team"
                            label={t("team")}
                            inputProps={{ 'aria-label': 'Without label' }}
                            value={teamValue}
                            onChange={handleTeamChange}
                        >

                            {teamsData.sort((a, b) => a.name.localeCompare(b.name)).map((element) => (
                                <MenuItem key={element.name} value={element._id}>{element.name}</MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <FormControl className="mb-16 w-full" fullWidth sx={{ minWidth: 80 }} required>
                        <InputLabel id="demo-multiple-checkbox-label">{t("users")}</InputLabel>
                        <Select
                            labelId="demo-multiple-checkbox-label"
                            id="demo-multiple-checkbox"
                            multiple
                            value={personName}
                            onChange={handleChange}
                            input={<OutlinedInput label={t("users")} />}
                            renderValue={(selected) => selected.join(', ')}
                            MenuProps={MenuProps}
                        >
                            {usersList.map((x) => (
                                <MenuItem key={x.fname + ' ' + x.lname} value={x.fname + ' ' + x.lname}>
                                    <Checkbox checked={personName.indexOf(x.fname + ' ' + x.lname) > -1} />
                                    <ListItemText primary={x.fname + ' ' + x.lname} />
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <div className='flex justify-end'>
                        <Button
                            variant="contained"
                            color="primary"
                            className=" w-auto mr-16 mt-10"
                            aria-label="Register"
                            type="submit"
                            size="large"
                        >
                            {t("save")}
                        </Button>
                        <Button
                            variant="contained"
                            color="secondary"
                            className=" w-auto mt-10"
                            aria-label="Register"
                            type="button"
                            size="large"
                            onClick={handleCancel}
                        >
                            {t("cancel")}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
}

export default AddUserDialog;
