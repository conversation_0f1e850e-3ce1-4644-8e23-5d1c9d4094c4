import React, { forwardRef } from "react";
import { Autocomplete, TextField } from "@mui/material";
import { makeStyles } from "@mui/styles";

const useStyles = makeStyles((theme) => ({
    formControl: {
        minWidth: 120,
        width: "100%",
    },
    selectEmpty: {
        marginTop: theme.spacing(2),
    },
    paperHeight: {
        maxHeight: 'none',
        overflowY: 'visible',
    },
    listbox: {
        maxHeight: 230,
        overflowY: 'auto',
    },
    backdrop: { zIndex: theme.zIndex.drawer + 1, color: '#fff', },
}));

export const handleSelectKeyDown = (event, data, optionLabel, handleChange) => {
    // Only handle Tab key specifically
    if (event.key === 'Tab') {
        const listboxNode = document.querySelector('[role="listbox"]');

        // Ensure the dropdown is open and get the active option
        if (listboxNode) {
            const activeOption = listboxNode.querySelector('[class="MuiAutocomplete-option Mui-focused Mui-focusVisible"]');

            if (activeOption) {
                // Find the selected option
                const selectedItem = data.find(option =>
                    option[optionLabel] === activeOption.innerText.trim() // Use trim() to avoid leading/trailing spaces
                );

                if (selectedItem) {
                    // Trigger selection
                    handleChange(null, selectedItem);
                }
            }
        }
        // Allow the default behavior (move to the next input field)
    }
}


const CommonAutocomplete = forwardRef((props, ref) => {
    const classes = useStyles();

    const handleOnChange = (event, newValue) => {
        props.parentCallback(event, newValue);
    }

    const handleInputChange = (event, inputValue) => {
        if (!inputValue) return;

        // Convert inputValue to string to ensure compatibility
        const inputStr = inputValue.toString().toLowerCase();

        // Find all matching options
        const matchedOptions = props.options.filter(option => {
            const optionLabel = option[props.optionLabel];

            // Convert to string safely before comparison
            return optionLabel.toString().toLowerCase().startsWith(inputStr);
        });

        // Only autofill if there's exactly one unique match
        if (Array.isArray(matchedOptions) && matchedOptions.length === 1) {
            handleOnChange(null, matchedOptions[0]); // Autofill with the unique match
        }
    };

    return (
        <Autocomplete
            fullWidth={true}
            disablePortal={false}
            disabled={props.disabled}
            id="commonAutocomplete"
            onChange={handleOnChange}
            onInputChange={handleInputChange}
            filterOptions={(options, { inputValue }) => {
                return options
                    .filter((option) => {
                        const label = props.getOptionLabelFn
                            ? props.getOptionLabelFn(option)
                            : option[props.optionLabel]?.toString?.() || '';

                        return label.toLowerCase().includes(inputValue.toLowerCase());
                    })
                    .slice(0, 100);
            }}
            options={props.options ? props.options : []}
            value={props.value ? props.value : null}
            classes={{ paper: classes.paperHeight, listbox: classes.listbox }}
            getOptionLabel={(option) => {
                if (props.getOptionLabelFn) {
                    return props.getOptionLabelFn(option);
                }
                return option[props.optionLabel]?.toString?.() || '';
            }}
            // isOptionEqualToValue={(option, value) => option.id === value.id}
            onKeyDown={(event) => {
                props.onKeyDown(event, props.options, props.optionLabel, handleOnChange);
            }}
            onOpen={props.onOpen} // Handle dropdown open
            onClose={props.onClose} // Handle dropdown close
            open={props.open}
            renderInput={(params) =>
                <TextField
                    {...params}
                    label={props.fieldName}
                    ref={ref}
                    InputProps={{
                        ...params.InputProps,
                        disableUnderline: true, // Optionally customize the input's underline
                    }}
                    required={props.required}
                />
            }
        />
    )
})


export default CommonAutocomplete;