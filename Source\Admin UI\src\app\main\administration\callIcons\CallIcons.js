import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FusePageCarded from '@fuse/core/FusePageCarded';
import { motion } from 'framer-motion';
import { Table, TableHead, TableRow, TableCell, TableBody, TextField, TableFooter } from '@mui/material';
import FuseScrollbars from '@fuse/core/FuseScrollbars';
import { uploadCallIcon, getDispatchIcons } from '../store/callIconSlice';
import { useTranslation } from "react-i18next";
import { newUserAudit } from '../../../main/userAuditPage/store/userAuditSlice';

function CallIcons() {

	const { t } = useTranslation('laguageConfig');
	const dispatch = useDispatch();
	const user = useSelector(({ auth }) => auth.user);
	const iconsDetails = useSelector(({ administration }) => administration.callIconUploadSlice.iconsData)
	const [file, setFile] = React.useState([]);
	const [callType, setSelectedType] = React.useState("");
	const [selectedFile, setSelectedFile] = React.useState({});
	const [newCallType, setNewCallType] = React.useState(false);
	const [newCallText, setNewCalltext] = React.useState("");

	useEffect(() => {
		dispatch(newUserAudit({
			activity: "Access Dispatch Call Icons",
			user: user,
			appName: "Admin",
		}));
	}, []);


	async function uploadIcon(e) {
		var formData = new FormData();
		formData.append("file", file[0]);
		await dispatch(uploadCallIcon(formData, e.callType));
		dispatch(getDispatchIcons())
		setSelectedType("");
	}

	function onFileChange(e, type) {

		setFile(e.target.files)
		setSelectedFile(e.target.files[0])
		setSelectedType(type);
	}
	function clearFile(e) {
		setFile({})
		setSelectedFile({})
		setSelectedType("");
	}
	function onRowAddchange() {
		setNewCallType(!newCallType)
		setSelectedType("new")
	}
	useEffect(() => {
		if (iconsDetails.length === 0)
			dispatch(getDispatchIcons())
	}, [dispatch, iconsDetails, callType, newCallType, selectedFile]);
	// function disableButton() {
	// 	//setIsFormValid(false);
	// }

	return (
		<FusePageCarded
			classes={{
				content: 'flex',
				header: 'min-h-72 h-72 sm:h-136 sm:min-h-136'
			}}
			header={
				<div className="flex flex-1 w-full items-center justify-between">
					<div className="flex items-center">
						<Icon
							component={motion.span}
							initial={{ scale: 0 }}
							animate={{ scale: 1, transition: { delay: 0.2 } }}
							className="text-32">computer</Icon>
						<Typography
							component={motion.span}
							initial={{ x: -20 }}
							animate={{ x: 0, transition: { delay: 0.2 } }}
							delay={300}
							className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
							{t("dispatchCallIcons")}
						</Typography>
					</div>
				</div>
			}
			content={<div className="w-full flex flex-col">
				<FuseScrollbars className="flex-grow overflow-x-auto">
					<Table stickyHeader className="min-w-xl" aria-labelledby="tableTitle">
						<TableHead>
							<TableRow className="h-64">
								<TableCell className="p-4 md:p-16">
									{t("callCategory")}
								</TableCell>
								<TableCell className="p-4 md:p-16">
									{t("upload")}
								</TableCell>
								<TableCell className="p-4 md:p-16">
									{t("preview")}
								</TableCell>
								<TableCell className="p-4 md:p-16">
									{t("action")}
								</TableCell>
							</TableRow>
						</TableHead>

						<TableBody>
							{iconsDetails.map(n => {
								return (
									<TableRow
										className="h-64 pl-8"
									>
										<TableCell className="w-52 px-4 pl-16"
											component="th"
											scope="row"
											padding="none">
											{n.callType}

										</TableCell>
										<TableCell className="w-52 px-4 pl-16"
											component="th"
											scope="row"

											padding="none">
											<input accept="image/*" id={n.callType} type="file"
												onChange={(e) => onFileChange(e, n.callType)}
												style={{ display: "none" }} />
											<label htmlFor={n.callType} >
												<IconButton color="primary" aria-label="upload picture" component="span" size="large">
													<Icon>cloud_upload</Icon>
												</IconButton>
												{callType === n.callType && selectedFile.name}
											</label>

										</TableCell>
										<TableCell className="w-52 px-4 pl-16"
											component="th"
											scope="row"
											padding="none">
											<img className="w-52 block rounded"
												src={(callType === n.callType && selectedFile) ? URL.createObjectURL(selectedFile) : n.iconURL}
												alt="iconImage"
											/>

										</TableCell>
										<TableCell className="w-52 px-4 pl-16"
											component="th"
											scope="row"
											padding="none">
											<Button type="button"
												variant="contained"
												className="normal-case m-16"
												aria-label="Back"
												color="secondary"
												disabled={callType !== n.callType}
												onClick={() => uploadIcon(n)}
											>{("Update")}</Button>
											{callType === n.callType && <Button type="button"
												variant="contained"
												className="normal-case m-16"
												aria-label="Back"
												color="primary"
												onClick={() => clearFile(n)}
											>{t("cancel")}</Button>}
										</TableCell>

									</TableRow>
								);

							})}
							{newCallType && <TableRow
								className="h-64 pl-8"
							>
								<TableCell className="w-52 px-4 pl-16"
									component="th"
									scope="row"
									padding="none">
									<TextField
										className="mb-16 pt-8"
										type="text"
										variant="outlined"
										onChange={(e) => setNewCalltext(e.target.value)}
										required
									/>
								</TableCell>
								<TableCell className="w-52 px-4 pl-16"
									component="th"
									scope="row"

									padding="none">
									<input accept="image/*" id="new" type="file"
										onChange={(e) => onFileChange(e, newCallText)}
										style={{ display: "none" }} />
									<label htmlFor="new" >
										<IconButton color="primary" aria-label="upload picture" component="span" size="large">
											<Icon>cloud_upload</Icon>
										</IconButton>
										{selectedFile.name}
									</label>

								</TableCell>
								<TableCell className="w-52 px-4 pl-16"
									component="th"
									scope="row"
									padding="none">
									{file.length > 0 && <img
										className="w-52 block rounded"
										src={URL.createObjectURL(selectedFile)}
										alt="iconImage"
									/>}

								</TableCell>
								<TableCell className="w-52 px-4 pl-16"
									component="th"
									scope="row"
									padding="none">
									<Button type="button"
										variant="contained"
										className="normal-case m-16"
										aria-label="Back"
										color="secondary"
									>Update</Button>
									<Button type="button"
										onClick={(n) => clearFile(n)}
										variant="contained"
										className="normal-case m-16"
										aria-label="Back"
										color="primary"
									>Cancel</Button>
								</TableCell>
							</TableRow>}
						</TableBody>
						<TableFooter>
							<IconButton
								type="button"
								variant="contained"
								className="normal-case m-16"
								aria-label="Back"
								color="secondary"
								onClick={onRowAddchange}
								size="large"><Icon>add_circle</Icon></IconButton>

						</TableFooter>
					</Table>

				</FuseScrollbars>
			</div>}
			innerScroll
		/>
	);
}

export default CallIcons;

