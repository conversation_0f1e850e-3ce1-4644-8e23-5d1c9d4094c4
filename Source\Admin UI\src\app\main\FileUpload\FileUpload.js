import './fileUpload.css';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FusePageSimple from '@fuse/core/FusePageSimple';
import makeStyles from "@mui/styles/makeStyles";

import withStyles from "@mui/styles/withStyles";

//import { withStyles } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

import Dialog from '@mui/material/Dialog';
import MuiDialogTitle from '@mui/material/DialogTitle';
import MuiDialogContent from '@mui/material/DialogContent';
import MuiDialogActions from '@mui/material/DialogActions';
import FileViewer from 'react-file-viewer';
import { useTranslation } from 'react-i18next';
import {
	uploadFileObject,
	getSavedFileObj,
	uploadFileObjList,
	getSavedFileObjList
} from './store/fileUploadSlice';

import { newUserAudit } from '../../main/userAuditPage/store/userAuditSlice';

import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';


const useStyles = makeStyles(theme => ({

	layoutRoot: {},
	root: {
		flexGrow: 1,
	},
	cardRoot: {
		minWidth: 275
	},
	title: {
		fontSize: 18,
		fontStyle: 'bold',
		textAlign: 'center',
	},
	formControl: {
		margin: theme.spacing(0.5),
		width: '100%',
	},
	selectEmpty: {
		marginTop: theme.spacing(2),
	},
	closeButton: {
		color: theme.palette.grey[500],
		position: 'relative',
		float: 'right',
		top: '2px',
		right: '10px',
		padding: '0px'

	},
	input: {
		display: 'none',
	},
}));


const controlStyle = {
	padding: 0,
	width: '305px',
	height: '250px'
};
const controlStyleAudio = {
	padding: 0,
	width: '305px',
	height: '50px',
};
const controlStyleSavedList = {
	padding: 20,
	textAlign: 'center',
	cursor: 'pointer'
};

const styles = (theme) => ({
	root: {
		margin: 0,
		padding: theme.spacing(2),
	},
	closeButton: {
		position: 'absolute',
		right: theme.spacing(1),
		top: theme.spacing(1),
		color: theme.palette.grey[500],
	},
});

const DialogTitle = withStyles(styles)((props) => {
	const { children, classes, onClose, ...other } = props;
	return (
		<MuiDialogTitle disableTypography className={classes.root} {...other}>
			<Typography variant="h6">{children}</Typography>
			{onClose ? (
				<IconButton aria-label="close" className={classes.closeButton} onClick={onClose}>
					<CloseIcon />
				</IconButton>
			) : null}
		</MuiDialogTitle>
	);
});

const DialogContent = withStyles((theme) => ({
	root: {
		padding: theme.spacing(2),
		width: 'auto',
		height: 'auto'
	},
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
	root: {
		margin: 0,
		padding: theme.spacing(1),
	},
}))(MuiDialogActions);

var fileObjFinal = [];
var fileArray = [];

function FileUploadPage(props) {
	const classes = useStyles(props);
	const { t } = useTranslation('laguageConfig');

	const dispatch = useDispatch();
	const user = useSelector(({ auth }) => auth.user);
	
	const navbarTheme = useSelector(selectNavbarTheme);

	const savedFileSuccess = useSelector(({ fileUpload }) => fileUpload.fileUpload.success);
	const savedFileObj = useSelector(({ fileUpload }) => fileUpload.fileUpload.savedFileObj);
	const savedFileView = useSelector(({ fileUpload }) => fileUpload.fileUpload.savedFileView);
	const uploadFileObj = useSelector(({ fileUpload }) => fileUpload.fileUpload.uploadFileObj);

	useEffect(() => {
		dispatch(newUserAudit({
			activity: "Access File Upload/Preview",
			user: user,
			appName: "Admin",
		}));
		// eslint-disable-next-line
	}, []);

	useEffect(() => {
		dispatch(getSavedFileObjList());
		if (savedFileSuccess) {
			fileArray = Object.assign([], fileArray);
			fileArray = [];
			fileObjFinal = Object.assign([], fileObjFinal);
			fileObjFinal = [];
			dispatch(uploadFileObject([]));
		}
	}, [dispatch, savedFileSuccess]);

	useEffect(() => {
		if (savedFileView.length !== 0) {
			setOpen(true);
		}
	}, [savedFileView]);

	function uploadMultipleFiles(e) {
		if (e.target.files) {
			for (let i = 0; i < e.target.files.length; i++) {
				let temp = { url: URL.createObjectURL(e.target.files[i]), type: e.target.files[i].type };
				fileArray = Object.assign([], fileArray);
				fileArray.push(temp);
				fileObjFinal = Object.assign([], fileObjFinal);
				fileObjFinal.push(e.target.files[i]);
			}
			dispatch(uploadFileObject([]));
			dispatch(uploadFileObject(fileArray));
		}
	}

	function removeFile(index) {
		fileArray = Object.assign([], fileArray);
		fileArray.splice(index, 1);
		fileObjFinal = Object.assign([], fileObjFinal);
		fileObjFinal.splice(index, 1);
		dispatch(uploadFileObject([]));
		dispatch(uploadFileObject(fileArray));
	}

	const [open, setOpen] = React.useState(false);

	const handleClickOpen = (Key) => {
		dispatch(getSavedFileObj(Key));
	};
	const handleClose = () => {
		setOpen(false);
		dispatch(getSavedFileObj([]));
	};

	function uploadFiles(e) {
		e.preventDefault()
		//code to upload file to server
		var file = fileObjFinal;
		if (file.length > 0) {
			var formData = new FormData();
			formData.append("file", file[0]);
			dispatch(uploadFileObjList(formData));
		}
	}

	const nav_css = {
		backgroundColor: navbarTheme.palette.primary.main,
		color: navbarTheme.palette.primary.contrastText,
	}

	return (
		<FusePageSimple
			classes={{ root: classes.layoutRoot }}
			// header={
			// 	<div className="p-24">
			// 		<h4>{t('title')}</h4>
			// 	</div>
			// }
			content={
				<div className="p-4">
					<Grid container spacing={1}>
						<Grid item xs={12}>
							<Card className={classes.cardRoot} variant="outlined">
								<CardContent style={nav_css} className='p-13'>
									<Typography className={classes.title} variant="h3">
										{t('fileUpload')}
									</Typography>
								</CardContent>
							</Card>
						</Grid>

						<Grid item xs={0} sm={2} md={2} lg={2} xl={2}>
						</Grid>

						<Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
							<input
								className={classes.input}
								id="contained-button-file"
								multiple
								type="file"
								onChange={uploadMultipleFiles}
							/>
							<label htmlFor="contained-button-file">
								<Button
									variant="contained"
									// color="default"
									component="span"
									className={classes.button}
									startIcon={<CloudUploadIcon />}
									style={{ width: '100%' }}
								>
									{t('selectFiles')}
								</Button>
							</label>

						</Grid>
						<Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
							{
								uploadFileObj.length === 0 ?
									<p>{t('noFileSelectedMsg')}</p>
									:
									uploadFileObj.length === 1 ?
										<p>{t('fileIsSelected')}</p>
										:
										<p>{uploadFileObj.length} {t('filesSelectedMsg')}</p>
							}
						</Grid>
						<Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
							<Button variant="contained" color="secondary" style={{ width: '100%' }} onClick={uploadFiles}>
								{t('upload')}
							</Button>
						</Grid>

						<Grid item xs={0} sm={2} md={2} lg={2} xl={2}>
						</Grid>

						{uploadFileObj.map((url, index) => (
							<Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
								<div className='card'>
									<IconButton aria-label="close" className={classes.closeButton}>
										<CloseIcon onClick={() => removeFile(index)} />
									</IconButton>
									{url.type.includes('image') &&
										<img src={url.url} alt="..." style={controlStyle}></img>
									}
									{url.type.includes('pdf') &&
										<iframe src={url.url} style={controlStyle} title="iframe"></iframe>
									}
									{url.type.includes('video') &&
										<video src={url.url} controls style={controlStyle}></video>
									}
									{url.type.includes('audio') &&
										<audio src={url.url} controls style={controlStyleAudio}></audio>
									}
								</div>
							</Grid>
						))}

						<Grid item xs={12}>
							<Card className={classes.cardRoot} variant="outlined">
								<CardContent style={nav_css} className='p-13'>
									<Typography className={classes.title} variant="h3">
									{t('uploadedFiles')}
									</Typography>
								</CardContent>
							</Card>
						</Grid>
						{savedFileObj.map((file) => (
							<Grid item xs={12} sm={4} md={4} lg={4} xl={4}>
								<div className='card' style={controlStyleSavedList} onClick={() => handleClickOpen(file.Key)}>
									{file.Key}
								</div>
							</Grid>
						))}

						<Dialog fullScreen onClose={handleClose} aria-labelledby="customized-dialog-title" open={open}>
							<DialogTitle id="customized-dialog-title" style={nav_css} onClose={handleClose}>
								{t("fileViewer")}
							</DialogTitle>
							<DialogContent dividers>
								<div>
									{savedFileView.type === "image" &&
										// eslint-disable-next-line
										<img
											className="image"
											src={savedFileView.url}
											alt="no image"
										/>
									}
									{savedFileView.type === "video" &&
										<video
											src={savedFileView.url}
											controls>
										</video>
									}
									{savedFileView.type === "audio" &&
										<audio
											src={savedFileView.url}
											controls>
										</audio>
									}
									{savedFileView.type === "pdf" &&
										// <PDFViewer
										// 	document={{
										// 		url: savedFileView.url
										// 	}} />
										<FileViewer
											fileType="pdf"
											filePath={savedFileView.url} />
									}
									{savedFileView.type === "wordDoc" &&
										<FileViewer
											fileType="docx"
											filePath={savedFileView.url} />
									}
								</div>
							</DialogContent>
							<DialogActions>
								<Button autoFocus onClick={handleClose} color="primary">
									{t("close")}
								</Button>
							</DialogActions>
						</Dialog>

					</Grid>
				</div>
			}
		/>
	);
}

export default FileUploadPage;
