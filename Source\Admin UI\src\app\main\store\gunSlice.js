import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';


export const getGunDetails = (data) => async dispatch => {
    
    await axios.post(process.env.REACT_APP_GateWayAPI_URL_URL.concat(`admin/api/gunSearch/getgunbysearch`), { data })
        .then(response => {
            
            if (response.data.length > 0) {
                return dispatch(gunListSuccess(response.data))
            }
            else {
                return dispatch(gunListSuccess([]));
            }
        }).catch(error => {
            
            return dispatch(gunListSuccess([]));

        });

};

export const clearGunSearchData = () => dispatch => {
    return dispatch(gunSearchListSuccess([]))
}


export const getGunDetailsCopy = (data) => async dispatch => {
    
    await axios.post(process.env.REACT_APP_GateWayAPI_URL_URL.concat(`admin/api/gunSearch/getgunbysearchCopy`), { data })
        .then(response => {
            
            if (response.data.length > 0) {
                return dispatch(gunListSuccessCopy(response.data))
            }
            else {
                return dispatch(gunListSuccessCopy([]))
            }
        }).catch(error => {
            
            return dispatch(gunListSuccessCopy([]))
        });

};

export const clearGunSearchDataCopy = () => dispatch => {
    return dispatch(gunSearchListSuccessCopy([]))
}


const initialState = {
    gunsuccess: false,
    gundata: [],

    gunsuccessCopy: false,
    gundataCopy: [],
};


const gunSlice = createSlice({
    name: 'gun',
    initialState,
    reducers: {

        gunListSuccess: (state, action) => {
            
            state.gunsuccess = true;
            state.gundata = action.payload;
        },
        gunSearchListSuccess: (state, action) => {
            state.gunsuccess = true;
            state.gundata = [];
        },

        gunListSuccessCopy: (state, action) => {
            
            state.gunsuccessCopy = true;
            state.gundataCopy = action.payload;
        },
        gunSearchListSuccessCopy: (state, action) => {
            state.gunsuccessCopy = true;
            state.gundataCopy = [];
        },

    },
    extraReducers: {}
});

export const {
    gunListSuccess,
    gunSearchListSuccess,

    gunListSuccessCopy,
    gunSearchListSuccessCopy,

} = gunSlice.actions;

export default gunSlice.reducer;
