import Typography from '@mui/material/Typography';
import { useTranslation } from "react-i18next";
import Paper from '@mui/material/Paper';
import Button from '@mui/material/Button';
import JwtService from 'src/app/services/jwtService';

function invalidSession() {
    const { t } = useTranslation("laguageConfig");
    let invalidMessage = window.location.search.replace('?', '')

    setTimeout(function () {
        RedirectToLogin();
    }, 4000);

    function RedirectToLogin() {
        JwtService.logout()
    }

    return (
        <div className="flex flex-col flex-auto items-center sm:justify-center min-w-0">
            <Paper className="flex items-center w-full w-auto rounded-0 py-32 px-16 sm:p-48 sm:rounded-2xl sm:shadow">
                <div className="w-full mx-auto sm:mx-0">
                    <img className="w-48 mx-auto" src="assets/images/logo/logo.svg" alt="logo" />

                    <Typography className="mt-32 text-4xl font-extrabold whitespace-nowrap tracking-tight leading-tight text-center">
                        {invalidMessage === "TokenExpire" ? t("tokenExpireMsg") : t("invalidSessionMsg")}
                    </Typography>
                    <Typography className="flex justify-center mt-2 font-medium">
                        {t("redirecting")}
                    </Typography>

                    <Typography className="mt-32 text-md font-medium text-center" color="text.secondary">
                        <span>{t("goto")}</span>

                        <Button variant="text" onClick={() => RedirectToLogin()} color="secondary">{t("signIn")}</Button>
                    </Typography>
                </div>
            </Paper>
        </div>
    );
}

export default invalidSession;
