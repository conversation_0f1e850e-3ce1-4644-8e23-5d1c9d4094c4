import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import { decrypt, encrypt } from '../../../security/crypto';
import axios from 'axios';

export const saveCallCategory = (data, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/callCategory/addCallCategory/${code}`, encrypt(JSON.stringify(data)))
            .then(async res => {
                debugger;
                if (res.status == 200) {
                    res.data = await JSON.parse(decrypt(res.data));
                    dispatch(setLoading(false));
                    dispatch(getCallCategory("CallCategoryID", "asc", "0", "100", code));
                }
                else {
                    response = JSON.parse(decrypt(res.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: "Call category failed",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: "Call category failed",
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'

                }))
                return console.error(error);
            });

    }
    catch (e) {
        dispatch(setLoading(false));
        dispatch(showMessage({
            message: "Call category failed",
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
        return console.error(e.message);
    }
}

export const getCallCategory = (sortField, sortDirection, pageIndex, pageLimit, code) => async (dispatch) => {
    try {
        debugger;
        dispatch(setLoading(true));
        await axios.get(`admin/api/callCategory/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setCallCategoryTotalCount(listData.totalCount));
                    dispatch(setLoading(false));
                    return dispatch(setCallCategoryData(listData.callCategoryList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
            .catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const DeleteCategory = (categoryName, code, pageIndex, pageLimit, sortField, sortDirection,) => async dispatch => {
    try {
        debugger;
        dispatch(setLoading(true));
        await axios.delete(`admin/api/callCategory/${categoryName}/${code}`)
            .then(async response => {
                if (response.status == 200) {
                    //response = JSON.parse(decrypt(response.data));
                    dispatch(getCallCategory(sortField, sortDirection, pageIndex, pageLimit, code));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: "Call category removed successfully",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                }
                else {
                    //response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: "Failed",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const setActiveCategory = (categoryID, code) => async dispatch => {
    await axios.put(`admin/api/callCategory/setActiveCategory/` + categoryID + code)
        .then(async res => {
        })
}

//for searching
export const searchCallCategory = (searchText, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        axios.get(`admin/api/callCategory/searchCallcategories/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchCallCategories(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    success: false,
    callCategories: [],
    searchCallCategories: [],
    isloading: false,
};

const callCategorySlice = createSlice({
    name: 'administration/callCategory',
    initialState,
    reducers: {
        setCallCategoryData: (state, action) => {
            state.callCategories = action.payload;
        },
        setSearchCallCategories: (state, action) => {
            state.searchCallCategories = action.payload;
        },
        setCallCategoryTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
    },
    extraReducers: {}
});

export const {
    setCallCategoryData,
    setCallCategoryTotalCount,
    setLoading,
    setSearchCallCategories
} = callCategorySlice.actions;

export default callCategorySlice.reducer;