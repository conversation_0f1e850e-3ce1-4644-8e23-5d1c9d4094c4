import { useDispatch, useSelector } from "react-redux";
import { setSelectedCallData, getPacket911GroupCallList } from "../store/call911Slice";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import CallInfo from "./CallListComponet";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { TextField } from "@mui/material";
import Button from "@mui/material/Button";
import { useTranslation } from "react-i18next";
import React, { useState, useEffect, useRef } from "react";

function GroupComponent(props) {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const packet911CallsGroup = useSelector(({ call911 }) => call911.call911.group);
    const GeoradiusValue = localStorage.getItem('GeoradiusValue');
    const TimeValue = localStorage.getItem('TimeValue');
    const selectedCallData = useSelector(({ call911 }) => call911.call911.selectedCall);
    const [geoRadius, setGeoRadius] = React.useState((GeoradiusValue !== null && GeoradiusValue !== undefined) ? JSON.parse(GeoradiusValue) : 0.25);
    const [time, setTime] = React.useState((TimeValue !== null && TimeValue !== undefined) ? JSON.parse(TimeValue) : 20);
    const cityValue = localStorage.getItem("call911_selectedCity");
    const countyValue = localStorage.getItem("call911_selectedCounty");
    const zoneValue = localStorage.getItem("call911_selectedZone");
    const pdZoneValue = localStorage.getItem("call911_selectedPDZone");

    const radiusChangeHandler = (event) => {
        if (event.target.value) {
            localStorage.setItem("GeoradiusValue", event.target.value)
            setGeoRadius(event.target.value);
        }
    };

    const timeChangeHandler = (event) => {
        if (event.target.value) {
            localStorage.setItem("TimeValue", event.target.value)
            setTime(event.target.value);
        }
    };

    useEffect(() => {
        if (selectedCallData._id !== undefined) createGroup(selectedCallData);
    }, [selectedCallData, countyValue, cityValue, zoneValue, pdZoneValue]);

    const createCallGroup = () => {
        createGroup(selectedCallData);
    };

    function createGroup(call) {
        var tempCall = {};
        tempCall.Packety = parseFloat(call.Packety);
        tempCall.Packetx = parseFloat(call.Packetx);
        tempCall.dateTime = call.PacketCallDateTime.DateTimeObj;
        tempCall.geoRadius = geoRadius * 1609.34; //Convert radius from miles to meter.
        tempCall.minutes = time;
        tempCall.countyID = countyValue;
        tempCall.cityName = cityValue;
        tempCall.zoneName = zoneValue;
        tempCall.pdZoneName = pdZoneValue;
        tempCall.PacketCallingPhone = call.PacketCallingPhone;
        dispatch(getPacket911GroupCallList(tempCall));
    };

    return (
        <>
            <Card variant="outlined">
                <CardContent>
                    <Grid container spacing={1}>
                        <Grid item xs={6}>
                            <TextField
                                id="outlined-number"
                                label={t("geoRadius")}
                                type="number"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                variant="outlined"
                                value={geoRadius}
                                onChange={radiusChangeHandler}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <TextField
                                id="outlined-number"
                                label={t("time")}
                                type="number"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                variant="outlined"
                                value={time}
                                onChange={timeChangeHandler}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <Button
                                variant="contained"
                                color="secondary"
                                style={{ width: "100%" }}
                                onClick={createCallGroup}
                            >
                                {t("makeGroup")}
                            </Button>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>
            <div>
                <Card variant="outlined">
                    <CardContent>
                        <Typography variant="body2" component="p">
                            {t("group1")}
                        </Typography>
                    </CardContent>
                </Card>
                <div tabIndex="2">
                    {packet911CallsGroup[0].map((name) => (
                        <div
                            onClick={() =>
                                dispatch(setSelectedCallData(name))
                            }
                        >
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="CallInfo" />}
                                onReset={() => { }}
                            >
                                <CallInfo value={name}></CallInfo>
                            </ErrorBoundary>
                        </div>
                    ))}
                </div>
                {packet911CallsGroup[0].length === 0 && (
                    <div>
                        <Card variant="outlined">
                            <CardContent>
                                <Typography variant="body2" component="p">
                                    {t("noCallFound")}
                                </Typography>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </>
    );
}

export default GroupComponent;