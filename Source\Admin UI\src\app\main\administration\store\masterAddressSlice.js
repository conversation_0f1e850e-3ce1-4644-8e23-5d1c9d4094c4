import { showMessage } from "app/store/fuse/messageSlice";
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';
import { createSlice } from '@reduxjs/toolkit';

export const getMasterAddress = (sortField, sortDirection, pageIndex, pageLimit, searchText, county, stateCode) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`admin/api/masterAddressPointDetail/addressList/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${county}/${stateCode}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setMasterAddressTotalCount(listData.totalCount));
                    dispatch(setMasterAddressData(listData.masterAddressesList));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const saveMasterAddress = (data, formData, file) => async dispatch => {
    try {
        let filetype = file != null ? file.type : null;
        if (file) {
            const uploadRes = await axios.post(`fileupload/upload/masterAddressFile`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            })
            if (uploadRes.status === 400) {
                return dispatch(showMessage({
                    message: uploadRes?.message || 'Something went wrong',
                    autoHideDuration: 4000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'error'
                }));
            }

            data.fileUrl = uploadRes.data.Location;
            data.fileKey = uploadRes.data.Key;
            data.mimeType = filetype;
        }

        dispatch(setLoading(true));
        await axios.post(`admin/api/masterAddressPointDetail/createMasterAddressPointDetail`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    return dispatch(setMasterAddressSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const updateMasterAddress = (data, formData, file) => async dispatch => {
    try {
        let filetype = file != null ? file.type : null;
        if (file) {
            const uploadRes = await axios.post(`fileupload/upload/masterAddressFile`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            })

            if (uploadRes.status === 400) {
                return dispatch(showMessage({
                    message: uploadRes?.message || 'Something went wrong',
                    autoHideDuration: 4000,
                    anchorOrigin: { vertical: 'top', horizontal: 'right' },
                    variant: 'error'
                }));
            }

            data.fileUrl = uploadRes.data.Location;
            data.fileKey = uploadRes.data.Key;
            data.mimeType = filetype;
        }
        dispatch(setLoading(true));
        await axios.post(`admin/api/masterAddressPointDetail/masterAddressPointDetailEdit`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(setMasterAddressSuccess(true));
                    dispatch(setMasterAddressByID(null));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return;
                } else {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }));
                }
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const deleteMasterAddress = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/masterAddressPointDetail/masterAddressPointDetailDelete/${data.id}/${data.county}/${data.stateCode}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(setMasterAddressSuccess(true));

                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),


                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const getMasterAddressByID = (id, county, stateCode) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/masterAddressPointDetail/masterAddressPointDetailGetByID/${id}/${county}/${stateCode}`)
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    return dispatch(setMasterAddressByID(JSON.parse(decrypt(response.data))));
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }

}

export const getAllPicklistOptions = () => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/masters/getStreetPrePostModifierAndPrePostDirectionAnsPlaceType`)
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setStreetPreModifierList(response.streetpremodifierList));
                    dispatch(setStreetPostModifierList(response.streetpostmodifierList));
                    dispatch(setStreetPredirectionAndPostDirectionList(response.streetpredirectionandpostdirectionList));
                    dispatch(setPlaceTypeList(response.placetypeList));
                    dispatch(setLoading(false));
                    return true;
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: 'Error Occured!', autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'error'
                }));
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const getCountryList = () => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/masters/getCountryList`)
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setCountrylist(response.formattedCountries));
                    dispatch(setLoading(false));
                    return true;
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: 'Error Occured!', autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'error'
                }));
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const getStreetNamePreTypesPostTypesAndSeparators = () => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/masters/getStreetNamePreTypesPostTypesAndSeparators`)
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setStreetNamePreTypesPostTypes(response.streetnamepretypesandstreetnameposttypesList));
                    dispatch(setStreetNamePreTypeSeparatorsList(response.streetnamepretypeseparatorsList));
                    dispatch(setLoading(false));
                    return true;
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: 'Error Occured!', autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'error'
                }));
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const getCityCountyStateZipCode = (county, stateCode) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/masters/getCityCountyStateZipCode/${county}/${stateCode}`)
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    if (response?.cityList?.length > 0)
                        dispatch(setCityList(response.cityList));
                    if (response?.countyList?.length > 0)
                        dispatch(setCountyList(response.countyList));
                    if (response?.stateList?.length > 0)
                        dispatch(setStateList(response.stateList));
                    if (response?.zipcodeList?.length > 0)
                        dispatch(setZipCodeList(response.zipcodeList));
                    dispatch(setLoading(false));
                    return true;
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: 'Error Occured!', autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'error'
                }));
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const getMasterCounty = (sortField, sortDirection, pageIndex, pageLimit, searchText) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`admin/api/masterAddressPointDetail/CountyList/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setMasterCountyTotalCount(listData.totalCount));
                    dispatch(setMasterCounty(listData.countyList));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

const initialState = {
    MasterCounty: [],
    selectedCounty: null,
    selectedCountyStateCode: null,
    countyTotalCount: 0,
    MasterAddress: [],
    MasterAddressData: null,
    totalCount: 0,
    isloading: false,
    masterAddressSuccess: false,
    streetPreModifierList: [],
    streetPostModifierList: [],
    streetPredirectionAndPostDirectionList: [],
    placeTypeList: [],
    countryList: [],
    streetNamePreTypesPostTypes: [],
    streetNamePreTypeSeparatorsList: [],
    cityList: [],
    countyList: [],
    stateList: [],
    zipcodeList: [],
};

const masterAddressSlice = createSlice({
    name: "MasterAddress",
    initialState,
    reducers: {
        setMasterAddressData: (state, action) => {
            state.MasterAddress = action.payload;
        },
        setMasterAddressTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setMasterAddressByID: (state, action) => {
            state.MasterAddressData = action.payload;
        },
        setMasterAddressSuccess: (state, action) => {
            state.masterAddressSuccess = action.payload;
        },
        setStreetPreModifierList: (state, action) => {
            state.streetPreModifierList = action.payload;
        },
        setStreetPostModifierList: (state, action) => {
            state.streetPostModifierList = action.payload;
        },
        setStreetPredirectionAndPostDirectionList: (state, action) => {
            state.streetPredirectionAndPostDirectionList = action.payload;
        },
        setPlaceTypeList: (state, action) => {
            state.placeTypeList = action.payload;
        },
        setCountrylist: (state, action) => {
            state.countryList = action.payload;
        },
        setStreetNamePreTypesPostTypes: (state, action) => {
            state.streetNamePreTypesPostTypes = action.payload;
        },
        setStreetNamePreTypeSeparatorsList: (state, action) => {
            state.streetNamePreTypeSeparatorsList = action.payload;
        },
        setCityList: (state, action) => {
            state.cityList = action.payload;
        },
        setCountyList: (state, action) => {
            state.countyList = action.payload;
        },
        setStateList: (state, action) => {
            state.stateList = action.payload;
        },
        setZipCodeList: (state, action) => {
            state.zipcodeList = action.payload;
        },
        setMasterCounty: (state, action) => {
            state.MasterCounty = action.payload;
        },
        setMasterCountyTotalCount: (state, action) => {
            state.countyTotalCount = action.payload;
        },
        setSelectedCounty: (state, action) => {
            state.selectedCounty = action.payload;
        },
        setSelectedCountyStateCode: (state, action) => {
            state.selectedCountyStateCode = action.payload;
        },
    }
})

export const {
    setMasterAddressData,
    setMasterAddressTotalCount,
    setLoading,
    setMasterAddressByID,
    setMasterAddressSuccess,
    setStreetPreModifierList,
    setStreetPostModifierList,
    setStreetPredirectionAndPostDirectionList,
    setPlaceTypeList,
    setCountrylist,
    setStreetNamePreTypesPostTypes,
    setStreetNamePreTypeSeparatorsList,
    setCityList,
    setCountyList,
    setStateList,
    setZipCodeList,
    setMasterCounty,
    setMasterCountyTotalCount,
    setSelectedCounty,
    setSelectedCountyStateCode,
} = masterAddressSlice.actions;

export default masterAddressSlice.reducer;