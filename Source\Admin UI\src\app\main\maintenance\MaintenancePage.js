import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grow from '@mui/material/Grow';
import { darken } from '@mui/material/styles';
import makeStyles from '@mui/styles/makeStyles';
import Typography from '@mui/material/Typography';
import clsx from 'clsx';
import React from 'react';
import JwtService from 'src/app/services/jwtService';

const useStyles = makeStyles(theme => ({
	root: {
		background: `radial-gradient(${darken(theme.palette.primary.dark, 0.5)} 0%, ${theme.palette.primary.dark} 80%)`,
		color: theme.palette.primary.contrastText
	}
}));

function MaintenancePage() {
	const classes = useStyles();

	setTimeout(function () {
		JwtService.logout();
	}, 4000);

	return (
		<div className={clsx(classes.root, 'flex flex-col flex-auto flex-shrink-0 items-center justify-center p-32')}>
			<div className="flex flex-col items-center justify-center w-full">
				<Grow in>
					<Card className="w-full max-w-384 rounded-8">
						<CardContent className="flex flex-col items-center justify-center text-center p-48">
							<img className="w-128 m-32" src="assets/images/logo/fuse.svg" alt="logo" />

							<Typography variant="subtitle1" className="mb-16">
								You do not have permission to access this site!
							</Typography>

							<Typography color="textSecondary" className="mb-40">
								Please contact your administrator.
							</Typography>
						</CardContent>
					</Card>
				</Grow>
			</div>
		</div>
	);
}

export default MaintenancePage;
