import { <PERSON><PERSON>, Card, CardContent, Checkbox, FormControl, FormControlLabel, FormLabel, Grid, Tab, Tabs, TextField, Typography } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import CircularProgressLoader from "../../SharedComponents/CircularProgressLoader/CircularProgressLoader";
import AppHeaderBar from "../components/AppHeaderBar";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import history from "@history";
import { createStateViolation } from "../store/stateViolationSlice";
import { Box } from "@mui/system";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import ClassificationTable from "./ClassificationTable";
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { showMessage } from "app/store/fuse/messageSlice";

const StateViolationAdd = () => {
    const { t } = useTranslation("laguageConfig");
    const selectedStateViolationColumn = useSelector(({ administration }) => administration.stateViolationSlice.selectedStateViolationColumn);
    const selectedStateViolationData = useSelector(({ administration }) => administration.stateViolationSlice.selectedStateViolationData);
    const isLoading = useSelector(({ administration }) => administration.stateViolationSlice.isLoading);
    const selectedStateCode = useSelector(({ administration }) => administration.stateViolationSlice.selectedStateCode);
    const violationTypes = useSelector(({ administration }) => administration.stateViolationSlice.violationTypes);
    const violationData = useSelector(({ administration }) => administration.stateViolationSlice.violationList);

    const [fields, setFields] = useState([]);
    const [pageType, setPageType] = useState("Add");
    const [selectedTypes, setSelectedTypes] = useState([]);
    const [selectedRadio, setSelectedRadio] = useState(null);
    const [selectedJailTypes, setSelectedJailTypes] = useState([]);
    const [selectedClassification, setSelectedClassification] = useState([]);
    const [selectedDefault, setSelectedDefault] = useState(null);
    const [isCheckboxDisabled, setIsCheckboxDisabled] = useState(false);
    const [isRadioDisabled, setIsRadioDisabled] = useState(false);

    const dispatch = useDispatch();
    const { control, setValue, formState, reset, handleSubmit, } = useForm({
        mode: 'onChange',
    });
    const { errors } = formState;

    const jailTypes = [
        {
            title: "FingerPrint Required"
        },
        {
            title: "DNA Required"
        }
    ];

    useEffect(() => {
        if (selectedStateViolationColumn.length > 0) {
            let controllerFields = selectedStateViolationColumn.filter((col) =>
                col.field !== '_id' &&
                col.field !== 'violationTypes' &&
                col.field !== 'JailTypes' &&
                col.field !== 'Classification' &&
                col.field !== 'Default' &&
                col.field !== 'bondAmountDefault'
            );
            setFields(controllerFields);
        }
    }, [selectedStateViolationColumn]);

    useEffect(() => {
        if (violationData && violationData.length > 0) {
            if (selectedStateViolationData !== null) {
                let selectedViolation = violationData.find((x) => x._id === selectedStateViolationData._id);
                setPageType("Update");
                // Populate form fields with the existing data
                Object.keys(selectedStateViolationData).forEach((key) => {
                    setValue(key, selectedStateViolationData[key]);
                });
                // Set the _id field for updating
                setValue('_id', selectedStateViolationData._id);
                setValue('bondAmountDefault', selectedViolation.bondAmountDefault);
                setSelectedClassification(selectedViolation.Classification !== undefined ? selectedViolation.Classification : []);
                setSelectedDefault(selectedViolation.Default !== undefined ? selectedViolation.Default : null);
                setSelectedJailTypes(selectedViolation.JailTypes !== undefined ? selectedViolation.JailTypes : []);
                const { violationTypes } = selectedViolation;
                if (typeof violationTypes === 'string') {
                    setSelectedRadio(violationTypes);
                    setSelectedTypes([]);
                } else if (Array.isArray(violationTypes)) {
                    setSelectedRadio(null);
                    setSelectedTypes(violationTypes.length === 1 ? violationTypes : []);
                }
            }
        }
    }, [selectedStateViolationData]);

    useEffect(() => {
        if (selectedRadio !== null) {
            setIsCheckboxDisabled(true);
        }
    }, [selectedRadio]);

    useEffect(() => {
        if (selectedTypes.length > 0) {
            setIsRadioDisabled(true);
        }
    }, [selectedTypes]);

    // Handle checkbox change
    const handleCheckboxChange = (title) => {
        setSelectedTypes((prevState) =>
            prevState.includes(title)
                ? prevState.filter((item) => item !== title)
                : [...prevState, title]
        );
    };

    const handleRadioChange = (event) => {
        setSelectedRadio(event.target.value);
    };

    const handleJailTypesCheckboxChange = (title) => {
        setSelectedJailTypes((prevSelected) =>
            prevSelected.includes(title)
                ? prevSelected.filter((item) => item !== title)
                : [...prevSelected, title]
        );
    };

    const navigateBack = () => {
        history.push(`/admin/states/stateViolation`);
    };

    const handleClassificationSelected = (selectedRecord) => {
        setSelectedClassification((prevState) => [...prevState, selectedRecord]);
    };

    const handleDefaultSelected = (selectedRecord) => {
        setSelectedDefault(selectedRecord);
    };

    const backButton = (
        <Button
            type="button"
            variant="contained"
            color='secondary'
            className="normal-case m-16"
            aria-label="Back"
            value="legacy"
            onClick={navigateBack}>
            {t('back')}
        </Button>
    );

    const submitButton = (
        <Button
            type="submit"
            variant="contained"
            color="secondary"
            className="normal-case m-16"
            aria-label="REGISTER"
            value="legacy">
            {pageType === "Add" ? t('save') : t('update')}
        </Button>
    );

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const onSubmit = (model) => {
        if (selectedClassification.length > 0 && selectedDefault === null) {
            ShowErroMessage(t('oneDefaultClassificationNeedsToBeSelected'))
        } else {
            // Prepare the submitted data
            let submittedData = { ...model };
            submittedData.violationTypes = selectedTypes.length > 0 ? selectedTypes : selectedRadio;
            submittedData.JailTypes = selectedJailTypes;
            if (selectedClassification.length > 0) {
                // List of keys to exclude
                const keysToExclude = ["$hashCode", "Action", "action", "IsDefault"];
                // Filter out unwanted properties
                const selectedDefaultClean = Object.fromEntries(
                    Object.entries(selectedDefault).filter(([key]) => !keysToExclude.includes(key))
                );

                // Clean each object in the array
                const selectedClassificationClean = selectedClassification.map(obj =>
                    Object.fromEntries(
                        Object.entries(obj).filter(([key]) => !keysToExclude.includes(key))
                    )
                );
                submittedData.Classification = selectedClassificationClean;
                submittedData.Default = selectedDefaultClean;
            }

            delete submittedData.$hashCode;
            delete submittedData.action;

            // Prepare the body for submission
            let body = {
                isUpdate: pageType !== "Add",
                data: submittedData
            };
            // Dispatch the action
            dispatch(createStateViolation(body, selectedStateCode));
            // Clear all fields after submission
            reset();
            navigateBack();
        }
    };

    const resetType = () => {
        setIsCheckboxDisabled(false);
        setIsRadioDisabled(false);
        setSelectedTypes([]);
        setSelectedRadio(null);
    };

    return (
        <div className="p-16">
            {isLoading && <CircularProgressLoader loading={isLoading} />}
            <Card className=" m-16 rounded-8 shadow">
                <form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)} autoSave={false} autoComplete={false} >
                    <AppHeaderBar backButton={backButton}
                        submitButton={submitButton} headerText={`${t(pageType)} ${t('violation')}`} />
                    <CardContent style={{ overflowX: "scroll", height: "90%" }}>
                        <div className="w-full pr-16 pl-16 pt-16">
                            <Grid container spacing={1} style={{ paddingLeft: 8 }}>
                                {fields?.map((item, index) => (
                                    <Grid item xs={12} sm={3} md={3} lg={3} xl={3} key={index}>
                                        <Controller
                                            name={item.field}
                                            control={control}
                                            defaultValue=""
                                            render={({ field }) => (
                                                <TextField
                                                    {...field}
                                                    className="mb-16  w-full"
                                                    label={t(item.headerText)}
                                                    type="text"
                                                    variant="outlined"
                                                    fullWidth
                                                    sx={{ mb: 2 }}
                                                />
                                            )}
                                        />
                                    </Grid>
                                ))}
                            </Grid>
                            <Grid container spacing={1} style={{ paddingLeft: 8 }}>
                                <Grid item xs={7}>
                                    <Box component="fieldset" sx={{ border: '1px solid #ccc', paddingLeft: 8, padding: 2, borderRadius: '5px', marginBottom: 2 }}>
                                        <legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("violationTypes")}</legend>
                                        <Grid container spacing={1} style={{ paddingLeft: 8, paddingBottom: 16 }}>
                                            {violationTypes && violationTypes.length > 0 &&
                                                violationTypes.map((ele, index) => (
                                                    <Grid item xs={12} sm={6} md={4} key={index}>
                                                        {ele.title === 'Citation Violation' || ele.title === 'Traffic Violation' ? (
                                                            // Render Checkbox for Citation and Traffic Violation
                                                            <FormControlLabel
                                                                control={
                                                                    <Checkbox
                                                                        checked={selectedTypes.includes(ele.title)}
                                                                        onChange={() => handleCheckboxChange(ele.title)}
                                                                        disabled={isCheckboxDisabled}  // Disable if any radio button is selected
                                                                    />
                                                                }
                                                                label={t(ele.title)}
                                                            />
                                                        ) : (
                                                            // Render Radio Button for other violations
                                                            <FormControl disabled={isRadioDisabled} component="fieldset">
                                                                <RadioGroup
                                                                    value={selectedRadio}
                                                                    onChange={handleRadioChange}
                                                                >
                                                                    <FormControlLabel
                                                                        value={ele.title}
                                                                        control={<Radio />}
                                                                        label={t(ele.title)}
                                                                    />
                                                                </RadioGroup>
                                                            </FormControl>
                                                        )}
                                                    </Grid>
                                                ))}
                                            {(selectedTypes.length > 0 || selectedRadio !== null) &&
                                                <div className="mx-auto" style={{ display: 'flex', justifyContent: 'right' }}>
                                                    <Button
                                                        type="button"
                                                        variant="contained"
                                                        color='secondary'
                                                        className="normal-case mr-15 mt-8"
                                                        aria-label="Back"
                                                        value="legacy"
                                                        onClick={resetType}>
                                                        {t('reset')}
                                                    </Button>
                                                </div>
                                            }
                                        </Grid>
                                    </Box>
                                </Grid>
                                <Grid item xs={5}>
                                    <Box component="fieldset" sx={{ border: '1px solid #ccc', paddingLeft: 8, padding: 2, borderRadius: '5px', marginBottom: 2 }}>
                                        <legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("jailTypes")}</legend>
                                        <Grid container spacing={1} style={{ paddingLeft: 8, paddingBottom: 8 }}>
                                            {jailTypes && jailTypes.length > 0 &&
                                                jailTypes.map((ele, index) => (
                                                    <Grid item xs={12} sm={6} md={4} key={index}>
                                                        <FormControlLabel
                                                            control={
                                                                <Checkbox
                                                                    checked={selectedJailTypes.includes(ele.title)}
                                                                    onChange={() => handleJailTypesCheckboxChange(ele.title)}
                                                                />
                                                            }
                                                            label={t(ele.title)}
                                                        />
                                                    </Grid>
                                                ))}
                                        </Grid>
                                        <Grid container spacing={1} style={{ paddingLeft: 8 }}>
                                            <Controller
                                                name="bondAmountDefault"
                                                control={control}
                                                defaultValue=""
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-12  w-full"
                                                        label={t("bondAmountDefault")}
                                                        type="text"
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 1 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                    </Box>
                                </Grid>
                            </Grid>
                            <Typography className="text-18 pl-10"> {t("classificationMapping")}</Typography>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                                <ClassificationTable showAction={false} searchText={""}
                                    handleDefaultSelected={handleDefaultSelected}
                                    classificationSelected={handleClassificationSelected}
                                    selectedClassification={selectedClassification}
                                    selectedDefault={selectedDefault}
                                ></ClassificationTable>
                            </ErrorBoundary>
                            <div className="mx-auto" style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
                                {submitButton}
                                {backButton}
                            </div>
                        </div>
                    </CardContent>
                </form>
            </Card>
        </div >
    )
};

export default StateViolationAdd;