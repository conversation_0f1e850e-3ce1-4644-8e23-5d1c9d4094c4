import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import IconButton from "@mui/material/IconButton";
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import { useDispatch, useSelector } from 'react-redux';
import { UpdateIsResolvedFlag } from '../../ErrorLog/store/errorLogSlice';
import { FormControl, TextField } from '@mui/material';
import { motion } from 'framer-motion';
import { Box } from '@mui/system';

const IsResolvedDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation('laguageConfig');
    const [open, setOpen] = React.useState(false);
    const [id, setId] = React.useState([]);
    const [pagingDetails, setPagingDetails] = React.useState(null);
    const [inputName, setInputName] = React.useState('');

    const handleClickOpen = () => {
        setOpen(true);
    };

    const lableStyle = {
        fontSize: "17px",
        fontWeight: "700"
    }

    useImperativeHandle(ref, () => ({
        openErrorDetails(id, PagingDetails) {
            setId(id);
            setPagingDetails(PagingDetails);
            handleClickOpen();
        }
    }))

    const handleClose = () => {
        setOpen(false);
        setId(null);
        setPagingDetails(null);
        setInputName(null);
    };

    const handleResolveIssue = () => {
        const body = {
            _id: id,
            name: inputName,
        }
        dispatch(UpdateIsResolvedFlag(body, pagingDetails));
        handleClose();
    }

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
                    {t("resolveError")}
                </DialogTitle>
                <IconButton
                    aria-label="close"
                    onClick={handleClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: (theme) => theme.palette.grey[500],
                    }}
                >
                    <CloseIcon />
                </IconButton>
                <DialogContent dividers>
                    <div className='m-16'>
                        <Typography gutterBottom>
                            <label style={lableStyle}> {t("confirmMsgErrorResolved")} </label>
                        </Typography>
                        <Box component={motion.div}
                            initial={{ y: -20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 }, width: '400px' }}
                            className="flex items-center w-full rounded-8" elevation={1}>
                            <FormControl fullWidth>
                                <TextField
                                    label={t("resolvedBy")}
                                    name="Resolved"
                                    className="w-full"
                                    value={inputName}
                                    margin="normal"
                                    onChange={(ev) => setInputName(ev.target.value)}
                                />
                            </FormControl>
                        </Box>

                    </div>
                </DialogContent>
                <DialogActions>
                    <div className='flex justify-end'>
                        < Button
                            variant="contained"
                            color="secondary"
                            className=" w-auto mr-16 mt-8"
                            aria-label="Register"
                            type="button"
                            onClick={handleClose}
                            size="large"
                        >
                            {t("no")}
                        </Button>

                        <Button
                            variant="contained"
                            color="primary"
                            className=" w-auto mt-8"
                            aria-label="Register"
                            type="button"
                            size="large"
                            onClick={handleResolveIssue}
                        >
                            {t("yes")}
                        </Button>
                    </div>
                </DialogActions>
            </Dialog >
        </div >
    );
});
export default IsResolvedDialog;

