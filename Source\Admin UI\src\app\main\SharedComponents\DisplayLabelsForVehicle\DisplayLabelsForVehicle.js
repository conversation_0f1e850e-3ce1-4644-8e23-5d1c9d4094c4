import React from "react";
import "leaflet/dist/leaflet.css";
import { calculateAge, checkValueEmptyOrNull, isEmptyOrNull } from "../../utils/utils";
import Grid from "@mui/material/Grid";
import { useTranslation } from "react-i18next";

function DisplayLabelsForVehicle(props) {
    const { t } = useTranslation("laguageConfig");
    let item = props.editButtonData;

    return (
        <>
            {item !== null && item !== undefined &&
                <div>
                    <Grid itemxs={12} sm={12} md={12} lg={12} xl={12}
                        style={{ fontSize: '1.15rem', padding: '0px 10px 15px' }}>
                        <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ padding: '0px' }}>
                            <span className="callType">
                                <span className="callType">
                                    {`
                                        ${checkValueEmptyOrNull(item.makeDesc)}, 
                                        ${checkValueEmptyOrNull(item.modelDesc)}, 
                                        ${checkValueEmptyOrNull(item.vehicleColor1)}, 
                                        ${checkValueEmptyOrNull(item.modelYear)}, 
                                        ${checkValueEmptyOrNull(item.tagNumber)}`
                                    }
                                </span>
                            </span>
                            <hr style={{ border: '1px solid #00000063;' }} />
                            <br />
                        </Grid>
                        <Grid item xs={12} sm={12} md={12} lg={12} xl={12} style={{ display: 'flex', paddingTop: '10px' }}>
                            <Grid item xs={12} sm={6} md={6} lg={6} xl={6} style={{ paddingTop: '0px' }}>
                                <span className="callCaller"><b>{t("ownerInformation")}</b></span>
                                <br />
                                <span className="callAddress">
                                    {`${checkValueEmptyOrNull(item?.ownerInformation?.LName)}, ${checkValueEmptyOrNull(item?.ownerInformation?.FName)} ${checkValueEmptyOrNull(item?.ownerInformation?.MName)}`}
                                </span>
                                <br />
                                <span className="callAddress">
                                    {`
                                                                ${checkValueEmptyOrNull(item?.ownerInformation?.Building)} ${checkValueEmptyOrNull(item?.ownerInformation?.St_Name)} ${checkValueEmptyOrNull(item?.ownerInformation?.St_PosTyp)} ${checkValueEmptyOrNull(item?.ownerInformation?.St_PosDir)}`
                                    }
                                </span>
                                <br />
                                <span className="callCaller">
                                    {`${checkValueEmptyOrNull(item?.ownerInformation?.Post_Comm)},${checkValueEmptyOrNull(item?.ownerInformation?.State)}, ${checkValueEmptyOrNull(item?.ownerInformation?.Post_Code)} ${checkValueEmptyOrNull(item.tagState)}`}
                                </span><br />
                                {!checkValueEmptyOrNull(item?.ownerInformation?.DLNumber) &&
                                    <>
                                        <span className="callCaller">
                                            {"D-L Number - " + item?.ownerInformation?.DLNumber + " "}
                                        </span>
                                        <br />
                                    </>
                                }
                                <span className="callCaller">
                                    {`Personal - ${checkValueEmptyOrNull(item?.ownerInformation?.Email)} `}</span>

                            </Grid>
                            <Grid item xs={12} sm={1} md={1} lg={1} xl={1} style={{ padding: '0px' }}>
                            </Grid>
                            <Grid item xs={12} sm={5} md={5} lg={5} xl={5} style={{ padding: '0px' }}>
                                <span className="callCaller"><b>{t("driverInformation")}</b></span>
                                <br />
                                <span className="callAddress">
                                    {`${checkValueEmptyOrNull(item?.driverInformation?.DLName)}, ${checkValueEmptyOrNull(item?.driverInformation?.DFName)} ${checkValueEmptyOrNull(item?.driverInformation?.DMName)}`}
                                </span>
                                <br />
                                <span className="callAddress">
                                    {`${checkValueEmptyOrNull(item?.driverInformation?.DBuilding)} ${checkValueEmptyOrNull(item?.driverInformation?.DSt_Name)} ${checkValueEmptyOrNull(item?.driverInformation?.DSt_PosTyp)} ${checkValueEmptyOrNull(item?.driverInformation?.DSt_PosDir)}`}
                                </span>
                                <br />

                                <span className="callCaller">
                                    {`${checkValueEmptyOrNull(item?.driverInformation?.DPost_Comm)}, ${checkValueEmptyOrNull(item?.driverInformation?.DState)}, ${checkValueEmptyOrNull(item?.driverInformation?.DPost_Code)} ${checkValueEmptyOrNull(item.tagState)}`}
                                </span><br />
                                {!checkValueEmptyOrNull(item?.driverInformation?.DDLNumber) &&
                                    <>
                                        <span className="callCaller">
                                            {"D-L Number - " + item?.driverInformation?.DDLNumber + " "}
                                        </span>
                                        <br />
                                    </>
                                }
                                <span className="callCaller">{`Personal - ${checkValueEmptyOrNull(item?.driverInformation?.DEmail)} `}</span>
                            </Grid>
                        </Grid>
                    </Grid>
                </div>
            }
        </>
    );
}

export default DisplayLabelsForVehicle;
