import { showMessage } from "app/store/fuse/messageSlice";
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';
import { createSlice } from '@reduxjs/toolkit';

export const getViolationClassification = (state, sortField, sortDirection, pageIndex, pageLimit, searchText) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`admin/api/violationclassification/violationClassificationList/${state}/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}`)
            .then((response) => {
                if (response.status === 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setViolationClassification(listData.list));
                    dispatch(setViolationClassificationTotalCount(listData.totalCount));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const deleteViolationClassification = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/violationclassification/violationClassificationDelete/${data.id}/${data.stateCode}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(setStateViolationClassificationSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const createViolationClassification = (data, stateCode) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/violationclassification/createViolationClassification/${stateCode}`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(setStateViolationClassificationSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

const initialState = {
    isloading: false,
    violationClassification: [],
    violationClassificationTotalCount: 0,
    selectedStateCode: null,
    stateViolationClassificationSuccess: false,
};

const violationClassificationSlice = createSlice({
    name: "ViolationClassification",
    initialState,
    reducers: {
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setViolationClassification: (state, action) => {
            state.violationClassification = action.payload;
        },
        setViolationClassificationTotalCount: (state, action) => {
            state.violationClassificationTotalCount = action.payload;
        },
        setSelectedStateCode: (state, action) => {
            state.selectedStateCode = action.payload;
        },
        setStateViolationClassificationSuccess: (state, action) => {
            state.stateViolationClassificationSuccess = action.payload;
        },
    }
})

export const {
    setLoading,
    setViolationClassification,
    setViolationClassificationTotalCount,
    setSelectedStateCode,
    setStateViolationClassificationSuccess,
} = violationClassificationSlice.actions;

export default violationClassificationSlice.reducer;