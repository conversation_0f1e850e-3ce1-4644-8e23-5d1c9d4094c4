import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from 'react-redux';
import { motion } from "framer-motion";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import FusePageCarded from "@fuse/core/FusePageCarded";
import DnsIcon from '@mui/icons-material/Dns';
import CancelIcon from '@mui/icons-material/Cancel';
import { StyledEngineProvider, ThemeProvider, Typography, Paper, Icon, Input, Button, TablePagination, Tooltip, Stack, IconButton, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Collapse, InputAdornment } from "@mui/material";
import { checkData, getNavbarTheme, getRowsPerPageOptions, isEmptyOrNull } from "src/app/main/utils/utils";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import ErrorRoundedIcon from '@mui/icons-material/ErrorRounded';
import moment from "moment";
import history from "@history";
import CircularProgressLoader from "src/app/main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { getMasterAddressErrorLogsById } from "src/app/main/store/importFromExcelSlice";

import {
    IgrGridModule, IgrGridToolbar, IgrGridToolbarActions, ColumnPinningPosition,
    ColumnPinning,
    IgrGridToolbarAdvancedFiltering, IgrGridToolbarHiding, IgrGridToolbarPinning
} from "@infragistics/igniteui-react-grids";
import { IgrGrid, IgrColumn } from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import { useWindowResizeHeight } from "../../utils/utils";

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "AddNum_Pre",
        align: "left",
        disablePadding: false,
        label: "Address No Prefix",
        sort: true,
    },
    {
        id: "Marker",
        align: "left",
        disablePadding: false,
        label: "Street Number Or Mile Marker",
        sort: true,
    },
    {
        id: "AddNum_Suf",
        align: "left",
        disablePadding: false,
        label: "Number Suffix",
        sort: true,
    },
    {
        id: "St_PreMod",
        align: "left",
        disablePadding: false,
        label: "Pre Mod",
        sort: true,
    },
    {
        id: "St_PreDir",
        align: "left",
        disablePadding: false,
        label: "Direction",
        sort: true,
    },
    {
        id: "St_PreTyp",
        align: "left",
        disablePadding: false,
        label: "Pre Type",
        sort: true,
    },
    {
        id: "St_PreSep",
        align: "left",
        disablePadding: false,
        label: "Pre Sep",
        sort: true,
    },
    {
        id: "St_Name",
        align: "left",
        disablePadding: false,
        label: "Street Name",
        sort: true,
    },
    {
        id: "St_PosTyp",
        align: "left",
        disablePadding: false,
        label: "Street Type",
        sort: true,
    },
    {
        id: "St_PosDir",
        align: "left",
        disablePadding: false,
        label: "Post Dir",
        sort: true,
    },
    {
        id: "St_PosMod",
        align: "left",
        disablePadding: false,
        label: "Post Mod",
        sort: true,
    },
    {
        id: "St_DirOfTravel",
        align: "left",
        disablePadding: false,
        label: "Street Dir Of Travel",
        sort: true,
    },
    {
        id: "MilePost",
        align: "left",
        disablePadding: false,
        label: "Mile Post",
        sort: true,
    },
    {
        id: "Site",
        align: "left",
        disablePadding: false,
        label: "Site",
        sort: true,
    },
    {
        id: "SubSite",
        align: "left",
        disablePadding: false,
        label: "Sub Site",
        sort: true,
    },
    {
        id: "Structure",
        align: "left",
        disablePadding: false,
        label: "Structure",
        sort: true,
    },
    {
        id: "Wing",
        align: "left",
        disablePadding: false,
        label: "Wing",
        sort: true,
    },
    {
        id: "Floor",
        align: "left",
        disablePadding: false,
        label: "Floor",
        sort: true,
    },
    {
        id: "UnitPreType",
        align: "left",
        disablePadding: false,
        label: "Unit Pre Type",
        sort: true,
    },
    {
        id: "UnitValue",
        align: "left",
        disablePadding: false,
        label: "Unit Value",
        sort: true,
    },
    {
        id: "Room",
        align: "left",
        disablePadding: false,
        label: "Room",
        sort: true,
    },
    {
        id: "Section",
        align: "left",
        disablePadding: false,
        label: "Section",
        sort: true,
    },
    {
        id: "Row",
        align: "left",
        disablePadding: false,
        label: "Row",
        sort: true,
    },
    {
        id: "Seat",
        align: "left",
        disablePadding: false,
        label: "Seat",
        sort: true,
    },
    {
        id: "Post_Comm",
        align: "left",
        disablePadding: false,
        label: "City Postal Comm",
        sort: true,
    },
    {
        id: "State",
        align: "left",
        disablePadding: false,
        label: "State",
        sort: true,
    },
    {
        id: "Post_Code",
        align: "left",
        disablePadding: false,
        label: "Zip Post Code",
        sort: true,
    },
    {
        id: "PostCodeEx",
        align: "left",
        disablePadding: false,
        label: "Zip (PostCodeEx)",
        sort: true,
    },
    {
        id: "Country",
        align: "left",
        disablePadding: false,
        label: "Country",
        sort: true,
    },
    {
        id: "County",
        align: "left",
        disablePadding: false,
        label: "County",
        sort: true,
    },
    {
        id: "Addtl_Loc",
        align: "left",
        disablePadding: false,
        label: "Additional Location Info",
        sort: true,
    },
    {
        id: "AdditionalAttributes",
        align: "left",
        disablePadding: false,
        label: "NERIS Additional Attributes",
        sort: true,
    },
    {
        id: "Marker",
        align: "left",
        disablePadding: false,
        label: "Marker",
        sort: true,
    },
    {
        id: "PlaceType",
        align: "left",
        disablePadding: false,
        label: "Place Type",
        sort: true,
    },
    {
        id: "lst_name",
        align: "left",
        disablePadding: false,
        label: "Legacy Street Name",
        sort: true,
    },
    {
        id: "lst_predir",
        align: "left",
        disablePadding: false,
        label: "Pre Dir",
        sort: true,
    },
    {
        id: "lst_name",
        align: "left",
        disablePadding: false,
        label: "Legacy Street Name 2",
        sort: true,
    },
    {
        id: "lst_type",
        align: "left",
        disablePadding: false,
        label: "Legacy Street Type",
        sort: true,
    },
    {
        id: "lst_posdir",
        align: "left",
        disablePadding: false,
        label: "Legacy Post Dir",
        sort: true,
    },
    {
        id: "Latitude",
        align: "left",
        disablePadding: false,
        label: "Latitude",
        sort: true,
    },
    {
        id: "Longitude",
        align: "left",
        disablePadding: false,
        label: "Longitude",
        sort: true,
    },
    {
        id: "Elevation",
        align: "left",
        disablePadding: false,
        label: "Elevation",
        sort: true,
    },
    {
        id: "nearestCrossStreet",
        align: "left",
        disablePadding: false,
        label: "Nearest Cross Street",
        sort: true,
    },
    {
        id: "distance",
        align: "left",
        disablePadding: false,
        label: "Distance",
        sort: true,
    },
    {
        id: "nxtNearestCross",
        align: "left",
        disablePadding: false,
        label: "Nxt Nearest Cross",
        sort: true,
    },
    {
        id: "nxtDistance",
        align: "left",
        disablePadding: false,
        label: "Nxt Distance",
        sort: true,
    },
    {
        id: "Inc_Muni",
        align: "left",
        disablePadding: false,
        label: "Incorporated Muni",
        sort: true,
    },
    {
        id: "Uninc_Comm",
        align: "left",
        disablePadding: false,
        label: "Unincorporated Muni",
        sort: true,
    },
    {
        id: "Nbrhd_Comm",
        align: "left",
        disablePadding: false,
        label: "Neighborhood Community",
        sort: true,
    },
    {
        id: "PSAP",
        align: "left",
        disablePadding: false,
        label: "PSAP",
        sort: true,
    },
    {
        id: "ESN",
        align: "left",
        disablePadding: false,
        label: "ESN",
        sort: true,
    },
    {
        id: "MSAGComm",
        align: "left",
        disablePadding: false,
        label: "MSAG Community",
        sort: true,
    },
    {
        id: "MapPage",
        align: "left",
        disablePadding: false,
        label: "Map Page",
        sort: true,
    },
    {
        id: "adr_label",
        align: "left",
        disablePadding: false,
        label: "Address Label",
        sort: true,
    },
    {
        id: "Placement",
        align: "left",
        disablePadding: false,
        label: "Placement",
        sort: true,
    },
    {
        id: "NationalGrid",
        align: "left",
        disablePadding: false,
        label: "National Grid",
        sort: true,
    },
    {
        id: "DiscrpAgID",
        align: "left",
        disablePadding: false,
        label: "Discrepancy Agency ID",
        sort: true,
    },
    {
        id: "DateUpdate",
        align: "left",
        disablePadding: false,
        label: "Date Updated",
        sort: true,
    },
    {
        id: "Effective",
        align: "left",
        disablePadding: false,
        label: "Effective Date",
        sort: true,
    },
    {
        id: "Expire",
        align: "left",
        disablePadding: false,
        label: "Expiration Date",
        sort: true,
    },
    {
        id: "NGUID",
        align: "left",
        disablePadding: false,
        label: "NGUID",
        sort: true,
    },
    {
        id: "AddCode",
        align: "left",
        disablePadding: false,
        label: "AddCode",
        sort: true,
    },
    {
        id: "AddDataURI",
        align: "left",
        disablePadding: false,
        label: "Address Data URL",
        sort: true,
    },
    {
        id: "PoliceZone",
        align: "left",
        disablePadding: false,
        label: "Police Zone",
        sort: true,
    },
    {
        id: "FireZone",
        align: "left",
        disablePadding: false,
        label: "Fire Zone",
        sort: true,
    },
    {
        id: "FireAutoAssist",
        align: "left",
        disablePadding: false,
        label: "Fire Auto Assist",
        sort: true,
    },
    {
        id: "FireMutualAssist",
        align: "left",
        disablePadding: false,
        label: "Fire Mutual Assist",
        sort: true,
    },
    {
        id: "wreckerServiceZone",
        align: "left",
        disablePadding: false,
        label: "WreckerServiceZone",
        sort: true,
    },
    {
        id: "EMSZone",
        align: "left",
        disablePadding: false,
        label: "EMS Zone",
        sort: true,
    },
    {
        id: "EMSAutoAssist",
        align: "left",
        disablePadding: false,
        label: "EMS Auto Assist",
        sort: true,
    },
    {
        id: "EMSMutualAssist",
        align: "left",
        disablePadding: false,
        label: "EMS Mutual Assist",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

IgrGridModule.register();

const MasterImportErrorLogs = () => {
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const gridRef = useRef(null);
    let colorCode = getNavbarTheme();
    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    const rowsPerPageOptions = getRowsPerPageOptions();

    const masterSummaryId = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.masterSummaryId);
    const masterAddressErrorLogs = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.masterAddressErrorLogs);
    const masterAddressErrorLogsCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.masterAddressErrorLogsCount);
    const isLoading = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.isLoading);
    const selectedCounty = useSelector(({ administration }) => administration.masterAddressSlice.selectedCounty)
    const selectedCountyStateCode = useSelector(({ administration }) => administration.masterAddressSlice.selectedCountyStateCode)
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "Country",
    });
    const [errorLogs, setErrorLogs] = useState([]);
    const [county, setCounty] = useState(null);
    const [countyState, setCountyState] = useState(null);
    const [totalCount, setTotalCount] = useState();
    const [pageIndex, setPageIndex] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [searchText, setSearchText] = useState('');

    useEffect(() => {
        if (masterAddressErrorLogs !== null) {
            setErrorLogs(masterAddressErrorLogs.masterImportErrorLogs);
            setTotalCount(masterAddressErrorLogs.masterImportErrorLogsCount);
        }
    });

    useEffect(() => {
        if (selectedCounty !== null && selectedCountyStateCode !== null) {
            setCounty(selectedCounty);
            setCountyState(selectedCountyStateCode);
            // dispatch(getInterSectionPointDetailsLogs(selectedCounty, selectedCountyStateCode));
        }
    }, [selectedCounty]);

    useEffect(() => {
        if (masterSummaryId !== null && masterSummaryId !== undefined) {
            dispatch(getMasterAddressErrorLogsById(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, masterSummaryId));
        }
    }, [order, pageIndex, rowsPerPage])

    const ActionIcons = (n) => {
        let x = checkData(n);
        return (
            <div className="flex">
                {/* <Button variant="contained" color="primary">{t("viewDetails")}</Button> */}
                <div className="flex">
                    <Tooltip title={t("viewDetails")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            // onClick={() => viewSummaryError(x)}
                            size="large"
                        >
                            <ErrorRoundedIcon />
                        </IconButton>
                    </Tooltip>
                </div>
            </div >
        );
    };

    const rowData = (errorLogs !== null && errorLogs !== undefined) && errorLogs.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id];
            row["_id"] = item._id;
            row["AddrNoPrefix"] = isEmptyOrNull(item.AddNum_Pre) ? "" : item.AddNum_Pre;
            row["Marker"] = isEmptyOrNull(item.Marker) ? "" : item.Marker;
            row["AddNum_Suf"] = isEmptyOrNull(item.AddNum_Suf) ? "" : item.AddNum_Suf;
            row["St_PreMod"] = isEmptyOrNull(item.St_PreMod) ? "" : item.St_PreMod;
            row["St_PreDir"] = isEmptyOrNull(item.St_PreDir) ? "" : item.St_PreDir;
            row["St_PreTyp"] = isEmptyOrNull(item.St_PreTyp) ? "" : item.St_PreTyp;
            row["St_PreSep"] = isEmptyOrNull(item.St_PreSep) ? "" : item.St_PreSep;
            row["St_Name"] = isEmptyOrNull(item.St_Name) ? "" : item.St_Name;
            row["St_PosTyp"] = isEmptyOrNull(item.St_PosTyp) ? "" : item.St_PosTyp;
            row["St_PosDir"] = isEmptyOrNull(item.St_PosDir) ? "" : item.St_PosDir;
            row["St_PosMod"] = isEmptyOrNull(item.St_PosMod) ? "" : item.St_PosMod;
            row["St_DirOfTravel"] = isEmptyOrNull(item.St_DirOfTravel) ? "" : item.St_DirOfTravel;
            row["MilePost"] = isEmptyOrNull(item.MilePost) ? "" : item.MilePost;
            row["Site"] = isEmptyOrNull(item.Site) ? "" : item.Site;
            row["SubSite"] = isEmptyOrNull(item.SubSite) ? "" : item.SubSite;
            row["Structure"] = isEmptyOrNull(item.Structure) ? "" : item.Structure;
            row["Wing"] = isEmptyOrNull(item.Wing) ? "" : item.Wing;
            row["Floor"] = isEmptyOrNull(item.Floor) ? "" : item.Floor;
            row["UnitPreType"] = isEmptyOrNull(item.UnitPreType) ? "" : item.UnitPreType;
            row["UnitValue"] = isEmptyOrNull(item.UnitValue) ? "" : item.UnitValue;
            row["Section"] = isEmptyOrNull(item.Section) ? "" : item.Section;
            row["Row"] = isEmptyOrNull(item.Row) ? "" : item.Row;
            row["Room"] = isEmptyOrNull(item.Room) ? "" : item.Room;
            row["Seat"] = isEmptyOrNull(item.Seat) ? "" : item.Seat;
            row["Post_Comm"] = isEmptyOrNull(item.Post_Comm) ? "" : item.Post_Comm;
            row["State"] = isEmptyOrNull(item.State) ? "" : item.State;
            row["Post_Code"] = item.Post_Code ? item.Post_Code : "";
            row["PostCodeEx"] = item.PostCodeEx ? item.PostCodeEx : "";
            row["Country"] = isEmptyOrNull(item.Country) ? "" : item.Country;
            row["County"] = isEmptyOrNull(item.County) ? "" : item.County;
            row["Addtl_Loc"] = isEmptyOrNull(item.Addtl_Loc) ? "" : item.Addtl_Loc;
            row["AdditionalAttributes"] = isEmptyOrNull(item.AdditionalAttributes) ? "" : item.AdditionalAttributes;
            row["Marker"] = isEmptyOrNull(item.Marker) ? "" : item.Marker;
            row["PlaceType"] = isEmptyOrNull(item.PlaceType) ? "" : item.PlaceType;
            row["lst_name"] = isEmptyOrNull(item.lst_name) ? "" : item.lst_name;
            row["lst_predir"] = isEmptyOrNull(item.lst_predir) ? "" : item.lst_predir;
            row["lst_name"] = isEmptyOrNull(item.lst_name) ? "" : item.lst_name;
            row["lst_type"] = isEmptyOrNull(item.lst_type) ? "" : item.lst_type;
            row["lst_posdir"] = isEmptyOrNull(item.lst_posdir) ? "" : item.lst_posdir;
            row["Latitude"] = item.Latitude ? item.Latitude : "";
            row["Longitude"] = item.Longitude ? item.Longitude : "";
            row["Elevation"] = item.Elevation ? item.Elevation : "";
            row["nearestCrossStreet"] = isEmptyOrNull(item.NearestXSt) ? "" : item.NearestXSt;
            row["distance"] = item.NearestXStDist ? item.NearestXStDist : "";
            row["nxtNearestCross"] = isEmptyOrNull(item.SecondNearestXSt) ? "" : item.SecondNearestXSt;
            row["nxtDistance"] = item.SecondNearestXStDist ? item.SecondNearestXStDist : "";
            row["Inc_Muni"] = isEmptyOrNull(item.Inc_Muni) ? "" : item.Inc_Muni;
            row["Uninc_Comm"] = isEmptyOrNull(item.Uninc_Comm) ? "" : item.Uninc_Comm;
            row["Nbrhd_Comm"] = isEmptyOrNull(item.Nbrhd_Comm) ? "" : item.Nbrhd_Comm;
            row["PSAP"] = isEmptyOrNull(item.PSAP) ? "" : item.PSAP;
            row["ESN"] = isEmptyOrNull(item.ESN) ? "" : item.ESN;
            row["MSAGComm"] = isEmptyOrNull(item.MSAGComm) ? "" : item.MSAGComm;
            row["MapPage"] = isEmptyOrNull(item.MapPage) ? "" : item.MapPage;
            row["adr_label"] = isEmptyOrNull(item.adr_label) ? "" : item.adr_label;
            row["Placement"] = isEmptyOrNull(item.Placement) ? "" : item.Placement;
            row["NationalGrid"] = isEmptyOrNull(item.NationalGrid) ? "" : item.NationalGrid;
            row["DiscrpAgID"] = isEmptyOrNull(item.DiscrpAgID) ? "" : item.DiscrpAgID;
            row["DateUpdate"] = isEmptyOrNull(item.DateUpdate) ? "" : moment(item.DateUpdate).format("MM/DD/YYYY");
            // row["DateUpdate"] = isEmptyOrNull(item.DateUpdate) ? new Date() : moment(item.DateUpdate).format("MM/DD/YYYY");
            row["Effective"] = isEmptyOrNull(item.Effective) ? "" : item.Effective === "0000/00/00" ? "" : moment(item.Effective).format("MM/DD/YYYY");
            row["Expire"] = isEmptyOrNull(item.Expire) ? "" : item.Expire === "0000/00/00" ? "" : moment(item.Expire).format("MM/DD/YYYY");
            row["NGUID"] = isEmptyOrNull(item.NGUID) ? "" : item.NGUID;
            row["AddCode"] = isEmptyOrNull(item.AddCode) ? "" : item.AddCode;
            row["AddDataURI"] = isEmptyOrNull(item.AddDataURI) ? "" : item.AddDataURI;
            row["PoliceZone"] = isEmptyOrNull(item.PoliceZone) ? "" : item.PoliceZone;
            row["FireZone"] = isEmptyOrNull(item.FireZone) ? "" : item.FireZone;
            row["FireAutoAssist"] = isEmptyOrNull(item.FireAutoAssist) ? "" : item.FireAutoAssist;
            row["FireMutualAssist"] = isEmptyOrNull(item.FireMutualAssist) ? "" : item.FireMutualAssist;
            // row["wreckerServiceZone"] = isEmptyOrNull(item.Seat) ? "" : item.Seat;
            row["EMSZone"] = isEmptyOrNull(item.EMSZone) ? "" : item.EMSZone;
            row["EMSAutoAssist"] = isEmptyOrNull(item.Seat) ? "" : item.EMSAutoAssist;
            row["EMSMutualAssist"] = isEmptyOrNull(item.Seat) ? "" : item.EMSMutualAssist;
            // row["action"] = ActionIcons(item);
        });
        return row;
    });

    // useEffect(() => {
    //     if (gridRef.current) {
    //         gridRef.current.dataSource = rowData;
    //         gridRef.current.refresh();
    //     }
    // }, [rowData, onToolbarRef, gridRef]);

    const navigateToMasterList = () => {
        history.push('/admin/masterImportLogs');
    }

    const handleChangePage = (event, value) => {
        setPageIndex(value);
    }

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(event.target.value);
        setPageIndex(0);
    }

    const errorTemplate = (props) => {
        const data = props.dataContext.implicit.errors;

        return (
            <div className="contact-container">
                {
                    data.map(error => <div style={{ marginTop: '10px', fontSize: '15px' }}>{error}</div>)
                }
            </div>
        );
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    const pinningConfig = new ColumnPinning();
    pinningConfig.columns = ColumnPinningPosition.End;

    return (
        <div>
            {isLoading && <CircularProgressLoader loading={isLoading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%", marginBottom: "50px" }}>
                        <div className="flex flex-1 items-center justify-between">
                            <div className="flex items-center">
                                <Tooltip title="Back to Master Logs" style={{ float: "right" }}>
                                    <Stack direction="row" spacing={2}>
                                        <Button
                                            className="backButton"
                                            variant="contained"
                                            startIcon={<ArrowBackOutlinedIcon />}
                                            onClick={() => navigateToMasterList()}
                                        >
                                            {t("back")}
                                        </Button>
                                    </Stack>
                                </Tooltip>
                            </div>
                        </div>
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={{ paddingTop: "20px" }}
                        >
                            <div className="flex items-center">
                                <DnsIcon style={{ fontSize: '40px' }} />
                                <div className="flex flex-col">
                                    <Typography
                                        component={motion.span}
                                        initial={{ x: -20 }}
                                        animate={{ x: 0, transition: { delay: 0.2 } }}
                                        delay={300}
                                        className="hidden sm:flex mx-0 sm:mx-12"
                                        variant="h6"
                                    >
                                        {t("masterAddressErrorLogs")}
                                    </Typography>
                                    <Typography
                                        component={motion.span}
                                        initial={{ x: -20 }}
                                        animate={{ x: 0, transition: { delay: 0.2 } }}
                                        delay={300}
                                        className="hidden sm:flex mx-0 sm:mx-12"
                                        variant="body2"
                                    >
                                        County: {county}, State: {countyState}
                                    </Typography>
                                </div>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                value={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText("")}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />

                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                        </div>
                    </div>
                }
                content={
                    < div className="w-full flex flex-col" >
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        'aria-label': 'Previous Page'
                                    }}
                                    nextIconButtonProps={{
                                        'aria-label': 'Next Page'
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>
                            <div>
                                <IgrGrid
                                    autoGenerate="false"
                                    data={errorLogs}
                                    primaryKey="_id"
                                    detailTemplate={errorTemplate}
                                    ref={gridRef}
                                    rowHeight={60}
                                    height={`${gridHeight}px`}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                    cssClass="custom-grid"
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="Country"
                                        header="Country"
                                        width="250px"
                                        resizable="true"
                                        groupable="true"
                                        sortable="true"
                                        pinned="false"
                                    />
                                    <IgrColumn
                                        header="State"
                                        field="State"
                                        width="250px"
                                        resizable="true"
                                        groupable="true"
                                        sortable="true"
                                        pinned="false"
                                    />
                                    <IgrColumn
                                        header="County"
                                        field="County"
                                        width="250px"
                                        resizable="true"
                                        groupable="true"
                                        sortable="true"
                                        pinned="false"
                                    />
                                    <IgrColumn
                                        header="Post_Code"
                                        field="Post_Code"
                                        width="250px"
                                        resizable="true"
                                        groupable="true"
                                        sortable="true"
                                        pinned="false"
                                    />
                                    <IgrColumn
                                        header="Street Name"
                                        field="StNam_Full"
                                        width="250px"
                                        resizable="true"
                                        groupable="true"
                                        sortable="true"
                                        pinned="false"
                                    />
                                    <IgrColumn
                                        header="Longitude"
                                        field="Longitude"
                                        width="250px"
                                        resizable="true"
                                        groupable="true"
                                        sortable="true"
                                        pinned="false"
                                    />
                                    <IgrColumn
                                        header="Latitude"
                                        field="Latitude"
                                        width="250px"
                                        resizable="true"
                                        groupable="true"
                                        sortable="true"
                                        pinned="false"
                                    />
                                </IgrGrid>
                            </div>
                        </div>

                    </div >
                }
            />
        </div >
    )
}


export default MasterImportErrorLogs;