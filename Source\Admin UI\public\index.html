<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="description" content=%REACT_APP_TITLE%>
    <meta name="keywords" content=%REACT_APP_TITLE%>
    <meta name="author" content="Withinpixels">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#000000">

    <meta http-equiv='cache-control' content='no-cache'>
    <meta http-equiv='expires' content='0'>
    <meta http-equiv='pragma' content='no-cache'>
    <base href="/">

    <link href="%PUBLIC_URL%/assets/tailwind-base.css" rel="stylesheet">


    <!-- Make sure you put this AFTER Leaflet's CSS -->

    <!--
          manifest.json provides metadata used when your web app is added to the
          homescreen on Android. See https://developers.google.com/web/fundamentals/engage-and-retain/web-app-manifest/
        -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json">
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico">

    <!--<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">-->

    <!--        You can choose main icon from variety of the material ui icon fonts-->
    <link href="%PUBLIC_URL%/assets/fonts/material-design-icons/MaterialIconsOutlined.css" rel="stylesheet">
    <!--        <link href="%PUBLIC_URL%/assets/fonts/material-design-icons/MaterialIcons.css" rel="stylesheet">-->
    <!--        <link href="%PUBLIC_URL%/assets/fonts/material-design-icons/MaterialIconsRound.css" rel="stylesheet">-->
    <!--        <link href="%PUBLIC_URL%/assets/fonts/material-design-icons/MaterialIconsSharp.css" rel="stylesheet">-->
    <!--        <link href="%PUBLIC_URL%/assets/fonts/material-design-icons/MaterialIconsTwoTone.css" rel="stylesheet">-->

    <link href="%PUBLIC_URL%/assets/fonts/inter/inter.css" rel="stylesheet">
    <link href="%PUBLIC_URL%/assets/fonts/meteocons/style.css" rel="stylesheet">

    <noscript id="emotion-insertion-point"></noscript>
    <!--
          Notice the use of %PUBLIC_URL% in the tags above.
          It will be replaced with the URL of the `public` folder during the build.
          Only files inside the `public` folder can be referenced from the HTML.

          Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
          work correctly both with client-side routing and a non-root public URL.
          Learn how to configure a non-root public URL by running `npm run build`.
        -->
    <title>%REACT_APP_TITLE%</title>

    <!-- FUSE Splash Screen CSS -->
    <style type="text/css">
        #fuse-splash-screen {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #121212;
            z-index: 99999;
            pointer-events: none;
        }

        #fuse-splash-screen .center {
            display: block;
            width: 100%;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }

        #fuse-splash-screen .logo {
            width: 128px;
            margin: 0 auto;
        }

        #fuse-splash-screen .logo img {
            filter: drop-shadow(0px 10px 6px rgba(0, 0, 0, 0.2))
        }

        #fuse-splash-screen .spinner-wrapper {
            display: block;
            position: relative;
            width: 100%;
            min-height: 100px;
            height: 100px;
        }

        #fuse-splash-screen .spinner-wrapper .spinner {
            position: absolute;
            overflow: hidden;
            left: 50%;
            margin-left: -50px;
            animation: outer-rotate 2.91667s linear infinite;
        }

        #fuse-splash-screen .spinner-wrapper .spinner .inner {
            width: 100px;
            height: 100px;
            position: relative;
            animation: sporadic-rotate 5.25s cubic-bezier(0.35, 0, 0.25, 1) infinite;
        }

        #fuse-splash-screen .spinner-wrapper .spinner .inner .gap {
            position: absolute;
            left: 49px;
            right: 49px;
            top: 0;
            bottom: 0;
            border-top: 10px solid;
            box-sizing: border-box;
        }

        #fuse-splash-screen .spinner-wrapper .spinner .inner .left,
        #fuse-splash-screen .spinner-wrapper .spinner .inner .right {
            position: absolute;
            top: 0;
            height: 100px;
            width: 50px;
            overflow: hidden;
        }

        #fuse-splash-screen .spinner-wrapper .spinner .inner .left .half-circle,
        #fuse-splash-screen .spinner-wrapper .spinner .inner .right .half-circle {
            position: absolute;
            top: 0;
            width: 100px;
            height: 100px;
            box-sizing: border-box;
            border: 10px solid #61DAFB;
            border-bottom-color: transparent;
            border-radius: 50%;
        }

        #fuse-splash-screen .spinner-wrapper .spinner .inner .left {
            left: 0;
        }

        #fuse-splash-screen .spinner-wrapper .spinner .inner .left .half-circle {
            left: 0;
            border-right-color: transparent;
            animation: left-wobble 1.3125s cubic-bezier(0.35, 0, 0.25, 1) infinite;
            -webkit-animation: left-wobble 1.3125s cubic-bezier(0.35, 0, 0.25, 1) infinite;
        }

        #fuse-splash-screen .spinner-wrapper .spinner .inner .right {
            right: 0;
        }

        #fuse-splash-screen .spinner-wrapper .spinner .inner .right .half-circle {
            right: 0;
            border-left-color: transparent;
            animation: right-wobble 1.3125s cubic-bezier(0.35, 0, 0.25, 1) infinite;
            -webkit-animation: right-wobble 1.3125s cubic-bezier(0.35, 0, 0.25, 1) infinite;
        }

        @keyframes outer-rotate {
            0% {
                transform: rotate(0deg) scale(0.5);
            }

            100% {
                transform: rotate(360deg) scale(0.5);
            }
        }

        @keyframes left-wobble {

            0%,
            100% {
                transform: rotate(130deg);
            }

            50% {
                transform: rotate(-5deg);
            }
        }

        @keyframes right-wobble {

            0%,
            100% {
                transform: rotate(-130deg);
            }

            50% {
                transform: rotate(5deg);
            }
        }

        @keyframes sporadic-rotate {
            12.5% {
                transform: rotate(135deg);
            }

            25% {
                transform: rotate(270deg);
            }

            37.5% {
                transform: rotate(405deg);
            }

            50% {
                transform: rotate(540deg);
            }

            62.5% {
                transform: rotate(675deg);
            }

            75% {
                transform: rotate(810deg);
            }

            87.5% {
                transform: rotate(945deg);
            }

            100% {
                transform: rotate(1080deg);
            }
        }
    </style>
    <!-- / FUSE Splash Screen CSS -->
</head>

<body>
    <noscript>
        You need to enable JavaScript to run this app.
    </noscript>
    <div id="root" class="flex">
        <!-- FUSE Splash Screen -->
        <div id="fuse-splash-screen">

            <div class="center">

                <div class="logo">
                    <img width="128" src="assets/images/logo/fuse.svg" alt="logo">
                </div>

                <!-- Material Design Spinner -->
                <div class="spinner-wrapper">
                    <div class="spinner">
                        <div class="inner">
                            <div class="gap"></div>
                            <div class="left">
                                <div class="half-circle"></div>
                            </div>
                            <div class="right">
                                <div class="half-circle"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- / Material Design Spinner -->

            </div>

        </div>
        <!-- / FUSE Splash Screen -->
    </div>
    <!--
          This HTML file is a template.
          If you open it directly in the browser, you will see an empty page.

          You can add webfonts, meta tags, or analytics to this file.
          The build step will place the bundled scripts into the <body> tag.

          To begin the development, run `npm start` or `yarn start`.
          To create a production bundle, use `npm run build` or `yarn build`.
        -->
</body>

</html>