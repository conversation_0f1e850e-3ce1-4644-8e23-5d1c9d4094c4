import FuseScrollbars from "@fuse/core/FuseScrollbars";
import _ from "@lodash";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import withRouter from "@fuse/core/withRouter";
import "./TwitterAutorize.css"
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import FusePageCarded from "@fuse/core/FusePageCarded";
import Typography from "@mui/material/Typography";
import Icon from "@mui/material/Icon";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import { getAndUpdateRefreshTokenFromTwitter } from "../agencyPage/store/agencySlice";



function TwitterAutorize(props) {
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const user = useSelector(({ auth }) => auth.user);

    const { t } = useTranslation("laguageConfig");

    useEffect(() => {
        let isSuperAdmin = user.data.isSuperAdmin !== undefined ? user.data.isSuperAdmin : false
        const queryString = window.location.href.split('?')[1]
        let Authorizecode = new URLSearchParams(queryString).get('code')
        const agencyCode = new URLSearchParams(queryString).get('state')
        dispatch(getAndUpdateRefreshTokenFromTwitter(Authorizecode, agencyCode, isSuperAdmin))
    }, []);

    return (
        <div className="w-full flex flex-col">
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        <div className="flex flex-1 p-32 items-center justify-between" >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    api
                                </Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("twitterAuthorize")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>

                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>

                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div class="wrapper">
                            <div class="bird">
                                <div class="body"></div>
                                <div class="tail-cut"></div>
                                <div class="feather-bottom"></div>
                                <div class="feather-bottom-cut"></div>
                                <div class="feather-middle"></div>
                                <div class="feather-middle-cut"></div>
                                <div class="feather-top"></div>
                                <div class="body-top-cut"></div>
                                <div class="mouth-bottom"></div>
                                <div class="mouth-bottom-cut"></div>
                                <div class="mouth-top"></div>
                                <div class="mouth-top-cut"></div>
                                <div class="head"></div>
                            </div>
                        </div>
                    </div>
                }
                innerScroll
            />

        </div>
    );
}

export default withRouter(TwitterAutorize);