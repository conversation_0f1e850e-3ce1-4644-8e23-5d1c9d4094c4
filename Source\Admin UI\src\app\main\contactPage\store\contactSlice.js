import { createSlice } from '@reduxjs/toolkit';
import history from '@history';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from "axios";
import { encrypt, decrypt } from '../../../security/crypto';

const initialState = {
    success: false,
    isloading: false,
    error: {
        contact: {}
    },
    data: [],
    searchcontacts: [],
    contactData: [],
    searchText: '',
    contactID: '0'
};

const contactSlice = createSlice({
    name: 'contact',
    initialState,
    reducers: {
        setContactTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setContactDetails: (state, action) => {
            state.data = action.payload;
        },
        setContactList: (state, action) => {
            state.data = action.payload;
        },
        setSearchcontacts: (state, action) => {
            state.searchcontacts = action.payload;
        },
        setSearchText: (state, action) => {
            state.searchText = action.payload;
        },
        setContactId: (state, action) => {
            state.contactID = action.payload;
        },
        setContactBYID: (state, action) => {
            state.contactData = action.payload;
        },
        setRemoveContactID: (state, action) => {
            state.data = state.data.filter(({ _id }) => _id !== action.payload)
        },
        setContactEditData: (state, action) => {
            const index = state.data.findIndex(obj => obj._id === action.payload._id);
            if (index !== -1) {
                state.data[index] = action.payload;
            }
        },
        setContactAddData: (state, action) => {
            state.data = [...state.data, action.payload];
        },
        removeContactData: (state, action) => {
            state.data = state.data.filter(x => x._id !== action.payload);
        },
    },
    extraReducers: {}
});

export default contactSlice.reducer;

// Actions
export const {
    setContactTotalCount,
    setLoading,
    setContactDetails,
    setContactList,
    setSearchText,
    setContactId,
    setSearchcontacts,
    setContactBYID,
    setRemoveContactID,
    setContactEditData,
    setContactAddData,
    removeContactData
} = contactSlice.actions;

//for searching
export const searchContact = (searchText, code) => async dispatch => {
    try {
        axios.get(`admin/api/contact/searchContact/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    return dispatch(setSearchcontacts(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

//Get contact List
export const getContactList = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/contact/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setContactTotalCount(listData.totalCount));
                    dispatch(setLoading(false));
                    return dispatch(setContactDetails(listData.contactList));
                    // return dispatch(setContactList(JSON.parse(decrypt(response.data))));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (e) {
        return console.error(e);
    }
};

// To add new Contact
export const newContact = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/contact/contactAdd`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 400) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }

                else if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    dispatch(setContactAddData(response.newData))
                    history.push(`/admin/contactList/${data.code}`);
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }

            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

// To update Contact
export const updateContact = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/contact/contactEdit`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status === 400) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else if (response.status === 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setContactEditData(response.newData))
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    history.push(`/admin/contactList/${data.code}`);
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }

            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),


                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//To get Contact By ID
export const getContactByID = (_id, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/contact/contactGetByID/${_id}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    return dispatch(setContactBYID(JSON.parse(decrypt(response.data))));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }
            );

    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//Remove Contact
export const removeContact = (_id, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/contact/contactDelete/${_id}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 1500,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                    return dispatch(setRemoveContactID(_id));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }
            );
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//To set Users Search Text
export const setUsersSearchText = (event) => async dispatch => {
    try {
        return dispatch(setSearchText(event.target.value));
    } catch (e) {
        return console.error(e.message);
    }
}

//To set Contact ID
export const setContactID = (contactID) => async dispatch => {
    try {
        return dispatch(setContactId(contactID));
    } catch (e) {
        return console.error(e.message);
    }
}

