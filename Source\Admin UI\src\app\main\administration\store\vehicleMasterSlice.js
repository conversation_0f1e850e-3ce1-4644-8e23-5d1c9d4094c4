import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';

export const getVehicleDetails = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/vehicleSearch/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setVehicleDetailsTotalCount(listData.totalCount));
                    dispatch(setLoading(false));
                    return dispatch(setVehicleDetails(listData.vehicleMasterList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const SaveVehicle = (data, tempData) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/vehicleSearch/VehicleCreate`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    //access the tempData required for get request
                    dispatch(getVehicleDetails(tempData.orderId, tempData.orderDirection, tempData.pageIndex, tempData.rowsPerPage, tempData.searchText, data.code));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }));
                    return dispatch(vehicleSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const UpdateVehicle = (data, tempData) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/vehicleSearch/VehicleUpdate`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(getVehicleDetails(tempData.orderId, tempData.orderDirection, tempData.pageIndex, tempData.rowsPerPage, tempData.searchText, data.code));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    return dispatch(vehicleSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const removeVehicle = (id, sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/vehicleSearch/deleteVehicle/${id}/${code}`)
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(getVehicleDetails(sortField, sortDirection, pageIndex, pageLimit, searchText, code));
                    dispatch(showMessage({
                        message: "Vehicle successfully removed.",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                    return dispatch(vehicleSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: 'Error Occured!', autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'error'
                }));
                return dispatch(vehicleSuccess(false));
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getVehicleOptionsListFromDB = () => async dispatch => {
    try {
        return axios.get(`dispatch/api/dispatchvehicle/getVehicleOptionsListFromDB`)
            .then(async (response) => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setVehicleMakeList(response.data.vehicleMakeList));
                    dispatch(setVehicleColorList(response.data.vehicleColorList));
                    dispatch(setVehicleModelsList(response.data.vehicleModelList));
                    dispatch(setVehicleTagStateList(response.data.vehicleTagStateList));
                    dispatch(setVehicleYearsList(response.data.vehicleYearsList));
                    return;
                }
            })
            .catch(error => {
                return console.error(error.message);
            });
    } catch (e) {
        return console.error(e.message);
    }
};

const initialState = {
    data: [],
    vehicleSuccess: false,
    isloading: false,
    totalCount: 0,
    vehicleMakeList: [],
    vehicleColorList: [],
    vehicleModelsList: [],
    tagStateList: [],
    vehicleYearsList: [],
};

const vehicleDetailSlice = createSlice({
    name: 'administration/vehicleMaster',
    initialState,
    reducers: {
        setVehicleDetails: (state, action) => {
            state.data = action.payload;
        },
        setVehicleDetailsTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        vehicleSuccess: (state, action) => {
            state.vehicleSuccess = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setVehicleMakeList: (state, action) => {
            state.vehicleMakeList = action.payload;
        },
        setVehicleColorList: (state, action) => {
            state.vehicleColorList = action.payload;
        },
        setVehicleModelsList: (state, action) => {
            state.vehicleModelsList = action.payload;
        },
        setVehicleTagStateList: (state, action) => {
            state.tagStateList = action.payload;
        },
        setVehicleYearsList: (state, action) => {
            state.vehicleYearsList = action.payload;
        },
    },
    extraReducers: {}
});

export const {
    setVehicleDetails,
    setVehicleDetailsTotalCount,
    vehicleSuccess,
    setLoading,
    setVehicleMakeList,
    setVehicleColorList,
    setVehicleModelsList,
    setVehicleTagStateList,
    setVehicleYearsList,
} = vehicleDetailSlice.actions;

export default vehicleDetailSlice.reducer;