import { useDispatch, useSelector } from 'react-redux';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import Grid from "@mui/material/Grid";
import "./GlobalSearchData.css"
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import Divider from '@mui/material/Divider';
import ListItemText from '@mui/material/ListItemText';
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import { getGlobalPersonData } from '../../store/personSlice';
import FormLabel from '@mui/material/FormLabel';
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import { useTranslation } from "react-i18next";

function GlobalSearchData(props) {
    const dispatch = useDispatch();
    const [id, setId] = React.useState("");
    const navbarTheme = useSelector(selectNavbarTheme);
    const { t } = useTranslation("laguageConfig");

    const globalPersonData = useSelector(
        ({ dispatchCallReducer }) => dispatchCallReducer.person.globalPersonData
    );
    function getGlobalSearchDetails(searchText) {
        dispatch(getGlobalPersonData(searchText._id));
        setId(searchText._id)
    }

    useEffect(() => {
        if (props.globalFlag == "false") {
            setId("")
        }
    }, [props.globalFlag]);

    return (
        <div className='padding'>
            {globalPersonData.length > 0 &&
                <Card>
                    <CardContent>
                        <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                            <FormLabel component="legend" className='font-extrabold'>{t("globalPersonSearch")}</FormLabel>
                            <List sx={{
                                width: '100%',
                                bgcolor: 'background.paper',
                                position: 'relative',
                                overflow: 'auto',
                                maxHeight: 250,
                                '& ul': { padding: 0 },
                            }}
                            >
                                {globalPersonData && globalPersonData.map((item) =>
                                    <ListItem className='pointer'
                                        style={item._id === id
                                            ? { backgroundColor: navbarTheme.palette.primary.main, color: navbarTheme.palette.primary.contrastText }
                                            : {}
                                        }
                                        alignItems="flex-start">
                                        <ListItemText
                                            primary={item.FullName + ', ' + item.Sex + ', ' + item.Race + ', ' + new Date(item.DateOfBirth).toDateString()}
                                            onClick={() => getGlobalSearchDetails(item)}
                                        />
                                        <Divider />
                                    </ListItem>
                                )}
                            </List>
                        </Grid>
                    </CardContent>
                </Card>
            }
        </div>
    );
}

export default GlobalSearchData;
