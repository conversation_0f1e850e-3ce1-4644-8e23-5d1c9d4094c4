import history from "@history";
import { yupResolver } from "@hookform/resolvers/yup";
import { Card, CardContent, Grid } from "@mui/material";
import Button from "@mui/material/Button";
import { useEffect, useRef, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import * as yup from "yup";
import { checkValueEmptyOrNull } from "../../utils/utils";
import AppHeaderBar from "../components/AppHeaderBar";
import {
  getAllPicklistOptions,
  getCityCountyStateZipCode,
  getCountryList,
  getStreetNamePreTypesPostTypesAndSeparators,
  saveMasterAddress,
  updateMasterAddress,
  uploadFileObject,
} from "../store/masterAddressSlice";
import AddressInfoFields from "./AddressInfoFields";
import FormTextField from "../../SharedComponents/SharedFormFields/FormTextField";
import makeStyles from '@mui/styles/makeStyles';

const useStyles = makeStyles({
  w50: {
    width: '100%',
    marginBottom: "1.6rem"
  }, root: {
    display: 'flex',
  },
  cardImage: {
    width: '305px',
    height: '50px',
    marginTop: '10px',
    paddingLeft: '5px',
  },
});

/*
 * Place validation rules in schema,
 * e.g:
 * AddrNoPrefix: yup
    .string()
    .max(5, 'Address Number Prefix cannot exceed 5 characters')
    .matches(/^[a-zA-Z0-9]+$/, 'Address Number Prefix must contain only letters and numbers'),
*/
const schema = yup.object().shape({
  addNumber: yup.string(),
  AddrNoPrefix: yup.string(),
  NumberSuffix: yup.string(),
  StreetName: yup.string(),
  Zip: yup.string(),
  Latitude: yup.string(),
  Longitude: yup.string(),
});

const defaultValues = {
  AddressInfo: "",
  addNumber: "",
  AddrNoPrefix: "",
  NumberSuffix: "",
  StreetName: "",
  StreetDirOfTravel: "",
  MilePost: "",
  Site: "",
  SubSite: "",
  Structure: "",
  Wing: "",
  Building: "",
  Floor: "",
  UnitPreType: "",
  UnitValue: "",
  Room: "",
  Section: "",
  Row: "",
  Seat: "",
  Zip: "",
  AdditionalLocationInfo: "",
  NERISAdditionalAttributes: "",
  Marker: "",
  LegacyStreetName2: "",
  AliasStreetName: "",
  Latitude: "",
  Longitude: "",
  Elevation: "",
  FIPS: "",
  NERISID: "",
  NibrsLocType: "",
  NearestCrossStreet: "",
  Distance: "",
  NxtDistance: "",
  NxtNearestCross: "",
  UnincorporatedMuni: "",
  landmarkName: "",
  PSAP: "",
  ESN: "",
  MSAGCommunity: "",
  MapPage: "",
  AddressLabel: "",
  NationalGrid: "",
  DiscrepancyAgencyID: "",
  DateAdded: null,
  DateUpdated: null,
  EffectiveDate: null,
  ExpirationDate: null,
  NGUID: "",
  AddCode: "",
  AddressDataURL: "",
  PoliceZone: "",
  FireZone: "",
  FireAutoAssist: "",
  FireMutualAssist: "",
  WreckerServiceZone: "",
  EMSZone: "",
  EMSAutoAssist: "",
  EMSMutualAssist: "",
  placement: "",
  neighborhoodCommunity: "",
  incorporatedMuni: "",
  notes: "",
  tags: "",
};

const MasterAddressPointDetail = () => {
  const classes = useStyles();
  const { t } = useTranslation("laguageConfig");
  const dispatch = useDispatch();
  const fileUpload = useRef(null);

  const methods = useForm({
    mode: "onChange",
    defaultValues,
    resolver: yupResolver(schema),
  });

  const {
    setValue,
    handleSubmit,
    reset,
    watch,
  } = methods;

  const {
    MasterAddressData,
    streetPreModifierList,
    streetPostModifierList,
    streetPredirectionAndPostDirectionList,
    placeTypeList,
    countryList,
    streetNamePreTypesPostTypes,
    streetNamePreTypeSeparatorsList,
    cityList,
    countyList,
    stateList,
    zipcodeList,
    selectedCounty,
    selectedCountyStateCode,
  } = useSelector((state) => state.administration.masterAddressSlice);

  const [filteredCityList, setFilteredCityList] = useState(cityList ?? []);
  const [filteredCountyList, setFilteredCountyList] = useState(
    countyList ?? []
  );
  const [filteredZipCodeList, setFilteredZipCodeList] = useState(
    zipcodeList ?? []
  );
  const [fileurl, setfileurl] = useState('');

  const preMod = watch("preMod");
  const preDirection = watch("preDirection");
  const preType = watch("preType");
  const preSep = watch("preSep");
  const streetType = watch("streetType");
  const postDir = watch("postDir");
  const postMod = watch("postMod");
  const cityPostalComm = watch("cityPostalComm");
  const state = watch("state");
  const zipPostCode = watch("zipPostCode");
  const country = watch("country");
  const county = watch("county");
  const placeType = watch("placeType");
  const preDir = watch("preDir");
  const legacyStreetType = watch("legacyStreetType");
  const legacyPostDir = watch("legacyPostDir");
  const aliasPreDir = watch("aliasPreDir");
  const aliasStreetType = watch("aliasStreetType");
  const aliasPostDir = watch("aliasPostDir");
  const pageType = watch("pageType", "ADD");
  const addressId = watch("addressId", "0");

  useEffect(() => {
    dispatch(getAllPicklistOptions());
    dispatch(getCountryList());
    dispatch(getStreetNamePreTypesPostTypesAndSeparators());
  }, [dispatch]);

  useEffect(() => {
    if (selectedCounty && selectedCountyStateCode) {
      dispatch(
        getCityCountyStateZipCode(selectedCounty, selectedCountyStateCode)
      );
      setValue("county", selectedCounty);
      setValue("state", selectedCountyStateCode);
    }
  }, [selectedCounty, selectedCountyStateCode, dispatch, setValue]);

  useEffect(() => {
    setFilteredCityList(Array.isArray(cityList) ? cityList : []);
    setFilteredCountyList(Array.isArray(countyList) ? countyList : []);
    setFilteredZipCodeList(Array.isArray(zipcodeList) ? zipcodeList : []);

    if (
      selectedCounty &&
      Array.isArray(stateList) &&
      Array.isArray(countyList) &&
      Array.isArray(cityList)
    ) {
      const selectedAddressCounty = countyList.find(
        (x) => x.CountyName === selectedCounty
      );
      const selectedAddressCountyState = stateList.find(
        (x) => x.StateCode === selectedCountyStateCode
      );

      if (selectedAddressCounty) {
        setValue("county", selectedAddressCounty);
      }
      if (selectedAddressCountyState) {
        setValue("state", selectedAddressCountyState);
        setFilteredCityList(
          cityList.filter((x) => x.StateCode === selectedCountyStateCode)
        );
      }
    }
  }, [
    cityList,
    countyList,
    zipcodeList,
    stateList,
    selectedCounty,
    selectedCountyStateCode,
    setValue,
  ]);

  useEffect(() => {
    if (!MasterAddressData || typeof MasterAddressData !== "object") {
      setValue("addressId", "0");
    } else {
      setValue("pageType", "UPDATE");
      setValue("addressId", MasterAddressData._id);

      // Set autocomplete values with defensive checks
      setValue(
        "preMod",
        Array.isArray(streetPreModifierList)
          ? streetPreModifierList.find(
            (x) => x.name === MasterAddressData.St_PreMod
          ) || null
          : null
      );
      setValue(
        "preDirection",
        Array.isArray(streetPredirectionAndPostDirectionList)
          ? streetPredirectionAndPostDirectionList.find(
            (x) => x.name === MasterAddressData.St_PreDir
          ) || null
          : null
      );
      setValue(
        "preType",
        MasterAddressData.St_PreTyp &&
          Array.isArray(streetNamePreTypesPostTypes)
          ? streetNamePreTypesPostTypes.find(
            (x) => x.Value === MasterAddressData.St_PreTyp
          ) || { _id: "local", Value: MasterAddressData.St_PreTyp }
          : null
      );
      setValue(
        "preSep",
        Array.isArray(streetNamePreTypeSeparatorsList)
          ? streetNamePreTypeSeparatorsList.find(
            (x) => x.Value === MasterAddressData.St_PreSep
          ) || null
          : null
      );
      setValue(
        "streetType",
        MasterAddressData.St_PosTyp &&
          Array.isArray(streetNamePreTypesPostTypes)
          ? streetNamePreTypesPostTypes.find(
            (x) => x.Value === MasterAddressData.St_PosTyp
          ) || { _id: "local", Value: MasterAddressData.St_PosTyp }
          : null
      );
      setValue(
        "postDir",
        Array.isArray(streetPredirectionAndPostDirectionList)
          ? streetPredirectionAndPostDirectionList.find(
            (x) => x.name === MasterAddressData.St_PosDir
          ) || null
          : null
      );
      setValue(
        "postMod",
        Array.isArray(streetPostModifierList)
          ? streetPostModifierList.find(
            (x) => x.name === MasterAddressData.St_PosMod
          ) || null
          : null
      );
      setValue(
        "cityPostalComm",
        Array.isArray(cityList)
          ? cityList.find((x) => x.CityName === MasterAddressData.Post_Comm) ||
          null
          : null
      );
      setValue(
        "state",
        Array.isArray(stateList)
          ? stateList.find((x) => x.StateCode === MasterAddressData.State) ||
          null
          : null
      );
      setValue(
        "zipPostCode",
        Array.isArray(zipcodeList)
          ? zipcodeList.find(
            (x) => x.ZipCode === MasterAddressData.Post_Code
          ) || null
          : null
      );
      setValue(
        "country",
        Array.isArray(countryList)
          ? countryList.find((x) => x.name === MasterAddressData.Country) ||
          null
          : null
      );
      setValue(
        "county",
        Array.isArray(countyList)
          ? countyList.find((x) => x.CountyName === MasterAddressData.County) ||
          null
          : null
      );
      setValue(
        "placeType",
        Array.isArray(placeTypeList)
          ? placeTypeList.find((x) => x.name === MasterAddressData.PlaceType) ||
          null
          : null
      );
      setValue(
        "preDir",
        Array.isArray(streetPredirectionAndPostDirectionList)
          ? streetPredirectionAndPostDirectionList.find(
            (x) => x.name === MasterAddressData.lst_predir
          ) || null
          : null
      );
      setValue(
        "legacyStreetType",
        Array.isArray(streetNamePreTypesPostTypes)
          ? streetNamePreTypesPostTypes.find(
            (x) => x.Value === MasterAddressData.lst_type
          ) || null
          : null
      );
      setValue(
        "legacyPostDir",
        Array.isArray(streetPredirectionAndPostDirectionList)
          ? streetPredirectionAndPostDirectionList.find(
            (x) => x.name === MasterAddressData.lst_posdir
          ) || null
          : null
      );
      setValue(
        "aliasPreDir",
        Array.isArray(streetPredirectionAndPostDirectionList)
          ? streetPredirectionAndPostDirectionList.find(
            (x) => x.name === MasterAddressData.aliasPreDir
          ) || null
          : null
      );
      setValue(
        "aliasStreetType",
        Array.isArray(streetNamePreTypesPostTypes)
          ? streetNamePreTypesPostTypes.find(
            (x) => x.Value === MasterAddressData.aliasStreetType
          ) || null
          : null
      );
      setValue(
        "aliasPostDir",
        Array.isArray(streetPredirectionAndPostDirectionList)
          ? streetPredirectionAndPostDirectionList.find(
            (x) => x.name === MasterAddressData.aliasPostDir
          ) || null
          : null
      );

      // Set form values with defensive checks
      const fields = [
        ["AddrNoPrefix", MasterAddressData.AddNum_Pre],
        ["addNumber", MasterAddressData.addNumber],
        ["Latitude", MasterAddressData.Latitude],
        ["Longitude", MasterAddressData.Longitude],
        ["Elevation", MasterAddressData.Elevation],
        ["Marker", MasterAddressData.Marker],
        ["FIPS", MasterAddressData.FIPS],
        ["NERISID", MasterAddressData.NERISID],
        ["NibrsLocType", MasterAddressData.NibrsLocType],
        ["NumberSuffix", MasterAddressData.AddNum_Suf],
        ["StreetName", MasterAddressData.St_Name],
        ["StreetDirOfTravel", MasterAddressData.St_DirOfTravel],
        ["NearestCrossStreet", MasterAddressData.CrossStreets[0]?.CrossStreetName],
        ["Distance", MasterAddressData.CrossStreets[0]?.CrossStreetDist],
        ["NxtNearestCross", MasterAddressData.CrossStreets[1]?.CrossStreetName],
        ["NxtDistance", MasterAddressData.CrossStreets[1]?.CrossStreetDist],
        ["incorporatedMuni", MasterAddressData.Inc_Muni],
        ["UnincorporatedMuni", MasterAddressData.Uninc_Comm],
        ["landmarkName", MasterAddressData.landmarkName],
        ["neighborhoodCommunity", MasterAddressData.Nbrhd_Comm],
        ["PSAP", MasterAddressData.PSAP],
        ["ESN", MasterAddressData.ESN],
        ["MSAGCommunity", MasterAddressData.MSAGComm],
        ["MapPage", MasterAddressData.MapPage],
        ["AddressLabel", MasterAddressData.adr_label],
        ["placement", MasterAddressData.Placement],
        ["NationalGrid", MasterAddressData.NationalGrid],
        ["DiscrepancyAgencyID", MasterAddressData.DiscrpAgID],
        ["NGUID", MasterAddressData.NGUID],
        ["AddCode", MasterAddressData.AddCode],
        ["AddressDataURL", MasterAddressData.AddDataURI],
        ["PoliceZone", MasterAddressData.PoliceZone],
        ["FireZone", MasterAddressData.FireZone],
        ["FireAutoAssist", MasterAddressData.FireAutoAssist],
        ["FireMutualAssist", MasterAddressData.FireMutualAssist],
        ["WreckerServiceZone", MasterAddressData.WreckerServiceZone],
        ["EMSZone", MasterAddressData.EMSZone],
        ["EMSAutoAssist", MasterAddressData.EMSAutoAssist],
        ["EMSMutualAssist", MasterAddressData.EMSMutualAssist],
        ["LegacyStreetName2", MasterAddressData.lst_name],
        ["AliasStreetName", MasterAddressData.AliasStreetName],
        ["notes", MasterAddressData.notes],
        ["tags", MasterAddressData.tags],
        ["Site", MasterAddressData.Site],
        ["SubSite", MasterAddressData.SubSite],
        ["Structure", MasterAddressData.Structure],
        ["Wing", MasterAddressData.Wing],
        ["Building", MasterAddressData.Building],
        ["Floor", MasterAddressData.Floor],
        ["UnitPreType", MasterAddressData.UnitPreType],
        ["UnitValue", MasterAddressData.UnitValue],
        ["Room", MasterAddressData.Room],
        ["Section", MasterAddressData.Section],
        ["Row", MasterAddressData.Row],
        ["Seat", MasterAddressData.Seat],
        ["AdditionalLocationInfo", MasterAddressData.Addtl_Loc],
        ["NERISAdditionalAttributes", MasterAddressData.AdditionalAttributes],
        ["MilePost", MasterAddressData.MilePost],
        [
          "DateAdded",
          MasterAddressData.DateAdded
            ? new Date(MasterAddressData.DateAdded)
            : new Date(),
        ],
        [
          "DateUpdated",
          MasterAddressData.DateUpdate
            ? new Date(MasterAddressData.DateUpdate)
            : new Date(),
        ],
        [
          "EffectiveDate",
          MasterAddressData.Effective
            ? new Date(MasterAddressData.Effective)
            : new Date(),
        ],
        [
          "ExpirationDate",
          MasterAddressData.Expire
            ? new Date(MasterAddressData.Expire)
            : new Date(),
        ],
      ];

      fields.forEach(([field, value]) => setValue(field, value ?? ""));
      setfileurl(MasterAddressData.fileUrl);
    }
  }, [MasterAddressData, setValue]);

  const handleZipPostCode = (event, value) => {
    setValue("zipPostCode", value);
    if (value) {
      const city = cityList.find((x) => x.CityName === value.CityName);
      setValue("cityPostalComm", city || null);
    } else {
      setValue("cityPostalComm", null);
    }
  };

  const handleStateChange = (event, value) => {
    setValue("state", value);
    setFilteredCityList(
      value ? cityList.filter((x) => x.StateCode === value.StateCode) : cityList
    );
  };

  const clear = () => {
    reset(defaultValues);
    setValue("pageType", "ADD");
    setValue("addressId", "0");
  };

  const navigateBack = () => {
    history.push("/admin/masterAddress");
    clear();
  };

  // const uploadMultipleFiles = (e) => {
  //   if (e.target.files) {
  //     let temp = {
  //       url: URL.createObjectURL(e.target.files[0]),
  //       type: e.target.files[0].type,
  //     };
  //   }
  // }

  const onSubmit = (model) => {
    const crossStreetLocation = [
      {
        CrossStreetName: model.NearestCrossStreet ?? "",
        CrossStreetType: "Nearest",
        CrossStreetDist: model.Distance ?? "",
      },
      {
        CrossStreetName: model.NxtDistance ?? "",
        CrossStreetType: "Second Nearest",
        CrossStreetDist: model.NxtNearestCross ?? "",
      },
    ];

    const addressParts = [
      model.AddrNoPrefix ?? "",
      model.addNumber ?? "",
      model.NumberSuffix ?? "",
      preMod?.name ?? "",
      preDirection?.name ?? "",
      preType?.Value ?? "",
      preSep?.Value ?? "",
      model.StreetName ?? "",
      streetType?.Value ?? "",
      postDir?.name ?? "",
      postMod?.name ?? ""
    ];

    const addressLine2Parts = [
      model.Site ?? "",
      model.SubSite ?? "",
      model.Structure ?? "",
      model.Wing ?? "",
      model.Building ?? "",
      model.Floor ?? ""
    ];

    const addressLine3Parts = [
      model.UnitPreType ?? "",
      model.UnitValue ?? "",
      model.Room ?? "",
      model.Section ?? "",
      model.Row ?? "",
      model.Seat ?? "",
      model.AdditionalLocationInfo ?? ""
    ];

    const addressLine4Parts = [
      cityPostalComm?.CityName ?? "",
      state?.StateCode ?? "",
      zipPostCode?.ZipCode ?? "",
    ];

    const data = {
      _id: addressId,
      AddNum_Pre: model.AddrNoPrefix ?? "",
      addNumber: model.addNumber ?? "",
      AddNum_Suf: model.NumberSuffix ?? "",
      St_PreMod: preMod?.name ?? "",
      St_PreDir: preDirection?.name ?? "",
      St_PreTyp: preType?.Value ?? "",
      St_PreSep: preSep?.Value ?? "",
      St_Name: model.StreetName ?? "",
      St_PosTyp: streetType?.Value ?? "",
      St_PosDir: postDir?.name ?? "",
      St_PosMod: postMod?.name ?? "",
      Site: model.Site ?? "",
      SubSite: model.SubSite ?? "",
      Structure: model.Structure ?? "",
      Wing: model.Wing ?? "",
      Building: model.Building ?? "",
      Floor: model.Floor ?? "",
      UnitPreType: model.UnitPreType ?? "",
      UnitValue: model.UnitValue ?? "",
      Room: model.Room ?? "",
      Section: model.Section ?? "",
      Row: model.Row ?? "",
      Seat: model.Seat ?? "",
      Addtl_Loc: model.AdditionalLocationInfo ?? "",
      DiscrpAgID: model.DiscrepancyAgencyID ?? "",
      DateUpdate: model.DateUpdated ?? null,
      Effective: model.EffectiveDate ?? null,
      Expire: model.ExpirationDate ?? null,
      NGUID: model.NGUID ?? "",
      Country: country?.name ?? null,
      State: state?.StateCode ?? null,
      County: county?.CountyName ?? null,
      AddCode: model.AddCode ?? "",
      AddDataURI: model.AddressDataURL ?? "",
      Inc_Muni: model.incorporatedMuni ?? "",
      Uninc_Comm: model.UnincorporatedMuni ?? "",
      landmarkName: model.landmarkName ?? "",
      Nbrhd_Comm: model.neighborhoodCommunity ?? "",
      Post_Comm: cityPostalComm?.CityName ?? "",
      Post_Code: zipPostCode?.ZipCode ?? "",
      PostCodeEx: model.Zip ?? "",
      St_DirOfTravel: model.StreetDirOfTravel ?? "",
      StNam_Full: `${checkValueEmptyOrNull(
        preDirection?.name
      )} ${checkValueEmptyOrNull(model.StreetName)} ${checkValueEmptyOrNull(
        preType?.Value
      )}`.trim(),
      Adr_Num_Comp: `${checkValueEmptyOrNull(
        model.Add_Number
      )} ${checkValueEmptyOrNull(model.AddrNoPrefix)}-${checkValueEmptyOrNull(
        model.NumberSuffix
      )}`.trim(),
      MilePost: model.MilePost ?? "",
      AdditionalAttributes: model.NERISAdditionalAttributes ?? "",
      Marker: model.Marker ?? "",
      PlaceType: placeType?.name ?? "",
      lst_predir: preDir?.name ?? "",
      lst_name: model.LegacyStreetName2 ?? "",
      lst_type: legacyStreetType?.Value ?? "",
      lst_posdir: legacyPostDir?.name ?? "",
      AliasStreetName: model.AliasStreetName ?? "",
      aliasStreetType: aliasStreetType?.Value ?? "",
      aliasPreDir: aliasPreDir?.name ?? "",
      aliasPostDir: aliasPostDir?.name ?? "",
      Longitude: model.Longitude ?? "",
      Latitude: model.Latitude ?? "",
      Elevation: model.Elevation ?? "",
      FIPS: model.FIPS ?? "",
      NERISID: model.NERISID ?? "",
      NibrsLocType: model.NibrsLocType ?? "",
      CrossStreets: crossStreetLocation,
      adr_label: model.AddressLabel ?? "",
      Placement: model.placement ?? "",
      MapPage: model.MapPage ?? "",
      PoliceZone: model.PoliceZone ?? "",
      FireZone: model.FireZone ?? "",
      FireAutoAssist: model.FireAutoAssist ?? "",
      FireMutualAssist: model.FireMutualAssist ?? "",
      EMSZone: model.EMSZone ?? "",
      EMSAutoAssist: model.EMSAutoAssist ?? "",
      EMSMutualAssist: model.EMSMutualAssist ?? "",
      PSAP: model.PSAP ?? "",
      ESN: model.ESN ?? "",
      MSAGComm: model.MSAGCommunity ?? "",
      NationalGrid: model.NationalGrid ?? "",
      notes: model.notes ?? "",
      tags: model.tags ?? "",
      AddressFull1: addressParts.filter(Boolean).join(" "),
      AddressFull2: addressLine2Parts.filter(Boolean).join(" "),
      AddressFull3: addressLine3Parts.filter(Boolean).join(" "),
      AddressFull4: addressLine4Parts.filter(Boolean).join(" "),
    };

    const file = fileUpload?.current?.files[0] ?? null;
    const formData = new FormData();
    formData.append("file", file);

    if (addressId === "0") {
      dispatch(saveMasterAddress(data, formData, file));
    } else {
      dispatch(updateMasterAddress(data, formData, file));
    }
    navigateBack();
  };

  const handleAutocompleteChange = (fieldName) => (event, value) => {
    setValue(fieldName, value);
  };

  return (
    <Card className="m-16 rounded-8 shadow">
      <AppHeaderBar headerText={t("masterAddressPointDetail")} />
      <CardContent style={{ overflowX: "scroll" }}>
        <div className="w-full p-16">
          <FormProvider {...methods}>
            <form
              className="flex flex-col justify-center w-full"
              onSubmit={handleSubmit(onSubmit)}
              autoComplete="off"
            >
              <AddressInfoFields
                cityPostalComm={cityPostalComm}
                country={country}
                countryList={countryList}
                county={county}
                preDirection={preDirection}
                filteredCityList={filteredCityList}
                filteredCountyList={filteredCountyList}
                filteredZipCodeList={filteredZipCodeList}
                handleAutocompleteChange={handleAutocompleteChange}
                handleStateChange={handleStateChange}
                handleZipPostCode={handleZipPostCode}
                placeType={placeType}
                placeTypeList={placeTypeList}
                postDir={postDir}
                postMod={postMod}
                preMod={preMod}
                preSep={preSep}
                preType={preType}
                state={state}
                stateList={stateList}
                streetNamePreTypeSeparatorsList={
                  streetNamePreTypeSeparatorsList
                }
                streetNamePreTypesPostTypes={streetNamePreTypesPostTypes}
                streetPostModifierList={streetPostModifierList}
                streetPredirectionAndPostDirectionList={
                  streetPredirectionAndPostDirectionList
                }
                streetPreModifierList={streetPreModifierList}
                streetType={streetType}
                zipPostCode={zipPostCode}
                preDir={preDir}
                legacyPostDir={legacyPostDir}
                legacyStreetType={legacyStreetType}
                aliasPostDir={aliasPostDir}
                aliasStreetType={aliasStreetType}
                aliasPreDir={aliasPreDir}
              />

              <Grid container spacing={1}>
                <FormTextField
                  name="notes"
                  label={t("notes")}
                  gridProps={{ xs: 6, sm: 6, md: 6 }}
                  multiline={true}
                  rows={4}
                />

                <FormTextField
                  name="tags"
                  label={t("tags")}
                  gridProps={{ xs: 6, sm: 6, md: 6 }}
                  multiline={true}
                  rows={4}
                />
              </Grid>

              <input
                type="file"
                className={classes.w50}
                ref={fileUpload}
                accept="image/*"
                style={{
                  padding: '13px',
                  border: '1px solid lightgray',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginTop: '8px',
                  height: '53px',
                }}
              />

              {(fileurl) ?
                <img src={fileurl} className={classes.cardImage} alt="..."></img>
                :
                ""
              }

              <div className="mx-auto">
                <Button
                  type="submit"
                  variant="contained"
                  color="secondary"
                  className="normal-case m-16"
                >
                  {pageType === "ADD" ? t("save") : t("update")}
                </Button>
                <Button
                  type="button"
                  variant="contained"
                  color="secondary"
                  className="normal-case m-16"
                  onClick={navigateBack}
                >
                  {t("back")}
                </Button>
              </div>
            </form>
          </FormProvider>
        </div>
      </CardContent>
    </Card>
  );
};

export default MasterAddressPointDetail;
