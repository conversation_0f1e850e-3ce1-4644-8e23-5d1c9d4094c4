import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import TablePagination from "@mui/material/TablePagination";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import {
    TextField, ThemeProvider, StyledEngineProvider, Paper,
} from "@mui/material";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import { useParams } from "react-router";
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { sendNotifications, getNotificatios } from "../../store/quikTipSlice";
import './Notifications.css';
import moment from "moment";
import { getNavbarTheme, getRowsPerPageOptions, isEmptyOrNull, useWindowResizeHeight } from "../../utils/utils";
import { showMessage } from "app/store/fuse/messageSlice";
import { ErrorBoundary } from "react-error-boundary";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn, ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "id",
        sort: true,
    },
    {
        id: "userID",
        align: "left",
        disablePadding: false,
        label: "Sender",
        sort: true,
    },
    {
        id: "userName",
        align: "left",
        disablePadding: false,
        label: "Sender",
        sort: true,
    },
    {
        id: "message",
        align: "left",
        disablePadding: false,
        label: "Pushed Message",
        sort: true,
    },
    {
        id: "createdDateTime",
        align: "left",
        disablePadding: false,
        label: "Create Date Time",
        sort: true,
    },
];

IgrGridModule.register();

function Notifications() {
    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);
    const gridRef = useRef(null);
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const user = useSelector(({ auth }) => auth.user);
    const [searchText, setSearchText] = React.useState("");
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.isloading);
    const notificationData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.notificationData);
    const totalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.totalCount);
    const routeParams = useParams();
    const [countData, setCountData] = React.useState(totalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [open, setOpen] = React.useState(false);
    const [loading, setLoading] = useState();
    const [message, setMessage] = React.useState('');
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [data, setData] = React.useState(notificationData);
    let colorCode = getNavbarTheme();

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "sender",
    });

    useEffect(() => {
        dispatch(getNotificatios(order.id, order.direction, pageIndex, rowsPerPage, routeParams.code));
    }, [pageIndex, rowsPerPage, order])

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const rowData = notificationData.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["userID"] = item.userName
            row["createdDateTime"] = moment(new Date(item.createdDateTime)).format("MM/DD/YY, HH:mm A"
            )
        });
        return row;
    });

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);


    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }


    function handleMessageChange(event, value) {
        setMessage(event.target.value);
    }
    function handleSendMessage(value) {
        let code = routeParams.code;
        let data = {
            userID: localStorage.getItem("userId"),
            userName: user.data.fullName,
            userEmail: user.data.email,
            message: message,
            code: code
        }
        if (!isEmptyOrNull(data.message)) {
            dispatch(sendNotifications(data, order.id, order.direction, pageIndex, rowsPerPage));
            setMessage('')
        } else {
            ShowErroMessage(t('MessageMustHavevalue'));
        }

    }

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    const pinningConfig = new ColumnPinning();
    pinningConfig.columns = ColumnPinningPosition.End;

    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 w-full items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center whitespace-nowrap">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    <NotificationsActiveIcon style={{ fontSize: '35px' }} />

                                </Icon>

                                <Typography className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
                                    {t("notificationCenter")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 px-12 justify-end w-full">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">message</Icon>
                                            <TextField
                                                style={{ paddingTop: "10px", height: "51px" }}
                                                id="standard-basic"
                                                sx={{ border: 'none', "& fieldset": { border: 'none' }, }}
                                                variant="outlined"
                                                className="flex flex-1 mx-8"
                                                multiline
                                                rows={2}
                                                fullWidth
                                                disableUnderline={false}
                                                placeholder={t('message')}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={handleMessageChange}
                                                value={message}
                                            //defaultValue="Default Value"
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <motion.div
                                initial={{ opacity: 0, y: 40 }}
                                animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
                            >
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_sendMessage" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="whitespace-nowrap normal-case btnStyle" btnName={t("sendMessage")} parentCallback={handleSendMessage}></CommonButton>
                                </ErrorBoundary>
                            </motion.div>

                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={totalCount}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{
                                    "aria-label": "Previous Page",
                                }}
                                nextIconButtonProps={{
                                    "aria-label": "Next Page",
                                }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />
                        </div>

                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="userID"
                                        header={t("sender")}
                                        field="userID"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="message"
                                        header={t("pushedMessage")}
                                        field="message"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="createdDateTime"
                                        header={t("createDateTime")}
                                        field="createdDateTime"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                </IgrGrid>
                            </div>
                        </div>
                    </div>
                }
            />
        </>
    )
}



export default Notifications;