import FuseScrollbars from '@fuse/core/FuseScrollbars';
import _ from '@lodash';
import TablePagination from '@mui/material/TablePagination';
import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import withRouter from '@fuse/core/withRouter';
import { useTranslation } from "react-i18next";
import { motion } from 'framer-motion';
import Icon from '@mui/material/Icon';
import Input from '@mui/material/Input';
import Paper from '@mui/material/Paper';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Button from "@mui/material/Button";
import FusePageCarded from '@fuse/core/FusePageCarded';
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { getErrorLogSearchData, setLoading } from '../store/errorLogSlice';
import IconButton from "@mui/material/IconButton";
import InfoIcon from '@mui/icons-material/Info';
import { checkData, isEmptyOrNull, getNavbarTheme, getRowsPerPageOptions, calculateColumnWidth, calculateOptimalMultiplier, useWindowResizeHeight } from '../../utils/utils';
import ErrorLogDetailsDialog from './ErrorLogDetailsDialog';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { Box } from '@mui/system';
import { FormControl, InputLabel, MenuItem, Select, Tooltip } from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { useDebounce } from '@fuse/hooks';
import IsResolvedDialog from './isResolvedDialog';
import Checkbox from '@mui/material/Checkbox';
import { showMessage } from 'app/store/fuse/messageSlice';
// import './ErrorLogs.css'
import { getAgencyData } from '../../agencyPage/store/agencySlice';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

const rows = [
    {
        id: '_id',
        align: 'left',
        disablePadding: false,
        label: 'ID',
        sort: true
    },
    {
        id: 'Method',
        align: 'left',
        disablePadding: false,
        label: 'Method',
        sort: true
    },
    {
        id: 'Source',
        align: 'left',
        disablePadding: false,
        label: 'Source',
        sort: true
    },
    {
        id: 'Message',
        align: 'left',
        disablePadding: false,
        label: 'Message',
        sort: true
    },
    {
        id: 'User',
        align: 'left',
        disablePadding: false,
        label: 'User',
        sort: true
    },
    {
        id: 'AgencyCode',
        align: 'left',
        disablePadding: false,
        label: 'AgencyCode',
        sort: true
    },
    {
        id: 'Timestamp',
        align: 'left',
        disablePadding: false,
        label: 'Date',
        sort: true
    },
];

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

// Test Comment
// Test comment 1
function ErrorLogTable() {
    const mainTheme = useSelector(selectMainTheme);
    const navbarTheme = useSelector(selectNavbarTheme);
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const errorLogData = useSelector(({ errorLog }) => errorLog.errorLog.data);
    const errorLogTotalCount = useSelector(({ errorLog }) => errorLog.errorLog.totalCount);
    const isloading = useSelector(({ errorLog }) => errorLog.errorLog.isloading);
    const agencyList = useSelector(({ agency }) => agency.agency.data);

    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;

    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const gridRef = useRef(null);
    const [headerBackground, setHeaderBackground] = useState(null);
    const [data, setData] = useState(errorLogData);
    const [page, setPage] = useState(0);
    const [countData, setCountData] = useState(errorLogTotalCount);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [loading, setloading] = useState();
    const [searchText, setSearchText] = React.useState(null);
    const [agency, setAgency] = React.useState('');
    const errorLogDetailsRef = useRef();
    const isResolvedRef = useRef();
    const [selectedToDate, setSelectedToDate] = useState(new Date());
    const [selectedFromDate, setSelectedFromDate] = useState(getDateFormat(selectedToDate));
    const [source, setSource] = React.useState('');
    const [isResolved, setIsResolved] = React.useState('UnResolved');
    const [isResolvedValue, setIsResolvedValue] = React.useState(false);
    const [selectedId, setSelectedId] = React.useState([]);
    const [isAllSelected, setIsAllSelected] = useState(false);
    const [order, setOrder] = useState({
        direction: "dsc",
        id: "Timestamp",
    });
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    let colorCode = getNavbarTheme();
    // Dummy state to force re-render
    const [, setRenderTrigger] = useState(false);

    const handleIsResolvedChange = (event) => {
        setIsResolved(event.target.value);
        if (event.target.value === "ALL") {
            setIsResolvedValue('');
        } else if (event.target.value === 'Resolved') {
            setIsResolvedValue(true);
        } else if (event.target.value === 'UnResolved') {
            setIsResolvedValue(false);
        }
    };

    const handleSourceChange = (event) => {
        if (event.target.value === "ALL") {
            setSource('');
        } else {
            setSource(event.target.value);
        }
    };

    const handleChangeAgency = (event) => {
        if (event.target.value === "ALL") {
            setAgency('');
        } else {
            setAgency(event.target.value);
        }
    };

    const handleFromDateChange = (date) => {
        setSelectedFromDate(new Date(date));
    };

    const handleToDateChange = (date) => {
        const newToDate = new Date(date);
        if (newToDate < selectedFromDate) {
            ShowErrorMessage(t('dateWarningMsg'));
        } else {
            setSelectedToDate(newToDate);
        }
    };

    function getDateFormat(selectedToDate) {
        // Create a new date object based on the selectedToDate
        const fromDate = new Date(selectedToDate);
        // Subtract 30 days
        fromDate.setDate(fromDate.getDate() - 30);
        // Update the state with the new fromDate
        return fromDate;
    };

    const getErrorLogData = async () => {
        dispatch(getErrorLogSearchData(
            page * rowsPerPage,
            rowsPerPage,
            selectedFromDate,
            selectedToDate,
            searchText !== null ? searchText : null,
            source === '' ? null : source,
            isResolvedValue === '' ? null : isResolvedValue,
            agency === '' ? null : agency,
            order.id, order.direction
        ));
    };

    useEffect(() => {
        dispatch(getAgencyData());
        // eslint-disable-next-line
    }, []);
    useEffect(() => {
        setHeaderBackground(navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main)
    }, [mainTheme.palette.mode]);

    useEffect(() => {
        setData(errorLogData);
        setCountData(errorLogTotalCount);
    }, [errorLogData]);

    function ShowErrorMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    };

    function handleChangePage(event, value) {
        setPage(value);
    };

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    };

    const search = useDebounce((search, page, rowsPerPage) => {
        dispatch(getErrorLogSearchData(page * rowsPerPage, rowsPerPage,
            selectedFromDate, selectedToDate, search,
            source === '' ? null : source, isResolvedValue === '' ? null : isResolvedValue,
            agency === '' ? null : agency,
            order.id, order.direction
        ));
    }, 500);

    useEffect(() => {
        getErrorLogData();
    }, [page, rowsPerPage, isResolvedValue, source, agency, order]);

    useEffect(() => {
        if (searchText !== null) {
            search(searchText, page, rowsPerPage);
        }
    }, [searchText]);

    useEffect(() => {
        //For clearing the selectedId array for multiple error resolve
        setSelectedId([]);
        setData(errorLogData);
        setCountData(errorLogTotalCount)
    }, [errorLogData, errorLogTotalCount]);

    useEffect(() => {
        //For clearing the selectedId array for multiple error resolve
        setIsAllSelected(false);
        setSelectedId([]);
    }, []);

    const getErrorLogAllData = async () => {
        setSelectedToDate(new Date());
        setSelectedFromDate(getDateFormat(new Date()));
        setSearchText("");
        setIsResolved('UnResolved');
        setIsResolvedValue(false);
        setSource('');
        setAgency('');
        setPage(0);
        let date1 = await getDateFormat(new Date());
        let date2 = new Date();
        dispatch(getErrorLogSearchData(page * rowsPerPage, rowsPerPage, date1, date2, null, null, false, null, order.id, order.direction));
    };

    const selectAllClick = async () => {
        dispatch(setLoading(true));
        if (isAllSelected) {
            // If all records are currently selected, unselect them
            setSelectedId([]); // Clear the selectedId array
        } else {
            // Select all records
            const allIds = data.map(item => item._id); // Assuming `data` holds all records in the table
            setSelectedId(allIds); // Update selectedId with all the IDs
        }
        setIsAllSelected(!isAllSelected); // Toggle the isAllSelected state
        dispatch(setLoading(false));
    };

    const resolveClick = async () => {
        isResolvedRef.current.openErrorDetails(selectedId, PagingDetails);
        setIsAllSelected(false);
    };

    let PagingDetails = {
        pageIndex: page * rowsPerPage,
        rowsPerPage: rowsPerPage,
        selectedFromDate: selectedFromDate,
        selectedToDate: selectedToDate,
        sortField: order.id,
        sortDirection: order.direction,
        search: search === '' ? null : search,
        source: source === '' ? null : source,
        agency: agency === '' ? null : agency,
        isResolvedValue: isResolvedValue === '' ? null : isResolvedValue,
    };

    function checkValue(item) {
        if (selectedId.length > 0)
            return selectedId.map(x => x).includes(item._id)
        else
            return selectedId.includes(item._id);
    };

    const handleCheckBoxChange = (event) => {
        const id = event.target.id;
        // Create a copy of the selected id array
        const updatedSelectedTags = [...selectedId];

        if (event.target.checked) {
            // If the checkbox is checked, add the id to the array
            updatedSelectedTags.push(id);
        } else {
            // If the checkbox is unchecked, remove the id from the array
            const index = updatedSelectedTags.indexOf(id);
            if (index !== -1) {
                updatedSelectedTags.splice(index, 1);
            }
        }
        // Update the selected id state
        setSelectedId(updatedSelectedTags);

        // If any item is unchecked, set isAllSelected to false
        if (!event.target.checked) {
            setIsAllSelected(false);
        }
    };

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            setOrder({ id: sortDesc.field, direction: sortDesc.sortDirection === 0 ? "asc" : "desc" });
        } else {
            console.log("No sorting applied.");
        }
    };

    const ActionIcons = (n) => {
        // let x = checkData(n);

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;
            const value = errorLogData.filter((y) => y._id == x._id)[0];

            return (
                <>
                    <Tooltip title={t("viewDetails")}>
                        <IconButton
                            variant="contained"
                            aria-label="Back"
                            color="inherit"
                            onClick={() =>
                                errorLogDetailsRef.current.openErrorDetails(value, PagingDetails)
                            }
                            size="large"
                        >
                            <InfoIcon />
                        </IconButton>
                    </Tooltip>
                    {!isEmptyOrNull(value) && value.isResolved === true ?
                        <Tooltip title={`${t("resolvedBy")} ${value.ResolvedBy}`}>
                            <IconButton
                                variant="contained"
                                aria-label="Back"
                                color="inherit"
                                size="large"
                            >
                                <CheckCircleOutlineIcon style={{ color: "green" }} />
                            </IconButton>
                        </Tooltip>
                        :
                        <>
                            {!isEmptyOrNull(value) &&
                                <>
                                    <Tooltip title={t("unResolved")}>
                                        <IconButton
                                            variant="contained"
                                            aria-label="Back"
                                            color="inherit"
                                            size="large"
                                            onClick={() =>
                                                isResolvedRef.current.openErrorDetails([value._id], PagingDetails)
                                            }
                                        >
                                            <RemoveCircleOutlineIcon style={{ color: "red" }} />
                                        </IconButton>
                                    </Tooltip>
                                    <Checkbox
                                        id={value._id}
                                        checked={checkValue(value)}
                                        onClick={handleCheckBoxChange}
                                        inputProps={{ 'aria-label': 'controlled' }}
                                    ></Checkbox>
                                </>
                            }
                        </>
                    }
                </>
            );
        }
    };

    const rowData = data !== undefined && data !== null ? data.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id];
            row["Method"] = item.Method ?? "";
            row["Source"] = item.Source ?? "";
            row["Message"] = item.Message ?? "";
            row["AgencyCode"] = item.AgencyCode;
            row["User"] = item.user.length > 0 ? item.user[0].fname + ' ' + item.user[0].lname + ' (' + item.user[0].email + ')' : ""
            row["action"] = ActionIcons(item);
            row["Timestamp"] = new Date(item.Timestamp).toLocaleString();
        });
        return row;
    }) : {};

    useEffect(() => {
        setloading(isloading)
    }, [isloading]);

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: 'flex',
                    header: 'min-h-72 h-72 sm:h-136 sm:min-h-136'
                }}
                header={
                    <div style={{ width: "100%" }}>
                        <div
                            className="flex flex-1 w-full items-center justify-between"
                            style={{ paddingTop: "20px" }}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32">error_outline</Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
                                    {t('errorLog')}
                                </Typography>
                            </div>

                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            component={motion.span}
                                            initial={{ x: -20 }}
                                            animate={{ x: 0, transition: { delay: 0.2 } }}
                                            delay={300}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8" elevation={1}>
                                            <Icon color="action">search</Icon>
                                            <Input
                                                placeholder={t('search')}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                value={searchText}
                                                inputProps={{
                                                    'aria-label': 'Search'
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>

                            <Box component={motion.div}
                                initial={{ y: -20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1, transition: { delay: 0.2 }, width: '200px' }}
                                className="flex items-center w-full rounded-8" elevation={1}>
                                <FormControl fullWidth>
                                    <InputLabel>{t('source')}</InputLabel>
                                    <Select
                                        labelId="demo-simple-select-label"
                                        label={t('source')}
                                        style={{ color: navbarTheme.palette.primary.contrastText, }}
                                        id="demo-simple-select"
                                        name="defaultagency"
                                        className="flex flex-1 mx-8"
                                        value={source !== '' ? source : "ALL"}
                                        onChange={handleSourceChange}
                                    >
                                        <MenuItem key="1" value="ALL">{t("all")}</MenuItem>
                                        <MenuItem key="2" value="RELATIVITY_API">{t("relativityAPI")}</MenuItem>
                                        <MenuItem key="3" value="INCIDENT_API">{t("incidentAPI")}</MenuItem>
                                        <MenuItem key="3" value="SOCKET_API">{t("socketAPI")}</MenuItem>
                                        <MenuItem key="3" value="DISPATCH_API">{t("dispatchAPI")}</MenuItem>
                                        <MenuItem key="4" value="DISPATCH_UI">{t("dispatchUI")}</MenuItem>
                                        <MenuItem key="5" value="ADMIN_UI">{t("adminUI")}</MenuItem>
                                    </Select>
                                </FormControl>
                            </Box>

                            <Box component={motion.div}
                                initial={{ y: -20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1, transition: { delay: 0.2 }, width: '200px' }}
                                className="flex items-center w-full rounded-8" elevation={1}>
                                <FormControl fullWidth>
                                    <InputLabel>{t('resolved')}</InputLabel>
                                    <Select
                                        labelId="demo-simple-select-label"
                                        label={t('resolved')}
                                        id="demo-simple-select"
                                        className="flex flex-1 mx-8"
                                        value={isResolved}
                                        onChange={handleIsResolvedChange}
                                    >
                                        <MenuItem key="1" value="ALL">{t("all")}</MenuItem>
                                        <MenuItem key="2" value="Resolved">{t("resolved")}</MenuItem>
                                        <MenuItem key="3" value="UnResolved">{t("unResolved")}</MenuItem>
                                    </Select>
                                </FormControl>
                            </Box>

                            <div className="flex items-center">
                                <Grid item xs={12} sm={12} md={9} lg={9} xl={9} className="m-2" style={{ textAlign: 'left', maxWidth: '200px' }}>
                                    <DatePicker
                                        disableFuture
                                        label={t('from')}
                                        id="date-picker-inline"
                                        openTo="day"
                                        views={['month', 'day', 'year']}
                                        value={selectedFromDate}
                                        onChange={handleFromDateChange}
                                        renderInput={(params) => <TextField {...params} />}
                                        ampm={false}
                                        fullWidth
                                        required
                                    />
                                </Grid>
                                <Grid item xs={12} sm={12} md={9} lg={9} xl={9} className="ml-4" style={{ textAlign: 'left', maxWidth: '200px' }}>
                                    <DatePicker
                                        disableFuture
                                        variant="inline"
                                        id="date-picker-inline"
                                        label={t('to')}
                                        openTo="day"
                                        views={['month', 'day', 'year']}
                                        value={selectedToDate}
                                        onChange={handleToDateChange}
                                        renderInput={(params) => <TextField {...params} />}
                                        ampm={false}
                                        fullWidth
                                        required
                                    />
                                </Grid>
                                <Grid item xs={12} sm={12} className="m-2" style={{ textAlign: 'left', maxWidth: '600px' }}>
                                    <Box component={motion.div}
                                        initial={{ y: -20, opacity: 0 }}
                                        animate={{ y: 0, opacity: 1, transition: { delay: 0.2 }, width: '200px' }}
                                        className="flex items-center w-full max-w-200 px-8 py-4 rounded-8 mb-2" elevation={1}>
                                        <FormControl fullWidth>
                                            <InputLabel>{t('agency')}</InputLabel>
                                            <Select
                                                labelId="demo-simple-select-label"
                                                id="demo-simple-select-filter"
                                                name="defaultagency"
                                                label={t('agency')}
                                                className="Pagination-Dropdown-select float-right"
                                                value={agency !== '' ? agency : "ALL"}
                                                onChange={handleChangeAgency}

                                            >
                                                <MenuItem value="ALL">{t("all")}</MenuItem>
                                                {agencyList.map((element) => (
                                                    <MenuItem key={element.code} value={element.code}>
                                                        {element.code}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>
                                    </Box>
                                </Grid>

                            </div>

                        </div>
                        <div className="flex justify-end mt-4">
                            <Button color="secondary" autoFocus variant="contained" onClick={() => getErrorLogData()}>
                                {t('search')}
                            </Button>
                            <Button className="ml-4" color="secondary"
                                autoFocus variant="contained" onClick={() => getErrorLogAllData()}>
                                {t('clear')}
                            </Button>
                            <Button className="ml-4" color="secondary" autoFocus variant="contained" onClick={selectAllClick}>
                                {isAllSelected ? t('deselectAll') : t('selectAll')}
                            </Button>
                            {selectedId.length > 0 &&
                                <Button className="ml-4" color="secondary" autoFocus variant="contained"
                                    onClick={() => resolveClick()}
                                >
                                    {t('resolve')}
                                </Button>
                            }
                        </div>
                    </div >
                }
                content={
                    < div className="w-full flex flex-col" >
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={countData}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    backIconButtonProps={{
                                        'aria-label': 'Previous Page'
                                    }}
                                    nextIconButtonProps={{
                                        'aria-label': 'Next Page'
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="Method"
                                        header={t("method")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("source")}
                                        field="Source"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("message")}
                                        field="Message"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("user")}
                                        field="User"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("agencyCode")}
                                        field="AgencyCode"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("date")}
                                        field="Timestamp"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        width="300px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}

                                    />
                                </IgrGrid>
                            </div>

                        </div>


                        <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="ErrorLogDetailsDialog" />}
                            onReset={() => { }} >
                            <ErrorLogDetailsDialog ref={errorLogDetailsRef} />
                        </ErrorBoundary>

                        <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="IsResolvedDialog" />} onReset={() => { }} >
                            <IsResolvedDialog ref={isResolvedRef} />
                        </ErrorBoundary>
                    </div >
                }

            />
        </>
    );
}

export default withRouter(ErrorLogTable);
