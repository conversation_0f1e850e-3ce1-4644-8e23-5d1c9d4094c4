import React from 'react';
import i18next from 'i18next';

const ViewTips = React.lazy(() => import('./ViewTips/ViewTips'))
const Tiptype = React.lazy(() => import('./MaintainTipTypes/TipType'))
const QuikTipDetail = React.lazy(() => import('./ViewTips/QuikTipDetail/QuikTipDetail'))
const Notifications = React.lazy(() => import('./NotificationCenter/Notifications'))


/**
 * Lazy load MapReplay
 */


const QuikTipConfig = {
    settings: {
        layout: {
            config: {}
        }
    },
    routes: [
        {
            path: '/quiktip/viewtips/:code',
            element: <ViewTips />
        },
        {
            path: '/quiktip/tiptype/:code',
            element: <Tiptype />
        },
        {
            path: '/quiktip/QuikTipDetail/:code',
            element: <QuikTipDetail />
        },
        {
            path: '/quiktip/Notifications/:code',
            element: <Notifications />
        }
    ]
};

export default QuikTipConfig;


