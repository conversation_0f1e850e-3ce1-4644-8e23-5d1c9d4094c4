import { authRoles } from '../../auth';
import React from 'react';

const Login = React.lazy(() => import('./Login'));
const VerifyUser = React.lazy(() => import('./tabs/VerifyUser'));
const VerifyUserForReset = React.lazy(() => import('./tabs/VerifyUserForReset'));

const LoginConfig = {
	settings: {
		layout: {
			config: {
				navbar: {
					display: false
				},
				toolbar: {
					display: false
				},
				footer: {
					display: false
				},
				leftSidePanel: {
					display: false
				},
				rightSidePanel: {
					display: false
				}
			}
		}
	},
	auth: authRoles.onlyGuest,
	routes: [
		{
			path: '/login',
			element: <Login />,
		},
		{
			path: '/VerifyUser/:id',
			element: <VerifyUser />
		},
		{
			path: '/VerifyUserForReset/:id',
			element: <VerifyUserForReset />
		}
	]
};

export default LoginConfig;
