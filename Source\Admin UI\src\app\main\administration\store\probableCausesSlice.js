import { createSlice } from "@reduxjs/toolkit";
import { showMessage } from "app/store/fuse/messageSlice";
import { decrypt, encrypt } from "../../../security/crypto";
import axios from "axios";

export const saveProbableCause = (data) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/probableCause`, encrypt(JSON.stringify(data)))
            .then(async (res) => {
                if (res.status == 200) {
                    res.data = await JSON.parse(decrypt(res.data));
                    if (await res.data.isSuccess) {
                        dispatch(setLoading(false));
                        dispatch(
                            showMessage({
                                message: data.isUpdate ? res.data.message : res.data.message,
                                autoHideDuration: 2000,
                                anchorOrigin: { vertical: "top", horizontal: "right", },
                                variant: "success",
                            })
                        );
                        if (data.isUpdate) {
                            dispatch(setProbableEditData(res.data.newData))
                            dispatch(setProbableCauseTotalCount(res.data.totalCount));
                        }
                        else {
                            dispatch(setProbableAddData(res.data.newData))
                            dispatch(setProbableCauseTotalCount(res.data.totalCount));
                        }
                    }
                }
                else {
                    dispatch(setLoading(false));
                    res = JSON.parse(decrypt(res.response.data));
                    dispatch(showMessage({
                        message: res.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getProbableCause = (sortField, sortDirection, pageIndex, pageLimit,searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/probableCause/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setProbableCauseTotalCount(listData.totalCount));
                    dispatch(setLoading(false));
                    return dispatch(setProbableCauseData(listData.probablecausesList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const removeProbableCause = (ID, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/probableCause/${ID}/${code}`)
            .then(async (response) => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: "top",
                            horizontal: "right",
                        },
                        variant: "success",
                    })
                    );
                    dispatch(removeProbableCauseData(ID));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

//for searching
export const searchProbableCause = (searchText, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        axios.get(`admin/api/probableCause/searchProbableCauses/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchProbableCause(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    success: false,
    searchprobableCauses: [],
    probableCauses: [],
    isloading: false,
};

const probableCausesSlice = createSlice({
    name: "administration/ProbableCause",
    initialState,
    reducers: {
        setProbableCauseData: (state, action) => {
            state.probableCauses = action.payload;
        },
        setSearchProbableCause: (state, action) => {
            state.searchprobableCauses = action.payload;
        },
        setProbableCauseTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setProbableEditData: (state, action) => {
            const index = state.probableCauses.findIndex(obj => obj._id === action.payload._id);
            if (index !== -1) {
                state.probableCauses[index] = action.payload;
            }
        },
        setProbableAddData: (state, action) => {
            state.probableCauses = [...state.probableCauses, action.payload];
        },
        removeProbableCauseData: (state, action) => {
            state.probableCauses = state.probableCauses.filter(x => x._id !== action.payload);
        },
    },
    extraReducers: {},
});

export const {
    setProbableCauseData,
    setProbableCauseTotalCount,
    setLoading,
    setProbableEditData,
    setProbableAddData,
    removeProbableCauseData,
    setSearchProbableCause
} = probableCausesSlice.actions;

export default probableCausesSlice.reducer;