import React from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const InmatesAlert = ({ inmates }) => {

    const { t } = useTranslation('languageConfig');

    const getInitials = (name) => {
        if (!name || typeof name !== 'string') return '';
        const [last, firstMiddle] = name.split(',').map(p => p.trim());
        const firstInitial = firstMiddle?.split(' ')[0]?.[0] || '';
        const lastInitial = last?.[0] || '';
        return (firstInitial + lastInitial).toUpperCase();
    };

    return (
        <div className="p-8">
            <Box className="text-center mb-16">
                <Typography variant="h3" className="text-red-600 font-extrabold text-4xl">
                    🧍 {t("inmatesRegistry")}
                </Typography>
                {/* <Typography variant="subtitle1" className="text-gray-600 text-xl mt-2">
          Correctional Management Dashboard
        </Typography> */}
            </Box>

            {!inmates || inmates.length === 0 ? (
                <div className="text-center text-gray-500 text-xl mt-10">
                    {t("noInmatesFound")}
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-16">
                    {Array.isArray(inmates) && inmates.map((inmate, index) => (
                        <div
                            key={index}
                            className="rounded-2xl shadow-lg p-6 border-t-4 border-red-600 transition-all duration-300 hover:shadow-2xl hover:-translate-y-1"
                        >
                            <div className="flex items-center space-x-6 mb-6">
                                <div className="h-40 w-40 rounded-full bg-red-600 flex items-center justify-center font-extrabold text-2xl text-white">
                                    {getInitials(inmate.inmateName)}
                                </div>
                                <div>
                                    <div className="text-xl font-semibold">
                                        {inmate.inmateName || 'N/A'}
                                    </div>
                                    <div className="text-blue-600 font-bold text-sm">
                                        {t("booking")} #: {inmate.bookingNumber || 'N/A'}
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 text-base gap-4 border-t pt-4">
                                <div>
                                    <div className="font-bold text-gray-500">{t("bookingDate")}</div>
                                    <div className="font-semibold">{inmate.bookingDateTime || 'N/A'}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("dobAbbreviation")}</div>
                                    <div className="font-semibold">{inmate.dob || 'N/A'}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("targetRaceSex")}</div>
                                    <div className="font-semibold">{inmate.sex || 'N/A'} / {inmate.race || 'N/A'}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("remoteSource")}</div>
                                    <div className="font-semibold">{inmate.remotesource || 'Unknown'}</div>
                                </div>
                            </div>

                            <div className="mt-6">
                                <div className="text-700 font-semibold mb-2 text-gray-500">{t("charges")}:</div>
                                <ul className="list-disc list-inside text-800 space-y-1">
                                    {inmate.charges?.map((charge, idx) => (
                                        <li key={idx} className="ml-2">
                                            <span className="font-semibold">{charge.offenseDescription || "N/A"}</span>{' '}
                                            <span className="text-sm text-600">({charge.statuteCitation || "N/A"}, {charge.misdFelDisplay || "N/A"})</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default InmatesAlert;
