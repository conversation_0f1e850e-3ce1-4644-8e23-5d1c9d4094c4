import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import TablePagination from "@mui/material/TablePagination";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import { DeleteCategory, getCallCategory, searchCallCategory } from "../store/callCategorySlice";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import { newUserAudit } from "../../../main/userAuditPage/store/userAuditSlice";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import CircularProgressLoader from '../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';
import CallCategoryDialog from "./CallcategoryDialog";
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { useDebounce } from '@fuse/hooks';
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";


// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: "CallCategoryID",
        align: "left",
        disablePadding: false,
        label: "CALL_CATEROGYID_LBL",
        sort: true,
    },
    {
        id: "CallCategoryName",
        align: "left",
        disablePadding: false,
        label: "CALL_CATEROY_NAME_LBL",
        sort: true,
    },
    {
        id: "CallCategoryBranch",
        align: "left",
        disablePadding: false,
        label: "CALL_CATEROY_BRANCH_LBL",
        sort: true,
    },

    {
        id: "Preview",
        align: "left",
        disablePadding: false,
        label: "PREVIEWLBL",
        sort: true,
    },

    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
];

let Data = [];

function CallCategoryNew() {
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");

    const navbarTheme = useSelector(selectNavbarTheme);
    const gridRef = useRef(null);
    const callCategoryRef = useRef();
    const routeParams = useParams();

    const user = useSelector(({ auth }) => auth.user);
    const CallCategories = useSelector(({ administration }) => administration.callCategorySlice.callCategories);
    const searchCallCategories = useSelector(({ administration }) => administration.callCategorySlice.searchCallCategories);
    const callCategoryTotalCount = useSelector(({ administration }) => administration.callCategorySlice.totalCount);
    const isloadingvalue = useSelector(({ administration }) => administration.callCategorySlice.isloading);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [open, setOpen] = React.useState(false);
    const [removeCategory, setRemoveCategory] = React.useState(0);
    const [searchText, setSearchText] = React.useState("");
    const [data, setData] = React.useState(CallCategories);
    const [countData, setCountData] = React.useState(callCategoryTotalCount);
    const [callType, setSelectedType] = React.useState("");
    const [selectedFile, setSelectedFile] = React.useState({});
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    let colorCode = getNavbarTheme();
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "CallCategoryID",
    });

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access Call Category",
                user: user,
                appName: "Admin",
            })
        );
    }, []);

    const deleteCallCategory = (n) => {
        debugger;
        setOpen(true);
        setRemoveCategory(n.CallCategoryName);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(DeleteCategory(removeCategory, routeParams.code, order.id, order.direction, pageIndex, rowsPerPage));
        }
    };

    useEffect(() => {
        if (searchText.length > 0) {
            search(searchText);
        } else {
            setData(CallCategories);
            setCountData(callCategoryTotalCount);
        }
    }, [searchText, CallCategories]);

    useEffect(() => {
        if (searchText.length !== 0) {
            setData(searchCallCategories);
            setPage(0);
            setCountData(searchCallCategories.length);
        } else {
            setData(CallCategories);
            setCountData(callCategoryTotalCount);
        }
    }, [CallCategories, searchText]);

    const search = useDebounce(search => {
        dispatch(searchCallCategory(search, routeParams.code));
    }, 500);

    useEffect(() => {
        dispatch(getCallCategory(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, routeParams.code));
    }, [dispatch, countData, rowsPerPage, pageIndex, order]);

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const [loading, setLoading] = useState();

    useEffect(() => {
        // gridRef.current.refresh();
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    let PagingDetails = {
        pageIndex: pageIndex,
        rowsPerPage: rowsPerPage,
        id: order.id,
        direction: order.direction,
    }

    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    <div>
                        <Tooltip title={t("delete")}>
                            <IconButton
                                variant="contained"
                                aria-label="Back"
                                color="inherit"
                                disabled={x.isActive}
                                onClick={() => deleteCallCategory(x)}
                                size="large"
                            >
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>
                    </div>
                </>
            );
        }
    };

    const rowData = data.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["Preview"] = callType === item.CallCategoryID && selectedFile ? URL.createObjectURL(selectedFile) : item.IconURL
            row["action"] = ActionIcons(item)
        });
        return row;
    });

    const rowsPerPageOptions = getRowsPerPageOptions();

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state

            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    //method for rendering image in grid
    const renderPreview = (ctx) => {
        const item = ctx.dataContext;
        return (
            <img
                src={item.implicit}
                style={{ height: '50px', width: '100px', objectFit: 'contain' }}
            />
        );
    };




    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 w-full items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    group_work
                                </Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("callCategories")}
                                    {/* Call Categories */}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>

                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addCallCategory" />} onReset={() => window.location.reload()} >
                                <CommonButton
                                    styleClass="whitespace-no-wrap normal-case ml-16"
                                    btnName={t("addCallCategory")}
                                    parentCallback={() => callCategoryRef.current.handleClickOpen(data, routeParams.code)}></CommonButton>
                            </ErrorBoundary>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={countData}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{ "aria-label": "Previous Page", }}
                                nextIconButtonProps={{ "aria-label": "Next Page", }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />

                        </div>
                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="CallCategoryID"
                                        field="CallCategoryID"
                                        header={t("id")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="CallCategoryName"
                                        header={t("name")}
                                        field="CallCategoryName"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="CallCategoryBranch"
                                        header={t("branch")}
                                        field="CallCategoryBranch"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Preview"
                                        header={t("preview")}
                                        field="Preview"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        bodyTemplate={renderPreview}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteMsgCallCategory")}
                                    onClose={handleClose}
                                    value={removeCategory}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>

                        </div>

                        <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="CallCategoryDialog" />} onReset={() => { }} >
                            <CallCategoryDialog
                                ref={callCategoryRef} />
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    );
}

export default CallCategoryNew;