import { createSlice } from "@reduxjs/toolkit";
import { showMessage } from "app/store/fuse/messageSlice";
import { decrypt, encrypt } from "../../../security/crypto";
import axios from "axios";


export const saveSupportedUnitType = (item, formData, file) => async dispatch => {
    dispatch(setLoading(true));
    let filetype = file != null ? file.type : null;
    try {
        if (file) {
            await axios.post(`fileupload/uploadSupportedUnitTypeFile/SupportedUnitType/${item.code}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    Authorization: `Bearer ${localStorage.getItem('jwt_access_token')}`
                }
            }).then(async response => {
                if (response.status === 400) {
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    const data = {
                        fileUrl: response.data.Location,
                        fileType: filetype,
                        key: response.data.Key,
                        data: item,
                        code: item.code
                    }
                    await axios.post(`admin/api/supportedUnitType`, encrypt(JSON.stringify(data)))
                        .then(async (res) => {
                            dispatch(setLoading(false));
                            if (res.status == 200) {
                                res.data = JSON.parse(decrypt(res.data));
                                if (res.data.isSuccess) {
                                    dispatch(
                                        showMessage({
                                            message: data.isUpdate ? res.data.message : res.data.message,
                                            autoHideDuration: 2000,
                                            anchorOrigin: { vertical: "top", horizontal: "right", },
                                            variant: "success",
                                        })
                                    );
                                    dispatch(getSupportedUnitType(item.code))
                                }
                            }
                            else {
                                res = JSON.parse(decrypt(res.response.data));
                                dispatch(showMessage({
                                    message: res.data.message,
                                    autoHideDuration: 2000,
                                    anchorOrigin: {
                                        vertical: 'top',
                                        horizontal: 'right'
                                    },
                                    variant: 'warning'

                                }))
                            }
                        }).catch(error => {
                            dispatch(setLoading(false));
                            return console.error(error);
                        });
                }
            })
        }
        else {
            dispatch(setLoading(false));
            dispatch(showMessage({
                message: "please select svg file.",
                autoHideDuration: 2000,
                anchorOrigin: {
                    vertical: 'top',
                    horizontal: 'right'
                },
                variant: 'warning'

            }))
        }

    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const updateSupportedUnitType = (item, formData, file, iconUrl, iconKey, _id) => async dispatch => {
    dispatch(setLoading(true));
    let filetype = file != null ? file.type : null;
    try {
        if (file) {
            const data = { key: iconKey, }
            await axios.post(`fileupload/deltefile`, data)
                .then(async response => {
                    await axios.post(`fileupload/uploadSupportedUnitTypeFile/SupportedUnitType/${item.code}`, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            Authorization: `Bearer ${localStorage.getItem('jwt_access_token')}`
                        }
                    }).then(async response => {
                        if (response.status === 400) {
                            //dispatch(setLoading(false));
                            dispatch(showMessage({
                                message: response, autoHideDuration: 2000,
                                anchorOrigin: {
                                    vertical: 'top',
                                    horizontal: 'right'
                                },
                                variant: 'error'
                            }));
                        }
                        else {
                            const data = {
                                id: _id,
                                fileUrl: response.data.Location,
                                fileType: filetype,
                                key: response.data.Key,
                                data: item,
                                code: item.code
                            }
                            await axios.post(`admin/api/supportedUnitType/update`, encrypt(JSON.stringify(data)))
                                .then(async (res) => {
                                    dispatch(setLoading(false));
                                    if (res.status == 200) {
                                        res.data = JSON.parse(decrypt(res.data));
                                        if (res.data.isSuccess) {
                                            dispatch(
                                                showMessage({
                                                    message: data.isUpdate ? res.data.message : res.data.message,
                                                    autoHideDuration: 2000,
                                                    anchorOrigin: { vertical: "top", horizontal: "right", },
                                                    variant: "success",
                                                })
                                            );
                                            dispatch(getSupportedUnitType(item.code))
                                        }
                                    }
                                    else {
                                        res = JSON.parse(decrypt(res.response.data));
                                        dispatch(showMessage({
                                            message: res.data.message,
                                            autoHideDuration: 2000,
                                            anchorOrigin: {
                                                vertical: 'top',
                                                horizontal: 'right'
                                            },
                                            variant: 'warning'

                                        }))
                                    }
                                }).catch(error => {
                                    dispatch(setLoading(false));
                                    return console.error(error);
                                });
                        }
                    })
                }).catch(error => {
                    dispatch(setLoading(false));
                    return console.error(error);
                });

        }
        else {
            const data = {
                id: _id,
                fileUrl: iconUrl,
                key: iconKey,
                data: item,
                code: item.code
            }
            await axios.post(`admin/api/supportedUnitType/update`, encrypt(JSON.stringify(data)))
                .then(async (res) => {
                    dispatch(setLoading(false));
                    if (res.status == 200) {
                        res.data = JSON.parse(decrypt(res.data));
                        if (res.data.isSuccess) {
                            dispatch(
                                showMessage({
                                    message: data.isUpdate ? res.data.message : res.data.message,
                                    autoHideDuration: 2000,
                                    anchorOrigin: { vertical: "top", horizontal: "right", },
                                    variant: "success",
                                })
                            );
                            dispatch(getSupportedUnitType(item.code))
                        }
                    }
                    else {
                        dispatch(setLoading(false));
                        res = JSON.parse(decrypt(res.response.data));
                        dispatch(showMessage({
                            message: res.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'warning'

                        }))
                    }
                }).catch(error => {
                    dispatch(setLoading(false));
                    return console.error(error);
                });
        }

    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const getSupportedUnitType = (code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/supportedUnitType/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setSupportedUnitTypeTotalCount(listData.totalCount));
                    setTimeout(() => {
                        dispatch(setLoading(false));
                    }, 800)
                    return dispatch(setSupportedUnitTypeData(listData.supportedUnitTypeList));
                }
                else {
                    dispatch(setLoading(true));
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(true));
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const deleteSupportedUnitType = (id, code, key) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        const data = { key: key, }
        await axios.post(`fileupload/deltefile`, data)
            .then(async response => {
                await axios.delete(`admin/api/supportedUnitType/${id}/${code}`)
                    .then(async (response) => {
                        if (response.status == 200) {
                            dispatch(setLoading(false));
                            response = JSON.parse(decrypt(response.data));
                            dispatch(showMessage({
                                message: response.message,
                                autoHideDuration: 2000,
                                anchorOrigin: {
                                    vertical: "top",
                                    horizontal: "right",
                                },
                                variant: "success",
                            })
                            );
                            dispatch(getSupportedUnitType(code))
                        }
                        else {
                            response = JSON.parse(decrypt(response.response.data));
                            dispatch(showMessage({
                                message: response.data.message,
                                autoHideDuration: 2000,
                                anchorOrigin: {
                                    vertical: 'top',
                                    horizontal: 'right'
                                },
                                variant: 'warning'

                            }))
                            dispatch(setLoading(false));
                        }
                    }).catch(error => {
                        return console.error(error);
                    });
            }).catch(error => {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: error.message,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'

                }))
            });

    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

//for searching
export const searchSupportedUnitType = (searchText, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        axios.get(`admin/api/supportedUnitType/searchsupportedUnitTypes/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchSupportedUnitTypes(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    success: false,
    searchSupportedUnitTypes: [],
    supportedUnitType: [],
    isloading: false,
};

const supportedUnitTypeSlice = createSlice({
    name: "administration/supportedUnitTypeSlice",
    initialState,
    reducers: {
        setSupportedUnitTypeData: (state, action) => {
            state.supportedUnitType = action.payload;
        },
        setSearchSupportedUnitTypes: (state, action) => {
            state.searchSupportedUnitTypes = action.payload;
        },
        setSupportedUnitTypeTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        }
    },
    extraReducers: {},
});

export const {
    setSupportedUnitTypeData,
    setSupportedUnitTypeTotalCount,
    setLoading,
    setSearchSupportedUnitTypes,
} = supportedUnitTypeSlice.actions;

export default supportedUnitTypeSlice.reducer;