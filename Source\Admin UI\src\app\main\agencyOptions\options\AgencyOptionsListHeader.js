import { motion } from "framer-motion";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import Typography from "@mui/material/Typography";
import React from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import { useParams } from "react-router";


function AgencyOptionsListHeader(props) {
  const dispatch = useDispatch();
  const { t } = useTranslation("laguageConfig");
  const routeParams = useParams();
  const users = useSelector(({ agencyOptions }) => agencyOptions.agencyOptions.agencyData);


  return (
    <div style={{ width: "100%" }}>
      {routeParams.code !== "list" && (
        <div className="flex flex-1 items-center justify-between">
          <div className="flex items-center">
            <Tooltip title="Back to agency" style={{ float: "right" }}>
              <Stack direction="row" spacing={2}>
                <Button
                  className="backButton"
                  variant="contained"
                  startIcon={<ArrowBackOutlinedIcon />}
                  onClick={() => history.push(`/admin/agencyList`)}
                >
                  {t('back')}
                </Button>
              </Stack>
            </Tooltip>
          </div>
        </div>
      )}
      <div
        className="flex flex-1 w-full items-center justify-between"
        style={{ paddingTop: "30px" }}
      >
        <div className="flex items-center" >
          <Icon
            component={motion.span}
            initial={{ scale: 0 }}
            animate={{ scale: 1, transition: { delay: 0.2 } }}
            className="text-32"
          >
            business
          </Icon>
          <Typography
            component={motion.span}
            initial={{ x: -20 }}
            animate={{ x: 0, transition: { delay: 0.2 } }}
            delay={300}
            className="hidden sm:flex mx-0 sm:mx-12"
            variant="h6"
          >
            {users}

          </Typography>
        </div>
      </div>
    </div>
  );
}

export default AgencyOptionsListHeader;
