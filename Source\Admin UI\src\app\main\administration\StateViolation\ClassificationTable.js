import React, { useEffect, useRef, useState } from 'react';
import { TablePagination, Tooltip, Checkbox } from '@mui/material';
import { useTranslation } from "react-i18next";
import { selectNavbarTheme, selectMainTheme } from "app/store/fuse/settingsSlice";
import { useSelector, useDispatch } from 'react-redux';
import { useDebounce } from '@fuse/hooks';
import { calculateColumnWidth, calculateOptimalMultiplier, checkData, getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from '../../utils/utils';
import IconButton from "@mui/material/IconButton";
import { deleteViolationClassification, getViolationClassification, setStateViolationClassificationSuccess, } from '../store/violationClassificationSlice';
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import AddClassification from './AddClassification';
import ConfirmationDialog from '../../components/ConfirmationDialog/ConfirmationDialog';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "Classification",
        align: "left",
        disablePadding: false,
        label: "Classification",
        sort: true,
    },
    {
        id: "Name",
        align: "left",
        disablePadding: false,
        label: "Name",
        sort: true,
    },
    {
        id: "MinSentence",
        align: "left",
        disablePadding: false,
        label: "MinSentence",
        sort: true,
    },
    {
        id: "MaxSentence",
        align: "left",
        disablePadding: false,
        label: "MaxSentence",
        sort: true,
    },
    {
        id: "SentenceType",
        align: "left",
        disablePadding: false,
        label: "SentenceType",
        sort: true,
    },
    {
        id: "Life",
        align: "left",
        disablePadding: false,
        label: "Life",
        sort: true,
    },
    {
        id: "MinFines",
        align: "left",
        disablePadding: false,
        label: "MinFines",
        sort: true,
    },
    {
        id: "NoMaximumFine",
        align: "left",
        disablePadding: false,
        label: "NoMaximumFine",
        sort: true,
    },
    {
        id: "Death",
        align: "left",
        disablePadding: false,
        label: "Death",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function ClassificationTable(props) {
    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();
    const onToolbarRef = useRef(null);
    const gridRef = useRef(null);
    const gridContainerRef = useRef(null);
    const classificationRef = useRef(null);

    const rowsPerPageOptions = getRowsPerPageOptions();
    let colorCode = getNavbarTheme();

    const mainTheme = useSelector(selectMainTheme);
    const navbarTheme = useSelector(selectNavbarTheme);

    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    const RowsSelectedBackgroundColor = navbarTheme.palette.secondary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [page, setPage] = useState(0);
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    const [state, setState] = React.useState(null);
    const [searchText, setSearchText] = React.useState("");
    const [selectedId, setSelectedId] = React.useState([]);
    const [headerBackground, setHeaderBackground] = useState(null);
    const [gridWidth, setGridWidth] = useState(1200); // Default width
    const [removeData, setRemoveData] = React.useState(null);
    const [open, setOpen] = React.useState(false);
    const [selectedIsDefaultId, setSelectedIsDefaultId] = React.useState(null);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "Classification",
    });

    const violationClassification = useSelector(({ administration }) => administration.violationClassificationSlice.violationClassification)
    const violationClassificationTotalCount = useSelector(({ administration }) => administration.violationClassificationSlice.violationClassificationTotalCount)
    const violationClassificationSuccess = useSelector(({ administration }) => administration.violationClassificationSlice.stateViolationClassificationSuccess)
    const selectedStateCode = useSelector(({ administration }) => administration.stateViolationSlice.selectedStateCode);

    useEffect(() => {
        if (props && props.searchText !== "") {
            setSearchText(props.searchText);
        }
    }, [props.searchText]);

    useEffect(() => {
        if (props && props.selectedClassification !== undefined && props.selectedDefault !== undefined) {
            if (props.selectedClassification.length > 0 && props.selectedDefault !== null) {
                setSelectedIsDefaultId(props.selectedDefault._id);
                setSelectedId(props.selectedClassification.map((x) => x._id));
            }
        }
    }, [props.selectedClassification, props.selectedDefault]);

    useEffect(() => {
        setHeaderBackground(navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main)
    }, [mainTheme.palette.mode]);

    useEffect(() => {
        if (selectedStateCode !== null) {
            setState(selectedStateCode);
        }
    }, [selectedStateCode]);

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    function checkValue(item) {
        if (selectedId.length > 0)
            return selectedId.map(x => x).includes(item._id);
        else
            return selectedId.includes(item._id);
    };

    const handleCheckBoxChange = (event, x) => {
        const id = x._id;
        // Create a copy of the selected id array
        const updatedSelectedTags = [...selectedId];

        if (event.target.checked) {
            // If the checkbox is checked, add the id to the array
            updatedSelectedTags.push(id);
        } else {
            // If the checkbox is unchecked, remove the id from the array
            const index = updatedSelectedTags.indexOf(id);
            if (index !== -1) {
                updatedSelectedTags.splice(index, 1);
            }
        }
        // Update the selected id state
        setSelectedId(updatedSelectedTags);
        if (props && !props.showAction) {
            props.classificationSelected(x);
        }
    };

    const ActionIcons = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        !props.showAction
                            ? <Checkbox
                                color='primary'
                                id={x._id}
                                checked={checkValue(x)}
                                onClick={(e) => handleCheckBoxChange(e, x)}
                                inputProps={{ 'aria-label': 'controlled' }}
                            ></Checkbox>
                            :
                            <div style={{ display: "flex" }}>
                                <Tooltip title={t("edit")}>
                                    <IconButton
                                        variant="contained"
                                        className="normal-case m-4"
                                        aria-label="Back"
                                        color="inherit"
                                        onClick={() => handleEditClick(x)}
                                        size="large"
                                    >
                                        <EditIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title={t("delete")}>
                                    <IconButton
                                        variant="contained"
                                        className="normal-case m-4"
                                        aria-label="Back"
                                        color="inherit"
                                        onClick={() => handleDeleteClick(x)}
                                        size="large"
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </Tooltip>
                            </div>
                    }
                </>
            )
        }
    };

    const handleIsDefaultChange = (data) => {
        setSelectedIsDefaultId((prevId) => (prevId === data._id ? null : data._id));
        if (props && !props.showAction) {
            props.handleDefaultSelected(data);
        }
    };


    const IsDefault = (n) => {
        if (n && n.dataContext) {
            let data = n.dataContext.cell.row.data;
            const isActionChecked = checkValue(data); // Check if the Action column checkbox is selected for this row

            return (
                <>
                    {isActionChecked && (
                        <Checkbox
                            color='primary'
                            id={`isDefault-${data._id}`}
                            checked={selectedIsDefaultId === data._id} // Only the selected ID is checked
                            onClick={() => handleIsDefaultChange(data)} // Update state on click
                            inputProps={{ 'aria-label': 'is default' }}
                        />
                    )}
                </>
            );
        }
    };

    const handleDeleteClick = (classificationData) => {
        setRemoveData({
            id: classificationData._id,
            stateCode: selectedStateCode,
        });
        setOpen(true);
    }

    const handleConfimationDialogClick = async (newValue) => {
        if (newValue) {
            await dispatch(deleteViolationClassification(removeData));
            setRemoveData(null);
        }
        setOpen(false);
    };

    const handleEditClick = (classificationData) => {
        classificationRef.current.handleOpen(true, "update", classificationData);
    }

    const rowData = (data).map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["_id"] = item._id
            row["Classification"] = item.Classification ? item.Classification : '';
            row["Name"] = item.Name ? item.Name : '';
            row["MinSentence"] = item.MinSentence ? item.MinSentence : '';
            row["MaxSentence"] = item.MaxSentence ? item.MaxSentence : '';
            row["SentenceType"] = item.SentenceType ? item.SentenceType : '';
            row["Life"] = item.Life ? item.Life : '';
            row["MinFines"] = item.MinFines ? item.MinFines : '';
            row["MaxFines"] = item.MaxFines ? item.MaxFines : '';
            row["NoMaximumFine"] = item.NoMaximumFine ? item.NoMaximumFine : '';
            row["Death"] = item.Death ? item.Death : '';
            row["action"] = ActionIcons(item);
            row["IsDefault"] = IsDefault(item);
        });
        return row;
    });

    useEffect(() => {
        if (violationClassification !== undefined && violationClassification.length > 0) {
            setData(violationClassification);
            setTotalCount(violationClassificationTotalCount);
        }
    }, [violationClassification, violationClassificationTotalCount])

    const search = useDebounce((search, page, rowsPerPage, state) => {
        dispatch(getViolationClassification(state, order.id, order.direction, page * rowsPerPage,
            rowsPerPage, search === '' ? null : search));
    }, 500);

    useEffect(() => {
        if (state !== null) {
            if (searchText !== '') {
                search(searchText, page, rowsPerPage, state);
            } else {
                dispatch(getViolationClassification(state, order.id, order.direction, page * rowsPerPage,
                    rowsPerPage, searchText === '' ? null : searchText));
            }
            dispatch(setStateViolationClassificationSuccess(false));
        }
    }, [searchText, page, rowsPerPage, order, state, violationClassificationSuccess]);

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 0 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            <div className="w-full flex flex-col">
                <div className="igrGridClass">
                    <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                        <TablePagination
                            className="tablePaging"
                            component="div"
                            count={totalCount}
                            rowsPerPage={rowsPerPage}
                            page={page}
                            backIconButtonProps={{
                                "aria-label": "Previous Page",
                            }}
                            nextIconButtonProps={{
                                "aria-label": "Next Page",
                            }}
                            onPageChange={handleChangePage}
                            onRowsPerPageChange={handleChangeRowsPerPage}
                            rowsPerPageOptions={rowsPerPageOptions}
                        />
                    </div>

                    <div>
                        <IgrGrid
                            id="grid"
                            autoGenerate="false"
                            data={rowData}
                            primaryKey="_id"
                            ref={gridRef}
                            height={`${gridHeight}px`}
                            rowHeight={60}
                            groupRowTemplate={groupByRowTemplate}
                            filterMode="ExcelStyleFilter"
                            moving={true}
                            allowFiltering={false}
                            allowAdvancedFiltering={true}
                            allowPinning={true}
                            pinning={pinningConfig}
                        >
                            <IgrGridToolbar>
                                <IgrGridToolbarActions>
                                    <IgrGridToolbarAdvancedFiltering />
                                    <IgrGridToolbarHiding />
                                    <IgrGridToolbarPinning />
                                </IgrGridToolbarActions>
                            </IgrGridToolbar>
                            <IgrColumn
                                field="Classification"
                                header={t("classification")}
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("name")}
                                field="Name"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("minSentence")}
                                field="MinSentence"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("maxSentence")}
                                field="MaxSentence"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("sentenceType")}
                                field="SentenceType"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("life")}
                                field="Life"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("minFines")}
                                field="MinFines"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("maxFines")}
                                field="MaxFines"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("createdDate")}
                                field="CreateDate"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("lastModifiedDate")}
                                field="LastModDate"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("noMaximumFine")}
                                field="NoMaximumFine"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                header={t("death")}
                                field="Death"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                field="action"
                                header={t("action")}
                                width="250px"
                                resizable={true}
                                pinned={true}
                                bodyTemplate={ActionIcons}
                            />
                            <IgrColumn
                                field="IsDefault"
                                isHidden={selectedId.length === 0 ? true : false}
                                pinned={selectedId.length > 0 ? true : false}
                                header={t("isDefault")}
                                width="250px"
                                resizable={true}
                                bodyTemplate={IsDefault}
                            />
                        </IgrGrid>
                    </div>

                </div>
            </div>
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                <ConfirmationDialog
                    id="ringtone-menu"
                    keepMounted
                    open={open}
                    text={t("deleteRecord")}
                    onClose={handleConfimationDialogClick}
                    value={removeData}
                >
                </ConfirmationDialog>
            </ErrorBoundary>
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="AddClassification" />} onReset={() => window.location.reload()} >
                <AddClassification ref={classificationRef} />
            </ErrorBoundary>
        </>
    );
}

export default ClassificationTable;