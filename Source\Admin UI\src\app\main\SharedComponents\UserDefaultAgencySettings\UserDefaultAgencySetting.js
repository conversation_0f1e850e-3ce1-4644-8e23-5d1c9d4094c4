import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import FormControl from "@mui/material/FormControl";
import { updateDefaultAgency } from "../../../auth/store/userSlice";
import "./UserDefaultAgencySetting.css";
import Box from '@mui/material/Box';
import CommonAutocomplete, { handleSelectKeyDown } from "src/app/main/SharedComponents/ReuseComponents/CommonAutocomplete";
import CircularProgressLoader from "../CircularProgressLoader/CircularProgressLoader";
import { useEffect, useState } from "react";

function UserDefaultAgencySetting(props) {
  const { t } = useTranslation("laguageConfig");
  const dispatch = useDispatch();
  const user = useSelector(({ auth }) => auth.user);
  const isLoading = useSelector(({ auth }) => auth.user.isLoading);

  const [defaultAgencyValue, setDefaultAgencyValue] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (props.agencies && props.defaultAgencyValue) {
      const defaultAgency = props.agencies.find(x => x.agencyCode === props.defaultAgencyValue);
      setDefaultAgencyValue(defaultAgency || null);
    }
  }, [props.agencies, props.defaultAgencyValue]);

  const handleDefaultAgencyChange = (event, newValue) => {
    // Prevent triggering during initial mount
    if (!isInitialized) {
      setIsInitialized(true);
      return;
    }

    setDefaultAgencyValue(newValue);

    if (!props.registerUser) {
      let id = localStorage.getItem("userId");
      let defaultAgency = newValue?.agencyCode ? newValue.agencyCode : null;
      dispatch(updateDefaultAgency({ defaultAgency, id }));
    } else {
      props.defaultAgency(newValue?.agencyCode);
    }
  };

  return (
    <>
      {isLoading && <CircularProgressLoader loading={isLoading} />}
      <div className="flex flex-col justify-center w-full">
        <Box sx={{ minWidth: 120 }} className="adminControlPadding">
          <FormControl fullWidth>
            <CommonAutocomplete
              value={defaultAgencyValue || null}
              parentCallback={handleDefaultAgencyChange}
              options={props.agencies || []}
              fieldName={t("selectDefaultAgency")}
              optionLabel={"label"}
              onKeyDown={handleSelectKeyDown}
            />
          </FormControl>
        </Box>
      </div>
    </>
  );
}

export default UserDefaultAgencySetting;
