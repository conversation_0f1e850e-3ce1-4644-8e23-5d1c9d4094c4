const locale = {
    applications: "Applications sp",
    administration: "Administration sp",
    dispatchCallType: "Dispatch Call Type sp",
    violationCharges: "Violation/Charges sp",
    fileUploadAndPreview: "File Upload & Preview sp",
    userNewTest: "User New Test sp",
    nfirs: "NFIRS Unit Type sp",
    shift: "Shift sp",
    team: "Team sp",
    settings: "Settings sp",
    counties: "Manage Address/Intersection Points sp",
    incomingCallAndMessage: "Incoming Calls and Message sp",
    currentSelectedCall: "Current Selected Call sp",
    recentCallAndMessage: "Recent Call and Messages sp",
    group: "Group sp",
    showAllCalls: "Show All Calls sp",
    towerInfo: "Tower Info sp",
    time: "Time sp",
    lat: "Lat sp",
    long: "Long sp",
    group1: "Group 1 sp",
    group2: "Group 2 sp",
    group3: "Group 3 sp",
    noCallFound: "No call found sp",
    makeGroup: "Make Group sp",
    hangUp911: "911 HANG UP/DROPPED CALL sp",
    acccidentCall: "ACCIDENT CALL sp",
    rebidCall: "REBID CALL sp",
    sendCall: "SEND CALL sp",
    archiveCalls: "Archive Calls sp",
    domestic: "DOMESTIC sp",
    assault: "ASSAULT/FIGHT IN PROGRESS sp",
    shotsHeard: "SHOTS HEARD/FIRED sp",
    burglary: "BURGLARY/ROBBERY IN PROGRESS sp",
    structureFire: "STRUCTURE FIRE sp",
    visibleSmoke: "VISIBLE SMOKE sp",
    vehicleFire: "VEHICLE FIRE sp",
    grassFire: "GRASS/BRUSH FIRE sp",
    medicalCall: "MEDICAL CALL sp",
    newCall: "New Call sp",
    pdZone: "PD Zone sp",
    loading: "Loading... sp",
    seenAll: "Yay! You have seen it all sp",
    confidence: "Confidence sp",
    radius: "Radius sp",
    autoRefresh: "Auto Refresh sp",
    rg: "RG sp",
    geoRadius: "Geo Radius ( in mile ) sp",
    timeInMin: "Time ( in min.) sp",
    mVAInjury: "MVA Injury sp",
    mVAInjuryProcessHere: "MVA Injury process here sp",
    saveChanges: "Save changes sp",
    mVAUNKInjury: "MVA UNK Injury sp",
    mVAUNKInjuryProcessHere: "MVA UNK Injury process here... sp",
    mVANOInjury: "MVA NO Injury sp",
    mVANOInjuryProcessHere: "MVA NO Injury process here... sp",
    rawData: "Raw Data sp",
    selectedCall: "Selected Call sp",
    rapidSoS: "Rapid SoS sp",
    medicAlertTest: "MedicAlert Test sp",
    contemplativeReptile: "Contemplative Reptile sp",
    locationHistory: "Location History sp",
    estimatedAddress: "Estimated address: sp",
    indoorLocation: "Indoor Location sp",
    uncertaintyRadius: "Uncertainty Radius: sp",
    callingPhone: "Calling Phone: sp",
    receivedOn: "Received On: sp",
    location: "Location: sp",
    callerName: "Caller Name sp",
    callerNumber: "Caller Number sp",
    callerLocation: "Caller Location sp",
    locationInfo: "Location Info sp",
    groupCalls: "Group Calls: sp",
    suppliment: "Suppliment sp",
    create: "Create sp",
    new: "New sp",
    next: "Next sp",
    createCall: "Create Call sp",
    closest5addresses: "Closest 5 addresses sp",
    createUser: "Create User sp",
    agencyAdmin: "Agency Admin sp",
    superAdmin: "Super Admin sp",
    dispatchChat: "Dispatch Chat sp",
    selectAgencies: "Select Agencies sp",
    photo: "Photo sp",
    agencies: "Agencies sp",
    isEnabled: "IsEnabled sp",
    superAdminUsers: "Super Admin Users sp",
    regularUsers: "Regular Users sp",
    userAccountType: "User Account Type sp",
    fulltTime: "Full Time sp",
    partTime: "Part Time sp",
    unitAccount: "Unit Account sp",
    addNewSeverConfiguration: "Add Server Configuration sp",
    initialCode: "Initial Code sp",
    isDefault: "Is Default sp",
    adminSetting: "Admin Setting sp",
    mobileDispatch: "Mobile Dispatch sp",
    quikTip: "QuikTip sp",
    watches: "Watches sp",
    relativityAdmin: "Relativity Admin sp",
    call911: "911 Call sp",
    dispatch: "Dispatch sp",
    mugshot: "Mugshot sp",
    userAccessRights: "User Access Rights sp",
    deleteMsgCallViolation: "Are you sure you want to delete Call Violation ? sp",
    deleteMsgCallTypes: "Are you sure you want to delete Call Types ? sp",
    deleteMsgCallCategory: "Are you sure you want to delete Call Category ? sp",
    deleteMsgCallResponse: "Are you sure you want to delete Call Response ? sp",
    deleteMsgDevice_Licenses: "Are you sure you want to delete Device Licenses ? sp",
    deleteMsgProbableCause: "Are you sure you want to delete Probable Cause ? sp",
    deleteMsgommunicationLog: "Are you sure you want to delete Communication Log ? sp",
    deleteRecord: "Are you sure you want to delete this Record ? sp",
    violationId: "Violation ID sp",
    statuteTitle: "Statute Title sp",
    statutedesc: "Statute Desc sp",
    offenseDescription: "Offense Description sp",
    localCode: "Local Code sp",
    ncicId: "NcicID sp",
    violationExample: "Violation Example sp",
    violationType: "Violation Type sp",
    nibrsId: "NibrsID sp",
    classification: "Classification sp",
    isCriminal: "Is Criminal sp",
    isWarrant: "Is Warrant sp",
    isTraffic: "Is Traffic sp",
    shortDescription: "Short Description sp",
    isDWI1: "Is DWI1 sp",
    isDWI2: "Is DWI2 sp",
    isDWI3: "Is DWI3 sp",
    lastValidYear: "Last Valid Year sp",
    dateLastValid: "Date Last Valid sp",
    arCriminalTrafficID: "AR Criminal Traffic ID sp",
    ndexSubmitType: "NDEX Submit Type sp",
    dnaRequired: "DNA Required sp",
    bondAmountDefault: "Bond Amount Default sp",
    complianceReview: "Compliance Review sp",
    sendToDOG: "Send To DOG sp",
    noMaximumFine: "No Maximum Fine sp",
    isSeatbeltDeductible: "Is Seatbelt Deductible sp",
    isInStateList: "Is In State List sp",
    isCitationViol: "Is Citation Viol sp",
    isLocalOrdinance: "Is Local Ordinance sp",
    isDomesticViolence: "Is Domestic Violence sp",
    acFlag: "AC Flag sp",
    cleryCat: "Clery Cat sp",
    mustAppear: "Must Appear sp",
    fingerPrint: "Finger Print sp",
    minSentence: "Min Sentence sp",
    maxSentence: "Max Sentence sp",
    sentenceType: "Sentence Type sp",
    defaultClassificationID: "Default Classification ID sp",
    life: "Life sp",
    minFines: "Min Fines sp",
    maxFines: "Max Fines sp",
    callType: "Call Type sp",
    deviceLicense: "Device License sp",
    callTypeID: "Call Type ID sp",
    categoryWarning: "Category should not be empty sp",
    callCategoryID: "Call Category ID sp",
    code: "Code sp",
    id: "ID sp",
    codeDescription: "Code Description sp",
    codeProQA: "Code Pro QA sp",
    codeWestNet: "Code West Net sp",
    callTypeName: "Call Type Name sp",
    abbrevaition: "Abbrevaition sp",
    chat: "Chat sp",
    currentMap: "Current Map sp",
    replayHistory: "Replay History sp",
    fileUploadPreview: "File Upload/Preview sp",
    violations: "Violations sp",
    callCategory: "Call Category sp",
    UserSettingsMsg: "User settings saved successfully. sp",
    callCategories: "Call Categories sp",
    emailValidationMsg: "Please enter a valid email sp",
    minLengthMsg: "Min character length is 2 sp",
    phoneValidMsg: "This is not a valid phone number sp",
    personalInformation: "Personal Information sp",
    profilePicture: "Profile Picture sp",
    rpsAuthentication: "RPS Authentication sp",
    userName: "UserName sp",
    verify: "Verify sp",
    defaultLocation: "Default Location sp",
    personalDetails: "Personal Details : sp",
    mapZoomLevel: "Map zoom level sp",
    defaultLocationType: "Default location type sp",
    selectLocation: "Select location sp",
    authenticator: "Authenticator sp",
    text: "Text sp",
    agencyAdminSetting: "Agency Admin Setting sp",
    enterRpsCreds: "Enter your RPS Credentials! sp",
    defaultApplication: "Default Application sp",
    searchFeature: "Search Feature sp",
    broadcastMessages: "Broadcast Messages sp",
    mySchedule: "My Schedule sp",
    responseCode: "Response Code sp",
    priority: "Priority sp",
    callResponseCode: "Call Response Code sp",
    dispatchCallIcons: "Dispatch Call Icons sp",
    violationsCharges: "Violations/Charges sp",
    dispatchCallTypes: "Dispatch Call Types sp",
    callResponse: "Call Response sp",
    serverConfiguration: "Server Configuration sp",
    rpsCode: "RPS Code sp",
    availability: "Availability sp",
    deviceLicenses: "Device license sp",
    probableCause: "Probable Cause sp",
    addCommunicationLog: "Add Communication Logs sp",
    deviceId: "Device Id sp",
    deviceDescription: "Device Description sp",
    srno: "Sr no. sp",
    disableUserMsg: "Are you sure you want to disable this user? sp",
    deleteUserMsg: "Are you sure you want to delete this user? sp",
    enableUserMsg: "Are you sure you want to enable this user? sp",
    selectDefaultAgency: "Select Default Agency sp",
    defaultMFAType: "Default MFA Type sp",
    defaultAgency: "Default Agency sp",
    selectCallTypeImageMsg: "Please select a call type image sp",
    deviceLimitReachedMsg: "Cannot add device license. You have reached the maximum limit of allocated licences. sp",
    deviceAlreadyExistingMsg: "Device already existing cannot add this device sp",
    oneAgencySelectMsg: "Atleast one agency must be selected. sp",
    departmentValidationMsg: "Department must be selected. sp",
    cannotSelectMoreThanOneAgencyMSg: "You cannot select more than one agency. sp",
    systemAgencySelectMsg: "Please select system agency also. sp",
    oneAccessRightsSelectMsg: "Atleast one access right needs to be selected. sp",
    userLimitReachedUpdateMsg: "Cannot update User. You have reached the maximum limit of allocated Users to this agency. sp",
    userLimitReachedMsg: "Cannot add User. You have reached the maximum limit of allocated Users. sp",
    systemAgencySelectedMsg: "System agency already selected. sp",
    pleaseChooseLocationMsg: "Please choose a location to continue. sp",
    filterByAgency: "Filter by agency sp",
    addCitation: "Add Citation sp",
    citiationEndMsg: "No more Citations sp",
    citation: "Citation sp",
    citationDeleteMsg: "Are you sure you want to delete this citation ? sp",
    citationViolationDeleteMsg: "Are you sure you want to delete this citation violation ? sp",
    citationPersonDeleteMsg: "Are you sure you want to delete this citation person ? sp",
    citationVehicleDeleteMsg: "Are you sure you want to delete this citation vehicle ? sp",
    teams: "Teams sp",
    emailConfiguration: "Email Configuration sp",
    host: "Host sp",
    port: "Port sp",
    importUsers: "Import Users sp",
    import: "Import sp",
    addMultipleUsers: "Add Multiple User sp",
    importedUserLogs: "Imported User Logs sp",
    fieldsWithIssues: "Fields With Issues sp",
    twitterconfiguration: "Twitter Configuration sp",
    baseUrl: "Base Url sp",
    responseType: "Response Type sp",
    clientID: "Client ID sp",
    redirectUrl: "Redirect Url sp",
    scope: "Scope sp",
    codeChallenge: "Code Challenge sp",
    codeChallengeMethod: "Code Challenge Method sp",
    grantType: "Grant Type sp",
    grantTypeForRefreshToken: "Grant Type For Refresh Token sp",
    authUrl: "Auth Url sp",
    maintainTipTypes: "Maintain Tip Types sp",
    addCallViolation: "Add Violation sp",
    addCallType: "Add Call Type sp",
    addCallCategory: "Add Call Category sp",
    addCallResponse: "Add Call Response sp",
    addNewDevice: "Add New Device sp",
    addNewCause: "Add New Probable Cause sp",
    viewTip: "View Tip sp",
    viewTips: "View Tips sp",
    notification: "Notification sp",
    ncicUserName: "NCIC UserName sp",
    ncicPassword: "NCIC Password sp",
    terminalId: "Terminal ID sp",
    ncicAuthentication: "NCIC Authentication sp",
    archivechatHistories: "Archive Chat Histories sp",
    shortCode: "Short Code sp",
    locationRequirement: "Location Requirement sp",
    addNonCallStatus: "Add Non Call Status sp",
    nonCallStatus: "Non Call Status sp",
    dispatchCitation: "Dispatch Citation sp",
    trafficStop: "Traffic Stop sp",
    personSearch: "Person Search sp",
    vehicleSearch: "Vehicle Search sp",
    gunSearch: "Gun Search sp",
    articleSearch: "Article Search sp",
    boatSearch: "Boat Search sp",
    incidentReport: "Incident Report sp",
    accidentReport: "Accident Report sp",
    dispatchApp: "Dispatch App sp",
    dispatchWebsite: "Dispatch Website sp",
    securityCheck: "Security Check sp",
    transportCall: "Transport Call sp",
    medicalTransport: "Medical Transport sp",
    showAlert: "Show Alert sp",
    tokenRequired: "Token Required sp",
    otpDetails: "Otp Details : sp",
    ncicDetails: "NCIC Details : sp",
    permissionDetails: "Permission Details : sp",
    allowPermanantLogin: "Allow Permanent Login sp",
    receiveVideoCall: "Receive Video Call sp",
    showTwitterIcon: "Show Twitter Icon sp",
    receiveCallAlert: "Receive Call Alert sp",
    userSettings: "User Settings : sp",
    locationDetails: "Location Details sp",
    twitterAccountSettings: "Twitter Account Settings sp",
    zipPostCodeEx: "Zip+ (PostCodeEx) sp",
    street: "Street sp",
    street1: "Street 1 sp",
    street2: "Street 2 sp",
    street3: "Street 3 sp",
    street4: "Street 4 sp",
    intersectionPointDetail: "Intersection Point Detail sp",
    addIntersection: "Add Intersection sp",
    addressDetails: "Address Details sp",
    errorLog: "Error Log sp",
    addressNoPrefix: "Address No Prefix sp",
    streetNumberOrMileMarker: "Street Number Or Mile Marker sp",
    addressInfo: "Address Info sp",
    addrNoPrefix: "Addr No Prefix sp",
    numberSuffix: "Number Suffix sp",
    preMod: "Pre-Mod sp",
    direction: "Direction sp",
    preType: "Pre-Type sp",
    preSep: "Pre-Sep sp",
    streetNameFull: "Street Name Full sp",
    postMod: "Post Mod sp",
    streetDirOfTravel: "Street Dir of Travel sp",
    milePost: "Mile Post sp",
    site: "Site sp",
    subStie: "Sub Site sp",
    structure: "Structure sp",
    wing: "Wing sp",
    unitType: "Unit Type sp",
    unitId: "Unit ID sp",
    tags: "Tags sp",
    room: "Room sp",
    section: "Section sp",
    row: "Row sp",
    seat: "Seat sp",
    cityPostalComm: "City (PostalComm) sp",
    postalCommunity: "Postal Community sp",
    postalCode: "Postal Code sp",
    zipPostCode: "Zip (PostCode) sp",
    zipPlus: "Zip+ sp",
    country: "Country sp",
    additionalLocationInfo: "Additional Location Info sp",
    nerisAdditionalAttributes: "NERIS Additional Attributes sp",
    locationMarker: "Location Marker sp",
    landmarkName: "Landmark Name sp",
    fips: "FIPS sp",
    nerisId: "NERIS ID sp",
    nibrsLocType: "NIBRS Loc Type sp",
    dateAdded: "Date Added sp",
    aliasFields: "Alias Fields sp",
    placeType: "Place Type sp",
    legacyStreetName: "Legacy Street Name sp",
    legacyStreetType: "Legacy Street Type sp",
    elevation: "Elevation sp",
    nearestCrossStreet: "Nearest Cross Street sp",
    distance: "Distance sp",
    nxtNearestCross: "Next Nearest Cross sp",
    incorporatedMuni: "Incorporated Muni sp",
    unincorporatedMuni: "Unincorporated Muni sp",
    neighborhoodCommunity: "Neighborhood Community sp",
    psap: "PSAP sp",
    esn: "ESN sp",
    msagCommunity: "MSAG Community sp",
    mapPage: "Map Page sp",
    addressLabel: "Address Label sp",
    placement: "Placement sp",
    nationalGrid: "National Grid sp",
    discrepancyAgencyID: "Discrepancy Agency Id sp",
    dateUpdated: "Date Updated sp",
    effectiveDate: "Effective Date sp",
    expirationDate: "Expiration Date sp",
    nguid: "NGUID sp",
    addCode: "Add Code sp",
    addressDataURL: "Address Data URL sp",
    zone: "Zone sp",
    policeZone: "Police Zone sp",
    fireZone: "Fire Zone sp",
    fireAutoAssist: "Fire Auto Assist sp",
    fireMutualAssist: "Fire Mutual Assist sp",
    wreckerServiceZone: "Wrecker Service Zone sp",
    emsZone: "EMS Zone sp",
    emsAutoAssist: "EMS Auto Assist sp",
    emsMutualAssist: "EMS Mutual Assist sp",
    addMasterAddress: "Add Master Address sp",
    masterAddress: "Master Address sp",
    masterAddressPointDetail: "Master Address Point Detail sp",
    county: "County sp",
    municipality: "Municipality sp",
    stateCode: "State Code sp",
    stateName: "State Name sp",
    countySelection: "County Selection sp",
    viewAddress: "Master Address Point sp",
    viewIntersection: "Intersection Point sp",
    importMasterAddress: "Import Master Address sp",
    importIntersectionPoint: "Import Intersection Point sp",
    viewImportSummaryLogs: "View Import/Summary Logs sp",
    loginOtpRequired: "Login OTP Setting sp",
    isOtpRequired: "OTP Required sp",
    addSupportTypeUnit: "Add Support Type Unit sp",
    agencyName: "Agency Name sp",
    agencyCode: "Agency Code sp",
    agencyType: "Agency Type sp",
    agencyEmail: "Agency Email sp",
    url: "URL sp",
    global: "Global sp",
    local: "Local sp",
    main: "Main sp",
    sub: "Sub sp",
    tenant: "Tenant sp",
    timeZone: "Time Zone sp",
    agencyConfigCode: "Agency Config Code sp",
    physicalAddress: "Physical Address sp",
    billingAddress: "Billing Address sp",
    billingCity: "Billing City sp",
    billingState: "Billing State sp",
    billingZip: "Billing Zip sp",
    billingCounty: "Billing County sp",
    oriNumber: "ORI Number sp",
    fdID: "FD ID sp",
    nemsisID: "NEMSIS ID sp",
    facilityCode: "Facility Code sp",
    mobileXAPIPath: "Mobile X API Path sp",
    liveStreamHost: "Live Stream Host sp",
    liveStreamParserType: "Live Stream Parser Type sp",
    liveStreamPort: "Live Stream Port sp",
    locationParserURL: "Location Parser URL sp",
    mugshotAgencyCode: "Mugshot Agency Code sp",
    mugshotAgencyName: "Mugshot Agency Name sp",
    mugshotURL: "Mugshot URL sp",
    mugshotAgencyID: "Mugshot Agency ID sp",
    agencyLicenses: "Agency Licenses sp",
    noOfUserLicenses: "No of User Licenses sp",
    noOfDeviceLicenses: "No of Device Licenses sp",
    internationalDateLineWest: "(GMT-12:00) International Date Line West sp",
    midwayIslandSamoa: "(GMT-11:00) Midway Island, Samoa sp",
    hawaii: "(GMT-10:00) Hawaii sp",
    alaska: "(GMT-09:00) Alaska sp",
    pacificTimeUSCanada: "(GMT-08:00) Pacific Time (US & Canada) sp",
    tijuanaBajaCalifornia: "(GMT-08:00) Tijuana, Baja California sp",
    arizona: "(GMT-07:00) Arizona sp",
    chihuahuaLaPazMazatlan: "(GMT-07:00) Chihuahua, La Paz, Mazatlan sp",
    mountainTimeUSCanada: "(GMT-07:00) Mountain Time (US & Canada) sp",
    centralAmerica: "(GMT-06:00) Central America sp",
    centralTimeUSCanada: "(GMT-06:00) Central Time (US & Canada) sp",
    bogotaLimaQuitoRioBranco: "(GMT-05:00) Bogota, Lima, Quito, Rio Branco sp",
    easternTimeUSCanada: "(GMT-05:00) Eastern Time (US & Canada) sp",
    indianaEast: "(GMT-05:00) Indiana (East) sp",
    atlanticTimeCanada: "(GMT-04:00) Atlantic Time (Canada) sp",
    caracasLaPaz: "(GMT-04:00) Caracas, La Paz sp",
    manaus: "(GMT-04:00) Manaus sp",
    santiago: "(GMT-04:00) Santiago sp",
    newfoundland: "(GMT-03:30) Newfoundland sp",
    brasilia: "(GMT-03:00) Brasilia sp",
    buenosAiresGeorgetown: "(GMT-03:00) Buenos Aires, Georgetown sp",
    greenland: "(GMT-03:00) Greenland sp",
    montevideo: "(GMT-03:00) Montevideo sp",
    mid_Atlantic: "(GMT-02:00) Mid-Atlantic sp",
    capeVerdeIs: "(GMT-01:00) Cape Verde Is. sp",
    azores: "(GMT-01:00) Azores sp",
    casablancaMonroviaReykjavik: "(GMT+00:00) Casablanca, Monrovia, Reykjavik sp",
    greenwichMeanTimeDublinEdinburghLisbonLondon: "(GMT+00:00) Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London sp",
    amsterdamBerlinBernRomeStockholmVienna: "(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna sp",
    belgradeBratislavaBudapestLjubljanaPrague: "(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague sp",
    brusselsCopenhagenMadridParis: "(GMT+01:00) Brussels, Copenhagen, Madrid, Paris sp",
    sarajevoSkopjeWarsawZagreb: "(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb sp",
    westentralAfrica: "(GMT+01:00) West Central Africa sp",
    amman: "(GMT+02:00) Amman sp",
    athensBucharestIstanbul: "(GMT+02:00) Athens, Bucharest, Istanbul sp",
    beirut: "(GMT+02:00) Beirut sp",
    cairo: "(GMT+02:00) Cairo sp",
    hararePretoria: "(GMT+02:00) Harare, Pretoria sp",
    helsinkiKyivRigaSofiaTallinnVilnius: "(GMT+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius sp",
    jerusalem: "(GMT+02:00) Jerusalem sp",
    minsk: "(GMT+02:00) Minsk sp",
    windhoek: "(GMT+02:00) Windhoek sp",
    kuwaitRiyadhaghdad: "(GMT+03:00) Kuwait, Riyadh, Baghdad sp",
    moscowStPetersburgVolgograd: "(GMT+03:00) Moscow, St. Petersburg, Volgograd sp",
    nairobi: "(GMT+03:00) Nairobi sp",
    tbilisi: "(GMT+03:00) Tbilisi sp",
    tehran: "(GMT+03:30) Tehran sp",
    abuDhabiMuscat: "(GMT+04:00) Abu Dhabi, Muscat sp",
    baku: "(GMT+04:00) Baku sp",
    yerevan: "(GMT+04:00) Yerevan sp",
    kabul: "(GMT+04:30) Kabul sp",
    yekaterinburg: "(GMT+05:00) Yekaterinburg sp",
    islamabadKarachiTashkent: "(GMT+05:00) Islamabad, Karachi, Tashkent sp",
    sriJayawardenapura: "(GMT+05:30) Sri Jayawardenapura sp",
    chennaiKolkataMumbaiNewDelhi: "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi sp",
    kathmandu: "(GMT+05:45) Kathmandu sp",
    almatyNovosibirsk: "(GMT+06:00) Almaty, Novosibirsk sp",
    astanaDhaka: "(GMT+06:00) Astana, Dhaka sp",
    yangonRangoon: "(GMT+06:30) Yangon (Rangoon) sp",
    bangkokHanoiJakarta: "(GMT+07:00) Bangkok, Hanoi, Jakarta sp",
    krasnoyarsk: "(GMT+07:00) Krasnoyarsk sp",
    beijingChongqingHongKongUrumqi: "(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi sp",
    kualaLumpurSingapore: "(GMT+08:00) Kuala Lumpur, Singapore sp",
    irkutskUlaanBataar: "(GMT+08:00) Irkutsk, Ulaan Bataar sp",
    perth: "(GMT+08:00) Perth sp",
    taipei: "(GMT+08:00) Taipei sp",
    osakaSapporoTokyo: "(GMT+09:00) Osaka, Sapporo, Tokyo sp",
    seoul: "(GMT+09:00) Seoul sp",
    yakutsk: "(GMT+09:00) Yakutsk sp",
    adelaide: "(GMT+09:30) Adelaide sp",
    darwin: "(GMT+09:30) Darwin sp",
    brisbane: "(GMT+10:00) Brisbane sp",
    canberraMelbourneSydney: "(GMT+10:00) Canberra, Melbourne, Sydney sp",
    hobart: "(GMT+10:00) Hobart sp",
    guamPortMoresby: "(GMT+10:00) Guam, Port Moresby sp",
    vladivostok: "(GMT+10:00) Vladivostok sp",
    magadanSolomonIsNewCaledonia: "(GMT+11:00) Magadan, Solomon Is., New Caledonia sp",
    aucklandWellington: "(GMT+12:00) Auckland, Wellington sp",
    fijiKamchataMarshallIs: "(GMT+12:00) Fiji, Kamchatka, Marshall Is. sp",
    nukualofa: "(GMT+13:00) Nuku'alofa sp",
    connectionString: "Agency Connection String sp",
    ncicApiUrl: "NCIC Api Url sp",
    deviceLicenseRequired: "Device License Required sp",
    otp: "OTP sp",
    userStatus: "Show user status in header sp",
    selectCallCategories: "Select Call Categories sp",
    socketListenerUrl: "Socket Listener Url sp",
    socketApiUrl: "Socket Api Url sp",
    none: "None sp",
    dispatchDetails: "Dispatch Details sp",
    agencyDetails: "Agency Details sp",
    physicalAddressDetails: "Physical Address Details sp",
    billingAddressDetails: "Billing Address Details sp",
    codeIDDetails: "Code/ID Details sp",
    mugshotDetails: "Mugshot Details sp",
    urlDetails: "URL Details sp",
    deleteDepartment: "Are you sure you want to remove this department ? sp",
    shiftType: "Shift Type sp",
    addDepartment: "Add Department sp",
    supportedUnitType: "Supported Unit Type sp",
    onDuty: "On-Duty sp",
    offDuty: "Off Duty sp",
    nibrsOri: "NIBRS ORI sp",
    ncicOri: "NCIC ORI sp",
    oriOtherJurisdition: "ORI-Other Jurisdiction sp",
    addNewAgency: "Add New Agency sp",
    addNewPerson: "Add New Person sp",
    parentAgency: "Parent Agency sp",
    startDate: "Start Date sp",
    endDate: "End Date sp",
    weeklyOffAfterNoOfShift: "Weekly Off After No of Shift sp",
    noOfOffShift: "No of off Shifts sp",
    noOfTeamsInEachShift: "No of Teams in each shift sp",
    shiftAllocation: "Shift Allocation sp",
    shiftAllocationSchedule: "Shift Allocation Schedule sp",
    backToAllocation: "Back to schedule allocation sp",
    noBreak: "No Break sp",
    scheduleType: "Schedule Type sp",
    schedule: "Schedule sp",
    to: "To sp",
    shiftUsers: "Shift Users sp",
    savePublish: "Save & Publish sp",
    publishMessage: "There is no data for publishing the schedule. sp",
    reScheduleConfirmation: "IF you click ok your data will lost. Are you sure you want to reschedule ? sp",
    userExist: "This user is already exist in same day, same shift. Please select different user or different shift. sp",
    deleteScheduleText: "Are you sure you want to remove this schedule ? sp",
    newUser: "Add New User sp",
    addShiftAllocation: "Add Shift Allocation sp",
    startTime: "Start Time sp",
    endTime: "End Time sp",
    shiftName: "Shift Name sp",
    shifts: "Shifts sp",
    shiftDuration: "Shift Duration sp",
    dateValidationMsg: "End date must be greater than start date sp",
    dateAlreadyExistMsg: "Shift hours exceeds shift limit. sp",
    deleteShiftMsg: "Are you sure you want to remove the shift ? sp",
    color: "Color sp",
    shiftDetails: "Shift Details sp",
    shiftCode: "Shift Code sp",
    selectTime: "Select time sp",
    addShift: "Add Shift sp",
    classOfService: "Class of Service sp",
    addNewCode: "Add New Code sp",
    codeName: "Code Name sp",
    icon: "Icon sp",
    vehicle: "Vehicle sp",
    modelYear: "Model Year sp",
    individualEntry: "Individual Entry sp",
    preDir: "Pre Dir sp",
    postDir: "Post Dir sp",
    building: "Building sp",
    dlNumber: "DLNumber sp",
    sameAsOwner: "Same As Owner sp",
    st: "ST sp",
    tag: "Tag sp",
    year: "Year sp",
    make: "Make sp",
    model: "Model sp",
    personChangeHistory: "Person Change History sp",
    personInvolmentTagsChangeHistory: "Person Involvement Tags History sp",
    vehicleChangeHistory: "Vehicle Change History sp",
    globalPersonSearch: "Global Person Search sp",
    addrNumber: "Addr Number sp",
    numSuffix: "Num Suffix sp",
    preDirection: "Pre Direction sp",
    streetName: "Street Name sp",
    streetType: "Street Type sp",
    postDirection: "Post Direction sp",
    buildingNbr: "Building Nbr sp",
    unitID: "Unit ID sp",
    buildingName: "Building Name sp",
    floor: "Floor sp",
    cityName: "City Name sp",
    zip: "Zip sp",
    zipPlus4: "Zip Plus 4 sp",
    dateOfBirth: "Date Of Birth sp",
    age: "Age sp",
    race: "Race sp",
    sex: "Sex sp",
    ethnicity: "Ethnicity sp",
    height: "Height sp",
    hair: "Hair sp",
    eyes: "Eyes sp",
    phoneType: "Phone Type sp",
    idType: "Id Type sp",
    idNumber: "ID Number sp",
    emailType: "Email Type sp",
    latitude: "Latitude sp",
    longitude: "Longitude sp",
    statuteCode: "Statute Code sp",
    statuteCitation: "Statute Citation sp",
    attempted: "Attempted sp",
    logType: "Log Type sp",
    completed: "Completed sp",
    misdemeanor: "Misdemeanor sp",
    other: "Other sp",
    felony: "Felony sp",
    incidentViolationDetails: "Incident Violation Details sp",
    offenseInformation: "Offense Information sp",
    aboutMe: "About Me sp",
    generalInformation: "General Information sp",
    birthday: "Birthday sp",
    locations: "Locations sp",
    involvementTags: "Involvement Tags sp",
    attemptedCompleted: "Attempted/Completed sp",
    counts: "Count(s) sp",
    misdFelony: "Misd/Felony sp",
    ownersInformation: "Owner's Information sp",
    driversInformation: "Driver's Information sp",
    involvement: "Involvement sp",
    notes: "Notes sp",
    suffix: "Suffix sp",
    residentialAddress: "Residential Address sp",
    residenceAddrNumber: "Residence Addr Number sp",
    residenceNumSuffix: "Residence Num Suffix sp",
    residencePreDirection: "Residence Pre Direction sp",
    residenceStreetName: "Residence Street Name sp",
    residenceStreetType: "Residence Street Type sp",
    residencePostDirection: "Residence Post Direction sp",
    residenceBuildingNbr: "Residence Building Nbr sp",
    residenceUnitType: "Residence Unit Type sp",
    residenceUnitID: "Residence Unit ID sp",
    residenceBuildingName: "Residence Building Name sp",
    residenceFloor: "Residence Floor sp",
    residenceCityName: "Residence City Name sp",
    residenceState: "Residence State sp",
    residenceZIP: "Residence ZIP sp",
    residenceZIPPlus4: "Residence ZIP Plus 4 sp",
    officeAddress: "Office Address sp",
    officeAddrNumber: "Office Addr Number sp",
    officeNumSuffix: "Office Num Suffix sp",
    officePreDirection: "Office Pre Direction sp",
    officeStreetName: "Office Street Name sp",
    officeStreetType: "Office Street Type sp",
    officePostDirection: "Office Post Direction sp",
    officeBuildingNbr: "Office Building Nbr sp",
    officeUnitType: "Office Unit Type sp",
    officeUnitID: "Office Unit ID sp",
    officeBuildingName: "Office Building Name sp",
    officeFloor: "Office Floor sp",
    officeCityName: "Office City Name sp",
    officeState: "Office State sp",
    officeZIP: "Office ZIP sp",
    officeZIPPlus4: "Office ZIP Plus 4 sp",
    number: "Number sp",
    personGlobalSearch: "Person Global Search sp",
    involvementNotes: "Involvement / Notes sp",
    complainant: "COMPLAINANT sp",
    caller: "CALLER sp",
    victim: "VICTIM sp",
    witness: "WITNESS sp",
    suspect: "SUSPECT sp",
    arrestee: "ARRESTEE sp",
    associated: "ASSOCIATED sp",
    driver: "DRIVER sp",
    passenger: "PASSENGER sp",
    cyclist: "CYCLIST sp",
    pedestrian: "PEDESTRIAN sp",
    incidentFileDeleteMsg: "Are you sure you want to delete this incident file ? sp",
    incidentNoteDeleteMsg: "Are you sure you want to delete this incident Note ? sp",
    incidentPropertyDeleteMsg: "Are you sure you want to delete this incident Property ? sp",
    incidentCommunicationDeleteMsg: "Are you sure you want to delete this incident Communication Logs ? sp",
    incidentSecurityDeleteMsg: "Are you sure you want to delete this incident Security ? sp",
    incidentViolationDeleteMsg: "Are you sure you want to delete this incident Violation ? sp",
    incidentOrganizationDeleteMsg: "Are you sure you want to delete this incident Organization ? sp",
    deleteIncidentDetail: "Delete Incident Detail sp",
    emailAddress: "Email Address sp",
    changeEmail: "Change email sp",
    emailConfirm: "Your email has been changed successfully. You will need to login again to continue. sp",
    currentPassword: "Current Password sp",
    password: "Password sp",
    confirmPassword: "Confirm Password sp",
    changePassword: "Change password sp",
    passwordConfirm: "Your password has been changed successfully. You will need to login again to continue. sp",
    addIncidentPerson: "Add Incident Person sp",
    recentPersonsUsed: "Recent Persons Used sp",
    recentAddressesUsed: "Recent Addresses Used sp",
    recentVehicleUsed: "Recent Vehicle Used sp",
    tokenExpireMsg: "Your token has expired and you will need to Sign in again. You will be redirected to the login page in 5 seconds. sp",
    invalidSessionMsg: "You have logged in from another device, so this session is no longer valid. sp",
    heightFeet: "{{feet}} ft",
    heightFeetInches: "{{feet}} ft {{inches}} in",
    heightInches: "{{inches}} in",
    invalidHeightEntry: "Invalid height entry",
    redirecting: "Redirecting in 5 seconds sp",
    goto: "Go to sp",
    signIn: "Sign in sp",
    personDeleteMsg: "Are you sure you want to delete this person ? sp",
    pleaseFillNote: "Please fill the note sp",
    pleaseSelectFile: "Please select file sp",
    communicationWith: "Communication With sp",
    communicationNote: "Communication Note sp",
    involvementLossType: "Involvement (LossType) sp",
    quantity: "Quantity sp",
    weight: "Weight sp",
    brand: "Brand/Make/Manufacturer sp",
    caliber: "Caliber/Gauge sp",
    estimatedValue: "Estimated Value sp",
    freeForm: "Free-Form sp",
    serialNumber: "Serial Number sp",
    ownedAppliedNumber: "Owner Applied Number sp",
    ncicRefNumber: "NCIC Ref Number sp",
    recovered: "Recovered sp",
    recoveredDate: "Recovered Date sp",
    recoveryLocation: "Recovery Location sp",
    recoveryCondition: "Recovery Condition sp",
    recoveredValue: "Recovered Value sp",
    pleaseSelectTitle: "Please Select Title sp",
    changeFrom: "Change From sp",
    changedBy: "Changed By sp",
    ncicNumber: "NCIC Number sp",
    ncicStatus: "NCIC Status sp",
    recoveryInformation: "Recovery Information sp",
    recoveredBy: "Recovered By sp",
    ncicInformation: "NCIC Information sp",
    owner: "Owner sp",
    automatic: "Automatic sp",
    unknown: "Unknown sp",
    drugType: "Drug Type sp",
    drugName: "Drug Name sp",
    measure: "Measure sp",
    drugQuantity: "Estimated Drug Quantity sp",
    pharmacy: "Pharmacy sp",
    prescriptionNumber: "Prescription Number sp",
    testedVia: "Tested Via sp",
    submittedToLab: "Submitted To Lab sp",
    evidence: "Evidence sp",
    evidenceNumber: "Evidence Number sp",
    evidenceStatus: "Evidence Status sp",
    agencyExhibitNumber: "Agency/Case Exhibit Number sp",
    evidenceLocation: "Evidence Location sp",
    acquiredDateTime: "Acquired Date Time sp",
    acquiredBy: "Acquired By sp",
    acquiredLocation: "Acquired Location sp",
    nextAction: "Next Action sp",
    markLostFound: "Mark as Lost/Found Item sp",
    permanent: "Permanent sp",
    reviewDate: "Review Date sp",
    dispositionReason: "Disposition Reason sp",
    reconciliationHistory: "Reconciliation History sp",
    custodyHistory: "Custody History sp",
    labResults: "Lab Results sp",
    evidenceInformation: "Evidence Information sp",
    organizationName: "Organization Name sp",
    organizationType: "Organization Type sp",
    primaryLocation: "PRIMARY LOCATION sp",
    primaryContact: "PRIMARY CONTACT sp",
    addNewIncident: "Add New Incident sp",
    violationToAdd: "Incident Violation to Add sp",
    scanDriversLicense: "Scan Drivers License sp",
    searchbyName: "Search by Name sp",
    addressValidated: "Address Validated sp",
    unableToValidate: "Unable To Validate sp",
    offense: "Offense sp",
    dob: "DOB sp",
    hgt: "Hgt sp",
    wgt: "Wgt sp",
    file: "File sp",
    note: "Note sp",
    doesNotSupportMsg: "Browser doesn't support speech recognition sp",
    searchForOwner: "Search For Owner sp",
    searchForDriver: "Search For Driver sp",
    searchForDriverAddress: "Search Driver Address sp",
    searchForOwnerAddress: "Search Owner Address sp",
    Add: "Add sp",
    add: "Add sp",
    update: "Update sp",
    contact: "Contact sp",
    addNewContact: "Add New Contact sp",
    title: "Title sp",
    firstName: "First Name sp",
    middleName: "Middle Name sp",
    lastName: "Last Name sp",
    city: "City sp",
    state: "State sp",
    phone: "Phone sp",
    contactsDeleteMsg: "Are you sure you want to delete Contacts ? sp",
    errorLogs: "Error Logs sp",
    user: "User sp",
    relativityAPI: "Relativity API sp",
    incidentAPI: "Incident API sp",
    dispatchUI: "Dispatch UI sp",
    adminUI: "Admin UI sp",
    all: "ALL sp",
    resolved: "Resolved sp",
    unResolved: "Unresolved sp",
    resolveError: "Error Resolved sp",
    confirmMsgErrorResolved: "Are you sure you want to mark this error as resolved? sp",
    errorDetails: "Error Details sp",
    method: "Method sp",
    timeStamp: "Timestamp sp",
    source: "Source sp",
    stackTrace: "StackTrace sp",
    resolvedBy: "Resolved By sp",
    resolvedAt: "Resolved At sp",
    resolve: "Resolve sp",
    dateWarningMsg: "Ensure To date is not greater than From date. sp",
    dispatchAPI: "Dispatch API sp",
    socketAPI: "Socket API sp",
    fileUpload: "File Upload sp",
    noFileSelectMsg: "No File Selected sp",
    upload: "Upload sp",
    fileSelectMsg: "1 File is Selected sp",
    filesSelectedMsg: "Files Selected sp",
    uploadedFiles: "Uploaded Files sp",
    fileViewer: "File Viewer sp",
    incidentInfo: "Incident Info sp",
    incidentLocation: "Incident Location sp",
    incidentFile: "Incident File sp",
    close: "Close sp",
    incidentNotes: "Incident Notes sp",
    incidentPerson: "Incident Person sp",
    vehicleEntry: "Vehicle Entry sp",
    searchForVehicle: "Search For Vehicle sp",
    incidentViolation: "Incident Violation sp",
    person: "Person sp",
    ownerInformation: "Owner Information sp",
    driverInformation: "Driver Information sp",
    incidentInformation: "Incident Information sp",
    incidentPDF: "Incident PDF sp",
    narrative: "Narrative sp",
    addNewVehicle: "Add New Vehicle sp",
    incidents: "Incidents sp",
    addIncident: "Add Incident sp",
    incidentEndMsg: "No more Incidents sp",
    incidentDeleteMsg: "Are you sure you want to delete this incident ? sp",
    incidentPersonDeleteMsg: "Are you sure you want to delete this incident person ? sp",
    incidentVictimDeleteMsg: "Are you sure you want to delete this incident victim ? sp",
    incidentVictimDeleteMsg: "Are you sure you want to delete this incident victim ? sp",
    incidentVehicleDeleteMsg: "Are you sure you want to delete this incident vehicle ? sp",
    pleaseSelectViolation: "Please Select Violation sp",
    pleaseSelectPerson: "Please Select Person sp",
    pleaseSelectViolation: "Please Select Violation sp",
    pleaseSelectPerson: "Please Select Person sp",
    edit: "Edit sp",
    individualType: "Individual Type sp",
    businessName: "Business Name sp",
    contactName: "Contact Name sp",
    ext: "Ext sp",
    placeName: "Place name sp",
    address: "Address sp",
    reportedBy: "Reported By sp",
    incidentNumber: "Incident Number sp",
    zipCode: "Zip Code sp",
    violation: "Violation sp",
    reportedDate: "Reported Date sp",
    persons: "Persons sp",
    vehicles: "Vehicles sp",
    organization: "Organization sp",
    property: "Property sp",
    narratives: "Narratives sp",
    files: "Files sp",
    communicationLogs: "Communication Logs sp",
    security: "Security sp",
    locationPlaceOcurred: "Location/Place Ocurred sp",
    nfirsEquipmentType: "NFIRS Equipment Type sp",
    addNFIRS: "Add Nfirs sp",
    type: "Type sp",
    date: "Date sp",
    gpsLocation: "GPS Location sp",
    phoneNumber: "Phone Number sp",
    email: "Email sp",
    comments: "Comments sp",
    details: "Details sp",
    delete: "Delete sp",
    customer: "Customer sp",
    status: "Status sp",
    dateValidation: "Start date must less than end date! sp",
    dateTime: "Date Time sp",
    from: "From sp",
    tipComment: "Tip Comment sp",
    commentType: "Comment Type sp",
    answered: "Answered sp",
    quikTipDetails: "QuikTip Details sp",
    tipTypes: "Tip Types sp",
    addTipType: "Add Tip Type sp",
    notificationCenter: "Notification Center sp",
    sendMessage: "Send Message sp",
    tipType: "Tip Type sp",
    description: "Description sp",
    deleteMsg: "Are you sure you want to delete ? sp",
    sender: "Sender sp",
    pushedMessage: "Pushed Message sp",
    createDateTime: "Created Date Time sp",
    message: "Message sp",
    tipDetail: "Tip Details sp",
    attachments: "Attachments sp",
    tipComments: "Tip Comments sp",
    comment: "Comment sp",
    addInternalComment: "Add Internal Comment sp",
    addPublicComment: "Add Public Comment sp",
    addQuestion: "Add Question sp",
    messageMustHavevalue: "Message Must Have Value! sp",
    commentShouldNotBeEmptyOrNull: "Comment Should Not Be Empty! sp",
    changeStatus: "Change Status sp",
    name: "Name sp",
    users: "Users sp",
    teamDeleteMsg: "Are you sure you want to delete team ? sp",
    confirmationmsg: "This user is present in other team. Are you sure you want this user ? sp",
    department: "Department sp",
    departmentRequired: "You must choose department... sp",
    teamLimit: "No of teams in this department is Exceeds.. sp",
    teamlead: "Team Leader sp",
    colorCode: "Color Code sp",
    cancel: "Cancel sp",
    addTeam: "Add Team sp",
    twitterAuthorize: "Twitter Authorize sp",
    unit: "Units sp",
    unitName: "Unit Name sp",
    agency: "Agency sp",
    branch: "Branch sp",
    station: "Station sp",
    manufacturer: "Manufacturer sp",
    style: "Style sp",
    "color1/color2": "Color 1 / Color 2 sp",
    category: "Category sp",
    emsLevel: "EMS Level sp",
    unitPositions: "Unit Positions sp",
    oilType: "Oil Type sp",
    pmcsSchedule: "PMCS Schedule sp",
    nextServiceDate: "Next Service Date sp",
    nextServiceMiles: "Next Service Miles sp",
    latestMileage: "Latest Mileage sp",
    estimatedEndofLife: "Estimated End of Life sp",
    inServiceDate: "In Service Date sp",
    tireSize: "Tire Size sp",
    fueltype: "Fuel Type sp",
    vinNumber: "VIN Number sp",
    tagState: "Tag State sp",
    tagNumber: " Tag Number sp",
    nextMaintenance: "Next Maintenance sp",
    role: "Role sp",
    capabilityTags: "Capability Tags sp",
    issuedAssignedTo: "Issued/Assigned To sp",
    confirm: "Confirm sp",
    unitDeleteMsg: "Are you sure you want to delete Units ? sp",
    no: "No sp",
    yes: "Yes sp",
    save: "Save sp",
    back: "Back sp",
    selectFile: "Select File sp",
    preview: "Preview sp",
    policecar: "Police Car sp",
    fireTruck: "Fire Truck sp",
    ems: "EMS sp",
    unitType: "Unit Type sp",
    addRole: "Add Role sp",
    ncic: "ACIC/NCIC sp",
    neim: "NEIM sp",
    action: "Action sp",
    pleaseSelectImage: "Please select image sp",
    addUnits: "Add Unit sp",
    userAudit: "User Audit sp",
    search: "Search sp",
    appName: "Application Name sp",
    activity: "Activity sp",
    activityDateTime: "Activity date time sp",
    incident: "Incident sp",
    viewDetails: "View Details sp",
    intersectionErrorLogs: "Intersection Error Logs sp",
    importLogsIntersectionPoint: "Import Logs - Intersection Point sp",
    filePath: "File Path sp",
    fileName: "File Name sp",
    updatedCount: "Updated Count sp",
    insertedCount: "Inserted Count sp",
    matchCount: "Match Count sp",
    failedCount: "Failed Count sp",
    totalCount: "Total Count sp",
    viewImportLogs: "View Import Logs sp",
    masterAddressList: "Master Address List sp",
    masterIntersectionList: "Master Intersection List sp",
    municipalityType: "Municipality Type sp",
    addressNumber: "Address Number sp",
    addressNumberComp: "Address Number Comp sp",
    addressBuilding: "Address Building sp",
    legacyAddress: "Legacy Address sp",
    localName: "Local Name sp",
    nearestXStDist: "Nearest Distance sp",
    secondNearestXStDist: "SecondNearest Distance sp",
    masterAddressErrorLogs: "Master Address Error Logs sp",
    importLogsMasterAddressPoint: "Import Logs - Master Address Point sp",
    importDate: "Import Date sp",
    deleteWarning: "Are you sure you want to delete? sp",
    others: "Others sp",
    soundAlertSetting: "Sound Alert Setting sp",
    deleteShiftText: "Are you sure you want to remove the shift? sp",
    addNumber: "Add Number sp",
    deletethisVehicleMsg: "Are you sure you want to delete this vehicle ? sp",
    areYouSureYouWantDelete: "Are You Sure You Want Delete sp",
    selectFiles: "Select Files sp",
    noFileSelectedMsg: "No file is selected. sp",
    fileIsSelected: "1 file is selected. sp",
    accidentalCall: "Accidental Call sp",
    callBackDeadCall: "Call Back Dead Call sp",
    callBackNoAnswer: "Call Back No Answer sp",
    nonAccidentalCallNeedOfficer: "Non Accidental Call Need Officer sp",
    callForService: "Call For Service - Initial Entry From 911 sp",
    showRectangle: "Show Rectangle sp",
    archiveChatOlderThan: "Archive chat older than sp",
    permanentLogin: "Permanent Login sp",
    userAgencyDetails: "User Agency Details sp",
    clear: "Clear sp",
    lastLoginDate: "Last Login Date sp",
    objectId: "ObjectId sp",
    subAddress: "SubAddress sp",
    selectAll: "Select All sp",
    deleteDepartmentMsg: "Are you sure you want to remove this department? sp",
    available: "Available sp",
    unAvailable: "UnAvailable sp",
    where: "Where sp",
    downloadTemplate: "Download Template sp",
    nibrsCode: "Nibrs Code sp",
    addNibrsCode: "Add Nibrs Code sp",
    stateViolation: "State Violation sp",
    violationTypes: "Violation Types sp",
    linkedClassification: "Linked Classification sp",
    defaultClassification: "Default Classification sp",
    jailTypes: "Jail Types sp",
    fingerPrintRequired: "FingerPrint Required sp",
    citationViolation: "Citation Violation sp",
    criminalViolation: "Criminal Violation sp",
    warrentViolation: "Warrent Violation sp",
    trafficViolation: "Traffic Violation sp",
    localOrdinance: "Local Ordinance sp",
    violationClassification: "Violation Classification sp",
    community: "Community sp",
    weaponType: "Weapon Type sp",
    warningMsgForCriminalActivity: " You can only select up to 3 criminal activities. sp",
    criminalActivity: "Criminal Activity sp",
    gangType: "Gang Type sp",
    warningMsgForGangType: "Can select up to 2 gangs sp",
    biasMotivation: "Bias Motivation sp",
    addClassification: "Add Classification sp",
    classificationMapping: "Classification Mapping sp",
    reset: "Reset sp",
    oneDefaultClassificationNeedsToBeSelected: "One Default Classification Needs To Be Selected. sp",
    locationTypes: 'Location Types sp',
    earliestDate: "Earliest Date/Time sp",
    latestDate: "Latest Date/Time sp",
    reportDate: "Report Date/Time",
    updateRequest: "Update Request sp",
    sendUpdateRequest: "Send Update Request sp",
    updatedFields: "Updated Fields sp",
    updatedOn: "Updated On sp",
    updatedBy: "Updated By sp",
    updateAddress: "Update Address sp",
    approve: "Approve sp",
    reject: "Reject sp",
    acceptedRejectedBy: "Accepted/Rejected By sp",
    acceptedRejectedByOn: "Accepted/Rejected On sp",
    masterAddressUpdateRequest: "Master Address Update Request sp",
    oldFields: "Old sp",
    newFields: "New sp",
    dispatchVersion: "Dispatch Version sp",
    socketVersion: "Socket Version sp",
    pollingVersion: "Polling Version sp",
    importViolation: "Import Violation sp",
    outOfJurisdiction: "Out Of Jurisdiction",
    ARStatuteCode: "AR Statute Code sp",
    TextStatuteTitle: "Text Statute Title sp",
    OffenseDescriptionAbbreviated: "Offense Description Abbreviated sp",
    Type: "Type sp",
    Class: "Class sp",
    NIBRSCode: "NIBRS Code sp",
    BeginDate: "Begin Date sp",
    EndDate: "End Date sp",
    JailTypes: "Jail Types sp",
    ViolationTypes: "Violation Types sp",
    resetPassword: "Reset Password sp",
    rpsCredentials: "RPS Credentials sp",
    mugshotCredentials: "Mugshot Credentials sp",
    disableUser: "Disable User sp",
    enableUser: "Enable User sp",
    options: "Options sp",
    sourceFields: "Source Fields sp",
    destinationFields: "Destination Fields sp",
    selectField: "Select Field sp",
    death: "Death sp",
    createdDate: "Created Date sp",
    lastModifiedDate: "Last Modified Date sp",
    rpsCredentialsFor: "RPS Credentails For sp",
    pleaseEnterUsernameAndPassword: "Please Enter Your Username And Password sp",
    mugshotCredentialsFor: "Mugshot Credentials For sp",
    backtoAgency: "Back to agency sp",
    printPDF: "Print PDF sp",
    audit: "Audit sp",
    generateShift: "Generate Shift sp",
    viewAndEdit: "View & Edit sp",
    reSchedule: "Re-Schedule sp",
    generateShiftAllocation: "Generate Shift Allocation sp",
    changePasswordFor: "Change Password For sp",
    sendEmailToUser: "Send Email to User sp",
    deselectAll: "Deselect All sp",
    displayFields: "Display Fields sp",
    statute: "Statute sp",
    searchFields: "Search Fields sp",
    changeAgency: "Change Agency sp",
    userNotFound: "User not found sp",
    userDisabled: "User disabled successfully sp",
    selectAgencyToBeChanged: "Select agency to be changed sp",
    sortColumns: "Sort Columns sp",
    addressValidateMsg: "You need to validate the address. sp",
    stateMsg: "State should same as agency sp",
    searchForAddress: "Search for address sp",
    incidentOrganization: "Incident Organization sp",
    selectUsers: "Select users sp",
    citationTypeToAdd: "Citation Type to Add sp",
    formatted: "Formatted sp",
    callBackAnswer: "Call Back Answer sp",
    shiftLimitExceedsForDepartment: "The shift limit is exceeds for this department please choose other department. sp",
    shiftAlreadyExists: "This shift is already exists, please select different shift name. sp",
    ageFrom: "Age From sp",
    ageTo: "Age To sp",
    ageType: "Age Type sp",
    issuingAuthority: "Issuing Authority sp",
    dateIssued: "Date Issued sp",
    dateExpires: "Date Expires sp",
    revoked: "Revoked sp",
    revokedDate: "Revoked Date sp",
    idInfo: "Id Info sp",
    phoneNumberType: "Phone Number Type sp",
    extension: "Extension sp",
    extendedDescription: "Extended Description sp",
    skinTone: "Skin Tone sp",
    eyeColor: "Eye Color sp",
    hairLength: "Hair Length sp",
    eyeWear: "Eyewear sp",
    hairStyle: "Hair Style sp",
    demeanor: "Demeanor sp",
    facialHairColor: "Facial Hair Color sp",
    dexterity: "Dexterity sp",
    facialHairStyle: "Facial Hair Style sp",
    speech: "Speech sp",
    build: "Build sp",
    dental: "Dental/Teeth sp",
    clothing: "Clothing and General Appearance sp",
    addAddress: "Please add address to send update request sp",
    recent: "Recent sp",
    suspectedOfUsing: "Suspected of Using sp",
    methodOfEntry: "Method of Entry sp",
    EarliestLessLatestDate: "Earliest Date should be less or equal to Latest Date sp",
    EarliestLessReportDate: "Earliest Date should less or equal to Report Date sp",
    LatestLessEarliestDate: "Latest Date should be equal or greater than Earliest Date sp",
    LatestLessReportDate: "Latest Date should be less or equal to Report Date sp",
    ReportDateGreater: "Report Date should be greater or equal to Earliest and Latest Date sp",
    primaryColor: "Primary Color sp",
    secondaryColor: "Secondary Color sp",
    vin: "Vin sp",
    lastNcicChange: "Last Ncic Change Date sp",
    lastRpsChange: "Last Rps Change Date sp",
    recentPerson: "Person(s) sp",
    recentAddress: "Address(es) sp",
    recentVehicle: "Vehicle(s) sp",
    recentOrganisation: "Organisation(s) sp",
    fireArmType: "Fire Arm Type sp",
    gunType: "Gun Type sp",
    gunDescription: "Gun Description sp",
    noReportDate: "No Report Date sp",
    reportedPerson: "Reported Person sp",
    baby: "Baby",
    newborn: "Newborn",
    neonatal: "Neonatal",
    brandLabel: "Brand sp",
    pleaseSelectLogType: "Please select log type sp",
    gatheringResults: "Gathering Results sp",
    dLScan: "DL SCAN sp",
    nCICSearch: "NCIC SEARCH sp",
    localSearch: "LOCAL SEARCH sp",
    globalSearch: "GLOBAL SEARCH sp",
    manualEntry: "MANUAL ENTRY sp",
    recentList: "RECENT LIST sp",
    residence: "Residence sp",
    USCitizen: "US Citizen sp",
    legalAlien: "Legal Alien sp",
    nationality: "Nationality sp",
    arrestInformation: "Arrest Information sp",
    arrestDate: "Arrest Date sp",
    arrestNumber: "Arrest Number sp",
    arrestSequence: "Arrest Sequence sp",
    arrestType: "Arrest Type sp",
    arresteeArmed: "Arrestee Armed sp",
    multipleArresteeSegmentIndicator: "Multiple Arrestee Segment Indicator sp",
    juvenileDisposition: "Juvenile Disposition sp",
    searchByVehicle: "Search by Vehicle sp",
    reportingOfficer: "Reporting officer sp",
    exceptionalClearance: "Exceptional clearance sp",
    exceptionalClearanceDate: "Exceptional clearance date sp",
    cargoTheft: "Cargo theft sp",
    domesticViolence: "Domestic violence sp",
    sealed: "Sealed sp",
    active: "Active sp",
    awaitingInformation: "Awaiting information sp",
    closed: "Closed sp",
    inactive: "Inactive sp",
    unfounded: "Unfounded sp",
    LEOKAInformation: "LEOKA Information sp",
    circumstance: "Circumstance sp",
    registrationScan: "REGISTRATION SCAN sp",
    victimConnectedToOffense: "Victim Connected To Offense sp",
    offenseConnectedToVictim: "Offense Connected To Victim  sp",
    victims: "Victim(s) sp",
    selectPerson: "Select Person sp",
    selectViolation: "Select Violation sp",
    CheckAllVictims: "Check all victims against whom this offense was attempted or committed sp",
    personVictimDeleteMsg: "Are you sure you want to delete this person victim ? sp",
    warningMsgForDelete: "This person is associated with multiple violations. Please remove the person from the violation before deleting. sp",
    victimSuspectRelationship: "Victim To Suspect Relationship(s) sp",
    registrationScan: "REGISTRATION SCAN sp",
    victimSuspectInfo: "The Victim's relationship to each suspect/offender is reported when the victim was the object of a Crime Against Person, i.e. Assault Offense, Homicide Offense, Kidnaping/Abduction, Forcible Sex Offense, or Nonforcible Sex Offense sp",
    victimRelationship: "Victim Relationship sp",
    noViolation: "No offense is currently available to link. You need to create an offense first sp.",
    selectIncidentAccessRight: "The default app is Incident, so you must select the Incident access right sp.",
    selectDispatchAccessRight: "The default app is Dispatch, so you must select the Mobile Dispatch access right sp.",
    selectAdminAccessRight: "The default app is Admin, so you must select the Users access right sp.",
    createMasterOrganizationSaveSuccess: "Master Organization created successfully! sp",
    createMasterOrganizationSaveFailed: "Error while creating Master Organization! sp",
    masterOrganizationUpdateSuccess: "Master Organization updated successfully! sp",
    masterOrganizationUpdateFailed: "Error while updating Master Organization! sp",
    searchOrganizations: "Search Organizations sp",
    businessName: "Business Name sp",
    doesNotUseOldRps: "Does not use old RPS sp",
    incidentApp: "Incident App sp",
    incidentWebsite: "Incident Website sp",
    selectIncidentAppOrWebsiteAccessRight: "Please select either 'Incident App' or 'Incident Website' access rights when 'Incident' access is selected sp.",
    selectDispatchAppOrWebsiteAccessRight: "Please select either 'Dispatch App' or 'Dispatch Website' access rights when 'Dispatch' access is selected sp.",
    intersectionUniqueStreetName: "This intersection only has one unique street name.  Do you want to add another street name or delete the intersection? sp",
    streetNames: "Street Names sp",
    showPersonAlert: "Show Person Alert sp"
};

export default locale;