import i18next from 'i18next';
import React from 'react';

const AgencyList = React.lazy(() => import('./agency/AgencyList'));
const Scheduler2 = React.lazy(() => import('../Scheduler 2/scheduler2'));

const AgencyAppConfig = {
	settings: {
		layout: {}
	},
	routes: [
		{
			path: '/admin/agencyList',
			element: <AgencyList />
		},
		// {
		// 	path: '/admin/graph',
		// 	element: <Grpah />
		// },
		{
			path: '/admin/scheduler2',
			element: <Scheduler2 />
		},
	]
};

export default AgencyAppConfig;
