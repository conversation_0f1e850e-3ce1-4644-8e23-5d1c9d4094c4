import axios from "axios";
import { showMessage } from 'app/store/actions/fuse';
export const GET_SAVEDFILEOBJLIST = 'GET_SAVEDFILEOBJLIST';
export const GET_SAVEDFILEOBJ = 'GET_SAVEDFILEOBJ';
export const UPLOAD_SUCCESS = 'UPLOAD_SUCCESS';
export const UPLOAD_FILEOBJ = 'UPLOAD_FILEOBJ';


//Get saved file List
export function getSavedFileObjList() {
	const request = axios.get(process.env.REACT_APP_FILE_UPLOAD_API_URL.concat(`listObjects`));
	return dispatch =>
		request.then(response => {
			dispatch({
				type: GET_SAVEDFILEOBJLIST,
				payload: response.data.Contents
			})
		}
		);
}

//Get a saved file object
export function getSavedFileObj(key) {
	if (key.length == 0) {
		return {
			type: GET_SAVEDFILEOBJ,
			payload: []
		};
	}
	else {
		const request = axios.get(process.env.REACT_APP_FILE_UPLOAD_API_URL.concat(`getObject?fileKey=`) + key);
		return dispatch =>
			request.then(response => {
				dispatch({
					type: GET_SAVEDFILEOBJ,
					payload: response.data
				})
			}
			);
	}

}


//Upload seleted documents
export function uploadFileObjList(formData) {

	const request = axios.post(process.env.REACT_APP_FILE_UPLOAD_API_URL.concat(`upload`), formData, {
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
	return dispatch =>
		request.then(res => {
			if (res.status == 400) {
				dispatch(showMessage({
					message: res, autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'error'
				}));
			}
			else {
				dispatch(showMessage({
					message: 'Files uploaded sucessfully..', autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'success'
				}));
				getSavedFileObjList();
				dispatch({
					type: UPLOAD_SUCCESS,
					payload: res.data
				})
			}
		}).catch(error => {
			return dispatch(
				showMessage({
					message: error.response.data,
					autoHideDuration: 2000,
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'right'
					},
					variant: 'warning'
				})	,


			);
		});

}

export function uploadFileObject(fileList) {
	return {
		type: UPLOAD_FILEOBJ,
		uploadFileObj: fileList
	};
}
