import { showMessage } from "app/store/fuse/messageSlice";
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';
import { createSlice } from '@reduxjs/toolkit';

export const getNibrsList = (sortField, sortDirection, pageIndex, pageLimit, searchText) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`admin/api/nibrsCode/nibrsList/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}`)
            .then((response) => {
                if (response.status === 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setNibrsList(listData.nibrsCodesList));
                    dispatch(setNibrsTotalCount(listData.totalCount));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const getNibrsListByCode = (nibrsCode) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`admin/api/nibrsCode/nibrsListByCode/${nibrsCode}`)
            .then((response) => {
                if (response.status === 200) {
                    let res = JSON.parse(decrypt(response.data));
                    dispatch(setNibrsByCodeData(res.nibrsList));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    } catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const deleteNibrs = (id) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/nibrsCode/nibrsDelete/${id}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(setNibrsSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const createNibrs = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/nibrsCode/createNibrs`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(setNibrsSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

const initialState = {
    isloading: false,
    nibrsList: [],
    nibrsTotalCount: 0,
    nibrsSuccess: false,
    nibrsByCodeData: null,
};

const nibrsCodeSlice = createSlice({
    name: "NibrsCode",
    initialState,
    reducers: {
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setNibrsList: (state, action) => {
            state.nibrsList = action.payload;
        },
        setNibrsTotalCount: (state, action) => {
            state.nibrsTotalCount = action.payload;
        },
        setNibrsSuccess: (state, action) => {
            state.nibrsSuccess = action.payload;
        },
        setNibrsByCodeData: (state, action) => {
            state.nibrsByCodeData = action.payload;
        },
    }
})

export const {
    setLoading,
    setNibrsList,
    setNibrsTotalCount,
    setNibrsSuccess,
    setNibrsByCodeData
} = nibrsCodeSlice.actions;

export default nibrsCodeSlice.reducer;