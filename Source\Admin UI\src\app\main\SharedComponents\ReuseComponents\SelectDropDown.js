import Grid from "@mui/material/Grid";
import { useTranslation } from "react-i18next";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { makeStyles } from "@mui/styles";

const useStyles = makeStyles((theme) => ({
    layoutRoot: {},
    root: {
        maxWidth: 345,
        flexGrow: 1,
    },
    expand: {
        transform: "rotate(0deg)",
        marginLeft: "auto",
        transition: theme.transitions.create("transform", {
            duration: theme.transitions.duration.shortest,
        }),
    },
    expandOpen: {
        transform: "rotate(180deg)",
    },
    dividerFullWidth: {
        margin: `5px 0 0 ${theme.spacing(2)}px`,
    },
    media: {
        width: "30%",
        paddingTop: "30%",
        height: 0,
        marginLeft: "15%",
    },
    cardRoot: {
        minWidth: 275,
        borderRadius: '0%'
    },
    title: {
        fontSize: 18,
        fontStyle: "bold",
        textAlign: "center",
    },
    formControl: {
        margin: theme.spacing(0.5),
        width: "100%",
    },
    selectEmpty: {
        marginTop: theme.spacing(2),
    },
    rootTab: {
        flexGrow: 1,
        width: "100%",
        backgroundColor: theme.palette.background.paper,
    },
    backdrop: {
        zIndex: theme.zIndex.drawer + 1,
        color: "#fff",
    },
}));



function SelectDropDown(props) {
    const classes = useStyles();

    const handleChange = (e) => {
        props.parentHandleChange(e.target.value, props.localSettings)
    }

    return (
        <div>
            <Grid item xs={12}>
                <FormControl className={classes.formControl}>
                    <InputLabel
                        shrink
                        id="demo-simple-select-placeholder-label-label"
                    >
                        {props.inputLable}
                    </InputLabel>
                    <Select
                        labelId="demo-simple-select-placeholder-label-label"
                        id="demo-simple-select-placeholder-label"
                        value={props.defaultValue}
                        onChange={handleChange}
                        displayEmpty
                        className={classes.selectEmpty}
                    >
                        {props.data.map((item) => (
                            <MenuItem value={item[props.value]}>
                                {item[props.label]}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Grid>
        </div>
    );
}

export default SelectDropDown;