const locale = {
    applications: 'Applications',
    administration: 'Administration',
    dispatchCallType: "Dispatch Call Type",
    violationCharges: "Violation/Charges",
    fileUploadAndPreview: "File Upload & Preview",
    userNewTest: "User New Test",
    nfirs: "NFIRS Unit Type",
    shift: "Shift",
    team: "Team",
    settings: "Settings",
    counties: "Manage Address/Intersection Points",
    incomingCallAndMessage: "Incoming Calls and Message",
    currentSelectedCall: "Current Selected Call",
    recentCallAndMessage: "Recent Call and Messages",
    group: "Group",
    showAllCalls: "Show All Calls",
    towerInfo: "Tower Info",
    time: "Time",
    lat: "Lat",
    long: "Long",
    group1: "Group 1",
    group2: "Group 2",
    group3: "Group 3",
    noCallFound: "No call found",
    makeGroup: "Make Group",
    hangUp911: "911 HANG UP/DROPPED CALL",
    acccidentCall: "ACCIDENT CALL",
    rebidCall: "REBID CALL",
    sendCall: "SEND CALL",
    archiveCalls: "Archive Calls",
    domestic: "DOMESTIC",
    assault: "ASSAULT/FIGHT IN PROGRESS",
    shotsHeard: "SHOTS HEARD/FIRED",
    burglary: "BURGLARY/ROBBERY IN PROGRESS",
    structureFire: "STRUCTURE FIRE",
    visibleSmoke: "VISIBLE SMOKE",
    vehicleFire: "VEHICLE FIRE",
    grassFire: "GRASS/BRUSH FIRE",
    medicalCall: "MEDICAL CALL",
    newCall: "New Call",
    pdZone: "PD Zone",
    loading: "Loading...",
    seenAll: "Yay! You have seen it all",
    confidence: "Confidence",
    radius: "Radius",
    autoRefresh: "Auto Refresh",
    rg: "RG",
    geoRadius: "Geo Radius ( in mile )",
    timeInMin: "Time ( in min.)",
    mVAInjury: "MVA Injury",
    mVAInjuryProcessHere: "MVA Injury process here",
    saveChanges: "Save changes",
    mVAUNKInjury: "MVA UNK Injury",
    mVAUNKInjuryProcessHere: "MVA UNK Injury process here...",
    mVANOInjury: "MVA NO Injury",
    mVANOInjuryProcessHere: "MVA NO Injury process here...",
    rawData: "Raw Data",
    selectedCall: "Selected Call",
    rapidSoS: "Rapid SoS",
    medicAlertTest: "MedicAlert Test",
    contemplativeReptile: "Contemplative Reptile",
    locationHistory: "Location History",
    estimatedAddress: "Estimated address:",
    indoorLocation: "Indoor Location",
    uncertaintyRadius: "Uncertainty Radius:",
    callingPhone: "Calling Phone:",
    receivedOn: "Received On:",
    location: "Location:",
    callerName: "Caller Name",
    callerNumber: "Caller Number",
    callerLocation: "Caller Location",
    locationInfo: "Location Info",
    groupCalls: "Group Calls:",
    suppliment: "Suppliment",
    create: "Create",
    new: "New",
    next: "Next",
    createCall: "Create Call",
    closest5addresses: "Closest 5 addresses",
    createUser: "Create User",
    agencyAdmin: "Agency Admin",
    superAdmin: "Super Admin",
    dispatchChat: "Dispatch Chat",
    selectAgencies: "Select Agencies",
    photo: "Photo",
    agencies: "Agencies",
    isEnabled: "IsEnabled",
    superAdminUsers: "Super Admin Users",
    regularUsers: "Regular Users",
    userAccountType: "User Account Type",
    fulltTime: "Full Time",
    partTime: "Part Time",
    unitAccount: "Unit Account",
    addNewSeverConfiguration: "Add Server Configuration",
    initialCode: "Initial Code",
    isDefault: "Is Default",
    adminSetting: "Admin Setting",
    mobileDispatch: "Mobile Dispatch",
    quikTip: "QuikTip",
    watches: "Watches",
    relativityAdmin: "Relativity Admin",
    call911: "911 Call",
    dispatch: "Dispatch",
    mugshot: "Mugshot",
    userAccessRights: "User Access Rights",
    deleteMsgCallViolation: "Are you sure you want to delete Call Violation ?",
    deleteMsgCallTypes: "Are you sure you want to delete Call Types ?",
    deleteMsgCallCategory: "Are you sure you want to delete Call Category ?",
    deleteMsgCallResponse: "Are you sure you want to delete Call Response ?",
    deleteMsgDevice_Licenses: "Are you sure you want to delete Device Licenses ?",
    deleteMsgProbableCause: "Are you sure you want to delete Probable Cause ?",
    deleteMsgommunicationLog: "Are you sure you want to delete Communication Log ?",
    deleteRecord: "Are you sure you want to delete this Record ?",
    violationId: "Violation ID",
    statuteTitle: "Statute Title",
    statutedesc: "Statute Desc",
    offenseDescription: "Offense Description",
    localCode: "Local Code",
    ncicId: "NcicID",
    violationExample: "Violation Example",
    violationType: "Violation Type",
    nibrsId: "NibrsID",
    classification: "Classification",
    isCriminal: "Is Criminal",
    isWarrant: "Is Warrant",
    isTraffic: "Is Traffic",
    shortDescription: "Short Description",
    isDWI1: "Is DWI1",
    isDWI2: "Is DWI2",
    isDWI3: "Is DWI3",
    lastValidYear: "Last Valid Year",
    dateLastValid: "Date Last Valid",
    arCriminalTrafficID: "AR Criminal Traffic ID",
    ndexSubmitType: "NDEX Submit Type",
    dnaRequired: "DNA Required",
    bondAmountDefault: "Bond Amount Default",
    complianceReview: "Compliance Review",
    sendToDOG: "Send To DOG",
    noMaximumFine: "No Maximum Fine",
    isSeatbeltDeductible: "Is Seatbelt Deductible",
    isInStateList: "Is In State List",
    isCitationViol: "Is Citation Viol",
    isLocalOrdinance: "Is Local Ordinance",
    isDomesticViolence: "Is Domestic Violence",
    acFlag: "AC Flag",
    cleryCat: "Clery Cat",
    mustAppear: "Must Appear",
    fingerPrint: "Finger Print",
    minSentence: "Min Sentence",
    maxSentence: "Max Sentence",
    sentenceType: "Sentence Type",
    defaultClassificationID: "Default Classification ID",
    life: "Life",
    minFines: "Min Fines",
    maxFines: "Max Fines",
    callType: "Call Type",
    deviceLicense: "Device License",
    callTypeID: "Call Type ID",
    categoryWarning: "Category should not be empty",
    callCategoryID: "Call Category ID",
    code: "Code",
    id: "ID",
    codeDescription: "Code Description",
    codeProQA: "Code Pro QA",
    codeWestNet: "Code West Net",
    callTypeName: "Call Type Name",
    abbrevaition: "Abbrevaition",
    chat: "Chat",
    currentMap: "Current Map",
    replayHistory: "Replay History",
    fileUploadPreview: "File Upload/Preview",
    violations: "Violations",
    callCategory: "Call Category",
    UserSettingsMsg: "User settings saved successfully.",
    callCategories: "Call Categories",
    emailValidationMsg: "Please enter a valid email",
    minLengthMsg: "Min character length is 2",
    phoneValidMsg: "This is not a valid phone number",
    personalInformation: "Personal Information",
    profilePicture: "Profile Picture",
    rpsAuthentication: "RPS Authentication",
    userName: "UserName",
    verify: "Verify",
    defaultLocation: "Default Location",
    personalDetails: "Personal Details :",
    mapZoomLevel: "Map zoom level",
    defaultLocationType: "Default location type",
    selectLocation: "Select location",
    authenticator: "Authenticator",
    text: "Text",
    agencyAdminSetting: "Agency Admin Setting",
    enterRpsCreds: "Enter your RPS Credentials!",
    defaultApplication: "Default Application",
    searchFeature: "Search Feature",
    broadcastMessages: "Broadcast Messages",
    mySchedule: "My Schedule",
    responseCode: "Response Code",
    priority: "Priority",
    callResponseCode: "Call Response Code",
    dispatchCallIcons: "Dispatch Call Icons",
    violationsCharges: "Violations/Charges",
    dispatchCallTypes: "Dispatch Call Types",
    callResponse: "Call Response",
    serverConfiguration: "Server Configuration",
    rpsCode: "RPS Code",
    availability: "Availability",
    deviceLicenses: "Device license",
    probableCause: "Probable Cause",
    addCommunicationLog: "Add Communication Logs",
    deviceId: "Device Id",
    deviceDescription: "Device Description",
    srno: "Sr no.",
    disableUserMsg: "Are you sure you want to disable this user?",
    deleteUserMsg: "Are you sure you want to delete this user?",
    enableUserMsg: "Are you sure you want to enable this user?",
    selectDefaultAgency: "Select Default Agency",
    defaultMFAType: "Default MFA Type",
    defaultAgency: "Default Agency",
    selectCallTypeImageMsg: "Please select a call type image",
    deviceLimitReachedMsg: "Cannot add device license. You have reached the maximum limit of allocated licences.",
    deviceAlreadyExistingMsg: "Device already existing cannot add this device",
    oneAgencySelectMsg: "Atleast one agency must be selected.",
    departmentValidationMsg: "Department must be selected.",
    cannotSelectMoreThanOneAgencyMSg: "You cannot select more than one agency.",
    systemAgencySelectMsg: "Please select system agency also.",
    oneAccessRightsSelectMsg: "Atleast one access right needs to be selected.",
    userLimitReachedUpdateMsg: "Cannot update User. You have reached the maximum limit of allocated Users to this agency.",
    userLimitReachedMsg: "Cannot add User. You have reached the maximum limit of allocated Users.",
    systemAgencySelectedMsg: "System agency already selected.",
    pleaseChooseLocationMsg: "Please choose a location to continue.",
    filterByAgency: "Filter by agency",
    addCitation: "Add Citation",
    citiationEndMsg: "No more Citations",
    citation: "Citation",
    citationDeleteMsg: "Are you sure you want to delete this citation ?",
    citationViolationDeleteMsg: "Are you sure you want to delete this citation violation ?",
    citationPersonDeleteMsg: "Are you sure you want to delete this citation person ?",
    citationVehicleDeleteMsg: "Are you sure you want to delete this citation vehicle ?",
    teams: "Teams",
    emailConfiguration: "Email Configuration",
    host: "Host",
    port: "Port",
    importUsers: "Import Users",
    import: "Import",
    addMultipleUsers: "Add Multiple User",
    importedUserLogs: "Imported User Logs",
    fieldsWithIssues: "Fields With Issues",
    twitterconfiguration: "Twitter Configuration",
    baseUrl: "Base Url",
    responseType: "Response Type",
    clientID: "Client ID",
    redirectUrl: "Redirect Url",
    scope: "Scope",
    codeChallenge: "Code Challenge",
    codeChallengeMethod: "Code Challenge Method",
    grantType: "Grant Type",
    grantTypeForRefreshToken: "Grant Type For Refresh Token",
    authUrl: "Auth Url",
    maintainTipTypes: "Maintain Tip Types",
    addCallViolation: "Add Violation",
    addCallType: "Add Call Type",
    addCallCategory: "Add Call Category",
    addCallResponse: "Add Call Response",
    addNewDevice: "Add New Device",
    addNewCause: "Add New Probable Cause",
    viewTip: "View Tip",
    viewTips: "View Tips",
    notification: "Notification",
    ncicUserName: "NCIC UserName",
    ncicPassword: "NCIC Password",
    terminalId: "Terminal ID",
    ncicAuthentication: "NCIC Authentication",
    archivechatHistories: "Archive Chat Histories",
    shortCode: "Short Code",
    locationRequirement: "Location Requirement",
    addNonCallStatus: "Add Non Call Status",
    nonCallStatus: "Non Call Status",
    dispatchCitation: "Dispatch Citation",
    trafficStop: "Traffic Stop",
    personSearch: "Person Search",
    vehicleSearch: "Vehicle Search",
    gunSearch: "Gun Search",
    articleSearch: "Article Search",
    boatSearch: "Boat Search",
    incidentReport: "Incident Report",
    accidentReport: "Accident Report",
    dispatchApp: "Dispatch App",
    dispatchWebsite: "Dispatch Website",
    securityCheck: "Security Check",
    transportCall: "Transport Call",
    medicalTransport: "Medical Transport",
    showAlert: "Show Alert",
    tokenRequired: "Token Required",
    otpDetails: "Otp Details :",
    ncicDetails: "NCIC Details :",
    permissionDetails: "Permission Details :",
    allowPermanantLogin: "Allow Permanent Login",
    receiveVideoCall: "Receive Video Call",
    showTwitterIcon: "Show Twitter Icon",
    receiveCallAlert: "Receive Call Alert",
    userSettings: "User Settings :",
    locationDetails: "Location Details",
    twitterAccountSettings: "Twitter Account Settings",
    zipPostCodeEx: "Zip+ (PostCodeEx)",
    street: "Street",
    street1: "Street 1",
    street2: "Street 2",
    street3: "Street 3",
    street4: "Street 4",
    intersectionPointDetail: "Intersection Point Detail",
    addIntersection: "Add Intersection",
    addressDetails: "Address Details",
    errorLog: "Error Log",
    addressNoPrefix: "Address No Prefix",
    streetNumberOrMileMarker: "Street Number Or Mile Marker",
    addressInfo: "Address Info",
    addrNoPrefix: "Addr No Prefix",
    numberSuffix: "Number Suffix",
    preMod: "Pre-Mod",
    direction: "Direction",
    preType: "Pre-Type",
    preSep: "Pre-Sep",
    streetNameFull: "Street Name Full",
    postMod: "Post Mod",
    streetDirOfTravel: "Street Dir of Travel",
    milePost: "Mile Post",
    site: "Site",
    subStie: "Sub Site",
    structure: "Structure",
    wing: "Wing",
    unitType: "Unit Type",
    unitId: "Unit ID",
    tags: "Tags",
    room: "Room",
    section: "Section",
    row: "Row",
    seat: "Seat",
    cityPostalComm: "City (PostalComm)",
    postalCommunity: "Postal Community",
    postalCode: "Postal Code",
    zipPostCode: "Zip (PostCode)",
    zipPlus: "Zip+",
    country: "Country",
    additionalLocationInfo: "Additional Location Info",
    nerisAdditionalAttributes: "NERIS Additional Attributes",
    locationMarker: "Location Marker",
    landmarkName: "Landmark Name",
    fips: "FIPS",
    nerisId: "NERIS ID",
    nibrsLocType: "NIBRS Loc Type",
    dateAdded: "Date Added",
    aliasFields: "Alias Fields",
    placeType: "Place Type",
    legacyStreetName: "Legacy Street Name",
    legacyStreetType: "Legacy Street Type",
    elevation: "Elevation",
    nearestCrossStreet: "Nearest Cross Street",
    distance: "Distance",
    nxtNearestCross: "Next Nearest Cross",
    incorporatedMuni: "Incorporated Muni",
    unincorporatedMuni: "Unincorporated Muni",
    neighborhoodCommunity: "Neighborhood Community",
    psap: "PSAP",
    esn: "ESN",
    msagCommunity: "MSAG Community",
    mapPage: "Map Page",
    addressLabel: "Address Label",
    placement: "Placement",
    nationalGrid: "National Grid",
    discrepancyAgencyID: "Discrepancy Agency Id",
    dateUpdated: "Date Updated",
    effectiveDate: "Effective Date",
    expirationDate: "Expiration Date",
    nguid: "NGUID",
    addCode: "Add Code",
    addressDataURL: "Address Data URL",
    zone: "Zone",
    policeZone: "Police Zone",
    fireZone: "Fire Zone",
    fireAutoAssist: "Fire Auto Assist",
    fireMutualAssist: "Fire Mutual Assist",
    wreckerServiceZone: "Wrecker Service Zone",
    emsZone: "EMS Zone",
    emsAutoAssist: "EMS Auto Assist",
    emsMutualAssist: "EMS Mutual Assist",
    addMasterAddress: "Add Master Address",
    masterAddress: "Master Address",
    masterAddressPointDetail: "Master Address Point Detail",
    county: "County",
    municipality: "Municipality",
    stateCode: "State Code",
    stateName: "State Name",
    countySelection: "County Selection",
    viewAddress: "Master Address Point",
    viewIntersection: "Intersection Point",
    importMasterAddress: "Import Master Address",
    importIntersectionPoint: "Import Intersection Point",
    viewImportSummaryLogs: "View Import/Summary Logs",
    loginOtpRequired: "Login OTP Setting",
    isOtpRequired: "OTP Required",
    addSupportTypeUnit: "Add Support Type Unit",
    agencyName: "Agency Name",
    agencyCode: "Agency Code",
    agencyType: "Agency Type",
    agencyEmail: "Agency Email",
    url: "URL",
    global: "Global",
    local: "Local",
    main: "Main",
    sub: "Sub",
    tenant: "Tenant",
    timeZone: "Time Zone",
    agencyConfigCode: "Agency Config Code",
    physicalAddress: "Physical Address",
    billingAddress: "Billing Address",
    billingCity: "Billing City",
    billingState: "Billing State",
    billingZip: "Billing Zip",
    billingCounty: "Billing County",
    oriNumber: "ORI Number",
    fdID: "FD ID",
    nemsisID: "NEMSIS ID",
    facilityCode: "Facility Code",
    mobileXAPIPath: "Mobile X API Path",
    liveStreamHost: "Live Stream Host",
    liveStreamParserType: "Live Stream Parser Type",
    liveStreamPort: "Live Stream Port",
    locationParserURL: "Location Parser URL",
    mugshotAgencyCode: "Mugshot Agency Code",
    mugshotAgencyName: "Mugshot Agency Name",
    mugshotURL: "Mugshot URL",
    mugshotAgencyID: "Mugshot Agency ID",
    agencyLicenses: "Agency Licenses",
    noOfUserLicenses: "No of User Licenses",
    noOfDeviceLicenses: "No of Device Licenses",
    internationalDateLineWest: "(GMT-12:00) International Date Line West",
    midwayIslandSamoa: "(GMT-11:00) Midway Island, Samoa",
    hawaii: "(GMT-10:00) Hawaii",
    alaska: "(GMT-09:00) Alaska",
    pacificTimeUSCanada: "(GMT-08:00) Pacific Time (US & Canada)",
    tijuanaBajaCalifornia: "(GMT-08:00) Tijuana, Baja California",
    arizona: "(GMT-07:00) Arizona",
    chihuahuaLaPazMazatlan: "(GMT-07:00) Chihuahua, La Paz, Mazatlan",
    mountainTimeUSCanada: "(GMT-07:00) Mountain Time (US & Canada)",
    centralAmerica: "(GMT-06:00) Central America",
    centralTimeUSCanada: "(GMT-06:00) Central Time (US & Canada)",
    bogotaLimaQuitoRioBranco: "(GMT-05:00) Bogota, Lima, Quito, Rio Branco",
    easternTimeUSCanada: "(GMT-05:00) Eastern Time (US & Canada)",
    indianaEast: "(GMT-05:00) Indiana (East)",
    atlanticTimeCanada: "(GMT-04:00) Atlantic Time (Canada)",
    caracasLaPaz: "(GMT-04:00) Caracas, La Paz",
    manaus: "(GMT-04:00) Manaus",
    santiago: "(GMT-04:00) Santiago",
    newfoundland: "(GMT-03:30) Newfoundland",
    brasilia: "(GMT-03:00) Brasilia",
    buenosAiresGeorgetown: "(GMT-03:00) Buenos Aires, Georgetown",
    greenland: "(GMT-03:00) Greenland",
    montevideo: "(GMT-03:00) Montevideo",
    mid_Atlantic: "(GMT-02:00) Mid-Atlantic",
    capeVerdeIs: "(GMT-01:00) Cape Verde Is.",
    azores: "(GMT-01:00) Azores",
    casablancaMonroviaReykjavik: "(GMT+00:00) Casablanca, Monrovia, Reykjavik",
    greenwichMeanTimeDublinEdinburghLisbonLondon: "(GMT+00:00) Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London",
    amsterdamBerlinBernRomeStockholmVienna: "(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna",
    belgradeBratislavaBudapestLjubljanaPrague: "(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague",
    brusselsCopenhagenMadridParis: "(GMT+01:00) Brussels, Copenhagen, Madrid, Paris",
    sarajevoSkopjeWarsawZagreb: "(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb",
    westentralAfrica: "(GMT+01:00) West Central Africa",
    amman: "(GMT+02:00) Amman",
    athensBucharestIstanbul: "(GMT+02:00) Athens, Bucharest, Istanbul",
    beirut: "(GMT+02:00) Beirut",
    cairo: "(GMT+02:00) Cairo",
    hararePretoria: "(GMT+02:00) Harare, Pretoria",
    helsinkiKyivRigaSofiaTallinnVilnius: "(GMT+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius",
    jerusalem: "(GMT+02:00) Jerusalem",
    minsk: "(GMT+02:00) Minsk",
    windhoek: "(GMT+02:00) Windhoek",
    kuwaitRiyadhaghdad: "(GMT+03:00) Kuwait, Riyadh, Baghdad",
    moscowStPetersburgVolgograd: "(GMT+03:00) Moscow, St. Petersburg, Volgograd",
    nairobi: "(GMT+03:00) Nairobi",
    tbilisi: "(GMT+03:00) Tbilisi",
    tehran: "(GMT+03:30) Tehran",
    abuDhabiMuscat: "(GMT+04:00) Abu Dhabi, Muscat",
    baku: "(GMT+04:00) Baku",
    yerevan: "(GMT+04:00) Yerevan",
    kabul: "(GMT+04:30) Kabul",
    yekaterinburg: "(GMT+05:00) Yekaterinburg",
    islamabadKarachiTashkent: "(GMT+05:00) Islamabad, Karachi, Tashkent",
    sriJayawardenapura: "(GMT+05:30) Sri Jayawardenapura",
    chennaiKolkataMumbaiNewDelhi: "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi",
    kathmandu: "(GMT+05:45) Kathmandu",
    almatyNovosibirsk: "(GMT+06:00) Almaty, Novosibirsk",
    astanaDhaka: "(GMT+06:00) Astana, Dhaka",
    yangonRangoon: "(GMT+06:30) Yangon (Rangoon)",
    bangkokHanoiJakarta: "(GMT+07:00) Bangkok, Hanoi, Jakarta",
    krasnoyarsk: "(GMT+07:00) Krasnoyarsk",
    beijingChongqingHongKongUrumqi: "(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi",
    kualaLumpurSingapore: "(GMT+08:00) Kuala Lumpur, Singapore",
    irkutskUlaanBataar: "(GMT+08:00) Irkutsk, Ulaan Bataar",
    perth: "(GMT+08:00) Perth",
    taipei: "(GMT+08:00) Taipei",
    osakaSapporoTokyo: "(GMT+09:00) Osaka, Sapporo, Tokyo",
    seoul: "(GMT+09:00) Seoul",
    yakutsk: "(GMT+09:00) Yakutsk",
    adelaide: "(GMT+09:30) Adelaide",
    darwin: "(GMT+09:30) Darwin",
    brisbane: "(GMT+10:00) Brisbane",
    canberraMelbourneSydney: "(GMT+10:00) Canberra, Melbourne, Sydney",
    hobart: "(GMT+10:00) Hobart",
    guamPortMoresby: "(GMT+10:00) Guam, Port Moresby",
    vladivostok: "(GMT+10:00) Vladivostok",
    magadanSolomonIsNewCaledonia: "(GMT+11:00) Magadan, Solomon Is., New Caledonia",
    aucklandWellington: "(GMT+12:00) Auckland, Wellington",
    fijiKamchataMarshallIs: "(GMT+12:00) Fiji, Kamchatka, Marshall Is.",
    nukualofa: "(GMT+13:00) Nuku'alofa",
    connectionString: "Agency Connection String",
    ncicApiUrl: "NCIC Api Url",
    deviceLicenseRequired: "Device License Required",
    otp: "OTP",
    userStatus: "Show user status in header",
    selectCallCategories: "Select Call Categories",
    socketListenerUrl: "Socket Listener Url",
    socketApiUrl: "Socket Api Url",
    none: "None",
    dispatchDetails: "Dispatch Details",
    agencyDetails: "Agency Details",
    physicalAddressDetails: "Physical Address Details",
    billingAddressDetails: "Billing Address Details",
    codeIDDetails: "Code/ID Details",
    mugshotDetails: "Mugshot Details",
    urlDetails: "URL Details",
    deleteDepartment: "Are you sure you want to remove this department ?",
    shiftType: "Shift Type",
    addDepartment: "Add Department",
    supportedUnitType: "Supported Unit Type",
    onDuty: "On-Duty",
    offDuty: "Off Duty",
    nibrsOri: "NIBRS ORI",
    ncicOri: "NCIC ORI",
    oriOtherJurisdition: "ORI-Other Jurisdiction",
    addNewAgency: "Add New Agency",
    addNewPerson: "Add New Person",
    parentAgency: "Parent Agency",
    startDate: "Start Date",
    endDate: "End Date",
    weeklyOffAfterNoOfShift: "Weekly Off After No of Shift",
    noOfOffShift: "No of off Shifts",
    noOfTeamsInEachShift: "No of Teams in each shift",
    shiftAllocation: "Shift Allocation",
    shiftAllocationSchedule: "Shift Allocation Schedule",
    backToAllocation: "Back to schedule allocation",
    noBreak: "No Break",
    scheduleType: "Schedule Type",
    schedule: "Schedule",
    to: "To",
    shiftUsers: "Shift Users",
    savePublish: "Save & Publish",
    publishMessage: "There is no data for publishing the schedule.",
    reScheduleConfirmation: "IF you click ok your data will lost. Are you sure you want to reschedule ?",
    userExist: "This user is already exist in same day, same shift. Please select different user or different shift.",
    deleteScheduleText: "Are you sure you want to remove this schedule ?",
    newUser: "Add New User",
    addShiftAllocation: "Add Shift Allocation",
    startTime: "Start Time",
    endTime: "End Time",
    shiftName: "Shift Name",
    shifts: "Shifts",
    shiftDuration: "Shift Duration",
    dateValidationMsg: "End date must be greater than start date",
    dateAlreadyExistMsg: "Shift hours exceeds shift limit.",
    deleteShiftMsg: "Are you sure you want to remove the shift ?",
    color: "Color",
    shiftDetails: "Shift Details",
    shiftCode: "Shift Code",
    selectTime: "Select time",
    addShift: "Add Shift",
    classOfService: "Class of Service",
    addNewCode: "Add New Code",
    codeName: "Code Name",
    icon: "Icon",
    vehicle: "Vehicle",
    modelYear: "Model Year",
    individualEntry: "Individual Entry",
    preDir: "Pre Dir",
    postDir: "Post Dir",
    building: "Building",
    dlNumber: "DLNumber",
    sameAsOwner: "Same As Owner",
    st: "ST",
    tag: "Tag",
    year: "Year",
    make: "Make",
    model: "Model",
    personChangeHistory: "Person Change History",
    personInvolmentTagsChangeHistory: "Person Involvement Tags History",
    vehicleChangeHistory: "Vehicle Change History",
    globalPersonSearch: "Global Person Search",
    addrNumber: "Addr Number",
    numSuffix: "Num Suffix",
    preDirection: "Pre Direction",
    streetName: "Street Name",
    streetType: "Street Type",
    postDirection: "Post Direction",
    buildingNbr: "Building Nbr",
    unitID: "Unit ID",
    buildingName: "Building Name",
    floor: "Floor",
    cityName: "City Name",
    zip: "Zip",
    zipPlus4: "Zip Plus 4",
    dateOfBirth: "Date Of Birth",
    age: "Age",
    race: "Race",
    sex: "Sex",
    ethnicity: "Ethnicity",
    height: "Height",
    hair: "Hair",
    eyes: "Eyes",
    phoneType: "Phone Type",
    idType: "Id Type",
    idNumber: "ID Number",
    emailType: "Email Type",
    latitude: "Latitude",
    longitude: "Longitude",
    statuteCode: "Statute Code",
    statuteCitation: "Statute Citation",
    attempted: "Attempted",
    logType: "Log Type",
    completed: "Completed",
    misdemeanor: "Misdemeanor",
    other: "Other",
    felony: "Felony",
    incidentViolationDetails: "Incident Violation Details",
    offenseInformation: "Offense Information",
    aboutMe: "About Me",
    generalInformation: "General Information",
    birthday: "Birthday",
    locations: "Locations",
    involvementTags: "Involvement Tags",
    attemptedCompleted: "Attempted/Completed",
    counts: "Count(s)",
    misdFelony: "Misd/Felony",
    ownersInformation: "Owner's Information",
    driversInformation: "Driver's Information",
    involvement: "Involvement",
    notes: "Notes",
    suffix: "Suffix",
    residentialAddress: "Residential Address",
    residenceAddrNumber: "Residence Addr Number",
    residenceNumSuffix: "Residence Num Suffix",
    residencePreDirection: "Residence Pre Direction",
    residenceStreetName: "Residence Street Name",
    residenceStreetType: "Residence Street Type",
    residencePostDirection: "Residence Post Direction",
    residenceBuildingNbr: "Residence Building Nbr",
    residenceUnitType: "Residence Unit Type",
    residenceUnitID: "Residence Unit ID",
    residenceBuildingName: "Residence Building Name",
    residenceFloor: "Residence Floor",
    residenceCityName: "Residence City Name",
    residenceState: "Residence State",
    residenceZIP: "Residence ZIP",
    residenceZIPPlus4: "Residence ZIP Plus 4",
    officeAddress: "Office Address",
    officeAddrNumber: "Office Addr Number",
    officeNumSuffix: "Office Num Suffix",
    officePreDirection: "Office Pre Direction",
    officeStreetName: "Office Street Name",
    officeStreetType: "Office Street Type",
    officePostDirection: "Office Post Direction",
    officeBuildingNbr: "Office Building Nbr",
    officeUnitType: "Office Unit Type",
    officeUnitID: "Office Unit ID",
    officeBuildingName: "Office Building Name",
    officeFloor: "Office Floor",
    officeCityName: "Office City Name",
    officeState: "Office State",
    officeZIP: "Office ZIP",
    officeZIPPlus4: "Office ZIP Plus 4",
    number: "Number",
    personGlobalSearch: "Person Global Search",
    involvementNotes: "Involvement / Notes",
    complainant: "COMPLAINANT",
    caller: "CALLER",
    victim: "VICTIM",
    witness: "WITNESS",
    suspect: "SUSPECT",
    arrestee: "ARRESTEE",
    associated: "ASSOCIATED",
    driver: "DRIVER",
    passenger: "PASSENGER",
    cyclist: "CYCLIST",
    pedestrian: "PEDESTRIAN",
    emailAddress: "Email Address",
    changeEmail: "Change email",
    emailConfirm: "Your email has been changed successfully. You will need to login again to continue.",
    currentPassword: "Current Password",
    password: "Password",
    confirmPassword: "Confirm Password",
    changePassword: "Change password",
    passwordConfirm: "Your password has been changed successfully. You will need to login again to continue.",
    recentPersonsUsed: "Recent Persons Used",
    recentAddressesUsed: "Recent Addresses Used",
    recentVehicleUsed: "Recent Vehicle Used",
    tokenExpireMsg: "Your token has expired and you will need to Sign in again. You will be redirected to the login page in 5 seconds.",
    invalidSessionMsg: "You have logged in from another device, so this session is no longer valid.",
    heightFeet: "{{feet}} ft",
    heightFeetInches: "{{feet}} ft {{inches}} in",
    heightInches: "{{inches}} in",
    invalidHeightEntry: "Invalid height entry",
    redirecting: "Redirecting in 5 seconds",
    goto: "Go to",
    signIn: "Sign in",
    personDeleteMsg: "Are you sure you want to delete this person ?",
    citationLocation: "Citation Location",
    citationInformation: "Citation Information",
    pleaseFillNote: "Please fill the note",
    pleaseSelectFile: "Please select file",
    communicationWith: "Communication With",
    communicationNote: "Communication Note",
    involvementLossType: "Involvement (LossType)",
    quantity: "Quantity",
    weight: "Weight",
    brand: "Brand/Make/Manufacturer",
    caliber: "Caliber/Gauge",
    estimatedValue: "Estimated Value",
    freeForm: "Free-Form",
    serialNumber: "Serial Number",
    ownedAppliedNumber: "Owner Applied Number",
    ncicRefNumber: "NCIC Ref Number",
    recovered: "Recovered",
    recoveredDate: "Recovered Date",
    recoveryLocation: "Recovery Location",
    recoveryCondition: "Recovery Condition",
    recoveredValue: "Recovered Value",
    pleaseSelectTitle: "Please Select Title",
    changeFrom: "Change From",
    changedBy: "Changed By",
    ncicNumber: "NCIC Number",
    ncicStatus: "NCIC Status",
    recoveryInformation: "Recovery Information",
    recoveredBy: "Recovered By",
    ncicInformation: "NCIC Information",
    owner: "Owner",
    automatic: "Automatic",
    unknown: "Unknown",
    drugType: "Drug Type",
    drugName: "Drug Name",
    measure: "Measure",
    drugQuantity: "Estimated Drug Quantity",
    pharmacy: "Pharmacy",
    prescriptionNumber: "Prescription Number",
    testedVia: "Tested Via",
    submittedToLab: "Submitted To Lab",
    evidence: "Evidence",
    evidenceNumber: "Evidence Number",
    evidenceStatus: "Evidence Status",
    agencyExhibitNumber: "Agency/Case Exhibit Number",
    evidenceLocation: "Evidence Location",
    acquiredDateTime: "Acquired Date Time",
    acquiredBy: "Acquired By",
    acquiredLocation: "Acquired Location",
    nextAction: "Next Action",
    markLostFound: "Mark as Lost/Found Item",
    permanent: "Permanent",
    reviewDate: "Review Date",
    dispositionReason: "Disposition Reason",
    reconciliationHistory: "Reconciliation History",
    custodyHistory: "Custody History",
    labResults: "Lab Results",
    evidenceInformation: "Evidence Information",
    organizationName: "Organization Name",
    organizationType: "Organization Type",
    primaryLocation: "PRIMARY LOCATION",
    primaryContact: "PRIMARY CONTACT",
    scanDriversLicense: "Scan Drivers License",
    searchbyName: "Search by Name",
    addressValidated: "Address Validated",
    unableToValidate: "Unable To Validate",
    offense: "Offense",
    dob: "DOB",
    hgt: "Hgt",
    wgt: "Wgt",
    file: "File",
    note: "Note",
    doesNotSupportMsg: "Browser doesn't support speech recognition",
    searchForOwner: "Search For Owner",
    searchForDriver: "Search For Driver",
    searchForDriverAddress: "Search Driver Address",
    searchForOwnerAddress: "Search Owner Address",
    Add: "Add",
    add: "Add",
    update: "Update",
    contact: "Contact",
    addNewContact: "Add New Contact",
    title: "Title",
    firstName: "First Name",
    middleName: "Middle Name",
    lastName: "Last Name",
    city: "City",
    state: "State",
    phone: "Phone",
    contactsDeleteMsg: "Are you sure you want to delete Contacts ?",
    errorLogs: "Error Logs",
    user: "User",
    relativityAPI: "Relativity API",
    incidentAPI: "Incident API",
    dispatchUI: "Dispatch UI",
    adminUI: "Admin UI",
    all: "ALL",
    resolved: "Resolved",
    unResolved: "Unresolved",
    resolveError: "Error Resolved",
    confirmMsgErrorResolved: "Are you sure you want to mark this error as resolved?",
    errorDetails: "Error Details",
    method: "Method",
    timeStamp: "Timestamp",
    source: "Source",
    stackTrace: "StackTrace",
    resolvedBy: "Resolved By",
    resolvedAt: "Resolved At",
    resolve: "Resolve",
    dateWarningMsg: "Ensure To date is not greater than From date.",
    dispatchAPI: "Dispatch API",
    socketAPI: "Socket API",
    fileUpload: "File Upload",
    noFileSelectMsg: "No File Selected",
    upload: "Upload",
    fileSelectMsg: "1 File is Selected",
    filesSelectedMsg: "Files Selected",
    uploadedFiles: "Uploaded Files",
    fileViewer: "File Viewer",
    incidentInfo: "Incident Info",
    incidentLocation: "Incident Location",
    incidentFile: "Incident File",
    close: "Close",
    incidentNotes: "Incident Notes",
    incidentPerson: "Incident Person",
    vehicleEntry: "Vehicle Entry",
    searchForVehicle: "Search For Vehicle",
    incidentViolation: "Incident Violation",
    person: "Person",
    ownerInformation: "Owner Information",
    driverInformation: "Driver Information",
    incidentInformation: "Incident Information",
    incidentPDF: "Incident PDF",
    narrative: "Narrative",
    addNewVehicle: "Add New Vehicle",
    incidents: "Incidents",
    pleaseSelectViolation: "Please Select Violation",
    pleaseSelectPerson: "Please Select Person",
    pleaseSelectViolation: "Please Select Violation",
    pleaseSelectPerson: "Please Select Person",
    edit: "Edit",
    individualType: "Individual Type",
    businessName: "Business Name",
    contactName: "Contact Name",
    ext: "Ext",
    placeName: "Place name",
    address: "Address",
    reportedBy: "Reported By",
    zipCode: "Zip Code",
    violation: "Violation",
    reportedDate: "Reported Date",
    persons: "Persons",
    vehicles: "Vehicles",
    organization: "Organization",
    property: "Property",
    narratives: "Narratives",
    files: "Files",
    communicationLogs: "Communication Logs",
    security: "Security",
    locationPlaceOcurred: "Location/Place Ocurred",
    nfirsEquipmentType: "NFIRS Equipment Type",
    addNFIRS: "Add Nfirs",
    type: "Type",
    date: "Date",
    gpsLocation: "GPS Location",
    phoneNumber: "Phone Number",
    email: "Email",
    comments: "Comments",
    details: "Details",
    delete: "Delete",
    customer: "Customer",
    status: "Status",
    dateValidation: "Start date must less than end date!",
    dateTime: "Date Time",
    from: "From",
    tipComment: "Tip Comment",
    commentType: "Comment Type",
    answered: "Answered",
    quikTipDetails: "QuikTip Details",
    tipTypes: "Tip Types",
    addTipType: "Add Tip Type",
    notificationCenter: "Notification Center",
    sendMessage: "Send Message",
    tipType: "Tip Type",
    description: "Description",
    deleteMsg: "Are you sure you want to delete ?",
    sender: "Sender",
    pushedMessage: "Pushed Message",
    createDateTime: "Created Date Time",
    message: "Message",
    tipDetail: "Tip Details",
    attachments: "Attachments",
    tipComments: "Tip Comments",
    comment: "Comment",
    addInternalComment: "Add Internal Comment",
    addPublicComment: "Add Public Comment",
    addQuestion: "Add Question",
    messageMustHavevalue: "Message Must Have Value!",
    commentShouldNotBeEmptyOrNull: "Comment Should Not Be Empty!",
    changeStatus: "Change Status",
    name: "Name",
    users: "Users",
    teamDeleteMsg: "Are you sure you want to delete team ?",
    confirmationmsg: "This user is present in other team. Are you sure you want this user ?",
    department: "Department",
    departmentRequired: "You must choose department...",
    teamLimit: "No of teams in this department is Exceeds..",
    teamlead: "Team Leader",
    colorCode: "Color Code",
    cancel: "Cancel",
    addTeam: "Add Team",
    twitterAuthorize: "Twitter Authorize",
    unit: "Units",
    unitName: "Unit Name",
    agency: "Agency",
    branch: "Branch",
    station: "Station",
    manufacturer: "Manufacturer",
    style: "Style",
    "color1/color2": "Color 1 / Color 2",
    category: "Category",
    emsLevel: "EMS Level",
    unitPositions: "Unit Positions",
    oilType: "Oil Type",
    pmcsSchedule: "PMCS Schedule",
    nextServiceDate: "Next Service Date",
    nextServiceMiles: "Next Service Miles",
    latestMileage: "Latest Mileage",
    estimatedEndofLife: "Estimated End of Life",
    inServiceDate: "In Service Date",
    tireSize: "Tire Size",
    fueltype: "Fuel Type",
    vinNumber: "VIN Number",
    tagState: "Tag State",
    tagNumber: " Tag Number",
    nextMaintenance: "Next Maintenance",
    role: "Role",
    capabilityTags: "Capability Tags",
    issuedAssignedTo: "Issued/Assigned To",
    confirm: "Confirm",
    unitDeleteMsg: "Are you sure you want to delete Units ?",
    no: "No",
    yes: "Yes",
    save: "Save",
    back: "Back",
    selectFile: "Select File",
    preview: "Preview",
    policecar: "Police Car",
    fireTruck: "Fire Truck",
    ems: "EMS",
    unitType: "Unit Type",
    addRole: "Add Role",
    ncic: "ACIC/NCIC",
    neim: "NEIM",
    action: "Action",
    pleaseSelectImage: "Please select image",
    addUnits: "Add Unit",
    userAudit: "User Audit",
    search: "Search",
    appName: "Application Name",
    activity: "Activity",
    activityDateTime: "Activity date time",
    incident: "Incident",
    viewDetails: "View Details",
    intersectionErrorLogs: "Intersection Error Logs",
    importLogsIntersectionPoint: "Import Logs - Intersection Point",
    filePath: "File Path",
    fileName: "File Name",
    updatedCount: "Updated Count",
    insertedCount: "Inserted Count",
    matchCount: "Match Count",
    failedCount: "Failed Count",
    totalCount: "Total Count",
    viewImportLogs: "View Import Logs",
    masterAddressList: "Master Address List",
    masterIntersectionList: "Master Intersection List",
    municipalityType: "Municipality Type",
    addressNumber: "Address Number",
    addressNumberComp: "Address Number Comp",
    addressBuilding: "Address Building",
    legacyAddress: "Legacy Address",
    localName: "Local Name",
    nearestXStDist: "Nearest Distance",
    secondNearestXStDist: "SecondNearest Distance",
    masterAddressErrorLogs: "Master Address Error Logs",
    importLogsMasterAddressPoint: "Import Logs - Master Address Point",
    importDate: "Import Date",
    deleteWarning: "Are you sure you want to delete?",
    others: "Others",
    soundAlertSetting: "Sound Alert Setting",
    deleteShiftText: "Are you sure you want to remove the shift?",
    addNumber: "Add Number",
    deletethisVehicleMsg: "Are you sure you want to delete this vehicle ?",
    areYouSureYouWantDelete: "Are You Sure You Want Delete",
    selectFiles: "Select Files",
    noFileSelectedMsg: "No file is selected.",
    fileIsSelected: "1 file is selected.",
    accidentalCall: "Accidental Call",
    callBackDeadCall: "Call Back Dead Call",
    callBackNoAnswer: "Call Back No Answer",
    nonAccidentalCallNeedOfficer: "Non Accidental Call Need Officer",
    callForService: "Call For Service - Initial Entry From 911",
    showRectangle: "Show Rectangle",
    archiveChatOlderThan: "Archive chat older than",
    permanentLogin: "Permanent Login",
    userAgencyDetails: "User Agency Details:",
    clear: "Clear",
    lastLoginDate: "Last Login Date",
    objectId: "ObjectId",
    subAddress: "SubAddress",
    selectAll: "Select All",
    deleteDepartmentMsg: "Are you sure you want to remove this department?",
    available: "Available",
    unAvailable: "UnAvailable",
    where: "Where",
    downloadTemplate: "Download Template",
    nibrsCode: "Nibrs Code",
    addNibrsCode: "Add Nibrs Code",
    stateViolation: "State Violation",
    violationTypes: "Violation Types",
    linkedClassification: "Linked Classification",
    defaultClassification: "Default Classification",
    jailTypes: "Jail Types",
    fingerPrintRequired: "FingerPrint Required",
    citationViolation: "Citation Violation",
    criminalViolation: "Criminal Violation",
    warrentViolation: "Warrent Violation",
    trafficViolation: "Traffic Violation",
    localOrdinance: "Local Ordinance",
    violationClassification: "Violation Classification",
    weaponType: "Weapon Types",
    warningMsgForCriminalActivity: " You can only select up to 3 criminal activities.",
    criminalActivity: "Criminal Activity",
    gangType: "Gang Type",
    warningMsgForGangType: "Can select up to 2 gangs",
    biasMotivation: "Bias Motivations",
    community: "Community",
    addClassification: "Add Classification",
    classificationMapping: "Classification Mapping",
    reset: "Reset",
    oneDefaultClassificationNeedsToBeSelected: "One Default Classification Needs To Be Selected.",
    locationTypes: 'Location Types',
    earliestDate: "Earliest Date/Time",
    latestDate: "Latest Date/Time",
    reportDate: "Report Date/Time",
    updateRequest: "Update Request",
    sendUpdateRequest: "Send Update Request",
    updatedFields: "Updated Fields",
    updatedOn: "Updated On",
    updatedBy: "Updated By",
    updateAddress: "Update Address",
    approve: "Approve",
    reject: "Reject",
    acceptedRejectedBy: "Accepted/Rejected By",
    acceptedRejectedByOn: "Accepted/Rejected On",
    masterAddressUpdateRequest: "Master Address Update Request",
    oldFields: "Old",
    newFields: "New",
    dispatchVersion: "Dispatch Version",
    socketVersion: "Socket Version",
    pollingVersion: "Polling Version",
    importViolation: "Import Violation",
    TextStatuteTitle: "Text Statute Title",
    OffenseDescriptionAbbreviated: "Offense Description Abbreviated",
    Type: "Type",
    Class: "Class",
    NIBRSCode: "NIBRS Code",
    BeginDate: "Begin Date",
    EndDate: "End Date",
    JailTypes: "Jail Types",
    ViolationTypes: "Violation Types",
    resetPassword: "Reset Password",
    rpsCredentials: "RPS Credentials",
    mugshotCredentials: "Mugshot Credentials",
    disableUser: "Disable User",
    enableUser: "Enable User",
    options: "Options",
    sourceFields: "Source Fields",
    destinationFields: "Destination Fields",
    selectField: "Select Field",
    death: "Death",
    createdDate: "Created Date",
    lastModifiedDate: "Last Modified Date",
    rpsCredentialsFor: "RPS Credentails For",
    pleaseEnterUsernameAndPassword: "Please Enter Your Username And Password",
    mugshotCredentialsFor: "Mugshot Credentials For",
    backtoAgency: "Back to agency",
    printPDF: "Print PDF",
    audit: "Audit",
    generateShift: "Generate Shift",
    viewAndEdit: "View & Edit",
    reSchedule: "Re-Schedule",
    generateShiftAllocation: "Generate Shift Allocation",
    changePasswordFor: "Change Password For",
    sendEmailToUser: "Send Email to User",
    deselectAll: "Deselect All",
    displayFields: "Display Fields",
    statute: "Statute",
    searchFields: "Search Fields",
    sortColumns: "Sort Columns",
    userNotFound: "User not found",
    outOfJurisdiction: "Out Of Jurisdiction",
    ARStatuteCode: "AR Statute Code",
    userDisabled: "User disabled successfully",
    selectAgencyToBeChanged: "Select agency to be changed",
    addressValidateMsg: "You need to validate the address.",
    stateMsg: "State should same as agency",
    searchForAddress: "Search for address",
    selectUsers: "Select users",
    citationTypeToAdd: "Citation Type to Add",
    formatted: "Formatted",
    callBackAnswer: "Call Back Answer",
    shiftLimitExceedsForDepartment: "The shift limit exceeds for this department please choose other department.",
    shiftAlreadyExists: "This shift is already exists, please select different shift name.",
    ageFrom: "Age From",
    ageTo: "Age To",
    ageType: "Age Type",
    issuingAuthority: "Issuing Authority",
    dateIssued: "Date Issued",
    dateExpires: "Date Expires",
    revoked: "Revoked",
    revokedDate: "Revoked Date",
    idInfo: "Id Info",
    phoneNumberType: "Phone Number Type",
    extension: "Extension",
    extendedDescription: "Extended Description",
    skinTone: "Skin Tone",
    eyeColor: "Eye Color",
    hairLength: "Hair Length",
    eyeWear: "Eyewear",
    hairStyle: "Hair Style",
    demeanor: "Demeanor",
    facialHairColor: "Facial Hair Color",
    dexterity: "Dexterity",
    facialHairStyle: "Facial Hair Style",
    speech: "Speech",
    build: "Build",
    dental: "Dental/Teeth",
    clothing: "Clothing and General Appearance",
    addAddress: "Please add address to send update request",
    suspectedOfUsing: "Suspected of Using",
    methodOfEntry: "Method of Entry",
    EarliestLessLatestDate: "Earliest Date should be less or equal to Latest Date",
    EarliestLessReportDate: "Earliest Date should less or equal to Report Date",
    LatestLessEarliestDate: "Latest Date should be equal or greater than Earliest Date",
    LatestLessReportDate: "Latest Date should be less or equal to Report Date",
    ReportDateGreater: "Report Date should be greater or equal to Earliest and Latest Date",
    primaryColor: "Primary Color",
    secondaryColor: "Secondary Color",
    vin: "Vin",
    recent: "Recent",
    lastNcicChange: "Last Ncic Change Date",
    lastRpsChange: "Last Rps Change Date",
    recentPerson: "Person(s)",
    recentAddress: "Address(es)",
    recentVehicle: "Vehicle(s)",
    recentOrganisation: "Organisation(s)",
    fireArmType: "Fire Arm Type",
    gunType: "Gun Type",
    gunDescription: "Gun Description",
    noReportDate: "No Report Date",
    reportedPerson: "Reported Person",
    brandLabel: "Brand",
    baby: "Baby",
    newborn: "Newborn",
    neonatal: "Neonatal",
    pleaseSelectLogType: "Please select log type",
    gatheringResults: "Gathering Results",
    dLScan: "DL SCAN",
    nCICSearch: "NCIC SEARCH",
    localSearch: "LOCAL SEARCH",
    globalSearch: "GLOBAL SEARCH",
    manualEntry: "MANUAL ENTRY",
    recentList: "RECENT LIST",
    searchByVehicle: "Search by Vehicle",
    residence: "Residence",
    USCitizen: "US Citizen",
    legalAlien: "Legal Alien",
    nationality: "Nationality",
    arrestInformation: "Arrest Information",
    arrestDate: "Arrest Date",
    arrestNumber: "Arrest Number",
    arrestSequence: "Arrest Sequence",
    arrestType: "Arrest Type",
    arresteeArmed: "Arrestee Armed",
    multipleArresteeSegmentIndicator: "Multiple Arrestee Segment Indicator",
    juvenileDisposition: "Juvenile Disposition",
    searchByVehicle: "Search by Vehicle",
    reportingOfficer: "Reporting officer",
    exceptionalClearance: "Exceptional clearance",
    exceptionalClearanceDate: "Exceptional clearance date",
    cargoTheft: "Cargo theft",
    domesticViolence: "Domestic violence",
    sealed: "Sealed",
    active: "Active",
    awaitingInformation: "Awaiting information",
    closed: "Closed",
    inactive: "Inactive",
    unfounded: "Unfounded",
    LEOKAInformation: "LEOKA Information",
    circumstance: "Circumstance",
    registrationScan: "REGISTRATION SCAN",
    victimConnectedToOffense: "Victim Connected To Offense",
    offenseConnectedToVictim: "Offense Connected To Victim",
    victims: "Victim(s)",
    selectPerson: "Select Person",
    selectViolation: "Select Violation",
    CheckAllVictims: "Check all victims against whom this offense was attempted or committed",
    personVictimDeleteMsg: "Are you sure you want to delete this person victim ?",
    warningMsgForDelete: "This person is associated with multiple violations. Please remove the person from the violation before deleting.",
    victimSuspectRelationship: "Victim To Suspect Relationship(s)",
    registrationScan: "REGISTRATION SCAN",
    noViolation: "No offense is currently available to link. You need to create an offense first.",
    victimSuspectInfo: "The Victim's relationship to each suspect/offender is reported when the victim was the object of a Crime Against Person, i.e. Assault Offense, Homicide Offense, Kidnaping/Abduction, Forcible Sex Offense, or Nonforcible Sex Offense",
    victimRelationship: "Victim Relationship",
    selectIncidentAccessRight: "The default app is Incident, so you must select the Incident access right.",
    selectDispatchAccessRight: "The default app is Dispatch, so you must select the Mobile Dispatch access right.",
    selectAdminAccessRight: "The default app is Admin, so you must select the Users access right.",
    createMasterOrganizationSaveSuccess: "Master Organization created successfully!",
    createMasterOrganizationSaveFailed: "Error while creating Master Organization!",
    masterOrganizationUpdateSuccess: "Master Organization updated successfully!",
    masterOrganizationUpdateFailed: "Error while updating Master Organization!",
    searchOrganizations: "Search Organizations",
    businessName: "Business Name",
    doesNotUseOldRps: "Does not use old RPS",
    incidentApp: "Incident App",
    incidentWebsite: "Incident Website",
    selectIncidentAppOrWebsiteAccessRight: "Please select either 'Incident App' or 'Incident Website' access rights when 'Incident' access is selected.",
    selectDispatchAppOrWebsiteAccessRight: "Please select either 'Dispatch App' or 'Dispatch Website' access rights when 'Dispatch' access is selected.",
    intersectionUniqueStreetName: "This intersection only has one unique street name.  Do you want to add another street name or delete the intersection?",
    streetNames: "Street Names",
    showPersonAlert: "Show Person Alert"
};

export default locale;