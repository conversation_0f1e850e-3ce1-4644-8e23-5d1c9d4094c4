import HangUpCallComponent from "../CallActions/HangUp";
import MVAInjuryComponent from "../CallActions/MVAInjury";
import MVAUNKInjuryComponent from "../CallActions/MVAUNKInjury";
import MVANOInjuryComponent from "../CallActions/MVANOInjury";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";

function ActionComponent(props) {
    const { t } = useTranslation("laguageConfig");
    const selectedCallData = useSelector(({ call911 }) => call911.call911.selectedCall);

    return (
        <Grid container spacing={0}>
            <Grid item xs={12}>
                <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="HangUpCallComponent" />} onReset={() => { }} >
                    <HangUpCallComponent value={selectedCallData} ></HangUpCallComponent>
                </ErrorBoundary>
            </Grid>
            <Grid item xs={12}>
                <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="MVAInjuryComponent" />} onReset={() => { }} >
                    <MVAInjuryComponent></MVAInjuryComponent>
                </ErrorBoundary>
            </Grid>
            <Grid item xs={12}>
                <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="MVAUNKInjuryComponent" />} onReset={() => { }} >
                    <MVAUNKInjuryComponent></MVAUNKInjuryComponent>
                </ErrorBoundary>
            </Grid>
            <Grid item xs={12}>
                <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="MVANOInjuryComponent" />} onReset={() => { }} >
                    <MVANOInjuryComponent></MVANOInjuryComponent>
                </ErrorBoundary>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("domestic")}
                </Button>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("assault")}
                </Button>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("shotsHeard")}
                </Button>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("burglary")}
                </Button>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("structureFire")}
                </Button>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("visibleSmoke")}
                </Button>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("vehicleFire")}
                </Button>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("grassFire")}
                </Button>
            </Grid>
            <Grid item xs={12}>
                <Button
                    variant="contained"
                    color="secondary"
                    style={{ width: "95%", margin: 5 }}
                >
                    {t("medicalCall")}
                </Button>
            </Grid>
        </Grid>
    );
}

export default ActionComponent;