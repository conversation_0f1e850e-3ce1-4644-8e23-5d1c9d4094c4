import React from "react";
import { Button } from "@mui/material";

const SaveCancelButtonGroup = ({
    onSave,
    onCancel,
    saveText = "Save",
    cancelText = "Cancel",
    saveButtonId = "saveButton",
    cancelButtonId = "cancelButton",
}) => {
    return (
        <>

            <Button
                id={saveButtonId}
                type="submit"
                color="primary"
                autoFocus
                variant="contained"
                className="m-4"
                onClick={onSave}
            >
                {saveText}
            </Button>
            <Button
                id={cancelButtonId}
                autoFocus
                onClick={onCancel}
                color="primary"
                variant="contained"
                className="m-4"
            >
                {cancelText}
            </Button>
        </>
    );
};

export default SaveCancelButtonGroup;
