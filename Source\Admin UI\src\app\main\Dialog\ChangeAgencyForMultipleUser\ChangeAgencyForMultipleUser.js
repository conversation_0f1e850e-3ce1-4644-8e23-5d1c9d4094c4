import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import "./ChangeAgencyForMultipleUser.css"
import { useDispatch, useSelector } from 'react-redux';
import { Box } from '@mui/system';
import { Button, DialogActions, FormControl, MenuItem, Select, Typography } from '@mui/material';
import { motion } from 'framer-motion';
import { changesUserAgency, getRegularUsers } from '../../administration/store/usersSlice';
import CloseIcon from '@mui/icons-material/Close';

const ChangeAgencyForMultipleUserDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation('laguageConfig');
    const [open, setOpen] = React.useState(false);
    const [selectedIds, setSelectedIds] = React.useState([]);
    const [agencyCode, setAgencyCode] = React.useState('');
    const [selectedAgency, setSelectedAgency] = React.useState(null);
    const [pagingDetails, setPagingDetails] = React.useState(null);

    const agencyList = useSelector(({ agency }) => agency.agency.data);
    const RegularUsers = useSelector(({ administration }) => administration.user.RegularUsers);

    const handleClickOpen1 = () => {
        setOpen(true);
    };
    useImperativeHandle(ref, () => ({
        handleClickOpen(selectedIds, pagingDetails) {
            setSelectedIds(selectedIds);
            setPagingDetails(pagingDetails);
            handleClickOpen1();
        },
    }));

    const handleClose = () => {
        setOpen(false);
        dispatch(getRegularUsers(
            "fname", "asc",
            pagingDetails.pageIndex, pagingDetails.pageLimit,
            pagingDetails.selectedAgency, pagingDetails.searchText,
        ));
        setSelectedIds([]);
        setAgencyCode('');
        setPagingDetails(null);
        setSelectedAgency(null);
    };

    const handleChange = (event) => {
        setAgencyCode(event.target.value);
        let filteredAgency = agencyList.filter((x) => x.code === event.target.value);
        setSelectedAgency(filteredAgency[0]);
    };

    const handleAgencyChange = () => {
        let usersArray = [];
        // selectedIds is an array of user IDs
        selectedIds.forEach((element) => {
            // Find the user in RegularUsers by _id
            const user = RegularUsers.find((x) => x._id === element);
            if (user) {
                // Create the object with the required properties and push it into usersArray
                let body = {
                    _id: user._id,
                    defaultAgency: agencyCode,
                    defaultApp: user.defaultApp,
                    userAgencies: [{
                        value: selectedAgency._id,
                        label: selectedAgency.name,
                        agencyCode: agencyCode,
                        RPSUserName: user.userAgencies[0].RPSUserName,
                        RPSPassword: user.userAgencies[0].RPSPassword,
                        RPSName: user.userAgencies[0].RPSName,
                        RPSUserGroupName: user.userAgencies[0].RPSUserGroupName,
                        RPSUserID: user.userAgencies[0].RPSUserID,
                        RMDUserID: user.userAgencies[0].RMDUserID,
                        ncicUserName: user.userAgencies[0].ncicUserName,
                        ncicPassword: user.userAgencies[0].ncicPassword,
                    }],
                    userAccessRights: [{
                        Users: selectedAgency.agencyLicenses[0].Users ? user.userAccessRights[0].Users !== undefined ? user.userAccessRights[0].Users : false : false,
                        Contact: selectedAgency.agencyLicenses[0].Contact ? user.userAccessRights[0].Contact !== undefined ? user.userAccessRights[0].Contact : false : false,
                        Server: selectedAgency.agencyLicenses[0].Server ? user.userAccessRights[0].Server !== undefined ? user.userAccessRights[0].Server : false : false,
                        Icons: selectedAgency.agencyLicenses[0].Icons ? user.userAccessRights[0].Icons !== undefined ? user.userAccessRights[0].Icons : false : false,
                        CallCategory: selectedAgency.agencyLicenses[0].CallCategory ? user.userAccessRights[0].CallCategory !== undefined ? user.userAccessRights[0].CallCategory : false : false,
                        CallViolation: selectedAgency.agencyLicenses[0].CallViolation ? user.userAccessRights[0].CallViolation !== undefined ? user.userAccessRights[0].CallViolation : false : false,
                        CallType: selectedAgency.agencyLicenses[0].CallType ? user.userAccessRights[0].CallType !== undefined ? user.userAccessRights[0].CallType : false : false,
                        CallResponse: selectedAgency.agencyLicenses[0].CallResponse ? user.userAccessRights[0].CallResponse !== undefined ? user.userAccessRights[0].CallResponse : false : false,
                        Call: selectedAgency.agencyLicenses[0].Call ? user.userAccessRights[0].Call !== undefined ? user.userAccessRights[0].Call : false : false,
                        FileUpload: selectedAgency.agencyLicenses[0].FileUpload ? user.userAccessRights[0].FileUpload !== undefined ? user.userAccessRights[0].FileUpload : false : false,
                        ClassofService: selectedAgency.agencyLicenses[0].ClassofService ? user.userAccessRights[0].ClassofService !== undefined ? user.userAccessRights[0].ClassofService : false : false,
                        Incident: selectedAgency.agencyLicenses[0].Incident ? user.userAccessRights[0].Incident !== undefined ? user.userAccessRights[0].Incident : false : false,
                        Dispatch: selectedAgency.agencyLicenses[0].Dispatch ? user.userAccessRights[0].Dispatch !== undefined ? user.userAccessRights[0].Dispatch : false : false,
                        Chat: selectedAgency.agencyLicenses[0].Chat ? user.userAccessRights[0].Chat !== undefined ? user.userAccessRights[0].Chat : false : false,
                        Current: selectedAgency.agencyLicenses[0].Current ? user.userAccessRights[0].Current !== undefined ? user.userAccessRights[0].Current : false : false,
                        Replay: selectedAgency.agencyLicenses[0].Replay ? user.userAccessRights[0].Replay !== undefined ? user.userAccessRights[0].Replay : false : false,
                        SearchFeature: selectedAgency.agencyLicenses[0].SearchFeature ? user.userAccessRights[0].SearchFeature !== undefined ? user.userAccessRights[0].SearchFeature : false : false,
                        Watches: selectedAgency.agencyLicenses[0].Watches ? user.userAccessRights[0].Watches !== undefined ? user.userAccessRights[0].Watches : false : false,
                        BroadcastMessages: selectedAgency.agencyLicenses[0].BroadcastMessages ? user.userAccessRights[0].BroadcastMessages !== undefined ? user.userAccessRights[0].BroadcastMessages : false : false,
                        MySchedule: selectedAgency.agencyLicenses[0].MySchedule ? user.userAccessRights[0].MySchedule !== undefined ? user.userAccessRights[0].MySchedule : false : false,
                        DeviceLicense: selectedAgency.agencyLicenses[0].DeviceLicense ? user.userAccessRights[0].DeviceLicense !== undefined ? user.userAccessRights[0].DeviceLicense : false : false,
                        Citation: selectedAgency.agencyLicenses[0].Citation ? user.userAccessRights[0].Citation !== undefined ? user.userAccessRights[0].Citation : false : false,
                        Units: selectedAgency.agencyLicenses[0].Units ? user.userAccessRights[0].Units !== undefined ? user.userAccessRights[0].Units : false : false,
                        Nfirs: selectedAgency.agencyLicenses[0].Nfirs ? user.userAccessRights[0].Nfirs !== undefined ? user.userAccessRights[0].Nfirs : false : false,
                        Shift: selectedAgency.agencyLicenses[0].Shift ? user.userAccessRights[0].Shift !== undefined ? user.userAccessRights[0].Shift : false : false,
                        Team: selectedAgency.agencyLicenses[0].Team ? user.userAccessRights[0].Team !== undefined ? user.userAccessRights[0].Team : false : false,
                        ShiftAllocation: selectedAgency.agencyLicenses[0].ShiftAllocation ? user.userAccessRights[0].ShiftAllocation !== undefined ? user.userAccessRights[0].ShiftAllocation : false : false,
                        Department: selectedAgency.agencyLicenses[0].Department ? user.userAccessRights[0].Department !== undefined ? user.userAccessRights[0].Department : false : false,
                        Mugshot: selectedAgency.agencyLicenses[0].Mugshot ? user.userAccessRights[0].Mugshot !== undefined ? user.userAccessRights[0].Mugshot : false : false,
                        TipType: selectedAgency.agencyLicenses[0].TipType ? user.userAccessRights[0].TipType !== undefined ? user.userAccessRights[0].TipType : false : false,
                        ViewTip: selectedAgency.agencyLicenses[0].ViewTip ? user.userAccessRights[0].ViewTip !== undefined ? user.userAccessRights[0].ViewTip : false : false,
                        DispatchApp: selectedAgency.agencyLicenses[0].DispatchApp ? user.userAccessRights[0].DispatchApp !== undefined ? user.userAccessRights[0].DispatchApp : false : false,
                        DispatchWebsite: selectedAgency.agencyLicenses[0].DispatchWebsite ? user.userAccessRights[0].DispatchWebsite !== undefined ? user.userAccessRights[0].DispatchWebsite : false : false,
                        DispatchCitation: selectedAgency.agencyLicenses[0].DispatchCitation ? user.userAccessRights[0].DispatchCitation !== undefined ? user.userAccessRights[0].DispatchCitation : false : false,
                        TrafficStop: selectedAgency.agencyLicenses[0].TrafficStop ? user.userAccessRights[0].TrafficStop !== undefined ? user.userAccessRights[0].TrafficStop : false : false,
                        TransportCall: selectedAgency.agencyLicenses[0].TransportCall ? user.userAccessRights[0].TransportCall !== undefined ? user.userAccessRights[0].TransportCall : false : false,
                        TransportMedicalCall: selectedAgency.agencyLicenses[0].TransportMedicalCall ? user.userAccessRights[0].TransportMedicalCall !== undefined ? user.userAccessRights[0].TransportMedicalCall : false : false,
                        SecurityCheck: selectedAgency.agencyLicenses[0].SecurityCheck ? user.userAccessRights[0].SecurityCheck !== undefined ? user.userAccessRights[0].SecurityCheck : false : false,
                        PersonSearch: selectedAgency.agencyLicenses[0].PersonSearch ? user.userAccessRights[0].PersonSearch !== undefined ? user.userAccessRights[0].PersonSearch : false : false,
                        VehicleSearch: selectedAgency.agencyLicenses[0].VehicleSearch ? user.userAccessRights[0].VehicleSearch !== undefined ? user.userAccessRights[0].VehicleSearch : false : false,
                        GunSearch: selectedAgency.agencyLicenses[0].GunSearch ? user.userAccessRights[0].GunSearch !== undefined ? user.userAccessRights[0].GunSearch : false : false,
                        ArticleSearch: selectedAgency.agencyLicenses[0].ArticleSearch ? user.userAccessRights[0].ArticleSearch !== undefined ? user.userAccessRights[0].ArticleSearch : false : false,
                        BoatSearch: selectedAgency.agencyLicenses[0].BoatSearch ? user.userAccessRights[0].BoatSearch !== undefined ? user.userAccessRights[0].BoatSearch : false : false,
                        IncidentReport: selectedAgency.agencyLicenses[0].IncidentReport ? user.userAccessRights[0].IncidentReport !== undefined ? user.userAccessRights[0].IncidentReport : false : false,
                        AccidentReport: selectedAgency.agencyLicenses[0].AccidentReport ? user.userAccessRights[0].AccidentReport !== undefined ? user.userAccessRights[0].AccidentReport : false : false,
                        UserAudit: selectedAgency.agencyLicenses[0].UserAudit ? user.userAccessRights[0].UserAudit !== undefined ? user.userAccessRights[0].UserAudit : false : false,
                        EmailConfiguration: selectedAgency.agencyLicenses[0].EmailConfiguration ? user.userAccessRights[0].EmailConfiguration !== undefined ? user.userAccessRights[0].EmailConfiguration : false : false,
                        TwitterConfiguration: selectedAgency.agencyLicenses[0].TwitterConfiguration ? user.userAccessRights[0].TwitterConfiguration !== undefined ? user.userAccessRights[0].TwitterConfiguration : false : false,
                        TwitterAccountSettings: selectedAgency.agencyLicenses[0].TwitterAccountSettings ? user.userAccessRights[0].TwitterAccountSettings !== undefined ? user.userAccessRights[0].TwitterAccountSettings : false : false,
                        ErrorLog: selectedAgency.agencyLicenses[0].ErrorLog ? user.userAccessRights[0].ErrorLog !== undefined ? user.userAccessRights[0].ErrorLog : false : false,
                        ArchiveChatHistories: selectedAgency.agencyLicenses[0].ArchiveChatHistories ? user.userAccessRights[0].ArchiveChatHistories !== undefined ? user.userAccessRights[0].ArchiveChatHistories : false : false,
                    }],
                }
                usersArray.push(body);
            }
        });

        dispatch(changesUserAgency(usersArray, pagingDetails));
        handleClose();
    }

    const lableStyle = {
        fontSize: "17px",
        fontWeight: "700"
    }

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='sm'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
                    {t("changeAgency")}
                </DialogTitle>
                <IconButton
                    aria-label="close"
                    onClick={handleClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: (theme) => theme.palette.grey[500],
                    }}
                >
                    <CloseIcon />
                </IconButton>
                <DialogContent dividers>
                    <div className='m-16'>
                        <Typography gutterBottom>
                            <label style={lableStyle}> {t("selectAgencyToBeChanged")} </label>
                        </Typography>
                        <Box component={motion.div}
                            initial={{ y: -20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8" elevation={1}>
                            <FormControl fullWidth>
                                <Select
                                    labelId="demo-simple-select-label"
                                    id="demo-simple-select"
                                    name="defaultagency"
                                    value={agencyCode}
                                    onChange={handleChange}
                                >
                                    {agencyList.map((element) => (
                                        <MenuItem key={element.code} value={element.code}>
                                            {element.name}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>
                    </div>
                </DialogContent>
                <DialogActions>
                    <div className='flex justify-end'>
                        < Button
                            variant="contained"
                            color="secondary"
                            className=" w-auto mr-16 mt-8"
                            aria-label="Register"
                            type="button"
                            onClick={handleClose}
                            size="large"
                        >
                            {t("no")}
                        </Button>

                        <Button
                            variant="contained"
                            color="primary"
                            className=" w-auto mt-8"
                            aria-label="Register"
                            type="button"
                            size="large"
                            onClick={handleAgencyChange}
                        >
                            {t("yes")}
                        </Button>
                    </div>
                </DialogActions>
            </Dialog>
        </div>
    );
});
export default ChangeAgencyForMultipleUserDialog;

