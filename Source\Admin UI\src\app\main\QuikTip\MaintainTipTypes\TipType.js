import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import {
    ThemeProvider,
    StyledEngineProvider,
    Paper,
    Input,
} from "@mui/material";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import history from "@history";
import Stack from "@mui/material/Stack";
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { DeleteTipType, getTipTypes } from "../../store/quikTipSlice";
import { ErrorBoundary } from "react-error-boundary";
import AddNewTipType from "../componets/AddNewTipType";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import { useWindowResizeHeight } from "../../utils/utils";

const rows = [
    {
        id: "name",
        align: "left",
        disablePadding: false,
        label: "Name",
        sort: true,
    },
    {
        id: "description",
        align: "left",
        disablePadding: false,
        label: "Description",
        sort: true,
    },
    {
        id: "department",
        align: "left",
        disablePadding: false,
        label: "Department",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function TipType() {
    const gridRef = useRef(null);
    const tipTypeRef = useRef();
    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const [searchText, setSearchText] = React.useState("");
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.isloading);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const tipTypes = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.tipTypesdata);
    const totalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.tipTypesTotalCount);
    const routeParams = useParams();
    const [countData, setCountData] = React.useState(totalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [open, setOpen] = React.useState(false);
    const [loading, setLoading] = useState();

    const [removeID, setRemoveID] = React.useState(0);

    const [data, setData] = React.useState(tipTypes);

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "name",
    });


    useEffect(() => {
        dispatch(getTipTypes(order.id,
            order.direction,
            pageIndex,
            rowsPerPage,
            routeParams.code));
    }, [pageIndex, rowsPerPage, countData, order])

    function handleRequestSort(event, property) {
        const id = property;
        let direction = "desc";

        if (order.id === property && order.direction === "desc") {
            direction = "asc";
        }
        setOrder({
            direction,
            id,
        });
    }

    const deleteRecord = (n) => {
        setOpen(true);
        setRemoveID(n._id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(DeleteTipType(removeID, routeParams.code));
        }
    }


    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <IconButton
                                aria-label="Back"
                                color="inherit"
                                onClick={() => tipTypeRef.current.handleClickOpen(x, routeParams.code, true)}
                                size="large"
                            >
                                <EditIcon />
                            </IconButton>
                            <IconButton
                                aria-label="Back"
                                color="inherit"
                                onClick={() => deleteRecord(x)}
                                size="large"
                            >
                                <DeleteIcon />
                            </IconButton>
                        </div>
                    }
                </>
            );
        };
    }



    const rowData = tipTypes.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["_id"] = item._id
            row["name"] = item.name
            row["description"] = item.description
            if (item.departments.length > 0) {
                row["department"] = item.departments[0].name
                row["departmentId"] = item.departments[0]._id
            }
            else {
                row["department"] = ""
                row["departmentId"] = ""
            }

            row["action"] = ActionIcons(item)
        });
        return row;
    });

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);


    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 w-full items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    <TipsAndUpdatesIcon style={{ fontSize: '35px' }} />

                                </Icon>

                                <Typography className="hidden sm:flex mx-0 sm:mx-12" variant="h6">
                                    {t("tipTypes")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <motion.div
                                initial={{ opacity: 0, y: 40 }}
                                animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
                            >
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addTipType" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="whitespace-nowrap normal-case" btnName={t("addTipType")} parentCallback={() => tipTypeRef.current.handleClickOpen("", routeParams.code, false)}></CommonButton>
                                </ErrorBoundary>
                            </motion.div>

                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="name"
                                        header={t("name")}
                                        field="name"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="description"
                                        header={t("description")}
                                        field="description"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="department"
                                        header={t("department")}
                                        field="department"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="Action"
                                        header={t("action")}
                                        field="Action"
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteMsg")}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>
                        </div>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="AddNewTipType" />} onReset={() => { }}>
                            <AddNewTipType ref={tipTypeRef} />
                        </ErrorBoundary>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={t("deleteMsg")}
                                onClose={handleClose}
                                value={removeID}
                            >
                            </ConfirmationDialog>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    )
}



export default TipType;