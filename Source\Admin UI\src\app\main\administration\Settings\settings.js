import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Card, AppBar, CardContent, Toolbar, TextField, Grid } from '@mui/material';
import _ from '@lodash';
import LoginSettings from '../../components/LoginSettings/loginSettings';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { ErrorBoundary } from "react-error-boundary";

function settings(props) {


    return (
        <div>
            <Grid>
                <ErrorBoundary
                    FallbackComponent={(props) => <ErrorPage {...props} componentName="LoginSettings" />} onReset={() => { }} >
                    <LoginSettings></LoginSettings>
                </ErrorBoundary>
            </Grid>
        </div>
    );
}

export default settings;

