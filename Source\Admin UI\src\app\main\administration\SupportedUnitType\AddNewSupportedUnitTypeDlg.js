import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Grid from '@mui/material/Grid';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import { saveSupportedUnitType, updateSupportedUnitType } from '../store/supportedUnitTypeSlice';
import { isEmptyOrNull } from '../../utils/utils';
import { showMessage } from 'app/store/fuse/messageSlice';

import CircularProgressLoader from '../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';

const defaultValues = { name: '', rpsCode: '', icon: "" };

const schema = yup.object().shape({
    // ShiftName: yup.string().required('Please enter Shift Name.'),
    // ShiftDuration: yup.string().required('Shift Duration not be empty.'),
});


const AddNewSupportedUnitTypeDlg = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [code, setCode] = React.useState("");
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [dataValue, setData] = React.useState("");
    const [availabilityValue, setAvailabilityValue] = React.useState("onduty");
    const [iconUrl, setIconUrl] = React.useState("");
    const [iconKey, setIconKey] = React.useState("");
    const isloadingvalue = useSelector(({ administration }) => administration.supportedUnitType.isloading);

    const [preview, setPreview] = React.useState(null)
    const [loading, setLoading] = React.useState();
    const fileUpload = useRef(null);
    const nameRef = useRef(null);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues
    });


    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            nameRef.current?.focus();
        }, 0);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, isUpdate) {
            setCode(code)
            setIsUpdate(isUpdate)
            setData(data);
            setIconUrl(data.iconUrl)
            setIconKey(data.iconKey)
            handleClickOpen1();
        },
    }));

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);


    useEffect(() => {
        if (!isEmptyOrNull(dataValue)) {
            let UnitAvailability = !isEmptyOrNull(dataValue.availability) ? dataValue.availability === "Available" ? "onduty" : "offduty" : "offduty"
            setValue('name', dataValue.name);
            setValue('rpsCode', dataValue.rpsCode);
            setPreview(dataValue.iconUrl)
            setValue('availability', UnitAvailability)
            setAvailabilityValue(UnitAvailability)
        }
        else {
            setValue('name', "");
            setValue('rpsCode', "");
            setPreview(null)
        }

    }, [open]);


    const handleClose = () => {
        setOpen(false);
        setValue('name', "");
        setValue('rpsCode', "");
        setPreview(null);
        setValue('availability', "onduty")
        setAvailabilityValue("onduty")

    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }


    function onSubmit(model) {
        const file = fileUpload.current.files[0] ? fileUpload.current.files[0] : null;
        const formData = new FormData();
        formData.append("file", file);
        let data = {
            name: model.name,
            rpsCode: model.rpsCode,
            isUpdate: true,
            code: code,
            availabilityValue: availabilityValue
        }
        if (isUpdate) {
            dispatch(updateSupportedUnitType(data, formData, file, iconUrl, iconKey, dataValue._id))
            handleClose();
        } else {
            if (file) {
                dispatch(saveSupportedUnitType(data, formData, file));
                handleClose();
            } else {
                ShowErroMessage("Please select svg file.")
            }
        }


    }

    function onFileChange(e) {
        const objectUrl = URL.createObjectURL(e.currentTarget.files[0])
        setPreview(objectUrl)
    }


    const handleAvailabilityChange = (event) => {
        setAvailabilityValue(event.target.value);
        setValue('availability', event.target.value);
    };

    return (
        <div>
            {loading && < CircularProgressLoader loading={loading} />}
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("supportedUnitType")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        autoSave={false}
                    >
                        <Controller
                            name="name"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mt-16 w-full"
                                    label={t("name")}
                                    type="text"
                                    variant="outlined"
                                    required
                                    inputRef={nameRef}
                                />
                            )}
                        />

                        <Controller
                            name="rpsCode"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mt-16 w-full"
                                    label={t("rpsCode")}
                                    type="text"
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Grid className="mt-2" container spacing={1} >
                            <Grid item xs={12} sm={12} md={12} lg={4} xl={4}>
                                <input
                                    type="file"
                                    //accept="image/*"
                                    ref={fileUpload}
                                    accept="image/svg+xml"
                                    style={{
                                        padding: '13px',
                                        border: '1px solid lightgray',
                                        borderRadius: '4px',
                                        // backgroundColor: 'antiquewhite',
                                        cursor: 'pointer',
                                        marginTop: '8px',
                                        height: '53px',
                                        width: '273px'
                                    }}

                                    onChange={(e) => onFileChange(e)}
                                />
                            </Grid>

                            <Grid item xs={12} sm={12} md={12} lg={4} xl={6}>
                                {preview !== null &&
                                    <img src={preview} width="50" height="50"></img>
                                }
                            </Grid>

                        </Grid>

                        <FormControl className="mt-16" component="fieldset">
                            <FormLabel component="legend">{t("availability")}</FormLabel>
                            <RadioGroup
                                row aria-label="gender"
                                name="availability"
                                value={availabilityValue}
                                onChange={handleAvailabilityChange}
                            >
                                <FormControlLabel value="onduty" control={<Radio />} label={t("available")} />
                                <FormControlLabel value="offduty" control={<Radio />} label={t("unAvailable")} />
                            </RadioGroup>
                        </FormControl>





                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>

        </div>
    );
});
export default AddNewSupportedUnitTypeDlg;

