import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import FuseScrollbars from "@fuse/core/FuseScrollbars";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import _ from "@lodash";
import { removeViolation, getCallViolations, searchViolation } from "../store/callViolationSlice";
import history from "@history";
import TablePagination from "@mui/material/TablePagination";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import CircularProgressLoader from '../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { checkData, getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import { ErrorBoundary } from "react-error-boundary";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { useDebounce } from '@fuse/hooks';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
import { IgrGrid, IgrColumn } from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "StatuteTitle",
        align: "left",
        disablePadding: false,
        label: "STATUTE_TITLE_LBL",
        sort: true,
    },
    {
        id: "StatuteCitation",
        align: "left",
        disablePadding: false,
        label: "STATUTE_CITATION_LBL",
        sort: true,
    },
    {
        id: "OffenseDescription",
        align: "left",
        disablePadding: false,
        label: "OFFENSE_DESCRIPTION_LBL",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

function CallViolationsNew() {
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const ActionIconColor = navbarTheme.palette.primary.light;
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    const RowsSelectedBackgroundColor = navbarTheme.palette.secondary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const gridRef = useRef(null);
    const dispatch = useDispatch();
    const callViolationData = useSelector(({ administration }) => administration.callViolations.data);
    const callViolationTotalCount = useSelector(({ administration }) => administration.callViolations.totalCount);
    const success = useSelector(({ administration }) => administration.callViolations.success);

    const [newCallViolation, setNewCallViolation] = React.useState(false);
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [searchText, setSearchText] = React.useState("");
    const [data, setData] = React.useState(callViolationData);
    const [countData, setCountData] = React.useState(callViolationTotalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "StatuteTitle",
    });
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    const [gridWidth, setGridWidth] = useState(1200); // Default width
    const routeParams = useParams();

    function onRowAddchange() {
        setNewCallViolation(!newCallViolation);
        setIsUpdate(false);
        //Added tempData to url as object to access parameters required for get request
        history.push(`/admin/callViolation/${routeParams.code}/0`, {
            tempData: {
                orderId: order.id,
                orderDirection: order.direction,
                pageIndex: pageIndex * rowsPerPage,
                rowsPerPage: rowsPerPage,
                searchText: searchText === '' ? null : searchText
            }
        });
    }

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const editRow = (n) => {
        setIsUpdate(true);
        // history.push(`/admin/callViolation/${routeParams.code}/${n._id}`);
        //Added tempData to url as object to access parameters required for get request
        history.push(`/admin/callViolation/${routeParams.code}/${n._id}`, {
            tempData: {
                orderId: order.id,
                orderDirection: order.direction,
                pageIndex: pageIndex * rowsPerPage,
                rowsPerPage: rowsPerPage,
                searchText: searchText === '' ? null : searchText
            }
        });
    };

    const deleteCallViolation = (n) => {
        setOpen(true);
        setRemoveID(n._id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeViolation(removeID, routeParams.code, pageIndex, rowsPerPage, order.id, order.direction, searchText == '' ? null : searchText));
            setCountData(countData - 1);
        }
    };


    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const search = useDebounce((search, pageIndex, rowsPerPage) => {
        dispatch(getCallViolations(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage,
            search === '' ? null : search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, pageIndex, rowsPerPage);
        } else {
            dispatch(getCallViolations(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage,
                searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, searchText, pageIndex, rowsPerPage, order, routeParams.code]);

    useEffect(() => {
        setData(callViolationData);
        setCountData(callViolationTotalCount)
    }, [callViolationData]);

    const isloadingvalue = useSelector(({ administration }) => administration.callViolations.isloading);
    const [loading, setLoading] = useState();
    let colorCode = getNavbarTheme();

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => editRow(x)}
                                    size="large"
                                >
                                    <EditIcon />
                                </IconButton>

                            </Tooltip>

                            <Tooltip title={t("delete")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    disabled={n.isActive}
                                    onClick={() => deleteCallViolation(x)}
                                    size="large"
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                    }
                </>
            );
        }

    };

    const rowData = data.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["StatuteTitle"] = item.StatuteTitle
            row["action"] = ActionIcons(item)
        });
        return row;
    });

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    call_to_action
                                </Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("violations")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addCallViolation" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="whitespace-no-wrap normal-case ml-16" btnName={t("addCallViolation")} parentCallback={onRowAddchange}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={countData}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{ "aria-label": "Previous Page", }}
                                nextIconButtonProps={{ "aria-label": "Next Page", }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />
                        </div>
                        <div className="igrGridClass" >

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="StatuteTitle"
                                        field="StatuteTitle"
                                        header={t("statuteTitle")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="StatuteCitation"
                                        header={t("statuteCitation")}
                                        field="StatuteCitation"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="OffenseDescription"
                                        header={t("offenseDescription")}
                                        field="OffenseDescription"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteMsgCallViolation")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>
                        </div>
                    </div>
                }
            />
        </>
    );
}

export default CallViolationsNew;