import React from 'react';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';

const CommonIconButton = ({ tooltipTitle, ariaLabel, onClick, icon, color = 'inherit', size = 'large' }) => {
    return (
        <Tooltip title={tooltipTitle}>
            <IconButton
                aria-label={ariaLabel}
                color={color}
                onClick={onClick}
                size={size}
            >
                {icon}
            </IconButton>
        </Tooltip>
    );
};

export default CommonIconButton;
