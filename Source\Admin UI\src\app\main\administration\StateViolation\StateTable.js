import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from "react-router";
import { Icon, Input, Paper, StyledEngineProvider, TablePagination, ThemeProvider, Typography, InputAdornment } from '@mui/material';
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useSelector, useDispatch } from 'react-redux';
import history from '@history';
import { useDebounce } from '@fuse/hooks';
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from '../../utils/utils';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import CorporateFareIcon from '@mui/icons-material/CorporateFare';
import CallToActionIcon from '@mui/icons-material/CallToAction';
import ImportExportIcon from '@mui/icons-material/ImportExport';
import ListAltIcon from '@mui/icons-material/ListAlt';
import { getMasterState, setLoading, setSelectedStateCode } from '../store/stateViolationSlice';
import CancelIcon from '@mui/icons-material/Cancel';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import ImportViolationDialog from './ImportViolationDialog';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { saveExcelViolationData, uploadViolationFile, setViolationUploadResponse } from '../../store/importFromExcelSlice';

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "StateCode",
        align: "left",
        disablePadding: false,
        label: "StateCode",
        sort: true,
    },
    {
        id: "StateName",
        align: "left",
        disablePadding: false,
        label: "StateName",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function StateTable(props) {
    const { t } = useTranslation("laguageConfig");
    const mainTheme = useSelector(selectMainTheme);
    const dispatch = useDispatch();
    const routeParams = useParams();
    const gridRef = useRef(null);
    const violationDialogRef = useRef(null);
    const rowsPerPageOptions = getRowsPerPageOptions();
    let colorCode = getNavbarTheme();

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [page, setPage] = useState(0);
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    const [finalData, setFinalData] = useState(null);
    const [importState, setImportState] = useState('');
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "StateName",
    });

    const [searchText, setSearchText] = React.useState("");
    const MasterAddress = useSelector(({ administration }) => administration.stateViolationSlice.MasterState)
    const TotalCount = useSelector(({ administration }) => administration.stateViolationSlice.stateTotalCount)
    const isloading = useSelector(({ administration }) => administration.stateViolationSlice.isloading);
    const violationUploadResponse = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.violationUploadResponse);

    const [loading, setloading] = useState(false);

    useEffect(() => {
        setloading(isloading);
    }, [isloading]);

    const viewViolationState = async (value) => {
        dispatch(setLoading(true));
        await dispatch(setSelectedStateCode(value.StateCode));
        dispatch(setLoading(false));
        history.push(`/admin/states/stateViolation`);
    };

    const viewViolationClassification = async (value) => {
        dispatch(setLoading(true));
        await dispatch(setSelectedStateCode(value.StateCode));
        dispatch(setLoading(false));
        history.push(`/admin/states/violationClassification`);
    }

    function handleChangePage(event, value) {
        setPage(value);
        setloading(true);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
        setloading(true);
    }

    const handleViolationImportClick = async (value) => {
        violationDialogRef.current.handleOpen(value);
        setImportState(value.StateCode);
    }

    const ActionIcons = (n) => {
        // let x = checkData(n);

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div className="flex">
                    <Tooltip title={t("violation")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            onClick={() => viewViolationState(x)}
                            size="large"
                        >
                            <CallToActionIcon />
                        </IconButton>
                    </Tooltip>

                    <Tooltip title={t("violationClassification")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            onClick={() => viewViolationClassification(x)}
                            size="large"
                        >
                            <ListAltIcon />
                        </IconButton>
                    </Tooltip>

                    <Tooltip title={t("importViolation")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            onClick={() => handleViolationImportClick(x)}
                            size="large"
                        >
                            <ImportExportIcon />
                        </IconButton>
                    </Tooltip>
                </div >
            );
        }
    };

    const rowData = (data).map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["_id"] = item._id
            row["StateCode"] = item.StateCode ? item.StateCode : '';
            row["StateName"] = item.StateName ? item.StateName : '';
            row["action"] = ActionIcons(item)
        });
        return row;
    });

    useEffect(() => {
        if (MasterAddress !== null && MasterAddress !== undefined) {
            setData(MasterAddress);
            setTotalCount(TotalCount);
        }
    }, [MasterAddress, totalCount])

    const search = useDebounce((search, page, rowsPerPage) => {
        dispatch(getMasterState(order.id, order.direction, page * rowsPerPage, rowsPerPage,
            search === '' ? null : search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, page, rowsPerPage);
        } else {
            dispatch(getMasterState(order.id, order.direction, page * rowsPerPage, rowsPerPage,
                searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, searchText, page, rowsPerPage, order, routeParams.code]);

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 0 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };


    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    const processedData = (columnsConfig, fileDetails, mappedData) => {
        if (fileDetails) {
            var formData = new FormData();
            formData.append("file", fileDetails);
            dispatch(uploadViolationFile(formData, importState));
            setFinalData((prev) => ({ ...prev, columnsConfig, fileName: fileDetails.name, mappedData }));
        }
    }

    useEffect(() => {
        if (violationUploadResponse !== null) {
            setFinalData((prev) => ({
                ...prev,
                violationUploadData: violationUploadResponse
            }))
        }
    }, [violationUploadResponse])

    useEffect(() => {
        if (finalData !== null && violationUploadResponse !== null && violationUploadResponse !== undefined) {
            dispatch(saveExcelViolationData(finalData, importState));
            dispatch(setViolationUploadResponse(null));
        }
    }, [finalData])

    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={{ paddingTop: "50px" }}
                        >
                            <div className="flex items-center">
                                <CorporateFareIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("state")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                // defaultValue={searchText}
                                                value={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText("")}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="StateName"
                                        header={t("name")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("stateCode")}
                                        field="StateCode"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        width="300px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}

                                    />
                                </IgrGrid>
                            </div>

                        </div>
                    </div>
                }
            />

            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="ImportViolationDialog" />} onReset={() => { }} >
                <ImportViolationDialog ref={violationDialogRef} processedData={processedData} />
            </ErrorBoundary>
        </>
    );
}

export default StateTable;