import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Tooltip from '@mui/material/Tooltip';
import React from 'react';
import { useTranslation } from 'react-i18next';


const rows = [
	{
		id: 'code',
		align: 'left',
		disablePadding: false,
		label: 'CODENAME',
		sort: true
	},
	{
		id: 'description',
		align: 'left',
		disablePadding: false,
		label: 'DESCRIPTION',
		sort: true
	},
	{
		id: 'color',
		align: 'left',
		disablePadding: false,
		label: 'COLOR',
		sort: true
	},
	{
		id: 'icon',
		align: 'left',
		disablePadding: false,
		label: 'ICON',
		sort: true
	},
	{
		id: 'action',
		align: 'left',
		disablePadding: false,
		label: '',
		sort: true
	}
];

function ClassOfServiceListTableHead(props) {
	const { t } = useTranslation('laguageConfig');

	const createSortHandler = property => event => {
		props.onRequestSort(event, property);
	};


	return (
		<TableHead>
			<TableRow className="h-64">
				{rows.map(row => {
					return (
						<TableCell
							key={row.id}
							align={row.align}
							padding={row.disablePadding ? 'none' : 'default'}
							sortDirection={props.order.id === row.id ? props.order.direction : false}
						>
							{row.sort && (
								<Tooltip
									title="Sort"
									placement={row.align === 'right' ? 'bottom-end' : 'bottom-start'}
									enterDelay={300}
								>
									<TableSortLabel
										active={props.order.id === row.id}
										direction={props.order.direction}
										onClick={createSortHandler(row.id)}
									>
										{t(row.label)}
									</TableSortLabel>
								</Tooltip>
							)}
						</TableCell>
					);
				}, this)}
			</TableRow>
		</TableHead>
	);
}

export default ClassOfServiceListTableHead;
