import Typography from "@mui/material/Typography";
import Icon from "@mui/material/Icon";
import React, { useState, useEffect, useRef } from "react";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import {
    ThemeProvider, StyledEngineProvider, Paper, Input, TablePagination, Button, Tooltip, Stack,
    InputAdornment
} from "@mui/material";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import history from "@history";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import CircularProgressLoader from "src/app/main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { ErrorBoundary } from "react-error-boundary";
import ConfirmationDialog from "src/app/main/components/ConfirmationDialog/ConfirmationDialog";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import { getNavbarTheme, getRowsPerPageOptions } from "src/app/main/utils/utils";
import CommonButton from "src/app/main/SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import { getAgencyByCode } from "src/app/main/agencyPage/store/agencySlice";
import { deleteStateViolation, getViolationByState, getViolationType, setSelectedStateCode, setSelectedStateViolationColumn, setSelectedStateViolationData, setViolationList, setViolationTotalCount } from "../store/stateViolationSlice";
import CallToActionIcon from '@mui/icons-material/CallToAction';
import CancelIcon from '@mui/icons-material/Cancel';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import { useWindowResizeHeight } from "../../utils/utils";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const StateViolation = () => {
    const gridRef = useRef(null);
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const user = useSelector(({ auth }) => auth.user);
    const violationData = useSelector(({ administration }) => administration.stateViolationSlice.violationList);
    const isLoading = useSelector(({ administration }) => administration.stateViolationSlice.isloading);
    const violationTotalCount = useSelector(({ administration }) => administration.stateViolationSlice.violationTotalCount);
    const selectedStateCode = useSelector(({ administration }) => administration.stateViolationSlice.selectedStateCode);
    const stateViolationSuccess = useSelector(({ administration }) => administration.stateViolationSlice.stateViolationSuccess);
    const agencyByCode = useSelector(({ agency }) => agency.agency.agencyByCode);
    let colorCode = getNavbarTheme();
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [removeData, setRemoveData] = React.useState(null);
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState([]);
    const [totalCount, setTotalCount] = useState(violationTotalCount);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [pageIndex, setPageIndex] = useState(0);
    const [searchText, setSearchText] = useState('');
    const [state, setState] = React.useState(null);
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    const [columns, setColumns] = useState([]);

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "OffenseCode",
    });

    useEffect(() => {
        if (selectedStateCode !== null) {
            setState(selectedStateCode);
        }
    }, [selectedStateCode]);

    const getCounty = async () => {
        await dispatch(getAgencyByCode(user.data.defaultAgency));
    }

    useEffect(() => {
        if (user.data.agencyAdmin) {
            getCounty();
        }
        dispatch(getViolationType());
    }, [user, state]);

    useEffect(() => {
        if (agencyByCode !== undefined && agencyByCode !== null &&
            agencyByCode.state !== undefined && agencyByCode.state !== null &&
            agencyByCode.state !== "") {
            setState(agencyByCode.state);
        }
    }, [agencyByCode]);

    const ActionIcons = (n) => {
        // let x = checkData(n);
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div style={{ display: "flex" }}>
                    <Tooltip title={t("edit")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => handleEditClick(x)}
                            size="large"
                        >
                            <EditIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={t("delete")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => handleDeleteClick(x)}
                            size="large"
                        >
                            <DeleteIcon />
                        </IconButton>
                    </Tooltip>
                </div>
            )
        }
    };

    const search = useDebounce((search, page, rowsPerPage, state, order) => {
        dispatch(getViolationByState(order.id, order.direction, page * rowsPerPage,
            rowsPerPage, search, state));
    }, 500);

    useEffect(() => {
        if (state !== null) {
            if (searchText !== '') {
                search(searchText, pageIndex, rowsPerPage, state, order);
            } else {
                dispatch(getViolationByState(order.id, order.direction, pageIndex * rowsPerPage,
                    rowsPerPage, searchText === '' ? null : searchText, state));
            }
        }
    }, [order, pageIndex, rowsPerPage, searchText, removeData, state, stateViolationSuccess]);

    useEffect(() => {
        if (violationData !== null && violationData !== undefined) {
            setData(violationData);
            setTotalCount(violationTotalCount);
        }
    }, [violationData, violationTotalCount]);

    // Extract column names from intersectionPointData
    useEffect(() => {
        if (violationData && violationData.length > 0) {
            const keys = Object.keys(violationData[0]);
            const newColumns = keys.map((key) => ({
                field: key,
                headerText: key.charAt(0).toUpperCase() + key.slice(1),
            }));
            setColumns(newColumns);
        }
    }, [violationData]);

    const rowData = ((data !== undefined) && data.map(item => {
        const row = {};
        columns.forEach(colConfig => {
            row[colConfig.field] = item[colConfig.field];  // Use dynamic columns
        });
        row["action"] = ActionIcons(item);  // Add action column
        return row;
    }));

    const handleChangePage = (event, value) => {
        setPageIndex(value);
    }

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(event.target.value);
        setPageIndex(0);
    }

    const navigateToState = () => {
        history.push(`/admin/states`);
        setColumns([]);
        dispatch(setSelectedStateCode(null));
        dispatch(setViolationList([]));
        dispatch(setViolationTotalCount(0));
    };

    const handleDeleteClick = (violationData) => {
        let data = {
            id: violationData._id,
            stateCode: selectedStateCode,
        }
        setRemoveData(data);
        setOpen(true);
    };

    const handleEditClick = (violationData) => {
        dispatch(setSelectedStateViolationColumn(columns));
        dispatch(setSelectedStateViolationData(violationData));
        history.push(`/admin/stateViolationAdd`);
    };

    const handleConfimationDialogClick = async (newValue) => {
        if (newValue) {
            await dispatch(deleteStateViolation(removeData));
            setRemoveData(null);
        }
        setOpen(false);
    };

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 0 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            // Call the sort handler with the field name and direction
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    const createNewViolation = () => {
        dispatch(setSelectedStateViolationColumn(columns));
        dispatch(setSelectedStateViolationData(null));
        history.push(`/admin/stateViolationAdd`);
    }

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {isLoading && <CircularProgressLoader loading={isLoading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {user.data.isSuperAdmin && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => navigateToState()}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={user.data.isSuperAdmin ? {} : { paddingTop: "29px" }}
                        >
                            <div className="flex items-center">
                                <CallToActionIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {state} - {t("violation")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText("")}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div className="flex items-center justify-between">
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addStateViolation" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addCallViolation")} parentCallback={createNewViolation}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>

                                    {columns.filter((col) => col.field !== '_id' && col.field !== "Classification" && col.field !== "Default").map((col) => (
                                        <IgrColumn field={col.field}
                                            header={t(col.headerText)}
                                            resizable={true}
                                            groupable={true}
                                            sortable={true}
                                            width="250px" />
                                    ))}

                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        width="250px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>

                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={t("deleteRecord")}
                                onClose={handleConfimationDialogClick}
                                value={removeData}
                            >
                            </ConfirmationDialog>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    )
}

export default StateViolation;