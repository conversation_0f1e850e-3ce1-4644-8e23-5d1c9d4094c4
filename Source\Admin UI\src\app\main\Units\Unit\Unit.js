import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { useParams } from "react-router";
import TablePagination from "@mui/material/TablePagination";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import { newUserAudit } from "../../../main/userAuditPage/store/userAuditSlice";
import { removeUnit, getUnits } from "../../store/unitSlice";
import history from '@history';
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import CircularProgressLoader from "../../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from '../../utils/utils';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function unit() {

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';

    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();
    const routeParams = useParams();
    const gridRef = useRef(null);

    const user = useSelector(({ auth }) => auth.user);
    const UnitData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.unit.data);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.unit.isloading);
    const unitsTotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.unit.totalCount);
    const [loading, setLoading] = useState();
    const [searchText, setSearchText] = React.useState("");
    const [newUnit, setNewUnit] = React.useState(false);
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [data, setData] = React.useState(UnitData);
    const [countData, setCountData] = React.useState(unitsTotalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "unitName",
    });
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    let colorCode = getNavbarTheme();

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access Units",
                user: user,
                appName: "Admin",
            })
        );
    }, []);

    const deleteUnit = (n) => {
        setOpen(true);
        setRemoveID(n._id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeUnit(removeID, routeParams.code, pageIndex, rowsPerPage, order.id, order.direction,));
            setCountData(countData - 1);
        }
    };

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const search = useDebounce((search, pageIndex, rowsPerPage) => {
        dispatch(getUnits(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, search === '' ? null : search, routeParams.code));
        // dispatch(searchUnits(search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, pageIndex, rowsPerPage);
        } else {
            dispatch(getUnits(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, searchText, pageIndex, rowsPerPage, order, routeParams.code]);

    useEffect(() => {
        setData(UnitData);
        setCountData(unitsTotalCount)
    }, [UnitData]);

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    useEffect(() => { }, [isUpdate, newUnit]);

    const editRow = (n) => {
        history.push(`/admin/unit/${routeParams.code}/${n._id}`);
    };

    function onRowAddchange() {
        setNewUnit(!newUnit);
        setIsUpdate(false);
        history.push(`/admin/unit/${routeParams.code}/0`);
    };

    const ActionIcons = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div style={{ display: "flex" }}>
                    <Tooltip title={t("edit")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => editRow(x)}
                            size="large"
                        >
                            <EditIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={t("delete")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            // disabled={x.isActive}
                            onClick={() => deleteUnit(x)}
                            size="large"
                        >
                            <DeleteIcon />
                        </IconButton>
                    </Tooltip>
                </div>
            )
        }
    };

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    const renderPreview = (ctx) => {
        const item = ctx.dataContext;
        return (
            <img
                src={item.implicit}
                alt={item.name}
                style={{ height: '50px', width: '100px', objectFit: 'contain' }}
            />
        );
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.code}`)}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    airport_shuttle
                                </Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("unit")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addUnits" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addUnits")} parentCallback={onRowAddchange}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={countData}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="unitName"
                                        field="unitName"
                                        header={t("unitName")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="unitPositions"
                                        header={t("unitPositions")}
                                        field="unitPositions"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="unitType"
                                        header={t("unitType")}
                                        field="unitType"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="branch"
                                        header={t("branch")}
                                        field="branch"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="fileUrl"
                                        header={t("preview")}
                                        field="fileUrl"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        bodyTemplate={renderPreview}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={t("unitDeleteMsg")}
                                onClose={handleClose}
                                value={removeID}
                            >
                            </ConfirmationDialog>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    );
}

export default unit;