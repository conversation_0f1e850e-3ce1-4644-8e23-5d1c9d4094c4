import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const saveShiftTime = (data, pageIndex, pageLimit, sortField, sortDirection) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/shitTime/CreateShiftTime`, encrypt(JSON.stringify(data)))
            .then(async (response) => {
                if (response.status == 200) {
                    response.data = await JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(
                        showMessage({
                            message: response.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "right",
                            },
                            variant: "success",
                        })
                    );
                    if (data.isUpdate) {
                        dispatch(setShiftEditData(response.data.newData))
                        dispatch(setShiftTimeTotalCount(response.data.totalCount));
                    }
                    else {
                        dispatch(setShiftAddData(response.data.newData))
                        dispatch(setShiftTimeTotalCount(response.data.totalCount));
                    }
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getShiftTime = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/shitTime/getShiftDetails/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setShiftTimeTotalCount(listData.totalCount));
                    return dispatch(setShiftTimeData(listData.ShiftTimeList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const removeShift = (ID, code, pageIndex, rowsPerPage, sortField, sortDirection) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .delete(`admin/api/shitTime/removeShift/${ID}/${code}`)
            .then(async (response) => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: "top",
                            horizontal: "right",
                        },
                        variant: "success",
                    })
                    );
                    dispatch(removeShiftData(ID));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

//for searching
export const searchShift = (searchText, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        axios.get(`admin/api/shitTime/searchshitTime/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchshiftTimes(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    data: [],
    searchshiftTimes: [],
    isloading: false,
};

const shiftTimeSlice = createSlice({
    name: 'shift',
    initialState,
    reducers: {
        setShiftTimeData: (state, action) => {
            state.data = action.payload;
        },
        setSearchshiftTimes: (state, action) => {
            state.searchshiftTimes = action.payload;
        },
        setShiftTimeTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setShiftEditData: (state, action) => {
            const index = state.data.findIndex(obj => obj._id === action.payload._id);
            if (index !== -1) {
                state.data[index] = action.payload;
            }
        },
        setShiftAddData: (state, action) => {
            state.data = [...state.data, action.payload];
        },
        removeShiftData: (state, action) => {
            state.data = state.data.filter(x => x._id !== action.payload);
        },
    },
    extraReducers: {}
});

export const {
    setShiftTimeData,
    setShiftTimeTotalCount,
    setLoading,
    setSearchshiftTimes,
    setShiftEditData,
    setShiftAddData,
    removeShiftData

} = shiftTimeSlice.actions;

export default shiftTimeSlice.reducer;
