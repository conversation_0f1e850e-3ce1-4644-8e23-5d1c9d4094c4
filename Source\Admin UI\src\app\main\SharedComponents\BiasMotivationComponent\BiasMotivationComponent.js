import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../ErrorPage/ErrorPage";
import CommonAutocomplete, { handleSelectKeyDown } from "../ReuseComponents/CommonAutocomplete";
import { useTranslation } from "react-i18next";

function BiasMotivationComponent(props) {
    const { t } = useTranslation("laguageConfig");

    const handleCallBack = (event, value) => { props.handleCallBack(event, value) };

    return (
        <ErrorBoundary
            FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_biasMotivation" />} onReset={() => { }}>
            <CommonAutocomplete
                disabled={props.disabled}
                parentCallback={handleCallBack}
                options={props.biasList}
                value={props.value}
                fieldName={t("biasMotivation")}
                optionLabel={"description"}
                onKeyDown={handleSelectKeyDown} />
        </ErrorBoundary>
    );
}

export default BiasMotivationComponent;