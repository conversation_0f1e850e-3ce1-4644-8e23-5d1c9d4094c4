const mongoos = require("mongoose");
 
var locCordinatesSchema = new mongoos.Schema({
    longitude: Number,
    latitude: Number
},{ _id: false, autoIndex: false,excludeIndexes:true });
mongoos.model('cordinates', locCordinatesSchema, 'cordinates')

var PacketCallDateTimeSchema = new mongoos.Schema({
    DateTimeObj: Date,
    offset: Number
},{ _id: false, autoIndex: false,excludeIndexes:true });
mongoos.model('PacketCallDateTime', PacketCallDateTimeSchema, 'PacketCallDateTime')
 
const Packet911Schema = mongoos.Schema({
    PacketCallReceivedDT:Date,
    PacketAliTrunk: String,
    PacketDBinfo: String,
    PacketCountyID:Number,
    PacketCallProcessed:Boolean,
    PacketESN: String,
 
    PacketPositionNumber: String,
 
    PacketCallingPhone: String,
 
    PacketCallTime: String,
 
    PacketCallDate: String,

    PacketCallDateTime: {
        type:Object,
        PacketCallDateTimeSchema
    },
 
    PacketStreetNumber: String,
 
    PacketApt: String,
 
    PacketStreetAddressDir: String,
 
    PacketStreetAddress: String,
 
    PacketLocationInfo: String,
 
    PacketCity: String,
 
    PacketState: String,
 
    PacketCustomerName: String,
 
    PacketPilot: String,
 
    PacketClassofService: String,
 
    PacketAltNumber: String,
 
    PacketTelco: String,
 
    Packetx: String,
 
    Packety: String,
 
    PacketCNF: String,
 
    PacketUNC: String,
 
    PacketPD: String,
 
    PacketFD: String,
 
    PacketEMS: String,
 
    GoodOrBad: String,
 
    ParseMessage: String,
    
    loc: {
        type:Object,
        coordinates: {
            locCordinatesSchema
        }
    }
});
Packet911Schema.index({ loc: "2dsphere" });
module.exports = mongoos.model("packet911", Packet911Schema);