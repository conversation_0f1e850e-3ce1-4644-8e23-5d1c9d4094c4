// excelWorker.js

onmessage = function(e) {
    const fileData = e.data; // File data passed from main thread
    const XLSX = require('xlsx'); // Make sure XLSX is available in the worker
    const data = new Uint8Array(fileData);
    const workbook = XLSX.read(data, { type: 'array' });

    // Read the first sheet
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, raw: false });
    const sheetHeaders = jsonData[0];
    const sheetData = jsonData.slice(1).map((row) => {
        return sheetHeaders.map((_, colIndex) => row[colIndex] ?? null);
    })

    postMessage({ headers: sheetHeaders, data: sheetData });
};
