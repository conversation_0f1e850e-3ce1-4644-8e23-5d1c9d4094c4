import React, { useRef, useState } from "react";
import PropTypes from "prop-types";
import TablePagination from "@mui/material/TablePagination";
import { IgrGrid, IgrGridToolbar, IgrColumn, IgrGridModule } from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

function CommonIgrGridTable({
    data,
    totalCount,
    page,
    rowsPerPage,
    onPageChange,
    onRowsPerPageChange,
    columns,
    onSortChanged,
    actionsTemplate,
    gridProps,
    paginationProps,
    toolbarContent,
    height = "750px",
    rowHeight = 60,
}) {
    const gridRef = useRef(null);

    return (
        <div className="w-full flex flex-col">
            <div>
                <TablePagination
                    component="div"
                    count={totalCount}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={onPageChange}
                    onRowsPerPageChange={onRowsPerPageChange}
                    {...paginationProps}
                />
            </div>

            <IgrGrid
                id="grid"
                data={data}
                autoGenerate="false"
                ref={gridRef}
                height={height}
                rowHeight={rowHeight}
                onSortChanged={onSortChanged}
                {...gridProps}
            >
                {toolbarContent && <IgrGridToolbar>{toolbarContent}</IgrGridToolbar>}

                {columns.map((col, index) => (
                    <IgrColumn
                        key={index}
                        field={col.field}
                        header={col.header}
                        width={col.width || "200px"}
                        resizable={col.resizable || true}
                        groupable={col.groupable || false}
                        sortable={col.sortable || false}
                        pinned={col.pinned || false}
                        bodyTemplate={col.field === "action" ? actionsTemplate : undefined}
                    />
                ))}
            </IgrGrid>
        </div>
    );
}

CommonIgrGridTable.propTypes = {
    data: PropTypes.array.isRequired,
    totalCount: PropTypes.number.isRequired,
    page: PropTypes.number.isRequired,
    rowsPerPage: PropTypes.number.isRequired,
    onPageChange: PropTypes.func.isRequired,
    onRowsPerPageChange: PropTypes.func.isRequired,
    columns: PropTypes.arrayOf(
        PropTypes.shape({
            field: PropTypes.string.isRequired,
            header: PropTypes.string.isRequired,
            width: PropTypes.string,
            resizable: PropTypes.bool,
            groupable: PropTypes.bool,
            sortable: PropTypes.bool,
            pinned: PropTypes.bool,
        })
    ).isRequired,
    onSortChanged: PropTypes.func,
    actionsTemplate: PropTypes.func,
    gridProps: PropTypes.object,
    paginationProps: PropTypes.object,
    toolbarContent: PropTypes.node,
    height: PropTypes.string,
    rowHeight: PropTypes.number,
};

export default CommonIgrGridTable;
