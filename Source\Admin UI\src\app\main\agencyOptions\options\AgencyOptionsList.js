import FusePageCarded from '@fuse/core/FusePageCarded';
import AgencyOptionsListHeader from './AgencyOptionsListHeader';
import AgencyOptionsListTable from './AgencyOptionsListTable';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Grid from "@mui/material/Grid";
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { newUserAudit } from '../../../main/userAuditPage/store/userAuditSlice';

function AgencyOptionsList(props) {
	const user = useSelector(({ auth }) => auth.user);
	const dispatch = useDispatch();

	useEffect(() => {
		dispatch(newUserAudit({
			activity: "Access Agency",
			user: user,
			appName: "Admin",
		}));
		// eslint-disable-next-line
	}, []);

	return (
		<FusePageCarded
			classes={{
				content: "flex",
				header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
			}}
			header={
				<ErrorBoundary
					FallbackComponent={(props) => <ErrorPage {...props} componentName="AgencyOptionsListHeader" />} onReset={() => { }}>
					<AgencyOptionsListHeader />
				</ErrorBoundary>
			}
			content={
				<ErrorBoundary
					FallbackComponent={(props) => <ErrorPage {...props} componentName="AgencyOptionsListTable" />} onReset={() => { }}>
					<AgencyOptionsListTable />
				</ErrorBoundary>
			}
			innerScroll
		/>
	);
}

export default AgencyOptionsList;
