import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import SocketIo from 'socket.io-client';
import { getComments, getViewTips, updateComment } from '../../store/quikTipSlice';
import { updatePacket911Call } from '../../911Call/store/call911Slice';

function SocketIoInitialization({ tipID, code, viewTipAlert, call911app }) {
    const dispatch = useDispatch();
    const user = useSelector(({ auth }) => auth.user);
    const socketListenerUrl = process.env.REACT_APP_SOCKETCONNECTION;
    const socket = SocketIo(socketListenerUrl, { autoConnect: false, transports: ["websocket", "polling"] });

    useEffect(() => {
        const joinRoom = (room) => {
            socket.auth = { room };
            socket.emit('join', room);
            socket.connect();
        };

        // Join the appropriate socket room based on props
        if (tipID) joinRoom(tipID);
        if (viewTipAlert) joinRoom(viewTipAlert);
        if (call911app) joinRoom(call911app);

        // Socket event handlers
        const handleSocketEvents = () => {
            socket.on('autoRefreshSocketComment', data => {
                dispatch(getComments(0, 10, data.TipID, code));
            });

            socket.on('autoRefreshSocketViewTip', () => {
                dispatch(getViewTips('_id', 'desc', 0, 100, code));
            });

            socket.on('receiveCall911Alert', tipid => {
                dispatch(updatePacket911Call(tipid));
            });
        };

        handleSocketEvents();

        // Cleanup on unmount
        return () => {
            socket.off('autoRefreshSocketComment');
            socket.off('autoRefreshSocketViewTip');
            socket.off('receiveCall911Alert');
            socket.disconnect();
        };
    }, [dispatch, tipID, viewTipAlert, call911app, code, socket]);

    return null;
}

export default SocketIoInitialization;