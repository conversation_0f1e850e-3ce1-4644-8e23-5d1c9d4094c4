import React from 'react';
import {
    FormControl,
    FormLabel,
    FormGroup,
    FormControlLabel,
    Checkbox
} from '@mui/material';

const Call911AccessRightGroup = ({
    t,
    accessRights,
    handleChange,
    showAccessRight,
    isSuperAdmin,
    defaultApp
}) => {
    const accessOptions = [
        { key: 'Call', label: t('call911') },
        { key: 'FileUpload', label: t('fileUploadPreview') },
        { key: 'ClassofService', label: t('classOfService') },
    ];

    return (
        <div className="col-span-1 w-full">
            <FormControl component="fieldset">
                <FormLabel component="legend">{t('call911')}</FormLabel>
                <FormGroup>
                    {accessOptions.map(({ key, label }) => {
                        const canShow = showAccessRight[key] === key || isSuperAdmin;
                        const isDisabled = defaultApp !== 'call' && !isSuperAdmin;

                        return canShow && (
                            <FormControlLabel
                                key={key}
                                control={
                                    <Checkbox
                                        checked={accessRights[key] || false}
                                        onChange={handleChange}
                                        name={key}
                                    />
                                }
                                label={label}
                                disabled={isDisabled}
                            />
                        );
                    })}
                </FormGroup>
            </FormControl>
        </div>
    );
};

export default Call911AccessRightGroup;
