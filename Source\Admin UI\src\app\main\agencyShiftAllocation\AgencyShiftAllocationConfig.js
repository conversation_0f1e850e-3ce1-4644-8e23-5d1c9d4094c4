import i18next from 'i18next';
import React from 'react';

const ShiftAllocationDetails = React.lazy(() => import('./shiftAllocation/shiftAllocationDetails'));
const ShiftAllocationSchedule = React.lazy(() => import('./shiftAllocationSchedule/shiftAllocationSchedule'));
const ViewAndEditSchedule = React.lazy(() => import('./ViewAndEditSchedule/ViewAndEditSchedule'));

const AgencyShiftAllocationConfig = {
    settings: {
        layout: {}
    },
    routes: [
        {
            path: '/admin/ShiftAllocation/:code/:id',
            element: <ShiftAllocationDetails />
        },
        {
            path: '/admin/ShiftAllocationSchedule/:code/:shiftID/:id',
            element: <ShiftAllocationSchedule />
        },
        {
            path: '/admin/ViewAndEditSchedule/:code/:shiftID/:id',
            element: <ViewAndEditSchedule />
        },
    ]
};

export default AgencyShiftAllocationConfig;