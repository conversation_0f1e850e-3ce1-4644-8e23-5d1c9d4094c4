const mongoose = require("mongoose");
const express = require("express");
const bodyParser = require("body-parser");
const dotenv = require('dotenv');
const call911Route = require("./routes/call911Route");
var createLocaleMiddleware = require('express-locale');
var startPloyglot = require('./utils/startPolyglot.js')
const jwt = require("jsonwebtoken");
const fuseConfig = require("./models/fuseConfig.model");
const io1 = require('socket.io')();
const packet911 = require("./models/packet911");
const classOfServiceRoute = require("./routes/classOfService");
const { initClientDbConnection } = require("./dbutil");
const i18next = require('i18next');
const Backend = require('i18next-fs-backend');
const middleware = require('i18next-http-middleware');


global.window = { screen: {} }
global.document = {
  documentElement: { style: {} },
  getElementsByTagName: () => { return [] },
  createElement: () => { return {} }
}
global.navigator = { userAgent: 'nodejs', platform: 'nodejs' }

const L = require('leaflet')

dotenv.config();

var app = express();
var server = require('http').createServer(app)
var io = io1.listen(server, {
  cors: {
    origin: '*',
  }
});

global.clientConnection = initClientDbConnection();

i18next.use(Backend).use(middleware.LanguageDetector)
  .init({
    fallbackLng: 'en',
    backend: {
      loadPath: './locales/{{lng}}/translation.json'
    }
  })
app.use(createLocaleMiddleware({
  "priority": ["accept-language", "default"],
  "default": "en_US"
}))
app.use(startPloyglot.startPolyglot);
app.use(middleware.handle(i18next));

app.use(bodyParser.json());
app.use(express.json());
var cors = require('cors');

app.use(cors());

app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, PUT, POST, DELETE");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, access_token"
  );
  next();
});


app.use("/api/call", call911Route);
app.use("/api/classOfService", classOfServiceRoute);

const port = process.env.PORT || 8080;
server.listen(port, () => console.log(`listening on port ${port}...`));
