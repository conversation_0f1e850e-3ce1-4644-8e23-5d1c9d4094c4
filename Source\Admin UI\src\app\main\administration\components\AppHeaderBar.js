import React, { useState, useRef, useEffect } from 'react';
import AppBar from '@mui/material/AppBar';
import Typography from '@mui/material/Typography';
import { Toolbar, Grid, CircularProgress, IconButton, Button } from '@mui/material';
import { border } from '@mui/system';



function AppHeaderBar(props) {
    return <>
        <AppBar position="static" elevation={0}>
            <Toolbar className="px-8">
                <Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
                    {props.headerText}
                </Typography>

                <div>
                    {props.backButton}
                    {props.submitButton}
                </div>

            </Toolbar>
        </AppBar>
    </>
}


export default AppHeaderBar;