import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CardContent, } from '@mui/material';

import AdminAccessRightGroup from "./AdminAccessRightGroup"
import Call911AccessRightGroup from "./Call911AccessRightGroup"
import DispatchAccessRightGroup from "./DispatchAccessRightGroup"
import IncidentAccessRightGroup from "./IncidentAccessRightGroup"
import QuikTipAccessRightGroup from "./QuikTipAccessRightGroup"
import OtherAccessRightGroup from "./OtherAccessRightGroup"
import NCICAccessRightGroup from "./NCICAccessRightGroup"



function AccessRightsSettings(props) {
    const { t } = useTranslation("laguageConfig");
    const defaultApp = props.defaultApp;

    const { doesNotUseOldRps } = props

    const [accessRights, setAccessRights] = React.useState(
        {
            Users: false,
            Contact: false,
            Server: false,
            Icons: false,
            CallCategory: false,
            CallViolation: false,
            CallType: false,
            CallResponse: false,
            Call: false,
            FileUpload: false,
            ClassofService: false,
            Incident: false,
            Dispatch: false,
            Chat: false,
            Current: false,
            Replay: false,
            SearchFeature: false,
            Watches: false,
            BroadcastMessages: false,
            MySchedule: false,
            DeviceLicense: false,
            Units: false,
            Nfirs: false,
            Shift: false,
            Team: false,
            ShiftAllocation: false,
            Department: false,
            Mugshot: false,
            TipType: false,
            ViewTip: false,
            Notification: false,
            QuikTip: false,
            DispatchWebsite: false,
            DispatchApp: false,
            Citation: false,
            TrafficStop: false,
            TransportCall: false,
            TransportMedicalCall: false,
            SecurityCheck: false,
            PersonSearch: false,
            VehicleSearch: false,
            GunSearch: false,
            ArticleSearch: false,
            BoatSearch: false,
            IncidentReport: false,
            AccidentReport: false,
            UserAudit: false,
            EmailConfiguration: false,
            TwitterConfiguration: false,
            TwitterAccountSettings: false,
            ErrorLog: false,
            ArchiveChatHistories: false
        }
    );

    useEffect(() => {
        if (props.accessRights !== undefined && props.accessRights !== null) {
            setAccessRights(props.accessRights);
        }
        else {
            setAccessRights({
                Users: false,
                Contact: false,
                Server: false,
                Icons: false,
                CallCategory: false,
                CallViolation: false,
                CallType: false,
                CallResponse: false,
                Call: false,
                FileUpload: false,
                ClassofService: false,
                Incident: false,
                Dispatch: false,
                Chat: false,
                Current: false,
                Replay: false,
                SearchFeature: false,
                Watches: false,
                BroadcastMessages: false,
                MySchedule: false,
                DeviceLicense: false,
                Units: false,
                Nfirs: false,
                Shift: false,
                Team: false,
                ShiftAllocation: false,
                Department: false,
                Mugshot: false,
                TipType: false,
                ViewTip: false,
                Notification: false,
                QuikTip: false,
                DispatchWebsite: false,
                DispatchApp: false,
                Citation: false,
                TrafficStop: false,
                TransportCall: false,
                TransportMedicalCall: false,
                SecurityCheck: false,
                PersonSearch: false,
                VehicleSearch: false,
                GunSearch: false,
                ArticleSearch: false,
                BoatSearch: false,
                IncidentReport: false,
                AccidentReport: false,
                UserAudit: false,
                EmailConfiguration: false,
                TwitterConfiguration: false,
                TwitterAccountSettings: false,
                ErrorLog: false,
                ArchiveChatHistories: false,
                IncidentApp: false,
                IncidentWebsite: false
            })
        }
    }, [props.accessRights])

    const handleChange = (event) => {
        const data = {
            name: event.target.name,
            cheked: event.target.checked
        }
        //setAccessRights({ ...accessRights, [event.target.name]: event.target.checked });
        props.parentAgencyAceessRightCallback(data);
        event.preventDefault();
    };

    return (
        <div style={{ padding: "15px" }}>
            <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-7">
                    <AdminAccessRightGroup
                        t={t}
                        accessRights={accessRights}
                        handleChange={handleChange}
                        showAccessRight={props.showAccessRight}
                        isSuperAdmin={props.isSuperAdmin}
                        defaultApp={defaultApp}
                    />

                    <Call911AccessRightGroup
                        t={t}
                        accessRights={accessRights}
                        handleChange={handleChange}
                        showAccessRight={props.showAccessRight}
                        isSuperAdmin={props.isSuperAdmin}
                        defaultApp={defaultApp}
                    />

                    {!doesNotUseOldRps &&
                        <DispatchAccessRightGroup
                            t={t}
                            accessRights={accessRights}
                            handleChange={handleChange}
                            showAccessRight={props.showAccessRight}
                            isSuperAdmin={props.isSuperAdmin}
                            defaultApp={defaultApp}
                            doesNotUseOldRps={doesNotUseOldRps}
                        />
                    }

                    <IncidentAccessRightGroup
                        t={t}
                        accessRights={accessRights}
                        handleChange={handleChange}
                        showAccessRight={props.showAccessRight}
                        isSuperAdmin={props.isSuperAdmin}
                        defaultApp={defaultApp}
                    />




                    <QuikTipAccessRightGroup
                        t={t}
                        accessRights={accessRights}
                        handleChange={handleChange}
                        showAccessRight={props.showAccessRight}
                        isSuperAdmin={props.isSuperAdmin}
                        defaultApp={defaultApp}
                        doesNotUseOldRps={doesNotUseOldRps}
                    />

                    <OtherAccessRightGroup
                        t={t}
                        accessRights={accessRights}
                        handleChange={handleChange}
                        showAccessRight={props.showAccessRight}
                        isSuperAdmin={props.isSuperAdmin}
                        defaultApp={defaultApp}
                        doesNotUseOldRps={doesNotUseOldRps}
                    />


                    <NCICAccessRightGroup
                        t={t}
                        accessRights={accessRights}
                        handleChange={handleChange}
                        showAccessRight={props.showAccessRight}
                        isSuperAdmin={props.isSuperAdmin}
                        defaultApp={defaultApp}
                    />


                </div>
            </CardContent>
        </div>
    );
}

export default AccessRightsSettings;