import React, { useState } from 'react';
import { ReactSearchAutocomplete } from 'react-search-autocomplete';
import { useDebounce } from "@fuse/hooks";
import { useSelector } from "react-redux";
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';

const AutoCompleteByList = ({
    dataList = [], // List of items passed as a prop
    nameFormatter, // Default formatter
    onSelect,
    placeholder,
    debounceTime = 500,
    formatResult,
    handleClear,
}) => {
    const [items, setItems] = useState([]);
    const navbarTheme = useSelector(selectNavbarTheme);

    // Local search with debounce
    const search = useDebounce((searchString) => {
        if (!searchString) {
            setItems([]);
            return;
        }
        // Filter the list based on the search string
        const filteredItems = dataList.filter((item) =>
            nameFormatter(item).toLowerCase().includes(searchString.toLowerCase())
        );
        setItems(
            filteredItems.map((item) => ({
                ...item,
                name: nameFormatter(item),
            }))
        );
    }, debounceTime);

    const handleOnSearch = (string) => {
        search(string);
    };

    const handleOnClear = () => {
        setItems([]);
        handleClear();
    };

    const handleOnSelect = (item) => {
        onSelect(item);
    };

    return (
        <ReactSearchAutocomplete
            items={items}
            onSearch={handleOnSearch}
            onClear={handleOnClear}
            onSelect={handleOnSelect}
            autoFocus={true}
            formatResult={formatResult}
            styling={{
                border: "1px solid #ccc",
                borderRadius: "8px",
                backgroundColor: navbarTheme.palette.mode === 'light' ? 'white' : 'black',
                boxShadow: "none",
                hoverBackgroundColor: navbarTheme.palette.secondary.main,
                color: navbarTheme.palette.mode === 'light' ? 'black' : 'white',
                fontSize: "15px",
                iconColor: "grey",
                lineColor: "grey",
                placeholderColor: navbarTheme.palette.primary.light,
                clearIconMargin: "3px 8px 0 0",
                zIndex: 4,
                cursor: 'text',
                height: "45px"
            }}
            placeholder={placeholder}
        />
    );
};

export default AutoCompleteByList;
