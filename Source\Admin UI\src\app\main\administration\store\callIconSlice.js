import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import { decrypt } from '../../../security/crypto';
import axios from 'axios';

export const uploadCallIcon = (fileData, CallType) => async dispatch => {
	try {
		await axios.post('fileupload/upload/CallIcons', fileData, {
			headers: {
				'Content-Type': 'multipart/form-data'
				//Authorization: `Bearer ${localStorage.getItem('jwt_access_token')}`
			}
		})
			.then(async response => {
				setUPLOADSUCCESS(true)
				if (response.data) {
					await axios.post('admin/api/dispatchUpload', {
						callType: CallType,
						iconURL: response.data.Location
					}).then(res => {
						res = JSON.parse(decrypt(res.data));
						dispatch(
							showMessage({
								message: res.message,
								autoHideDuration: 2000,
								anchorOrigin: {
									vertical: 'top',
									horizontal: 'right'
								},
								variant: 'success'
							}))
					})
				}
			})
	} catch (e) {
		return console.error(e.message);
	}
}

export const getDispatchIcons = () => async dispatch => {
//check
	await axios.get('admin/api/dispatchUpload').then(res => {
		dispatch(setIconsData(JSON.parse(decrypt(res.data))));
	})
}

const initialState = {
	success: false,
	iconsData: [],
};

const callIconUploadSlice = createSlice({
	name: 'administration/dispatchCallIcon',
	initialState,
	reducers: {
		setUPLOADSUCCESS: (state, action) => {

			state.success = action.payload;
		},
		setIconsData: (state, action) => {

			state.iconsData = action.payload;
		}
	},
	extraReducers: {}
});

export const {
	setUPLOADSUCCESS,
	setIconsData
} = callIconUploadSlice.actions;

export default callIconUploadSlice.reducer;
