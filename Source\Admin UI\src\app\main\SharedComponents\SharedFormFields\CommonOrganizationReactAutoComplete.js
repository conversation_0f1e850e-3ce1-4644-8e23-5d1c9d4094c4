import React, { useEffect, useState } from 'react';
import { ReactSearchAutocomplete } from 'react-search-autocomplete';
import axios from 'axios';
import { decrypt, encrypt } from "src/app/security";
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import { useSelector } from "react-redux";
import { useTranslation } from 'react-i18next';

const CommonOrganizationReactAutoComplete = ({
    onSelect,
    handleClear,
}) => {

    const [items, setItems] = useState([]);
    const { t } = useTranslation("laguageConfig");
    const navbarTheme = useSelector(selectNavbarTheme);

    const nameFormatter = (item) => {
        const parts = [
            item.BusinessName?.trim(),
            item.StreetNumber?.trim(),
            item.Dir?.trim(),
            item.StreetType?.trim(),
            item.City?.trim(),
            item.State?.trim(),
            item.Zip?.trim(),
            item.PhoneNumber?.trim()
        ];

        // Filter out empty strings or falsy values
        const filteredParts = parts.filter(Boolean);

        // Join all parts with a space
        return filteredParts.join(', ');
    };

    const search = (async (search) => {
        try {
            // Construct dynamic URL 
            const body = {
                searchText: search || "",
            };
            const dynamicUrl = `incident/api/incidentOrganisation/searchOrganization`;
            const response = await axios.post(dynamicUrl, encrypt(JSON.stringify(body)), {
                headers: {
                    "Content-Type": "application/json",
                },
            });
            if (response.status === 200) {
                const decryptedData = await JSON.parse(decrypt(response.data));
                const data = await decryptedData.map((item) => ({
                    ...item,
                    name: nameFormatter(item), // Use dynamic name formatter
                }));
                setItems(data);
            } else {
                const decryptedData = JSON.parse(decrypt(response.response.data));
                setItems([]);
            }
        } catch (error) {
            setItems([]);
            console.error("Error fetching data:", error);
        }
    });

    const handleOnSearch = (string) => {
        // Check if the input contains a slash ("/")
        if (string.includes("/")) {
            const searchTerms = string.split("/").map(term => term.trim()); // Split and trim spaces
            search(searchTerms.join(",")); // Join with a comma for API request
        } else {
            search(string);
        }
    };

    const handleOnHover = (result) => {
        console.log(result);
    };

    const handleOnSelect = (item) => {
        onSelect(item);
    };

    const handleOnClear = () => {
        setItems([]);
        handleClear();
    };

    const formatResult = (item) => {
        return (
            <table style={{ width: "100%", borderCollapse: "collapse", tableLayout: "fixed" }}>
                <tbody>
                    <tr>
                        <td
                            style={{
                                fontWeight: "bold",
                                textAlign: "left",
                                padding: "8px",
                                width: "200px", // Set a fixed width
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                            }}
                        >
                            {item.name}
                        </td>
                    </tr>
                </tbody>
            </table>
        );
    };

    //For removing the auto suggestion
    const onFocus = (event) => {
        event.target.setAttribute('autocomplete', 'new-password');
    };

    return (
        <ReactSearchAutocomplete
            items={items}
            inputDebounce={700}
            onSearch={handleOnSearch}
            onClear={handleOnClear}
            onHover={handleOnHover}
            onSelect={handleOnSelect}
            autoFocus={true}
            onFocus={onFocus}
            formatResult={formatResult}
            maxResults={100}
            styling={{
                border: "1px solid #ccc",
                borderRadius: "4px",
                backgroundColor: navbarTheme.palette.mode === 'light' ? 'white' : 'black',
                boxShadow: "none",
                hoverBackgroundColor: navbarTheme.palette.secondary.main,
                color: navbarTheme.palette.mode === 'light' ? 'black' : 'white',
                fontSize: "15px",
                iconColor: "grey",
                lineColor: "grey",
                placeholderColor: navbarTheme.palette.primary.light,
                clearIconMargin: "3px 8px 0 0",
                zIndex: 4,
                cursor: 'text',
                height: "40px",
            }}
            placeholder={t("searchOrganizations")}
        />
    );
}

export default CommonOrganizationReactAutoComplete;