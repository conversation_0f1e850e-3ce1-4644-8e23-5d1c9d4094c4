import FuseScrollbars from "@fuse/core/FuseScrollbars";
import _ from "@lodash";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableRow from "@mui/material/TableRow";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import withRouter from "@fuse/core/withRouter";
import { Link } from "react-router-dom";

import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import history from "@history";
import makeStyles from "@mui/styles/makeStyles";
import { getAgencyOptions } from "../../../main/utils/utils";
import { useParams } from "react-router";
import Paper from "@mui/material/Paper";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { selectMainTheme } from "app/store/fuse/settingsSlice";

function AgencyOptionsListTable(props) {
  const dispatch = useDispatch();
  const routeParams = useParams();
  const options = getAgencyOptions(routeParams.code);
  const { t } = useTranslation("laguageConfig");
  const mainTheme = useSelector(selectMainTheme);

  return (
    <div className="w-full flex flex-col">
      <FuseScrollbars className="flex-grow overflow-x-auto">
        <Grid
          container
          spacing={4}
          style={{ display: "flex", paddingTop: "24px" }}
        >
          {options.map((item) => (
            <Grid item xs={2} onClick={() => history.push(item.url)} >
              <Box
                sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  '& > :not(style)': {
                    m: 5,
                    width: 300,
                    height: 200,
                  },
                }}

              >
                <Paper
                  className="board flex flex-col items-center justify-center cursor"

                  style={{ display: "flex", paddingTop: "24px", cursor: 'pointer' }}

                  elevation={3}
                >
                  <Tooltip title={t(item.title)}>
                    <Grid style={{ display: "flex" }}>
                      <IconButton
                        aria-label="tty"
                        style={{color: mainTheme.palette.mode === 'light'? 'black': 'white'}}
                        size="large"
                        value={item.iconName}
                      >
                        {item.iconName}
                      </IconButton>
                    </Grid>
                  </Tooltip>
                  <Grid>{t(item.title)}</Grid>
                </Paper>
              </Box>
            </Grid>
          ))}
        </Grid>
      </FuseScrollbars>
    </div>
  );
}

export default withRouter(AgencyOptionsListTable);
