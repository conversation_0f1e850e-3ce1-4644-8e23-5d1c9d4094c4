import "./911Call.css";
import React, { useState, useEffect, forwardRef, useRef, useImperativeHandle } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageSimple from "@fuse/core/FusePageSimple";
import { makeStyles } from "@mui/styles";
import { useTranslation } from "react-i18next";
import Grid from "@mui/material/Grid";
import "video-react/dist/video-react.css";
import { isMobile } from "react-device-detect";
import { Backdrop } from "@mui/material";
import FuseLoading from "@fuse/core/FuseLoading";
import ErrorPage from "../SharedComponents/ErrorPage/ErrorPage";
import { ErrorBoundary } from "react-error-boundary";

import CallListComponet from "./components/CallListComponet";
import Call911MapComponent from "./components/Call911MapComponent";
import GroupAction from "./components/GroupAction";
import { SetCurrrentData, getCallTypes, getPacket911CallList } from "./store/call911Slice";

const useStyles = makeStyles((theme) => ({
  layoutRoot: {},
  root: {
    maxWidth: 345,
    flexGrow: 1,
  },
  expand: {
    transform: "rotate(0deg)",
    marginLeft: "auto",
    transition: theme.transitions.create("transform", {
      duration: theme.transitions.duration.shortest,
    }),
  },
  expandOpen: {
    transform: "rotate(180deg)",
  },
  dividerFullWidth: {
    margin: `5px 0 0 ${theme.spacing(2)}px`,
  },
  media: {
    width: "30%",
    paddingTop: "30%",
    height: 0,
    marginLeft: "15%",
  },
  cardRoot: {
    minWidth: 275,
    borderRadius: '0%'
  },
  title: {
    fontSize: 18,
    fontStyle: "bold",
    textAlign: "center",
  },
  formControl: {
    margin: theme.spacing(0.5),
    width: "100%",
  },
  selectEmpty: {
    marginTop: theme.spacing(2),
  },
  rootTab: {
    flexGrow: 1,
    width: "100%",
    backgroundColor: theme.palette.background.paper,
  },
  backdrop: {
    zIndex: theme.zIndex.drawer + 1,
    color: "#fff",
  },
}));

function Call911(props) {
  const classes = useStyles(props);
  const dispatch = useDispatch();
  const { t } = useTranslation("laguageConfig");
  const [countyValue, setCounty] = React.useState(0);
  const selectedCallData = useSelector(({ call911 }) => call911.call911.selectedCall);
  const packet911Calls = useSelector(({ call911 }) => call911.call911.data);
  const loader = useSelector(({ call911 }) => call911.call911.loader);
  const [callData, setData] = useState(packet911Calls);
  const [newCallOpen, setNewCallOpen] = React.useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [current, setCurrent] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [selectedAddress, setSelectedAddress] = useState([]);
  const [refreshMap, setRefreshMap] = useState(false);

  useEffect(() => {
    if (selectedCallData._id) {
      setRefreshMap(true)
      setSelectedCard(selectedCallData._id);
    }
  }, [selectedCallData]);

  useEffect(() => {
    dispatch(getCallTypes())
  }, []);

  function setSelectedCardValue(value) {
    setSelectedCard(value)
  }

  function setNearestAddress(value) {
    setSelectedAddress(value)
  }

  function setNewCallOpenValue(value) {
    setNewCallOpen(value)
  }

  function setNewCurrentValue(value) {
    setCurrent(value)
  }





  return (
    (
      <FusePageSimple
        className="cardborderclass"
        classes={{
          root: classes.layoutRoot,
        }}
        content={
          <div className="p-0">
            <Backdrop className={classes.backdrop} open={loader}>
              <FuseLoading variant="determinate" />
            </Backdrop>
            {isMobile ? (
              <div >
                {/* This UI code is used for Mobile Screen */}
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="CallListComponet" />}
                      onReset={() => { }} >
                      <CallListComponet
                        current={current}
                        classes={classes}
                        newCallOpen={newCallOpen}
                        setNearestAddress={setNearestAddress}
                        setNewCallOpen={setNewCallOpenValue}
                        setData={setData}
                        setCurrent={setCurrent}
                        hasMore={hasMore}
                        setHasMore={setHasMore}
                        selectedCard={selectedCard}
                        setSelectedCard={setSelectedCardValue}
                        setCounty={setCounty}
                        countyValue={countyValue}
                      ></CallListComponet>
                    </ErrorBoundary>

                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="Call911MapComponent" />}
                      onReset={() => { }} >
                      <Call911MapComponent
                        classes={classes}
                        callData={callData}
                        current={current}
                        setCurrent={setCurrent}
                        hasMore={hasMore}
                        setHasMore={setHasMore}
                        selectedAddress={selectedAddress}
                        newCallOpen={newCallOpen}
                        selectedCard={selectedCard}
                        setNewCallOpen={setNewCallOpenValue}
                        setCounty={setCounty}
                        setSelectedCard={setSelectedCardValue}
                      ></Call911MapComponent>
                    </ErrorBoundary>

                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="GroupAction" />}
                      onReset={() => { }} >
                      <GroupAction classes={classes}></GroupAction>
                    </ErrorBoundary>
                  </Grid>
                </Grid>
                {/* ------------------------------------- */}
              </div>
            ) : (
              <div>
                {/* This UI code is used for Large Screen */}
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="CallListComponet" />}
                      onReset={() => { }} >
                      <CallListComponet
                        current={current}
                        classes={classes}
                        newCallOpen={newCallOpen}
                        setNearestAddress={setNearestAddress}
                        setNewCallOpen={setNewCallOpenValue}
                        setData={setData}
                        setCurrent={setCurrent}
                        hasMore={hasMore}
                        setHasMore={setHasMore}
                        selectedCard={selectedCard}
                        setSelectedCard={setSelectedCardValue}
                        setCounty={setCounty}
                        countyValue={countyValue}
                        packet911Calls={packet911Calls}
                        setNewCurrentValue={setNewCurrentValue}
                      ></CallListComponet>
                    </ErrorBoundary>
                  </Grid>
                  <Grid item xs={12} sm={6} md={6} lg={6} xl={6}>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="Call911MapComponent" />}
                      onReset={() => { }} >
                      <Call911MapComponent
                        classes={classes}
                        callData={callData}
                        current={current}
                        setCurrent={setCurrent}
                        setHasMore={setHasMore}
                        selectedAddress={selectedAddress}
                        setNearestAddress={setNearestAddress}
                        hasMore={hasMore}
                        newCallOpen={newCallOpen}
                        selectedCard={selectedCard}
                        setNewCallOpen={setNewCallOpenValue}
                        setCounty={setCounty}
                        setNewCurrentValue={setNewCurrentValue}
                        refreshMap={refreshMap}
                        setSelectedCard={setSelectedCardValue}
                      ></Call911MapComponent>
                    </ErrorBoundary>
                  </Grid>
                  <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                    <ErrorBoundary
                      FallbackComponent={(props) => <ErrorPage {...props} componentName="GroupAction" />}
                      onReset={() => { }} >
                      <GroupAction classes={classes}></GroupAction>
                    </ErrorBoundary>
                  </Grid>
                </Grid>
                {/* ------------------------------------- */}
              </div>
            )}
          </div>
        }
      />
    )
  );
}

export default Call911;