import React, { forwardRef, useRef, useImperativeHandle, useEffect, } from "react";
import { useDispatch } from "react-redux";
// import { TextFieldform } from '@fuse/core/form';
import { Controller, useForm } from "react-hook-form";
import { TextField } from "@mui/material";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
// import form from 'form-react';
import Grid from "@mui/material/Grid";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import "./AddPerson.css";
import InvolvementTags from "../InvolvementTags/InvolvementTags";

import { checkValueEmptyOrNull } from "../../utils/utils";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { getTypes } from "../../agencyPage/store/agencySlice";

function AddPerson(props) {
    const { t } = useTranslation("laguageConfig");
    useEffect(() => {
        if (person.length > 0) {
            setControlValue(person[0]);
        } else if (editData === null || editData === undefined) {
            ClearAll();
        }
    }, [person]);

    useEffect(() => {
        if (editData !== null && editData !== undefined) {
            setControlValue(editData);
        }
    }, [editData]);

    useEffect(() => {
        dispatch(getTypes());
    }, []);

    const onChangePhoneType = (event) => {
        setPhoneNumTypeValue(event.target.value);
    };

    const onChangeEmailType = (event) => {
        setEmailTypeValue(event.target.value);
    };

    const onChangeIdType = (event) => {
        setidTypeValue(event.target.value);
    };



    const setControlValue = (data) => {

        setPersonidValue(data._id);
        setValue("LName", data.LastName);
        setValue("FName", data.FirstName);
        setValue("MName", data.MiddleName);
        setValue("Suffix", data.Suffix);
        setValue("AddrNumber", data.AddrNumber);
        setValue("NumSuffix", data.NumSuffix);
        setValue("PreDir", data.PreDir);
        setValue("StreetName", data.StreetName);
        setValue("StreetType", data.StreetType);
        setValue("PostDir", data.PostDir);
        setValue("BuildingNbr", data.BuildingNbr);
        setValue("UnitType", data.UnitType);
        setValue("UnitID", data.UnitID);
        setValue("BuildingName", data.BuildingName);
        setValue("Floor", data.Floor);
        setValue("CityName", data.City);
        setValue("State", data.State);
        setValue("Zip", data.Zip);
        setValue("ZIPPlus4", data.ZIPPlus4);
        setValue("DateOfBirth", data.DateOfBirth);
        setValue("Age", data.Age);
        setValue("Race", data.Race);
        setValue("Sex", data.Sex);
        setValue("Ethnicity", data.Ethnicity);
        setValue("Height", data.Height);
        setValue("Weight", data.Weight);
        setValue("Hair", data.Hair);
        setValue("Eyes", data.Eyes);
        setValue("PhoneNumber", data.PhoneNumber);
        setValue("IdNumber", data.IdNumber);
        setValue("Email", data.Email);

        setidTypeValue(data.IdType);
        setEmailTypeValue(data.EmailType);
        setPhoneNumTypeValue(data.PhoneNumberType);
    };

    const ClearAll = () => {
        setValue("LName", "");
        setValue("FName", "");
        setValue("MName", "");
        setValue("Suffix", "");
        setValue("AddrNumber", "");
        setValue("NumSuffix", "");
        setValue("PreDir", "");
        setValue("StreetName", "");
        setValue("StreetType", "");
        setValue("PostDir", "");
        setValue("BuildingNbr", "");
        setValue("UnitType", "");
        setValue("UnitID", "");
        setValue("BuildingName", "");
        setValue("Floor", "");
        setValue("CityName", "");
        setValue("State", "");
        setValue("Zip", "");
        setValue("ZIPPlus4", "");
        setValue("DateOfBirth", "");
        setValue("Age", "");
        setValue("Race", "");
        setValue("Sex", "");
        setValue("Ethnicity", "");
        setValue("Height", "");
        setValue("Weight", "");
        setValue("Hair", "");
        setValue("Eyes", "");
        setValue("PhoneNumber", "");
        setValue("IdNumber", "");
        setValue("Email", "");

        setidTypeValue("");
        setEmailTypeValue("");
        setPhoneNumTypeValue("");
    };

    const onSubmit = (model) => {

        let PersonData = {
            PersonId: personIdValue,
            LName: checkValueEmptyOrNull(model.LName),
            FName: checkValueEmptyOrNull(model.FName),
            MName: checkValueEmptyOrNull(model.MName),

            Suffix: checkValueEmptyOrNull(model.Suffix),
            //AddrName: model.AddrName,
            AddrNumber: checkValueEmptyOrNull(model.AddrNumber),
            NumSuffix: checkValueEmptyOrNull(model.NumSuffix),
            PreDir: checkValueEmptyOrNull(model.PreDir),
            StreetName: checkValueEmptyOrNull(model.StreetName),
            StreetType: checkValueEmptyOrNull(model.StreetType),
            PostDir: checkValueEmptyOrNull(model.PostDir),
            BuildingNbr: checkValueEmptyOrNull(model.BuildingNbr),
            UnitType: checkValueEmptyOrNull(model.UnitType),
            UnitID: checkValueEmptyOrNull(model.UnitID),
            BuildingName: checkValueEmptyOrNull(model.BuildingName),
            Floor: checkValueEmptyOrNull(model.Floor),
            CityName: checkValueEmptyOrNull(model.CityName),
            State: checkValueEmptyOrNull(model.State),
            ZIP: checkValueEmptyOrNull(model.Zip),
            ZIPPlus4: checkValueEmptyOrNull(model.ZIPPlus4),
            DOB: checkValueEmptyOrNull(model.DateOfBirth),
            Age: checkValueEmptyOrNull(model.Age),
            Race: checkValueEmptyOrNull(model.Race),
            Sex: checkValueEmptyOrNull(model.Sex),
            Ethnicity: checkValueEmptyOrNull(model.Ethnicity),
            Height: checkValueEmptyOrNull(model.Height),
            Weight: checkValueEmptyOrNull(model.Weight),
            Hair: checkValueEmptyOrNull(model.Hair),
            Eyes: checkValueEmptyOrNull(model.Eyes),

            IdType: checkValueEmptyOrNull(idTypeValue),
            EmailType: checkValueEmptyOrNull(emailTypeValue),
            PhoneNumberType: checkValueEmptyOrNull(phoneNumberTypeTypeValue),

            PhoneNumber: checkValueEmptyOrNull(model.PhoneNumber),
            IdNumber: checkValueEmptyOrNull(model.IdNumber),
            Email: checkValueEmptyOrNull(model.Email),

            involTagsData: involTagsArray,
            incidentid: selectedIncidentCall._id,
            code: props.code,
        };

    };

    const InvolvementTagCall = (value) => {

        involTagsArray = [];
        involTagsArray = value;
    };

    return (
        <div>
            <Grid container spacing={1}> <Grid item xs={12} sm={9} md={9} lg={9} xl={9}>
                <Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={3} lg={3} xl={3}>
                        <Controller
                            name="LName"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label={t("lastName")}
                                    variant="outlined"
                                //required
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={3} lg={3} xl={3}>
                        <Controller
                            name="FName"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label={t("firstName")}
                                    variant="outlined"
                                //required
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={3} lg={3} xl={3}>
                        <Controller
                            name="MName"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label={t("middleName")}
                                    variant="outlined"
                                //required
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Suffix"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label={t("suffix")}
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>
                <Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="AddrNumber"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Addr Number"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>

                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="NumSuffix"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Num Suffix"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="PreDir"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Pre Direction"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="StreetName"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Street Name"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="StreetType"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Street Type"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="PostDir"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Post Direction"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>
                <Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="BuildingNbr"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Building Nbr"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="UnitType"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Unit Type"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="UnitID"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Unit ID"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={3} lg={3} xl={3}>
                        <Controller
                            name="BuildingName"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Building Name"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>

                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Floor"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Floor"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>

                <Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
                        <Controller
                            name="CityName"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="City Name"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="State"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="State"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Zip"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="ZIP"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="ZIPPlus4"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="ZIP Plus 4"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>

                <Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={3} lg={3} xl={3}>
                        <Controller
                            name="DateOfBirth"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Date Of Birth"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Age"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Age"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Race"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Race"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Sex"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Sex"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={3} lg={3} xl={3}>
                        <Controller
                            name="Ethnicity"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Ethnicity"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>

                <Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Height"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Height"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Weight"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Weight"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>{" "}
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Hair"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Hair"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={12} sm={12} md={2} lg={2} xl={2}>
                        <Controller
                            name="Eyes"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Eyes"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>

                <Grid Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>

                        <FormControl
                            variant="outlined"
                            className={classes.formControl}
                        >
                            <InputLabel id="demo-simple-select-helper-label">
                                Phone Type
                            </InputLabel>
                            <Select
                                labelId="demo-simple-select-helper-label"
                                id="demo-simple-select-helper"
                                label="PhoneType"

                                value={phoneNumberTypeTypeValue}
                                onChange={onChangePhoneType}
                            >
                                {incident.Phonedata &&
                                    incident.Phonedata.map((item, index) => (
                                        <MenuItem value={item.name}>{item.name}</MenuItem>
                                    ))}
                            </Select>
                        </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
                        <Controller
                            name="PhoneNumber"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Phone Number"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>

                <Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>

                        <FormControl
                            variant="outlined"
                            className={classes.formControl}
                        >
                            <InputLabel id="demo-simple-select-helper-label">
                                Id Type
                            </InputLabel>
                            <Select
                                labelId="demo-simple-select-helper-label"
                                id="demo-simple-select-helper"
                                label="IdType"

                                value={idTypeValue}
                                onChange={onChangeIdType}
                            >
                                {incident.Iddata &&
                                    incident.Iddata.map((item, index) => (
                                        <MenuItem value={item.name}>{item.name}</MenuItem>
                                    ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>
                        <Controller
                            name="IdNumber"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="ID Number"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>

                <Grid container spacing={1} className="mb-8">
                    <Grid item xs={12} sm={12} md={4} lg={4} xl={4}>

                        <FormControl
                            variant="outlined"
                            className={classes.formControl}
                        >
                            <InputLabel id="demo-simple-select-helper-label">
                                Email Type
                            </InputLabel>
                            <Select
                                labelId="demo-simple-select-helper-label"
                                id="demo-simple-select-helper"
                                label="EmailType"

                                value={emailTypeValue}
                                onChange={onChangeEmailType}
                            >
                                {incident.Emaildata &&
                                    incident.Emaildata.map((item, index) => (
                                        <MenuItem value={item.name}>{item.name}</MenuItem>
                                    ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={12} md={6} lg={6} xl={6}>
                        <Controller
                            name="Email"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="w-full"
                                    type="text"
                                    label="Email"
                                    variant="outlined"
                                />
                            )}
                        />
                    </Grid>
                </Grid>
            </Grid>
                <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="InvolvementTags" />} onReset={() => { }} >
                        <InvolvementTags
                            involvmentarray={InvolvementTagCall}
                            value={involTagsArrayData}
                            checkdata={checkdata}
                        />
                    </ErrorBoundary>
                </Grid>
            </Grid>

        </div>
    );
};
export default AddPerson;
