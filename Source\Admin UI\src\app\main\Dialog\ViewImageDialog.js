import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Formsy from 'formsy-react';
import Grid from '@mui/material/Grid';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import "./dialogStyle.css";
import { object } from 'prop-types';

const controlStyle = { padding: 0, width: '850px', height: '500px', objectFit: "contain" };
const controlStyleAudio = { padding: 0, width: '305px', height: '50px', };

const ViewImageDialog = forwardRef((props, ref) => {
  const { t } = useTranslation('dialogPage');
  const [open, setOpen] = React.useState(false);
  const [Data, setData] = React.useState('');
  const [Type, setType] = React.useState('');
  const [Name, setName] = React.useState('');
  const [Description, setDescription] = React.useState('');
  const handleClickOpen = () => {
    setOpen(true);
  };
  useImperativeHandle(ref, () => ({
    openCitation(data, type, name, description) {
      setData(data);
      setType(type);
      setName(name);
      setDescription(description)
      handleClickOpen()
    }
  }))
  const handleClose = () => {
    setOpen(false);
  };
  const formRef = useRef(null);

  return (
    <div>
      <Dialog
        fullWidth={true}
        maxWidth='md'
        open={open}
        onClose={handleClose}
        aria-labelledby="responsive-dialog-title"
      >
        <IconButton
          onClick={handleClose}
          aria-label="show more"
          className="closeButton"
        >
          <Icon>close</Icon>
        </IconButton>
        <DialogTitle id="responsive-dialog-title"><h1><b>{Name}</b></h1></DialogTitle>
        <DialogTitle id="responsive-dialog-title">{Description}</DialogTitle>
        <DialogContent dividers>
          <Formsy
            ref={formRef}
          >
            <Grid container spacing={1}>
              <Grid item xs={12} sm={12} md={8} lg={6} xl={12} className="pb-4" style={{ minHeight: '300px' }}>
                <div className='card' style={{ padding: '0px' }}>
                  {Type.includes('image') &&
                    <img src={Data} style={controlStyle}></img>
                  }
                  {/* <h1>How to disable downloading of the PDF document</h1> */}
                  {Type.includes('pdf') &&
                    <iframe src={Data} style={controlStyle} title="iframe" type="application/pdf"></iframe>

                  }
                  {Type.includes('video') &&
                    <video src={Data} controls style={controlStyle}></video>
                  }
                  {Type.includes('audio') &&
                    <audio src={Data} controls style={controlStyleAudio}></audio>
                  }
                </div>

              </Grid>
            </Grid>
          </Formsy>
        </DialogContent>
      </Dialog>
    </div>
  );
});
export default ViewImageDialog;

