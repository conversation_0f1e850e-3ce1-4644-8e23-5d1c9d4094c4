import { TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { useRef } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { Controller, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import CommonAutocomplete, {
  handleSelectKeyDown,
} from "src/app/main/SharedComponents/ReuseComponents/CommonAutocomplete";
import FormTextField from "src/app/main/SharedComponents/SharedFormFields/FormTextField";
import ZoneFields from "./ZoneFields";
import { DesktopDatePicker } from "@mui/x-date-pickers";
import LegacyStreetNameFields from "./LegacyStreetNameFields";
import AliasStreetNameFields from "./AliasStreetNameFields";

const AddressInfoFields = ({
  cityPostalComm,
  country,
  countryList,
  county,
  preDirection,
  filteredCityList,
  filteredCountyList,
  filteredZipCodeList,
  handleAutocompleteChange,
  handleStateChange,
  handleZipPostCode,
  placeType,
  placeTypeList,
  postDir,
  postMod,
  preMod,
  preSep,
  preType,
  state,
  stateList,
  streetNamePreTypeSeparatorsList,
  streetNamePreTypesPostTypes,
  streetPostModifierList,
  streetPredirectionAndPostDirectionList,
  streetPreModifierList,
  streetType,
  zipPostCode,
  preDir,
  legacyPostDir,
  legacyStreetType,
  aliasStreetType,
  aliasPostDir,
  aliasPreDir,
}) => {
  const { t } = useTranslation("laguageConfig");
  const textFieldRef = useRef(null);

  const {
    control,
    formState: { errors },
  } = useFormContext();

  const handleKeyDown = (event) => {
    if (event.key === "Tab") {
      event.preventDefault();
      textFieldRef.current.children[1].children[0].focus();
    }
  };

  return (
    <Box
      component="fieldset"
      sx={{
        border: "1px solid #ccc",
        padding: 2,
        borderRadius: "5px",
        marginBottom: 2,
      }}>
      <legend style={{ padding: "0 10px", fontSize: "1.3rem" }}>
        {t("addressInfo")}
      </legend>
      <Grid container spacing={1}>
        <FormTextField
          name="AddrNoPrefix"
          label={t("addrNoPrefix")}
          gridProps={{ xs: 4, sm: 4, md: 0.9 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="addNumber"
          label={t("addNumber")}
          gridProps={{ xs: 4, sm: 4, md: 1.3 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="NumberSuffix"
          label={t("numberSuffix")}
          gridProps={{ xs: 4, sm: 4, md: 0.9 }}
          sx={{ mb: 2 }}
        />

        <Grid item xs={4} sm={4} md={0.9}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_preMod" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("preMod")}
              options={streetPreModifierList}
              value={preMod}
              fieldName={t("preMod")}
              optionLabel="name"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
        <Grid item xs={4} sm={4} md={0.9}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_direction" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("direction")}
              options={streetPredirectionAndPostDirectionList}
              value={preDirection}
              fieldName={t("preDirection")}
              optionLabel="name"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
        <Grid item xs={4} sm={4} md={0.9}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_preType" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("preType")}
              options={streetNamePreTypesPostTypes}
              value={preType}
              fieldName={t("preType")}
              optionLabel="Value"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
        <Grid item xs={4} sm={4} md={0.9}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_preSep" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("preSep")}
              options={streetNamePreTypeSeparatorsList}
              value={preSep}
              fieldName={t("preSep")}
              optionLabel="Value"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>

        <FormTextField
          name="StreetName"
          label={t("streetName")}
          gridProps={{ xs: 4, sm: 4, md: 2.5 }}
          sx={{ mb: 2 }}
        />

        <Grid item xs={4} sm={4} md={0.9}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_streetType" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("streetType")}
              options={streetNamePreTypesPostTypes}
              value={streetType}
              fieldName={t("streetType")}
              optionLabel="Value"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
        <Grid item xs={4} sm={4} md={0.9}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_postDir" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("postDir")}
              options={streetPredirectionAndPostDirectionList}
              value={postDir}
              fieldName={t("postDir")}
              optionLabel="name"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
        <Grid item xs={4} sm={4} md={0.9}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_postMod" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("postMod")}
              options={streetPostModifierList}
              value={postMod}
              fieldName={t("postMod")}
              optionLabel="name"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
      </Grid>

      <Grid container spacing={1}>
        <FormTextField
          name="Site"
          label={t("site")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="SubSite"
          label={t("subStie")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="Structure"
          label={t("structure")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="Wing"
          label={t("wing")}
          gridProps={{ xs: 5, sm: 5, md: 1 }}
          sx={{ mb: 2 }}
        />
        <FormTextField
          name="Building"
          label={t("building")}
          gridProps={{ xs: 5, sm: 5, md: 1 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="Floor"
          label={t("floor")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
        />
      </Grid>

      <Grid container spacing={1}>
        <FormTextField
          name="UnitPreType"
          label={t("unitType")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="UnitValue"
          label={t("unitId")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="Room"
          label={t("room")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="Section"
          label={t("section")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="Row"
          label={t("row")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="Seat"
          label={t("seat")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
          onKeyDown={handleKeyDown}
        />

        <FormTextField
          name="AdditionalLocationInfo"
          label={t("additionalLocationInfo")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />
      </Grid>

      <Grid container spacing={1}>
        <FormTextField
          name="MilePost"
          label={t("milePost")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="StreetDirOfTravel"
          label={t("streetDirOfTravel")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="Marker"
          label={t("locationMarker")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="NERISAdditionalAttributes"
          label={t("nerisAdditionalAttributes")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />
      </Grid>

      <Grid container spacing={1}>
        <Grid item xs={5} sm={5} md={3}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage
                {...props}
                componentName="Autocomplete_cityPostalComm"
              />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("cityPostalComm")}
              options={filteredCityList}
              value={cityPostalComm}
              fieldName={t("cityPostalComm")}
              optionLabel="CityName"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>

        <Grid item xs={4} sm={4} md={1.5}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_state" />
            )}>
            <CommonAutocomplete
              disabled
              parentCallback={handleStateChange}
              options={stateList}
              value={state}
              fieldName={t("state")}
              optionLabel="StateCode"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>

        <Grid item xs={4} sm={4} md={1.5}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_zipPostCode" />
            )}>
            <CommonAutocomplete
              parentCallback={handleZipPostCode}
              options={filteredZipCodeList}
              value={zipPostCode}
              fieldName={t("zipPostCode")}
              optionLabel="ZipCode"
              onKeyDown={handleSelectKeyDown}
              ref={textFieldRef}
            />
          </ErrorBoundary>
        </Grid>

        <FormTextField
          name="Zip"
          label={t("zip")}
          gridProps={{ xs: 4, sm: 4, md: 1 }}
          sx={{ mb: 2 }}
        />

        <Grid item xs={4} sm={4} md={1.5}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_county" />
            )}>
            <CommonAutocomplete
              disabled
              parentCallback={handleAutocompleteChange("county")}
              options={filteredCountyList}
              value={county}
              fieldName={t("county")}
              optionLabel="CountyName"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>

        <Grid item xs={4} sm={4} md={1.5}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_country" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("country")}
              options={countryList}
              value={country}
              fieldName={t("country")}
              optionLabel="name"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
      </Grid>

      <Grid container spacing={1}>
        <FormTextField
          name="incorporatedMuni"
          label={t("incorporatedMuni")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
        <FormTextField
          name="UnincorporatedMuni"
          label={t("unincorporatedMuni")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
        <FormTextField
          name="landmarkName"
          label={t("landmarkName")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
        <FormTextField
          name="neighborhoodCommunity"
          label={t("neighborhoodCommunity")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
      </Grid>

      <Grid container spacing={1}>
        <FormTextField
          name="NearestCrossStreet"
          label={t("nearestCrossStreet")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
        <FormTextField
          name="Distance"
          label={t("distance")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
        <FormTextField
          name="NxtNearestCross"
          label={t("nxtNearestCross")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
        <FormTextField
          name="NxtDistance"
          label={t("distance")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
      </Grid>

      <Grid container spacing={1}>
        <Grid item xs={4} sm={4} md={1.5} lg={1.5} xl={1.5}>
          <Controller
            name="Latitude"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label={t("latitude")}
                variant="outlined"
                fullWidth
                sx={{ mb: 2 }}
                error={!!errors.Latitude}
                helperText={errors?.Latitude?.message}
              />
            )}
          />
        </Grid>

        <Grid item xs={4} sm={4} md={1.5} lg={1.5} xl={1.5}>
          <Controller
            name="Longitude"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label={t("longitude")}
                variant="outlined"
                fullWidth
                sx={{ mb: 2 }}
                error={!!errors.Longitude}
                helperText={errors?.Longitude?.message}
              />
            )}
          />
        </Grid>

        <FormTextField
          name="Elevation"
          label={t("elevation")}
          gridProps={{ xs: 4, sm: 4, md: 1.5 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="FIPS"
          label={t("fips")}
          gridProps={{ xs: 4, sm: 4, md: 1.5 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="NERISID"
          label={t("nerisId")}
          gridProps={{ xs: 4, sm: 4, md: 1.5 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="NibrsLocType"
          label={t("nibrsLocType")}
          gridProps={{ xs: 4, sm: 4, md: 1.5 }}
          sx={{ mb: 2 }}
        />

        <Grid item xs={4} sm={4} md={3}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_placeType" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("placeType")}
              options={placeTypeList}
              value={placeType}
              fieldName={t("placeType")}
              optionLabel="name"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
      </Grid>

      <Grid container spacing={1}>
        <FormTextField
          name="MapPage"
          label={t("mapPage")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="AddressLabel"
          label={t("addressLabel")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="NationalGrid"
          label={t("nationalGrid")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="PSAP"
          label={t("psap")}
          gridProps={{ xs: 4, sm: 4, md: 2 }}
          sx={{ mb: 2 }}
        />
      </Grid>

      <ZoneFields />

      <Grid container spacing={1}>
        <FormTextField
          name="DiscrepancyAgencyID"
          label={t("discrepancyAgencyID")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="NGUID"
          label={t("nguid")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="AddCode"
          label={t("addCode")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="AddressDataURL"
          label={t("addressDataURL")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />
      </Grid>

      <Grid container spacing={1}>
        <Grid item xs={12} sm={3} md={2} lg={2} xl={2}>
          <Controller
            name="DateAdded"
            control={control}
            render={({ field }) => (
              <DesktopDatePicker
                {...field}
                label={t("dateAdded")}
                inputFormat="MM/dd/yyyy"
                onChange={(value) => field.onChange(value)}
                renderInput={(params) => <TextField {...params} />}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={3} md={2} lg={2} xl={2}>
          <Controller
            name="DateUpdated"
            control={control}
            render={({ field }) => (
              <DesktopDatePicker
                {...field}
                label={t("dateUpdated")}
                inputFormat="MM/dd/yyyy"
                onChange={(value) => field.onChange(value)}
                renderInput={(params) => <TextField {...params} />}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={3} md={2} lg={2} xl={2}>
          <Controller
            name="EffectiveDate"
            control={control}
            render={({ field }) => (
              <DesktopDatePicker
                {...field}
                label={t("effectiveDate")}
                inputFormat="MM/dd/yyyy"
                onChange={(value) => field.onChange(value)}
                renderInput={(params) => <TextField {...params} />}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={3} md={2} lg={2} xl={2}>
          <Controller
            name="ExpirationDate"
            control={control}
            render={({ field }) => (
              <DesktopDatePicker
                {...field}
                label={t("expirationDate")}
                inputFormat="MM/dd/yyyy"
                onChange={(value) => field.onChange(value)}
                renderInput={(params) => <TextField {...params} />}
              />
            )}
          />
        </Grid>
      </Grid>

      <LegacyStreetNameFields
        handleAutocompleteChange={handleAutocompleteChange}
        legacyPostDir={legacyPostDir}
        legacyStreetType={legacyStreetType}
        preDir={preDir}
        streetNamePreTypesPostTypes={streetNamePreTypesPostTypes}
        streetPredirectionAndPostDirectionList={
          streetPredirectionAndPostDirectionList
        }
      />

      <AliasStreetNameFields
        handleAutocompleteChange={handleAutocompleteChange}
        aliasPostDir={aliasPostDir}
        aliasPreDir={aliasPreDir}
        aliasStreetType={aliasStreetType}
        streetNamePreTypesPostTypes={streetNamePreTypesPostTypes}
        streetPredirectionAndPostDirectionList={
          streetPredirectionAndPostDirectionList
        }
      />

      <Grid container spacing={1}>
        <FormTextField
          name="placement"
          label={t("placement")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="ESN"
          label={t("esn")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />

        <FormTextField
          name="MSAGCommunity"
          label={t("msagCommunity")}
          gridProps={{ xs: 4, sm: 4, md: 3 }}
          sx={{ mb: 2 }}
        />
      </Grid>
    </Box>
  );
};

export default AddressInfoFields;
