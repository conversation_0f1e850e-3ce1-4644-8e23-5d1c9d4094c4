#input-2 {
  height: 50px;
  /* Set the desired height for the input field */
  font-size: 20px;
  /* Set the desired font size for the input field */
}

.special-label {
  background: none !important;
}



.react-tel-input .form-control {
  font-size: 16px;
  background: none;
  border: 1px solid #6b7280  !important;
  border-radius: 5px;
  outline: none;
  padding: 18.5px 14px 18.5px 58px;
  transition: box-shadow ease .25s,border-color ease .25s;
  height: 54px !important;
  width: 100% !important;
}

.flag-dropdown.open {
  color: black;
}

