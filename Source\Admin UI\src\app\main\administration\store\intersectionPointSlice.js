import { createSlice } from '@reduxjs/toolkit';
import axios from "axios";
import { showMessage } from 'app/store/fuse/messageSlice';
import { encrypt, decrypt } from 'src/app/security';

export const getIntersectionPointData = (sortField, sortDirection, pageIndex, pageLimit, searchText, county, stateCode) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/intersectionPointDetail/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${county}/${stateCode}`)
            .then((response) => {
                if (response.status === 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(setIntersectionPointTotalCount(listData.totalCount))
                    return dispatch(setIntersectionPointData(listData.intersectionPointList));
                } else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                return console.error(error);
            })
    } catch (e) {
        return console.error(e);
    }
}

// To add new intersection point 
export const addIntersectionPointData = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/intersectionPointDetail/createIntersectionPointDetail/`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    // dispatch(setAddIntersectionPointDetails(response.data.data));
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    dispatch(setIntersectionSuccess(true))
                }
                else if (response === undefined || response.status === 400) {
                    response.data = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e);
    }
}

export const updateIntersectionPointData = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/intersectionPointDetail/intersectionPointDetailEdit`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(setIntersectionPointDetailById(null));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    dispatch(setIntersectionSuccess(true));
                }
                else if (response.status == 400) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }

            })
            .catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e);
    }
};

export const removeIntersectionPointData = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/intersectionPointDetail/intersectionPointDetailDelete/${data.id}/${data.county}/${data.stateCode}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    dispatch(setIntersectionSuccess(true));

                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    dispatch(setLoading(false));

                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e);
    }
}

export const getIntersectionPointDetailById = (id, county, stateCode) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/intersectionPointDetail/intersectionPointDetailGetByID/${id}/${county}/${stateCode}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setIntersectionPointDetailById(response));
                    dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e);
    }
}

const initialState = {
    intersectionPointData: [],
    intersectionPointAddResposeData: [],
    isLoading: false,
    individualIntersectionPointDataID: 0,
    searchText: null,
    intersectionPointDetailById: null,
    totalCount: 0,
    intersectionSuccess: false,
};

const intersectionPointSlice = createSlice({
    name: 'intersectionPoint',
    initialState,
    reducers: {
        setIntersectionPointData: (state, action) => {
            state.intersectionPointData = action.payload;
        },
        setAddIntersectionPointDetails: (state, action) => {
            state.intersectionPointAddResposeData = action.payload;
        },
        setLoading: (state, action) => {
            state.isLoading = action.payload;
        },
        setIntersectionPointDetailById: (state, action) => {
            state.intersectionPointDetailById = action.payload;
        },
        setIntersectionPointTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setIntersectionSuccess: (state, action) => {
            state.intersectionSuccess = action.payload;
        }
    }
});

export default intersectionPointSlice.reducer;

export const {
    setIntersectionPointData,
    setLoading,
    setAddIntersectionPointDetails,
    setIntersectionPointDetailById,
    setIntersectionPointSearchText,
    setIntersectionPointTotalCount,
    setIntersectionSuccess
} = intersectionPointSlice.actions;