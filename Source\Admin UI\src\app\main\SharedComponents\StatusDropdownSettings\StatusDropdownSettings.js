import React, { useState, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import FormControl from "@mui/material/FormControl";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import "./StatusDropdownSettings.css"
import InputLabel from '@mui/material/InputLabel';
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';

let Status = [
    {
        "key": "newtip",
        "Status": "New Tip",
    },
    {
        "key": "processed",
        "Status": "Processed",
    },
    {
        "key": "unfounded",
        "Status": "Unfounded",
    }
]

function StatusDropdownSettings(props) {
    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();
    const user = useSelector(({ auth }) => auth.user);
    const [status, setStatus] = React.useState('');

    const handleStatus = (event, newValue) => {
        props.statusID(newValue ? newValue._id : "");
        setStatus(newValue)
    };

    useEffect(() => {
        if (props.clear) {
            setStatus(null);
            props.setclear(false);
        }
        else {
            const selectedStatus = Status.find(x => x._id === props.status) || null;
            setStatus(selectedStatus);

        }
    }, [props.clear])

    return (
        <div className="flex flex-col justify-center w-full">

            <FormControl fullWidth className="w-full">
                <CommonAutocomplete
                    parentCallback={handleStatus}
                    options={Status || []}
                    value={status || null}
                    fieldName={t("status")}
                    optionLabel={"Status"}
                    onKeyDown={handleSelectKeyDown}
                />
            </FormControl>

        </div >
    );
}

export default StatusDropdownSettings;