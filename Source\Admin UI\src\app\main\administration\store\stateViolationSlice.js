import { showMessage } from "app/store/fuse/messageSlice";
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';
import { createSlice } from '@reduxjs/toolkit';

export const getMasterState = (sortField, sortDirection, pageIndex, pageLimit, searchText) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`admin/api/stateViolation/StateList/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}`)
            .then((response) => {
                if (response.status === 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setMasterState(listData.stateList));
                    dispatch(setStateTotalCount(listData.totalCount));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const getViolationByState = (sortField, sortDirection, pageIndex, pageLimit, searchText, stateCode) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios
            .get(`admin/api/stateViolation/violationList/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${stateCode}`)
            .then((response) => {
                if (response.status === 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setViolationList(listData.violationList));
                    dispatch(setViolationTotalCount(listData.totalCount));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (error) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const deleteStateViolation = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/stateViolation/stateViolationDelete/${data.id}/${data.stateCode}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(setStateViolationSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const createStateViolation = (data, stateCode) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/stateViolation/createStateViolation/${stateCode}`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(setStateViolationSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getViolationType = () => async (dispatch) => {
    try {
        await axios
            .get(`admin/api/stateViolation/getViolationType`)
            .then((response) => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setViolationTypes(response.data));
                    return;
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                }));
            });
    }
    catch (error) {
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

const initialState = {
    isloading: false,
    MasterState: [],
    stateTotalCount: 0,
    violationList: [],
    violationTotalCount: 0,
    selectedStateCode: null,
    stateViolationSuccess: false,
    selectedStateViolationColumn: [],
    selectedStateViolationData: null,
    violationTypes: [],
};

const stateViolationSlice = createSlice({
    name: "StateViolation",
    initialState,
    reducers: {
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setMasterState: (state, action) => {
            state.MasterState = action.payload;
        },
        setStateTotalCount: (state, action) => {
            state.stateTotalCount = action.payload;
        },
        setViolationList: (state, action) => {
            state.violationList = action.payload;
        },
        setViolationTotalCount: (state, action) => {
            state.violationTotalCount = action.payload;
        },
        setSelectedStateCode: (state, action) => {
            state.selectedStateCode = action.payload;
        },
        setStateViolationSuccess: (state, action) => {
            state.stateViolationSuccess = action.payload;
        },
        setSelectedStateViolationColumn: (state, action) => {
            state.selectedStateViolationColumn = action.payload;
        },
        setSelectedStateViolationData: (state, action) => {
            state.selectedStateViolationData = action.payload;
        },
        setViolationTypes: (state, action) => {
            state.violationTypes = action.payload;
        },
    }
})

export const {
    setLoading,
    setMasterState,
    setStateTotalCount,
    setViolationList,
    setViolationTotalCount,
    setSelectedStateCode,
    setStateViolationSuccess,
    setSelectedStateViolationColumn,
    setSelectedStateViolationData,
    setViolationTypes
} = stateViolationSlice.actions;

export default stateViolationSlice.reducer;