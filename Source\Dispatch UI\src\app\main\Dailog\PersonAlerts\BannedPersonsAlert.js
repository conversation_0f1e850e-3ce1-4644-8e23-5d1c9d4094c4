import React from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const BannedPersonAlert = ({ bannedPersons }) => {

    const { t } = useTranslation('languageConfig');

    const getInitials = (fullName) => {
        if (!fullName || typeof fullName !== "string") return "";
        const parts = fullName.split(",").map((p) => p.trim());

        if (parts.length === 2) {
            const [lastName, firstAndMiddle] = parts;
            const firstName = firstAndMiddle.split(" ")[0] || "";
            const last = lastName.split(" ")[0] || "";
            return (
                (firstName[0] || "").toUpperCase() + (last[0] || "").toUpperCase()
            );
        }

        const names = fullName.trim().split(/\s+/);
        const first = names[0] || "";
        const last = names[names.length - 1] || "";
        return ((first[0] || "") + (last[0] || "")).toUpperCase();
    };

    return (
        <div className="p-8">
            <Box className="text-center mb-16">
                <Typography variant="h3" className="text-red-600 font-extrabold text-4xl">
                    🚫 {t("bannedPersonRegistry")}
                </Typography>
                <Typography variant="subtitle1" className="text-gray-600 text-xl mt-2">
                    {t("securityManagementDashboard")}
                </Typography>
            </Box>

            {(!bannedPersons || bannedPersons.length === 0) ? (
                <div className="text-center text-gray-500 text-xl mt-10">
                    {t("nobannedPersonFound")}
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-16">
                    {bannedPersons.map((person, index) => (
                        <div
                            key={index}
                            className="rounded-2xl shadow-lg p-6 border-t-4 border-red-500 transition-all duration-300 hover:shadow-2xl hover:-translate-y-1"
                        >
                            <div className="flex items-center space-x-6 mb-6">
                                <div className="h-40 w-40 rounded-full bg-red-600 flex items-center justify-center font-extrabold text-2xl text-white">
                                    {getInitials(person.bannedIndivDisplayname)}
                                </div>
                                <div>
                                    <div className="text-xl font-semibold">
                                        {person.bannedIndivDisplayname || ""}
                                    </div>
                                    <div className="text-blue-600 text-sm font-semibold">
                                        #{person.bannedPersonNumber || ""}
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 text-base gap-4 border-t pt-4">
                                <div>
                                    <div className="font-bold text-gray-500">{t("dob")}</div>
                                    <div className="font-semibold">{person.dob || ""}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("genderRace")}</div>
                                    <div className="font-semibold">
                                        {person.sex || ""} / {person.race || ""}
                                    </div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("banStartDate")}</div>
                                    <div className="font-semibold">{person.banBeginDate || ""}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("banExpires")}</div>
                                    <div className="italic text-gray-700">{person.banExpires || 'PERMANANT'}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("reason")}</div>
                                    <div className="font-semibold">{person.banReason || ""}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("bannedBy")}</div>
                                    <div className="font-semibold">
                                        {person.bannedByOfficerDispName || 'N/A'}
                                    </div>
                                </div>
                            </div>

                            <div className="mt-6 bg-gray-100 dark:bg-gray-800 rounded-md p-3 text-base leading-relaxed">
                                <div>
                                    <span className="font-bold text-gray-500">{t("bannedLocation")}:</span>{' '}
                                    <span className="font-bold">
                                        {person.BannedLocationName || person.bannedLocationName || ""}
                                    </span>
                                </div>
                                <div>
                                    <span className="font-bold text-gray-500">{t("address")}:</span>{' '}
                                    <span className="text-900">
                                        {person.bannedLocationFullAddress || <i>{t("noAddressOnFile")}</i>}
                                    </span>
                                </div>
                                <div>
                                    <span className="font-bold text-gray-500">{t("remoteSource")}:</span>{' '}
                                    <span className="font-semibold">
                                        {person.remotesource || 'Unknown'}
                                    </span>
                                </div>
                            </div>

                            <div className="mt-4 flex flex-wrap gap-3">
                                {person.lifetimeBan && (
                                    <span className="bg-yellow-200 text-yellow-900 px-4 py-1 rounded-full text-sm font-bold">
                                        {t("lifetimeBan")}
                                    </span>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default BannedPersonAlert;
