import React from 'react';
import {
    FormControl,
    FormLabel,
    FormGroup,
    FormControlLabel,
    Checkbox
} from '@mui/material';

const NCICAccessRightGroup = ({
    t,
    accessRights,
    handleChange,
    showAccessRight,
    isSuperAdmin,
    defaultApp
}) => {
    const accessItems = [
        { key: 'PersonSearch', label: t('personSearch') },
        { key: 'VehicleSearch', label: t('vehicleSearch') },
        { key: 'GunSearch', label: t('gunSearch') },
        { key: 'ArticleSearch', label: t('articleSearch') },
        { key: 'BoatSearch', label: t('boatSearch') },
        { key: 'IncidentReport', label: t('incidentReport') },
        { key: 'AccidentReport', label: t('accidentReport') },
    ];

    const isDisabled = !(defaultApp === 'mobile' || defaultApp === 'incident') && !isSuperAdmin;

    return (
        <div className="col-span-1 w-full">
            <FormControl component="fieldset">
                <FormLabel component="legend">{t('ncic')}</FormLabel>
                <FormGroup>
                    {accessItems.map(({ key, label }) => {
                        const canShow = showAccessRight[key] === key || isSuperAdmin;
                        return canShow && (
                            <FormControlLabel
                                key={key}
                                control={
                                    <Checkbox
                                        checked={accessRights[key] || false}
                                        onChange={handleChange}
                                        name={key}
                                    />
                                }
                                label={label}
                            //disabled={isDisabled}
                            />
                        );
                    })}
                </FormGroup>
            </FormControl>
        </div>
    );
};

export default NCICAccessRightGroup;
