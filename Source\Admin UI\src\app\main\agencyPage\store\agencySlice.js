import { createSlice } from '@reduxjs/toolkit';
import history from '@history';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from "axios";
import { encrypt, decrypt } from '../../../security/crypto';

const initialState = {
    success: false,
    error: {
        contact: {}
    },
    data: [],
    agencyAdd: false,
    agencyData: [],
    parentAgencyData: [],
    searchText: '',
    agencyServerConfigurationData: [],
    isloading: false,
    tenantData: [],
    filterAgencyValue: "",
    filterTabValue: 0,
    callCategories: [],
    agencyID: 0,
    emailExist: false,
    codeExist: false,
    ConnectionStringExist: false,
    agencyByCode: null,
    audioFileLocation: "",
    agencyState: null,
    dropdownTypes: [],
};

const agencySlice = createSlice({
    name: 'agency',
    initialState,
    reducers: {
        setAgencyList: (state, action) => {
            state.data = action.payload;
        },
        setAgencyId: (state, action) => {
            state.agencyID = action.payload;
            state.agencyData = [];
            state.agencyServerConfigurationData = [];
        },
        setAgencyBYID: (state, action) => {
            state.agencyData = action.payload;
        },
        setParentAgencyById: (state, action) => {
            state.parentAgencyData = action.payload;
        },
        clearParentAgencyById: (state, action) => {
            state.parentAgencyData = [];
        },
        setRemoveAgencyID: (state, action) => {
            state.data = state.data.filter(({ _id }) => _id !== action.payload)
        },
        setAgencyState: (state, action) => {
            state.agencyState = action.payload;
        },

        setAddAgencyDetails: (state, action) => {
            state.agencyData = action.payload;
            state.agencyAdd = true;
        },
        removeAgencyDetails: (state, action) => {
            state.agencyAdd = false;
            state.agencyData = [];
        },
        setServerConfigurations: (state, action) => {
            state.agencyServerConfigurationData = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setagencyTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setTenantList: (state, action) => {
            state.tenantData = action.payload;
        },
        setFilterAgencyValue: (state, action) => {
            state.filterAgencyValue = action.payload;
        },
        setFilterTabValue: (state, action) => {
            state.filterTabValue = action.payload;
        },
        setCallCategories: (state, action) => {
            state.callCategories = action.payload;
        },
        emailExist: (state, action) => {
            state.emailExist = action.payload;
        },
        codeExist: (state, action) => {
            state.codeExist = action.payload;
        },
        ConnectionStringExist: (state, action) => {
            state.ConnectionStringExist = action.payload;
        },
        setAgencyByCode: (state, action) => {
            state.agencyByCode = action.payload;
        },
        setAudioFileLocation: (state, action) => {
            state.audioFileLocation = action.payload;
        },
        GetDropdownTypes: (state, action) => {
            state.dropdownTypes = action.payload;
        },
    },
    extraReducers: {}
});

export default agencySlice.reducer;

// Actions
export const {
    setAgencyList,
    setSearchText,
    setAgencyId,
    setAgencyBYID,
    setParentAgencyById,
    clearParentAgencyById,
    setRemoveAgencyID,
    setAddAgencyDetails,
    removeAgencyDetails,
    setServerConfigurations,
    setagencyTotalCount,
    setLoading,
    setTenantList,
    setFilterAgencyValue,
    setFilterTabValue,
    setCallCategories,
    setAgencyState,
    emailExist,
    codeExist,
    ConnectionStringExist,
    setAgencyByCode,
    setAudioFileLocation,
    GetDropdownTypes
} = agencySlice.actions;

export const setFilterAgencyDDlValue = (value) => async dispatch => {
    dispatch(setFilterAgencyValue(value))
}

export const setFilterTabButtonValue = (value) => async dispatch => {
    dispatch(setFilterTabValue(value))
}



export const getTypes = () => async dispatch => {
    try {
        await axios
            .get(`admin/api/personMaster/getTypes`)
            .then(response => {
                if (response.status === 200) {
                    return dispatch(GetDropdownTypes(JSON.parse(decrypt(response.data))));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
};


export const getAgencyDetailsWithCode = (agencyCode) => async dispatch => {
    try {
        await axios
            .get(`admin/api/agencies/getAgencyDetailsWithCode/${agencyCode}`)
            .then(async response => {
                if (response.status === 200) {
                    let agencyState = JSON.parse(decrypt(response.data));
                    dispatch(setAgencyState(agencyState));
                }
                else {
                    dispatch(showMessage({
                        message: "not fond",
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
};

//for searching
export const getCallCategories = () => async dispatch => {
    try {
        await axios.get(`admin/api/agencies/getCallCategories`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    return dispatch(setCallCategories(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

//for searching
export const getAgencyByCode = (code) => async dispatch => {
    try {
        await axios.get(`admin/api/agencies/getAgencyByCode/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    return dispatch(setAgencyByCode(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

//Get agency List
export const getAgencyList = (sortField, sortDirection, pageIndex, pageLimit, searchText) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/agencies/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}`)
            .then(response => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(setagencyTotalCount(listData.totalCount));
                    return dispatch(setAgencyList(listData.agencyList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    return dispatch(setAgencyList([]));
                }
            });
    } catch (e) {
        return dispatch(setAgencyList([]));
    }
}

//for register new user drop down and users table drop down and for master address county filter
export const getAgencyData = () => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/agencies`)
            .then(response => {
                let listData = JSON.parse(decrypt(response.data));
                dispatch(setLoading(false));
                return dispatch(setAgencyList(listData.agencyList));
            });
    } catch (e) {
        return dispatch(setAgencyList([]));
    }
}

//To get Tenant Url Data
export const getTenantUrlList = () => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/agencies/getTenantUrl`)
            .then(response => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    return dispatch(setTenantList(listData));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    return dispatch(setTenantList([]));
                }
            });
    } catch (e) {
        return dispatch(setTenantList([]));
    }
}

// To add new Agency
export const newAgency = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/agencies/agency`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response !== undefined && response.status == 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(setAddAgencyDetails(response.data.data));
                    dispatch(createSearchIndex(response.data.data.code));
                }
                else if (response === undefined || response.status === 400) {
                    response.data = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const addAudioFile = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        const formData = new FormData();
        formData.append("file", data.file);
        await axios.post(`fileupload/uploadAudioFiles/${data._id}/AlertAudioFile`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        }).then(async response => {
            if (response.data) {
                dispatch(showMessage({
                    message: "Audio File Uploaded successfully",
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'success'
                }));
                dispatch(setLoading(false));
                return dispatch(setAudioFileLocation(response.data.Location));
            }
        })
    } catch (e) {
        dispatch(setLoading(false));
    }
}

export const GetUserByEmail = (email, users) => async dispatch => {
    let existingUser = users.filter(user => user.email === email.trim());
    dispatch(emailExist(false));
    if (existingUser.length > 0) {
        dispatch(showMessage({
            message: "Email is already registered with us, Please use different email.",
            autoHideDuration: 5000,
            variant: 'warning'
        }))
        return dispatch(emailExist(true));
    }
    else {
        await axios.get(`admin/api/users/getUserByEmail/` + email)
            .then(response => {
                if (response.status == 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    if (response.data) {
                        dispatch(showMessage({
                            message: "Email is already registered with us, Please use different email.",
                            autoHideDuration: 2000,
                            variant: 'warning'
                        }))
                        return dispatch(emailExist(true));
                    } else {
                        return dispatch(emailExist(false));
                    }
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch(error => {
                return console.log(error);
            });
    }
};

export const checkAgencyCode = (code, agency) => async dispatch => {
    let existingcode = agency.filter(agency => agency.code === code.trim());
    dispatch(codeExist(false));
    if (existingcode.length > 0) {
        dispatch(showMessage({
            message: "Agency Code is already registered with us, Please use different code.",
            autoHideDuration: 5000,
            variant: 'warning'
        }))
        return dispatch(codeExist(true));
    }
    else {
        await axios.get(`admin/api/agencies/verifyCode/` + code)
            .then(response => {
                if (response.status == 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    if (response.data) {
                        dispatch(showMessage({
                            message: "Agency Code is already registered with us, Please use different code.",
                            autoHideDuration: 2000,
                            variant: 'warning'
                        }))
                        return dispatch(codeExist(true));
                    } else {
                        return dispatch(codeExist(false));
                    }
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }).catch(error => {
                return console.log(error);
            });
    }
};

export const verifyConnectionString = (ConnectionString) => async dispatch => {
    dispatch(ConnectionStringExist(false));
    let data = { ConnectionString }
    await axios.post(`admin/api/agencies/verifyConnectionString/`, encrypt(JSON.stringify(data)))
        .then(response => {
            if (response.status == 200) {
                response.data = JSON.parse(decrypt(response.data));
                if (response.data) {
                    dispatch(showMessage({
                        message: "Invalid Database Connection String please enter valid connection string..",
                        autoHideDuration: 2000,
                        variant: 'warning'
                    }))
                    return dispatch(ConnectionStringExist(true));
                } else {
                    return dispatch(ConnectionStringExist(false));
                }
            }
            else {
                response = JSON.parse(decrypt(response.response.data));
                dispatch(showMessage({
                    message: response.data.message,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'
                }))
            }
        }).catch(error => {
            return console.log(error);
        });
};

export const createSearchIndex = (agencyCode) => async dispatch => {
    try {
        await axios.get(`admin/api/agencies/createAutocompleteSearchIndex/${agencyCode}`)
            .then(async res => {
                if (res.status === 200) {
                    res.data = JSON.parse(decrypt(res.data));
                    res.data.map((item) =>
                        dispatch(showMessage({
                            message: `${item}`, autoHideDuration: 1000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    );
                } else {
                    res.data = JSON.parse(decrypt(res.data));
                    dispatch(showMessage({
                        message: `Search index creation failed`, autoHideDuration: 1000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                }
            });
    }
    catch (err) {
        console.log(err.message);
    }
}

// To remove Agency Deatils
export const removeSavedAgencyDetails = (data) => async dispatch => {
    dispatch(removeAgencyDetails());
}

// To update Agency
export const updateAgency = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/agencies/agencyEdit`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    history.push("/admin/agencyList");
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                }
                else if (response.status == 400) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }

            })
            .catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//To get Agency By ID
export const getAgencyByID = (_id) => async dispatch => {
    try {
        await axios.get(`admin/api/agencies/agencyGetByID/` + _id)
            .then(response => {
                if (response.status == 200) {
                    return dispatch(setAgencyBYID(JSON.parse(decrypt(response.data))));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            });

    } catch (e) {
        console.log("error:", e);
        return console.error(e.message);
    }
}

//To get Parent Agency Details By ID
export const getParentAgencyDetailsByID = (_id) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/agencies/agencyGetByID/` + _id)
            .then(response => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    response.data = JSON.parse(decrypt(response.data));
                    return dispatch(setParentAgencyById(response.data));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//Remove Agency
export const removeAgency = (_id) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/agencies/agencyDelete/` + _id)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 1500,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                    return dispatch(setRemoveAgencyID(_id));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            }
            );
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//To set Agency ID
export const setAgencyID = (agencyID) => async dispatch => {
    try {
        return dispatch(setAgencyId(agencyID));
    } catch (e) {
        return console.error(e.message);
    }
}

export const getAgencyServerConfigurations = (agencyCode) => async dispatch => {
    await axios.get(`admin/api/agencies/getAgencyServerConfiguration/` + agencyCode)
        .then(response => {
            if (response.status == 200) {
                return dispatch(setServerConfigurations(JSON.parse(decrypt(response.data))))
            }
            else {
                response = JSON.parse(decrypt(response.response.data));
                dispatch(showMessage({
                    message: response.data.message,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'

                }))
            }
        })
}

//Need to remove as Server config is updeted from Edit agency 13/09/2024 by Jaydeep
export const saveAgencyServerConfiguration = (data) => async dispatch => {
    let code = data.code;
    try {
        await axios.post('admin/api/agencies/saveAgencyServerConfiguration', encrypt(JSON.stringify(data)))
            .then(async response => {
                if (response.status == 200) {
                    await axios.get(`fileupload/copySupportedUnitTypeFile/SupportedUnitType/System/${code}`)
                        .then(async response => {
                            if (response.status == 200) {
                                await axios.get(`admin/api/supportedUnitType/copydefaultIconData/${code}`)
                                    .then(async response => {
                                        if (response.status == 200) {
                                            dispatch(showMessage({
                                                message: "Success",
                                                autoHideDuration: 2000,
                                                anchorOrigin: {
                                                    vertical: 'top',
                                                    horizontal: 'right'
                                                },
                                                variant: 'success'

                                            }))
                                        }
                                    });
                            }
                            else {
                                response = JSON.parse(decrypt(response.response.data));
                                dispatch(showMessage({
                                    message: response.data.message,
                                    autoHideDuration: 2000,
                                    anchorOrigin: {
                                        vertical: 'top',
                                        horizontal: 'right'
                                    },
                                    variant: 'warning'

                                }))
                            }
                        })
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
    } catch (e) { return console.error(e.message); }
}

// To update Server Configuration
////Need to remove as Server config is updeted from Edit agency 13/09/2024 by Jaydeep
export const updateAgencyServerConfiguration = (data) => async dispatch => {
    try {
        await axios.post(`admin/api/agencies/updateAgencyServerConfiguration`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data))
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 1500,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => { });
    } catch (e) {
        return console.error(e.message);
    }
}

export const copySystemData = (agencyCode) => async dispatch => {
    try {
        await axios.get(localStorage.getItem("tenant").concat('admin/api/agencies/copySystemData/') + agencyCode)
            .then(response => {
                if (response.status == 200) {
                    let responseData = JSON.parse(decrypt(response.data));
                    const data = {
                        agencyCode: agencyCode,
                        arrayData: responseData
                    }
                    //function for tracking the status of the task
                    try {
                        async function updateStatus() {
                            await axios.post(localStorage.getItem("tenant").concat(`admin/api/agencies/isready`), encrypt(JSON.stringify(data)))
                                .then(function (response) {
                                    if (response.status == 200) {
                                        let responseDataStatus = JSON.parse(decrypt(response.data));
                                        let statusText = '';
                                        responseDataStatus.forEach(function (e) {
                                            statusText += `\n ${e.status}`;
                                        });
                                        let checkIsProcessComplete = responseDataStatus.every(function (e) {
                                            //statusText += `${statusText} \n ${e.status}`;
                                            return e.endDateTime !== null && e.endDateTime !== undefined;
                                        });
                                        dispatch(showMessage({
                                            message: statusText, autoHideDuration: 1000,
                                            anchorOrigin: {
                                                vertical: 'top',
                                                horizontal: 'right'
                                            },
                                            variant: 'success'
                                        }))
                                        if (!checkIsProcessComplete) {
                                            checkTaskTimeout = setTimeout(updateStatus, 1000);
                                        }
                                        else {
                                            dispatch(showMessage({
                                                message: "Data Copied successfully", autoHideDuration: 1000,
                                                anchorOrigin: {
                                                    vertical: 'top',
                                                    horizontal: 'right'
                                                },
                                                variant: 'success'
                                            }))
                                        }
                                    }
                                    else {
                                        response = JSON.parse(decrypt(response.response.data));
                                        dispatch(showMessage({
                                            message: response.data.message,
                                            autoHideDuration: 2000,
                                            anchorOrigin: {
                                                vertical: 'top',
                                                horizontal: 'right'
                                            },
                                            variant: 'warning'

                                        }))
                                    }
                                });
                        }
                        //start checking the task
                        var checkTaskTimeout = setTimeout(updateStatus, 100);
                    }
                    catch (e) {
                        return dispatch(showMessage({
                            message: e, autoHideDuration: 1500,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'error'
                        }))
                    }
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
    }
    catch (e) {
        return dispatch(showMessage({
            message: 'Data copy falied..', autoHideDuration: 1500,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'error'
        }))
    }
}

export const getAndUpdateRefreshTokenFromTwitter = (Authorizecode, AgencyCode, isSuperAdmin) => async dispatch => {
    try {
        await axios.post(`admin/api/agencies/getRefreshtokenFromTwitter`, encrypt(JSON.stringify({ Authorizecode, AgencyCode })))
            .then(response => {
                if (response.status == 200) {
                    response.data = JSON.parse(decrypt(response.data))
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    history.push(`/${response.data.redirectUrl}`);

                }
                else if (response.status === undefined || response.status === 400) {
                    response.data = JSON.parse(decrypt(response.response.data))
                    dispatch(showMessage({
                        message: response.data.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }


            }).catch(error => {
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
}
