import FuseScrollbars from '@fuse/core/FuseScrollbars';
import _ from '@lodash';
import React, { useEffect, useState } from 'react';
import IconButton from '@mui/material/IconButton';
import Icon from '@mui/material/Icon';
import ListItemText from '@mui/material/ListItemText';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import EditIcon from '@mui/icons-material/Edit';
import Tooltip from '@mui/material/Tooltip';
import withRouter from '@fuse/core/withRouter';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import TablePagination from '@mui/material/TablePagination';
import history from '@history';
import { useParams } from "react-router";

import { getContactList, setContactID } from '../store/contactSlice'

function ContactListCards(props) {
	const dispatch = useDispatch();
	const users = useSelector(({ contact }) => contact.contact.data);
	const searchText = useSelector(({ contact }) => contact.contact.searchText);
	const [data, setData] = useState(users);
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(100);
	const [sortingMenu, setSortingMenu] = useState(null);
	const [order, setOrder] = useState({direction: 'asc',id: null});
	const { t } = useTranslation('laguageConfig');
	const routeParams = useParams();

	const userStyle = {
		width: '100%',
		padding: '16px',
		boxShadow: '5px 5px 5px #9E9E9E',
		minHeight: '50px',
		borderRadius: '10px',
		marginBottom: '16px',
		backgroundColor: 'white'
	};

	useEffect(() => {
		dispatch(getContactList("_id", "desc", 0, 10000, null,routeParams.code));
	}, [dispatch]);

	useEffect(() => {
		if (searchText.length !== 0) {
			setData(
				_.filter(users, item =>
					// eslint-disable-next-line prefer-template
					(item.fName + ' ' +
						item.lName + ' ' +
						item.mName + ' ' +
						item.title + ' ' +
						item.address + ' ' +
						item.city + ' ' +
						item.state + ' ' +
						item.phone + ' ' +
						item.email).toLowerCase().includes(searchText.toLowerCase())
				)
			);
			setPage(0);
		} else {
			setData(users);
		}
	}, [users, searchText]);

	function handleChangePage(event, value) {
		setPage(value);
	}

	function handleChangeRowsPerPage(event) {
		setRowsPerPage(event.target.value);
		setPage(0);
	}

	function openSortingMenu(event) {
		setSortingMenu(event.currentTarget);
	}

	function closeSortingMenu() {
		setSortingMenu(null);
	}

	function handleRequestSort(property) {
		const id = property;
		let direction = 'desc';

		if (order.id === property && order.direction === 'desc') {
			direction = 'asc';
		}

		setOrder({
			direction,
			id
		});
	}

	const handleClickEdit = (contact) => {
		dispatch(setContactID(contact._id));
		history.push("/contactEdit");
	};



	return (
        <div className="w-full flex flex-col" style={{ backgroundColor: '#F3F3F3', borderRadius: '10px' }}>
			<FuseScrollbars className="flex-grow overflow-x-auto">
				{_.orderBy(
					data,
					[
						o => {
							switch (order.id) {
								case 'categories': {
									return o.categories[0];
								}
								default: {
									return o[order.id];
								}
							}
						}
					],
					[order.direction]
				)
					.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
					.map(n => {
						return (
                            <div style={userStyle}>
								<div className="w-full flex flex-row">
									<div className="w-full flex flex-col pl-16">
										<div className="w-full flex flex-row">
											<div className="w-full flex flex-col">
												<span style={{ fontWeight: '700', fontSize: '36px', color: '#E43636' }}>
													{n.lName} {n.fName} {n.mName}
												</span>
											</div>
											<div className="w-full flex flex-row flex-row-reverse">
												{/* <Tooltip title="Delete">
													<IconButton aria-label="delete">
														<DeleteIcon color="warn" />
													</IconButton>
												</Tooltip> */}
												<Tooltip title="Edit">
													<IconButton
                                                        aria-label="edit"
                                                        color="primary"
                                                        onClick={() => handleClickEdit(n)}
                                                        size="large">
														<EditIcon />
													</IconButton>
												</Tooltip>
											</div>
										</div>
										<div className="w-full flex flex-row" style={{ marginTop: '8px' }}>
											<div className="w-full flex flex-col">
												<h4>
													{t('title')}: {n.title}
												</h4>
											</div>
											<div className="w-full flex flex-col">
												<h4>

												</h4>
											</div>
											<div className="w-full flex flex-col">
												<h4>
													{t('phone')}: {n.phone}
												</h4>
											</div>
										</div>
										<div className="w-full flex flex-row" style={{ marginTop: '8px' }}>
											<div className="w-full flex flex-col">
												<h4>
													{t('address')}: {n.address}
												</h4>
											</div>
											<div className="w-full flex flex-col">
												<h4>
													{t('city')}: {n.city}
												</h4>
											</div>

											<div className="w-full flex flex-col">
												<h4>
													{t('email')}: {n.email}
												</h4>
											</div>
										</div>
									</div>
								</div>
							</div>
                        );
					})}
				<div className="w-full flex flex-row" style={userStyle}>
					<div className="w-full flex flex-row flex-row-reverse">
						<TablePagination
							className="tablePaging"
							component="div"
							count={data.length}
							rowsPerPage={rowsPerPage}
							page={page}
							backIconButtonProps={{
								'aria-label': 'Previous Page'
							}}
							nextIconButtonProps={{
								'aria-label': 'Next Page'
							}}
							onPageChange={handleChangePage}
							onRowsPerPageChange={handleChangeRowsPerPage}
						/>
						<IconButton
                            aria-owns={sortingMenu ? 'sortingMenu' : null}
                            aria-haspopup="true"
                            onClick={openSortingMenu}
                            size="large">
							<Icon>more_horiz</Icon>
						</IconButton>
						<Menu
							id="sortingMenu"
							anchorEl={sortingMenu}
							open={Boolean(sortingMenu)}
							onClose={closeSortingMenu}
						>
							<MenuList>
								<MenuItem
									onClick={() => {
										closeSortingMenu();
									}}
									disabled="true"
								>
									<ListItemText primary="Sort By" />
								</MenuItem>
								<MenuItem
									onClick={() => {
										closeSortingMenu();
									}}
								>
									<ListItemText primary="Email" onClick={() => handleRequestSort('email')} />
								</MenuItem>
								<MenuItem
									onClick={() => {
										closeSortingMenu();
									}}
								>
									<ListItemText primary="Phone" onClick={() => handleRequestSort('phone')} />
								</MenuItem>
								<MenuItem
									onClick={() => {
										closeSortingMenu();
									}}
								>
									<ListItemText primary="Name" onClick={() => handleRequestSort('fname')} />
								</MenuItem>
							</MenuList>
						</Menu>
					</div>
				</div>
			</FuseScrollbars>
		</div>
    );
}

export default withRouter(ContactListCards);
