import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';


export const getWreckerList = (searchText) => async dispatch => {
    try {
        await axios.post(process.env.REACT_APP_API_URL.concat('dispatch/api/wrecker/wreckerList'), searchText)
            .then(response => {
                dispatch(wreckerListSuccess(response.data));
                if (response.data.length > 0) {
                    dispatch(showMessage({
                        message: 'Wrecker details found.', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                }
                else {
                    dispatch(showMessage({
                        message: 'Wrecker details not found.', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }));
                }

            })
            .catch(error => {
                dispatch(wreckerListError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
};



const initialState = {
    wreckersuccess: false,
    wreckerdata: [],
    wreckererror: {
        username: null,
        password: null
    }
};


const wreckerSlice = createSlice({
    name: 'wrecker',
    initialState,
    reducers: {
        wreckerListSuccess: (state, action) => {
            state.wreckersuccess = true;
            state.wreckerdata = action.payload;
        },
        wreckerListError: (state, action) => {
            state.wreckersuccess = false;
            state.wreckerdata = [];
        }
    },
    extraReducers: {}
});

export const {
    wreckerListSuccess,
    wreckerListError
} = wreckerSlice.actions;

export default wreckerSlice.reducer;
