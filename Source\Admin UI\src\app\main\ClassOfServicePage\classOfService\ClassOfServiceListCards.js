import FuseScrollbars from '@fuse/core/FuseScrollbars';
import _ from '@lodash';
import React, { useEffect, useState } from 'react';
import IconButton from '@mui/material/IconButton';
import Icon from '@mui/material/Icon';
import ListItemText from '@mui/material/ListItemText';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import Tooltip from '@mui/material/Tooltip';
//import { withRouter } from 'react-router-dom';
import withRouter from '@fuse/core/withRouter';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import TablePagination from '@mui/material/TablePagination';
import history from '@history';
import PropTypes from 'prop-types';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Dialog from '@mui/material/Dialog';
import makeStyles from '@mui/styles/makeStyles';
import Button from '@mui/material/Button';
import { getClassOfServiceList, setClassOfServiceID, removeClassOfService } from '../store/classOfServiceSlice'

function ClassOfServiceListCards(props) {
	const dispatch = useDispatch();

	const users = useSelector(({ classOfService }) => classOfService.classOfService.data);
	const searchText = useSelector(({ classOfService }) => classOfService.classOfService.searchText);

	const [data, setData] = useState(users);
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(100);
	const [sortingMenu, setSortingMenu] = useState(null);
	const [order, setOrder] = useState({
		direction: 'asc',
		id: null
	});
	const { t } = useTranslation('laguageConfig');

	const userStyle = {
		width: '100%',
		padding: '16px',
		boxShadow: '5px 5px 5px #9E9E9E',
		minHeight: '50px',
		borderRadius: '10px',
		marginBottom: '16px',
		backgroundColor: 'white'
	};

	useEffect(() => {
		dispatch(getClassOfServiceList());
	}, [dispatch]);

	useEffect(() => {
		if (searchText.length !== 0) {
			setData(
				_.filter(users, item =>
					// eslint-disable-next-line prefer-template
					(item.code + ' ' +
						item.description + ' ' +
						item.color + ' ' +
						item.icon).toLowerCase().includes(searchText.toLowerCase())
				)
			);
			setPage(0);
		} else {
			setData(users);
		}
	}, [users, searchText]);

	function handleChangePage(event, value) {
		setPage(value);
	}

	function handleChangeRowsPerPage(event) {
		setRowsPerPage(event.target.value);
	}

	function openSortingMenu(event) {
		setSortingMenu(event.currentTarget);
	}

	function closeSortingMenu() {
		setSortingMenu(null);
	}

	function handleRequestSort(property) {
		const id = property;
		let direction = 'desc';

		if (order.id === property && order.direction === 'desc') {
			direction = 'asc';
		}

		setOrder({
			direction,
			id
		});
	}

	const handleClickEdit = (classOfService) => {
		dispatch(setClassOfServiceID(classOfService._id));
		history.push("/classOfServiceEdit");
	};

	const useStyles = makeStyles({
		root: {
			width: '100%',
		},
		container: {
			maxHeight: 440,
		},
	});

	const classes = useStyles();
	const [open, setOpen] = React.useState(false);
	const [value, setValue] = React.useState();

	const handleClickDelete = (agencyID) => {
		setValue(agencyID);
		setOpen(true);
	};

	const handleClose = (newValue) => {
		setOpen(false);
		if (newValue) {
			//after user select yes
			dispatch(removeClassOfService(newValue));
		}
	};

	return (
		<div className="w-full flex flex-col" style={{ backgroundColor: '#F3F3F3', borderRadius: '10px' }}>
			<FuseScrollbars className="flex-grow overflow-x-auto">
				{_.orderBy(
					data,
					[
						o => {
							switch (order.id) {
								case 'categories': {
									return o.categories[0];
								}
								default: {
									return o[order.id];
								}
							}
						}
					],
					[order.direction]
				)
					.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
					.map(n => {
						return (
							<div style={userStyle}>
								<div className="w-full flex flex-row">
									<div className="w-full flex flex-col pl-16">
										<div className="w-full flex flex-row">
											<div className="w-full flex flex-col">
												<span style={{ fontWeight: '700', fontSize: '36px', color: '#E43636' }}>
													{n.code}
												</span>
											</div>
											<div className="w-full flex flex-row flex-row-reverse">
												<Tooltip title="Delete">
													<IconButton aria-label="delete"
														onClick={() => handleClickDelete(n._id)}>
														<DeleteIcon color="warn" />
													</IconButton>
												</Tooltip>
												<Tooltip title="Edit">
													<IconButton aria-label="edit"
														color="primary"
														onClick={() => handleClickEdit(n)}>
														<EditIcon />
													</IconButton>
												</Tooltip>
											</div>
										</div>
										<div className="w-full flex flex-row" style={{ marginTop: '8px' }}>
											<div className="w-full flex flex-col">
												<h4>
													{t('title')}: {n.description}
												</h4>
											</div>
											<div className="w-full flex flex-col">
												<h4>
													{t('phone')}: {n.color}
												</h4>
											</div>
											<div className="w-full flex flex-col">
												<h4>
													{t('phone')}: {n.icon}
												</h4>
											</div>
										</div>
									</div>
								</div>
							</div>
						);
					})}
				<ConfirmationDialogRaw
					classes={{ paper: classes.paper }}
					id="ringtone-menu"
					keepMounted
					open={open}
					onClose={handleClose}
					value={value}
				/>
				<div className="w-full flex flex-row" style={userStyle}>
					<div className="w-full flex flex-row flex-row-reverse">
						<TablePagination
							className="tablePaging"
							component="div"
							count={data.length}
							rowsPerPage={rowsPerPage}
							page={page}
							backIconButtonProps={{
								'aria-label': 'Previous Page'
							}}
							nextIconButtonProps={{
								'aria-label': 'Next Page'
							}}
							onPageChange={handleChangePage}
							onChangeRowsPerPage={handleChangeRowsPerPage}
						/>
						<IconButton
							aria-owns={sortingMenu ? 'sortingMenu' : null}
							aria-haspopup="true"
							onClick={openSortingMenu}
						>
							<Icon>more_horiz</Icon>
						</IconButton>
						<Menu
							id="sortingMenu"
							anchorEl={sortingMenu}
							open={Boolean(sortingMenu)}
							onClose={closeSortingMenu}
						>
							<MenuList>
								<MenuItem
									onClick={() => {
										closeSortingMenu();
									}}
									disabled="true"
								>
									<ListItemText primary="Sort By" />
								</MenuItem>
								<MenuItem
									onClick={() => {
										closeSortingMenu();
									}}
								>
									<ListItemText primary="Email" onClick={() => handleRequestSort('email')} />
								</MenuItem>
								<MenuItem
									onClick={() => {
										closeSortingMenu();
									}}
								>
									<ListItemText primary="Phone" onClick={() => handleRequestSort('phone')} />
								</MenuItem>
								<MenuItem
									onClick={() => {
										closeSortingMenu();
									}}
								>
									<ListItemText primary="Name" onClick={() => handleRequestSort('fname')} />
								</MenuItem>
							</MenuList>
						</Menu>
					</div>
				</div>
			</FuseScrollbars>
		</div>
	);
}

ConfirmationDialogRaw.propTypes = {
	onClose: PropTypes.func.isRequired,
	open: PropTypes.bool.isRequired,
	value: PropTypes.any,
};

function ConfirmationDialogRaw(props) {
	const { t } = useTranslation('laguageConfig');
	const { onClose, value: valueProp, open, ...other } = props;

	const handleCancel = () => {
		onClose();
	};

	const handleOk = () => {
		onClose(valueProp);
	};

	return (
		<Dialog
			//disableBackdropClick
			disableEscapeKeyDown
			maxWidth="xs"
			aria-labelledby="confirmation-dialog-title"
			open={open}
			{...other}
		>
			<DialogTitle id="confirmation-dialog-title" color="primary">{t("CONFIRM")}</DialogTitle>
			<DialogContent dividers>
				<div className='m-16'>
					{t("DELETE")}
				</div>
			</DialogContent>
			<DialogActions>
				<Button autoFocus onClick={handleCancel} variant="contained" color="primary">
					{t("NO")}
				</Button>
				<Button onClick={handleOk} variant="contained" color="primary">
					{t("YES")}
				</Button>
			</DialogActions>
		</Dialog>
	);
}


export default withRouter(ClassOfServiceListCards);
