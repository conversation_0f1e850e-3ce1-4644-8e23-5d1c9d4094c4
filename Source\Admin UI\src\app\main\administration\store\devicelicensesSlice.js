import { createSlice } from "@reduxjs/toolkit";
import { showMessage } from "app/store/fuse/messageSlice";
import { decrypt, encrypt } from "../../../security/crypto";
import axios from "axios";

export const saveDeviceLincenses = (data) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/deviceLicenses`, encrypt(JSON.stringify(data)))
            .then(async (res) => {
                if (res.status == 200) {
                    res.data = await JSON.parse(decrypt(res.data));
                    dispatch(setLoading(false));
                    if (await res.data.isSuccess) {
                        dispatch(
                            showMessage({
                                message: data.isUpdate ? res.data.message : res.data.message,
                                autoHideDuration: 2000,
                                anchorOrigin: { vertical: "top", horizontal: "right", },
                                variant: "success",
                            })
                        );
                        if (data.isUpdate) {
                            dispatch(setDeviceEditData(res.data.newData))
                            dispatch(setDeviceLincensesTotalCount(res.data.totalCount));
                        }
                        else {
                            dispatch(setDeviceAddData(res.data.newData))
                            dispatch(setDeviceLincensesTotalCount(res.data.totalCount));
                        }
                    }
                }
                else {
                    res = JSON.parse(decrypt(res.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: res.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getDeviceLincenses = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/deviceLicenses/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                dispatch(setLoading(false));
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setDeviceLincensesTotalCount(listData.totalCount));
                    return dispatch(setDeviceLincensesData(listData.deviceLicensesList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const removeLicenses = (ID, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/deviceLicenses/${ID}/${code}`)
            .then(async (response) => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: "top",
                            horizontal: "right",
                        },
                        variant: "success",
                    })
                    );
                    dispatch(removeDeviceData(ID));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

//for searching
export const searchDeviceLicenses = (searchText, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        axios.get(`admin/api/deviceLicenses/searchDeviceLincenses/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchdeviceLicenses(data));
                }
            })
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

const initialState = {
    success: false,
    searchdeviceLicenses: [],
    deviceLicenses: [],
    isloading: false,
};

const deviceLicensesSlice = createSlice({
    name: "administration/DeviceLicenses",
    initialState,
    reducers: {
        setDeviceLincensesData: (state, action) => {
            state.deviceLicenses = action.payload;
        },
        setSearchdeviceLicenses: (state, action) => {
            state.searchdeviceLicenses = action.payload;
        },
        setDeviceLincensesTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setDeviceEditData: (state, action) => {
            const index = state.deviceLicenses.findIndex(obj => obj._id === action.payload._id);
            if (index !== -1) {
                state.deviceLicenses[index] = action.payload;
            }
        },
        setDeviceAddData: (state, action) => {
            state.deviceLicenses = [...state.deviceLicenses, action.payload];
        },
        removeDeviceData: (state, action) => {
            state.deviceLicenses = state.deviceLicenses.filter(x => x._id !== action.payload);
        },
    },
    extraReducers: {},
});

export const {
    setDeviceLincensesData,
    setDeviceLincensesTotalCount,
    setLoading,
    setDeviceEditData,
    setDeviceAddData,
    removeDeviceData,
    setSearchdeviceLicenses
} = deviceLicensesSlice.actions;

export default deviceLicensesSlice.reducer;