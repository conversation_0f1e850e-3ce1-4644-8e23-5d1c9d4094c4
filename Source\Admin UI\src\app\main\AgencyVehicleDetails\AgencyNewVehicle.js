import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, InputAdornment, } from "@mui/material";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import _ from "@lodash";
import { getVehicleDetails, removeVehicle } from "../administration/store/vehicleMasterSlice";
import history from "@history";
import { newUserAudit } from "../../main/userAuditPage/store/userAuditSlice";
import TablePagination from "@mui/material/TablePagination";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import ConfirmationDialog from "../components/ConfirmationDialog/ConfirmationDialog";
import './AgencyVehicle.css'
import { checkData, getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../utils/utils";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../SharedComponents/ErrorPage/ErrorPage";
import CommonButton from "../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import CircularProgressLoader from '../../main/SharedComponents/CircularProgressLoader/CircularProgressLoader';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    ColumnPinningPosition,
    ColumnPinning,
    IgrGrid,
    IgrColumn
} from "@infragistics/igniteui-react-grids";
import CancelIcon from '@mui/icons-material/Cancel';

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function VehicleNewDetails(props) {
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");

    const gridRef = useRef(null);
    const dispatch = useDispatch();
    const user = useSelector(({ auth }) => auth.user);
    const vehicleMasterData = useSelector(({ administration }) => administration.vehicleMaster.data);
    const vehicleMasterTotalCount = useSelector(({ administration }) => administration.vehicleMaster.totalCount);
    const isLoadingValue = useSelector(({ administration }) => administration.vehicleMaster.isloading);

    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [searchText, setSearchText] = React.useState(null);
    const [data, setData] = React.useState(vehicleMasterData);
    const [countData, setCountData] = React.useState(vehicleMasterTotalCount);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [loading, setLoading] = useState();
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "makeDesc",
    });

    const colorCode = getNavbarTheme();
    const rowsPerPageOptions = getRowsPerPageOptions();
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const routeParams = useParams();

    useEffect(() => {
        dispatch(
            newUserAudit({
                activity: "Access Agency Vehicle",
                user: user,
                appName: "Admin",
            })
        );
        // eslint-disable-next-line
    }, []);

    function onAddClick() {
        history.push(`/admin/vehicle/${routeParams.code}/0`, {
            tempData: {
                orderId: order.id,
                orderDirection: order.direction,
                pageIndex: pageIndex * rowsPerPage,
                rowsPerPage: rowsPerPage,
                searchText: searchText === '' ? null : searchText,
                data: null,
            }
        });
    }

    const onEditClick = (n) => {
        //Added tempData to url as object to access parameters required for get request
        history.push(`/admin/vehicle/${routeParams.code}/${n._id}`, {
            tempData: {
                orderId: order.id,
                orderDirection: order.direction,
                pageIndex: pageIndex * rowsPerPage,
                rowsPerPage: rowsPerPage,
                searchText: searchText === '' ? null : searchText,
                data: n,
            }
        });
    };

    const onDeleteClick = (id) => {
        setOpen(true);
        setRemoveID(id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(
                removeVehicle(
                    removeID,
                    order.id,
                    order.direction,
                    pageIndex * rowsPerPage,
                    rowsPerPage,
                    searchText == '' ? null : searchText,
                    routeParams.code
                )
            );
        }
    };

    const search = useDebounce((search, pageIndex, rowsPerPage, order, code) => {
        dispatch(getVehicleDetails(order.id, order.direction, pageIndex * rowsPerPage,
            rowsPerPage, search, code));
    }, 500);

    useEffect(() => {
        if (searchText !== null && searchText !== '') {
            search(searchText, pageIndex, rowsPerPage, order, routeParams.code);
        } else {
            dispatch(getVehicleDetails(order.id, order.direction, pageIndex * rowsPerPage,
                rowsPerPage, searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, order, searchText, pageIndex, rowsPerPage, routeParams.code]);

    useEffect(() => {
        setData(vehicleMasterData);
        setCountData(vehicleMasterTotalCount)
    }, [vehicleMasterData]);

    useEffect(() => {
        setLoading(isLoadingValue)
    }, [isLoadingValue]);

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const ActionIcons = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;
            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    variant="contained"
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => onEditClick(x)}
                                    size="large"
                                >
                                    <EditIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("delete")}>
                                <IconButton
                                    variant="contained"
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => onDeleteClick(x._id)}
                                    size="large"
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                    }
                </>
            );
        }
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() =>
                                                    history.push(
                                                        `/admin/agencyOptionsList/${routeParams.code}`
                                                    )
                                                }
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 p-32 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <Icon
                                    component={motion.span}
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1, transition: { delay: 0.2 } }}
                                    className="text-32"
                                >
                                    drive_eta
                                </Icon>
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("vehicles")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>
                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                value={searchText}
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText('')}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addNewVehicle" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="whitespace-no-wrap normal-case" btnName={t("addNewVehicle")} parentCallback={onAddClick}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={countData}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>
                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="makeDesc"
                                        field="makeDesc"
                                        header={t("description")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="modelDesc"
                                        header={t("model")}
                                        field="modelDesc"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="vehicleColor1"
                                        header={t("color")}
                                        field="vehicleColor1"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="modelYear"
                                        header={t("modelYear")}
                                        field="modelYear"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="tagNumber"
                                        header={t("tagNumber")}
                                        field="tagNumber"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="tagState"
                                        header={t("tagState")}
                                        field="tagState"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={t("deletethisVehicleMsg")}
                                onClose={handleClose}
                                value={removeID}
                            ></ConfirmationDialog>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    );
}

export default VehicleNewDetails;