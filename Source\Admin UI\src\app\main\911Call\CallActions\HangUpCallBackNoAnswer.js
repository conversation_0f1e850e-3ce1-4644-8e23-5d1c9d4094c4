import React from 'react';
import { makeStyles } from '@mui/styles';
import { withStyles } from '@mui/styles';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import MuiDialogTitle from '@mui/material/DialogTitle';
import MuiDialogContent from '@mui/material/DialogContent';
import MuiDialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import { Icon } from '@mui/material';

const useStyles = makeStyles(() => ({
  root: {
    '& > *': {
      // margin: theme.spacing(1),
      width: '95%'
    },
  },
}));

const styles = (theme) => ({
  root: {
    margin: 0,
    // padding: theme.spacing(2),
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle disableTypography className={classes.root} {...other}>
      <Typography variant="h6">{children}</Typography>
      {onClose ? (
        <IconButton aria-label="close" className={classes.closeButton} onClick={onClose}>
          <CloseIcon />
        </IconButton>
      ) : null}
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles(() => ({
  root: {
    // padding: theme.spacing(2),
    width: 500,
    height: 500
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);


const fw_8 = {
  fontWeight: '600'
}

export default function HangUpCallBackNoAnswerComponent(props) {
  const { t } = useTranslation('laguageConfig');
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Button variant="contained" color="secondary" size="small" onClick={handleClickOpen}>
        Call&nbsp;Back&nbsp;No&nbsp;Answer
      </Button>
      <Dialog onClose={handleClose} aria-labelledby="customized-dialog-title" open={open}>
        <DialogTitle id="customized-dialog-title" onClose={handleClose}>
          {t('callBackNoAnswer')}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container>

            <Grid item lg={4}>
              <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
                <CardContent>
                  <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                    <span style={fw_8}>Recommended Units</span>
                    <Grid container style={{ fontSize: '1.15rem' }}>
                      <Grid item xs={4}>J204</Grid>
                      <Grid item xs={4}><span style={fw_8}>Smith J, </span></Grid>
                      <Grid item xs={4}> <span style={fw_8}>1.2mi </span></Grid>
                    </Grid>
                    <Grid container style={{ fontSize: '1.15rem' }}>
                      <Grid item xs={4}>J309</Grid>
                      <Grid item xs={4}><span style={fw_8}>Thomas T, </span></Grid>
                      <Grid item xs={4}> <span style={fw_8}>2.0mi </span></Grid>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item lg={8}>
              <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
                <CardContent>
                  {(props.value.PacketClassofService === "WRLS" || props.value.PacketClassofService === "WPH1") &&
                    <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                      <Grid item xs={4} style={{ padding: '0px' }}>
                        <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                      </Grid>
                      <Grid item xs={4} style={{ padding: '0px' }}>
                        <span style={fw_8}>  {props.value.PacketCallTime}:{new Date(props.value.PacketCallReceivedDT).getSeconds()}  {props.value.PacketCallDate}</span>
                      </Grid>
                      <Grid item xs={2} style={{ padding: '0px' }}>
                        <span style={fw_8}> {props.value.PacketClassofService}</span>
                      </Grid>
                      <Grid item xs={2} style={{ padding: '0px' }}>
                        <span style={fw_8}>{props.value.SubCall && props.value.SubCall.filter(call => call.PacketCustomerName.trim() !== props.value.PacketCustomerName.trim()).length > 0 && "Moving"}</span>
                        {/* <span style={fw_8}>{props.count > 1 && "Moving"}</span> */}
                      </Grid>
                      <Grid item xs={12} style={{ padding: '0px' }}>
                        {t('towerInfo')}: <span style={fw_8}>{props.value.PacketCustomerName}</span>
                      </Grid>
                      <Grid item xs={12} style={{ padding: '0px' }}>
                        <span style={fw_8}>{props.value.PacketStreetAddress}, {props.value.PacketCity}, {props.value.PacketState}, {props.value.PacketState} </span>
                      </Grid>
                      <Grid item xs={6} style={{ padding: '0px' }}>
                        {t('lat')}: <span style={fw_8}>{props.value.Packety}</span>
                      </Grid>
                      <Grid item xs={6} style={{ padding: '0px' }}>
                        {t('long')}: <span style={fw_8}>{props.value.Packetx}</span>
                      </Grid>
                    </Grid>
                  }

                  {(props.value.PacketClassofService === "WPH2" || props.value.PacketClassofService === "TLMA" || props.value.PacketClassofService === "TELM") &&
                    <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                      <Grid item lg={4}>
                        <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                      </Grid>
                      <Grid item lg={4}>
                        <span style={fw_8}>  {props.value.PacketCallTime}:{new Date(props.value.PacketCallReceivedDT).getSeconds()}  {props.value.PacketCallDate}</span>
                      </Grid>
                      <Grid item lg={2}>
                        <span style={fw_8}> {props.value.PacketClassofService}</span>
                      </Grid>
                      <Grid item lg={2}>
                        <span style={fw_8}>{props.value.SubCall && props.value.SubCall.filter(call => call.Packety !== props.value.Packety || call.Packetx !== props.value.Packetx).length > 0 && "Moving"}</span>
                        {/* <span style={fw_8}>{props.count > 1 && "Moving"}</span> */}
                      </Grid>
                      <Grid item lg={12}>
                        {t('towerInfo')}: <span style={fw_8}>{props.value.PacketCustomerName}</span>
                      </Grid>
                      <Grid item lg={12}>
                        <span style={fw_8}>{props.value.PacketStreetAddress}, {props.value.PacketCity}, {props.value.PacketState}</span>
                      </Grid>
                      <Grid item lg={6}>
                        {t('lat')}: <span style={fw_8}>{props.value.Packety}</span>
                      </Grid>
                      <Grid item lg={6}>
                        {t('long')}: <span style={fw_8}>{props.value.Packetx}</span>
                      </Grid>
                    </Grid>
                  }


                  {(props.value.PacketClassofService !== "WRLS" && props.value.PacketClassofService !== "WPH1" && props.value.PacketClassofService !== "WPH2" && props.value.PacketClassofService !== "TLMA" && props.value.PacketClassofService !== "TELM") &&
                    <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                      <Grid item xs={4}>
                        <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                      </Grid>
                      <Grid item xs={4}>
                        <span style={fw_8}>  {props.value.PacketCallTime}:{new Date(props.value.PacketCallReceivedDT).getSeconds()}  {props.value.PacketCallDate}</span>
                      </Grid>
                      <Grid item xs={2}>
                        <span style={fw_8}> {props.value.PacketClassofService}</span>
                      </Grid>
                      <Grid item xs={12}>
                        {t('caller')}: <span style={fw_8}>{props.value.PacketCustomerName}</span>
                      </Grid>
                      <Grid item xs={12}>
                        {t('location')}: <span style={fw_8}>{props.value.PacketStreetNumber}, {props.value.PacketStreetAddress}, {props.value.PacketCity}, {props.value.PacketState}, {props.value.PacketLocationInfo}</span>
                      </Grid>
                      <Grid item xs={6}>
                        {t('lat')}: <span style={fw_8}>{props.value.Packety}</span>
                      </Grid>
                      <Grid item xs={6}>
                        {t('long')}: <span style={fw_8}>{props.value.Packetx}</span>
                      </Grid>
                    </Grid>
                  }
                </CardContent>
              </Card>

              <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
                <CardContent>
                  <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                    <Grid item xs={12} style={{ padding: '0px' }}>
                      <IconButton><Icon> add_location_alt</Icon></IconButton>
                      <IconButton><Icon> event_note</Icon></IconButton>
                      <IconButton><Icon> directions_car</Icon></IconButton>
                      <IconButton><Icon> attach_file</Icon></IconButton>
                      <IconButton><Icon> map</Icon></IconButton>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>


              <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
                <CardContent>
                  <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                    <Grid item xs={12} style={{ padding: '0px', height: '50px' }}>
                      <span style={fw_8}>Notes Entry Area </span>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
                <CardContent>
                  <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                    <Grid item xs={12} style={{ padding: '0px' }}>
                      <span style={{ textAlign: 'center', fw_8 }}>Timeline </span>
                      <Grid container spacing={1} style={{ fontSize: '1.15rem' }}>
                        <Grid item xs={6}>
                          00:51
                        </Grid>
                        <Grid item xs={6}>
                          <span style={fw_8}>Bennett </span>
                        </Grid>
                        <Grid item xs={12}> <span style={fw_8}>Dropped 911 call/ No answer </span>
                        </Grid>
                      </Grid>
                    </Grid>

                    <Grid container spacing={1} style={{ fontSize: '1.15rem' }} className="mt-8">
                      <Grid item xs={6}>
                        00:51
                      </Grid>
                      <Grid item xs={6}>
                        <span style={fw_8}>Bennett </span>
                      </Grid>
                      <Grid item xs={12}> <span style={fw_8}>Dispatched J204 </span>
                      </Grid>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>



          </Grid>
        </DialogContent>
        <DialogActions>
          <Button autoFocus onClick={handleClose} color="primary">
            Back
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

