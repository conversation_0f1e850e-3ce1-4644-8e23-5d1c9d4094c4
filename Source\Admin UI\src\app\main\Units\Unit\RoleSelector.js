import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import Button from '@mui/material/Button';
import { forwardRef } from 'react';
import { useTranslation } from "react-i18next";
import RoleModel from './RoleModel';
import RoleInput from './RoleInput';
import makeStyles from '@mui/styles/makeStyles';

const useStyles = makeStyles({
    w50: {
        width: '100%',
        marginBottom: "1.6rem"
    },
    root: {
        display: 'flex',
    },

});

const RoleSelector = forwardRef(({ value, onChange, className }, ref) => {
    const { t } = useTranslation("laguageConfig");
    const classes = useStyles();

    return (
        <div className={classes.w50} ref={ref}>
            <div className={classes.root}>
                {value.map((item, index) => (
                    <RoleInput
                        id={index + 1}
                        value={item}
                        key={index}
                        onChange={(val) => {
                            onChange(value.map((_item, _index) => (index === _index ? val : _item)));
                        }}
                        onRemove={() => {
                            onChange(value.filter((_item, _index) => index !== _index));
                        }}
                        hideRemove={value.length === 1}
                    />
                ))}
            </div>
            <Button
                className="group inline-flex items-center -ml-4 px-4 rounded cursor-pointer"
                onClick={() => onChange([...value, RoleModel().roles[0]])}
            >
                <FuseSvgIcon size={20}>heroicons-solid:plus-circle</FuseSvgIcon>

                <span className="ml-8 font-medium text-secondary group-hover:underline">
                    {t("addRole")}
                </span>
            </Button>
        </div >
    );
});

export default RoleSelector;