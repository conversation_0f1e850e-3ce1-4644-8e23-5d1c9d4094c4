import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Card, AppBar, CardContent, Toolbar, TextField } from '@mui/material';
import _ from '@lodash';
import { useParams } from 'react-router-dom';
import FormControl from '@mui/material/FormControl';
import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker';
import { archiveChatHistoriesUpdate } from '../store/usersSlice';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';


function archiveChatHistories(props) {
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();

    const routeParams = useParams();
    const [selectedArchiveDate, setSelectedArchiveDate] = React.useState(null);
    const [alertMessage, setAlertmessage] = React.useState(null);
    const isloadingvalue = useSelector(({ administration }) => administration.user.isloading);
    const [loading, setLoading] = useState();

    const handleArchiveDateChange = (date) => {
        setSelectedArchiveDate(new Date(date));
        setAlertmessage(null)
    };

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    const btnArchiveChatClick = () => {
        if (selectedArchiveDate === null) {
            setAlertmessage("Please select archive chat older than date")
            return
        }
        let newdate = new Date(selectedArchiveDate)
        // Add a day
        newdate.setDate(newdate.getDate() + 1)
        dispatch(archiveChatHistoriesUpdate({ date: newdate }))
    }



    return (
        <div class="p-16 w-2/4">
            {loading && <CircularProgressLoader loading={loading} />}
            <Card className="w-full mb-16 rounded-8 ml-4 shadow ">
                <AppBar position="static" elevation={0}>
                    <Toolbar className="px-8">
                        <Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
                            {t('archivechatHistories')}
                        </Typography>
                    </Toolbar>
                </AppBar>

                <CardContent>
                    <div className="mx-auto">
                        <FormControl fullWidth className="w-full">
                            <DesktopDatePicker
                                name="ArchiveDate"
                                label={t("archiveChatOlderThan")}
                                inputFormat="MM/dd/yyyy"
                                defaultDate={" "}
                                value={selectedArchiveDate}
                                onChange={handleArchiveDateChange}
                                emptyLabel="custom label"
                                renderInput={(field) => <TextField  {...field} sx={{ mt: 2 }} />}
                            />
                        </FormControl>
                    </div>
                    {alertMessage !== null &&
                        <p style={{ color: "red" }}>{alertMessage}</p>
                    }
                    <div className="mx-auto">
                        <Button
                            style={{ float: "right" }}
                            onClick={() => btnArchiveChatClick()}
                            variant="contained"
                            color="primary"
                            className="normal-case m-16"
                            aria-label="REGISTER"
                            value="legacy">
                            {t('update')}
                        </Button>

                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

export default archiveChatHistories;

