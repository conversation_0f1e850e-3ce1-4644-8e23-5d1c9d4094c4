import { createSlice } from '@reduxjs/toolkit';

export const setGlobalPersonDetails = (data) => dispatch => {
    return dispatch(SetGlobalPersonDetails(data))
}

export const getGlobalPersonData = (data) => async dispatch => {
    return dispatch(SetGlobalSearchPersonData(data));
};

export const clearGlobalSearchPersonData = () => async dispatch => {
    return dispatch(ClearGlobalSearchPersonData());
};

const initialState = {
    globalPersonData: [],
    selectedGlobalSearchData: []
};

const personSlice = createSlice({
    name: 'person',
    initialState,
    reducers: {
        SetGlobalPersonDetails: (state, action) => {
            state.globalPersonData = action.payload;
        },
        SetGlobalSearchPersonData: (state, action) => {
            state.selectedGlobalSearchData = state.globalPersonData.filter(x => x._id == action.payload || x.PersonUniqueId == action.payload);
        },
        ClearGlobalSearchPersonData: (state, action) => {
            state.selectedGlobalSearchData = [];
        },
    },
    extraReducers: {}
});

export const {
    SetGlobalPersonDetails,
    SetGlobalSearchPersonData,
    ClearGlobalSearchPersonData
} = personSlice.actions;

export default personSlice.reducer;