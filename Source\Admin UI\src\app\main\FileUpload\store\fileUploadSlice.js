import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from "axios";

const initialState = {
    success: false,
    error: {
        contact: {}
    },
    uploadFileObj: [],
    savedFileObj: [],
    savedFileView: []
};

const fileUploadSlice = createSlice({
    name: 'fileUpload',
    initialState,
    reducers: {
        setSAVEDFILEOBJLIST: (state, action) => {
            state.savedFileObj = action.payload;
        },
        setSAVEDFILEOBJ: (state, action) => {
            state.savedFileView = action.payload;
        },
        setUPLOADFILEOBJ: (state, action) => {
            state.uploadFileObj = action.payload;
        },
        setUPLOADSUCCESS: (state, action) =>{
            state.success = action.payload;
        }
    },
    extraReducers: {}
});

export default fileUploadSlice.reducer;

// Actions
export const {
    setUPLOADFILEOBJ,
    setSAVEDFILEOBJLIST,
    setSAVEDFILEOBJ,
    setUPLOADSUCCESS
} = fileUploadSlice.actions;


//Upload seleted documents
export const uploadFileObjList = (formData) => async dispatch => {
    try {
        await axios.post(`fileupload/upload`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
            .then(response => {
                if (response.status === 400) {
                    dispatch(showMessage({
                        message: response, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'error'
                    }));
                }
                else {
                    dispatch(showMessage({
                        message: 'Files uploaded sucessfully..', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(setUPLOADSUCCESS(true));
                }
            }).catch(error => {
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })	,


                );
            });
    } catch (e) {
        return console.error(e.message);
    }
}

//Get saved file List
export const getSavedFileObjList = () => async dispatch => {
    
    try {

        await axios.get(`fileupload/listObjects`)
            .then(response => {
                return dispatch(setSAVEDFILEOBJLIST(response.data.Contents));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

//Get a saved file object
export const getSavedFileObj = (key) => async dispatch => {
    try {
        if (key.length === 0) {
            return dispatch(setSAVEDFILEOBJ([]));
        }
        else {
            await axios.get(`fileupload/getObject?fileKey=` + key)
                .then(response => {
                    return dispatch(setSAVEDFILEOBJ(response.data));
                }
                );
        }
    } catch (e) {
        return console.error(e.message);
    }
}

//Current selected file is stored till file is not uploaded to server
export const uploadFileObject = (fileList) => async dispatch => {
    try {
        return dispatch(setUPLOADFILEOBJ(fileList));
    } catch (e) {
        return console.error(e.message);
    }
}
