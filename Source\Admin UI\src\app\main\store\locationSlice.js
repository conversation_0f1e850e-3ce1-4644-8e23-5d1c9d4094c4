import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';


export const getLocationDetails = (searchText) => async dispatch => {
    try {
        await axios.post(process.env.REACT_APP_API_URL.concat('dispatch/api/location/locationDetails'), { searchText })
            .then(response => {
                dispatch(locationListSuccess(response.data));
                if (response.data.length > 0) {
                    dispatch(showMessage({
                        message: 'Location details found.', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                }
                else {
                    dispatch(showMessage({
                        message: 'Location details not found.', autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }));
                }
            })
            .catch(error => {
                dispatch(locationListError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
};





const initialState = {
    locationsuccess: false,
    locationdata: [],
    locationerror: {
        username: null,
        password: null
    }
};


const locationSlice = createSlice({
    name: 'location',
    initialState,
    reducers: {
        locationListSuccess: (state, action) => {
            state.locationsuccess = true;
            state.locationdata = action.payload;
        },
        locationListError: (state, action) => {
            state.locationsuccess = false;
            state.locationdata = [];
        }
    },
    extraReducers: {}
});

export const {
    locationListSuccess,
    locationListError
} = locationSlice.actions;

export default locationSlice.reducer;
