/* horizontal table  */
/* .contact-container {
  padding: 10px;
}

.fields-table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

.fields-table th,
.fields-table td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: center;
  white-space: nowrap;
}

.fields-table th {
  background-color: #f9f9f9;
}

.field-item-old {
  color: red;
  font-size: 15px;
}

.field-item-new {
  color: green;
  font-size: 15px;
} */



/* vertical table  */
.contact-container {
  padding: 10px;
}

.fields-table {
  width: 100%;
  border-collapse: collapse;
}

.fields-table th,
.fields-table td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}

.fields-table th {
  background-color: #f9f9f9;
}

.field-item-old {
  color: red;
}

.field-item-new {
  color: green;
}
