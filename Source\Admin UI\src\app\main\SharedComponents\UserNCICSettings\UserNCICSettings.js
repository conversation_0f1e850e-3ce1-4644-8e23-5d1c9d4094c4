import Button from '@mui/material/Button';
import React, { useState, useRef, useEffect } from 'react';
import InputAdornment from '@mui/material/InputAdornment';
import { useSelector, useDispatch } from 'react-redux';
import Icon from '@mui/material/Icon';
import { useTranslation } from 'react-i18next';
import { Toolbar, Grid, CircularProgress, IconButton, Typography } from '@mui/material';
import { UserNcicUpdate } from '../../../auth/store/userSlice';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

const ncicSchema = yup.object().shape({
    ncicUserName: yup.string()
        .required('Please enter your NCIC userName.'),
    ncicPassword: yup
        .string()
        .required('Please enter your NCIC password.')
});

const ncicDefaultValues = {
    ncicUserName: '',
    ncicPassword: '',
};

function UserNCICSettings(props) {
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();
    const [showPassword, setShowPassword] = useState(false);
    const [loading, setLoading] = React.useState(false);

    const timer = React.useRef();
    const user = useSelector(({ auth }) => auth.user);
    const nciccrendentails = useSelector(({ auth }) => auth.user.nciccrendentails);

    const { control: ncicControl, setValue: setNcicValue, formState: ncicFormState, handleSubmit: NcicHandleSubmit,
        reset: ncicReset, trigger: ncicTrigger, setError: setNcicErrors } = useForm({
            mode: 'onChange',
            defaultValues: ncicDefaultValues,
            resolver: yupResolver(ncicSchema),
        });

    const { isValid: isNcicValid, dirtyFields: ncicDirtyFields, errors: ncicErrors } = ncicFormState;

    const onNcicSubmit = async (model) => {
        setLoading(true);
        await dispatch(UserNcicUpdate({
            _id: user.data.id,
            ncicUserName: model.ncicUserName,
            ncicPassword: model.ncicPassword
        }));
        setLoading(false);
    };

    useEffect(() => {
        if (nciccrendentails != undefined) {
            setNcicValue('ncicPassword', nciccrendentails.ncicPassword);
            setNcicValue('ncicUserName', nciccrendentails.ncicUserName);
        } else {
            setNcicValue('ncicPassword', user.data?.ncicPassword);
            setNcicValue('ncicUserName', user.data?.ncicUserName);
        }
    }, [setNcicValue, ncicReset, ncicTrigger]);

    return <>
        <form key={1} className="flex flex-col justify-center w-full mt-6" onSubmit={NcicHandleSubmit(onNcicSubmit)}>
            {
                user.data?.lastNcicChange &&
                <Typography className="font-semibold text-14 mb-6">{t("lastNcicChange")}: {new Date(user.data?.lastNcicChange)?.toLocaleString() ?? null}</Typography>
            }
            <Controller
                name="ncicUserName"
                control={ncicControl}
                render={({ field }) => (
                    <TextField
                        {...field}
                        className="mb-16"
                        label={t('userName')}
                        type="text"
                        disabled={loading}
                        error={!!ncicErrors.ncicUserName}
                        helperText={ncicErrors?.ncicUserName?.message}
                        variant="outlined"
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <Icon className="text-20" color="action">
                                        account_circle
                                    </Icon>
                                </InputAdornment>
                            ),
                        }}
                        required
                    />
                )}
            />
            <Controller
                name="ncicPassword"
                control={ncicControl}
                render={({ field }) => (
                    <TextField
                        {...field}
                        className="mb-16"
                        label={t('password')}
                        type="text"
                        disabled={loading}
                        error={!!ncicErrors.ncicPassword}
                        helperText={ncicErrors?.ncicPassword?.message}
                        variant="outlined"
                        InputProps={{
                            className: 'pr-2',
                            type: showPassword ? 'text' : 'password',
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton onClick={() => setShowPassword(!showPassword)} size="large">
                                        <Icon className="text-20" color="action">
                                            {showPassword ? 'visibility' : 'visibility_off'}
                                        </Icon>
                                    </IconButton>
                                </InputAdornment>
                            ),
                        }}
                        required
                    />
                )}
            />

            <div >
                <Button
                    type="buttom"
                    variant="contained"
                    color="primary"
                    className="normal-case m-16"
                    value="legacy">
                    {t('update')}
                </Button>
                {loading && <CircularProgress size={24} />}
            </div>
        </form>
    </>
}

export default UserNCICSettings;