import TextField from '@mui/material/TextField';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup/dist/yup';
import * as yup from 'yup';
import IconButton from '@mui/material/IconButton';
import { useEffect } from 'react';
import { useTranslation } from "react-i18next";
import makeStyles from '@mui/styles/makeStyles';

const schema = yup.object().shape({
    role: yup.string(),
});

const defaultValues = {
    role: '',
};

const useStyles = makeStyles({
    w50: {
        width: '100%',
        marginBottom: "1.6rem"
    }, root: {
        display: 'flex',
    },

});

function RoleInput(props) {
    const { t } = useTranslation("laguageConfig");
    const classes = useStyles();
    const { value, hideRemove, id } = props;

    const { control, formState, handleSubmit, reset } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });

    const { isValid, dirtyFields, errors } = formState;

    useEffect(() => {
        reset(value);
    }, [reset, value]);

    function onSubmit(data) {
        props.onChange(data);
    }

    return (
        <form className="flex space-x-16" onChange={handleSubmit(onSubmit)}>
            <Controller
                className={classes.w50}
                name="role"
                control={control}
                render={({ field }) => (
                    <TextField
                        {...field}
                        className="mt-8 mb-16 mx-4"
                        label={t('role') +' '+ id} 
                        type="text"
                        variant="outlined"
                        required
                        fullWidth
                    />
                )}
            />
            {!hideRemove && (
                <IconButton
                    onClick={(ev) => {
                        ev.stopPropagation();
                        props.onRemove();
                    }}
                >
                    <FuseSvgIcon size={20}>heroicons-solid:trash</FuseSvgIcon>
                </IconButton>
            )}
        </form>
    );
}

RoleInput.defaultProps = {
    hideRemove: false,
};

export default RoleInput;