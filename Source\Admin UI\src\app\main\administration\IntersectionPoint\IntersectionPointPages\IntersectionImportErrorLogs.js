import React, { useState, useEffect, useRef, useMemo } from "react";
import { useSelector, useDispatch } from 'react-redux';
import { motion } from "framer-motion";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import FusePageCarded from "@fuse/core/FusePageCarded";
import DnsIcon from '@mui/icons-material/Dns';
import { StyledEngineProvider, ThemeProvider, Typography, Paper, Icon, Input, Button, TablePagination, Tooltip, Stack, IconButton, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Collapse } from "@mui/material";
import { checkData, getNavbarTheme, getRowsPerPageOptions } from "src/app/main/utils/utils";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import ErrorRoundedIcon from '@mui/icons-material/ErrorRounded';
import history from "@history";
import CircularProgressLoader from "src/app/main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { getIntersectionPointErrorLogsById } from "src/app/main/store/importFromExcelSlice";
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import { useWindowResizeHeight } from "../../../utils/utils";

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "Country",
        align: "left",
        disablePadding: false,
        label: "Country",
        sort: true,
    },
    {
        id: "Str1",
        align: "left",
        disablePadding: false,
        label: "Street 1",
        sort: true,
    },
    {
        id: "Str2",
        align: "left",
        disablePadding: false,
        label: "Street 2",
        sort: true,
    },
    {
        id: "Str3",
        align: "left",
        disablePadding: false,
        label: "Street 3",
        sort: true,
    },
    {
        id: "Str4",
        align: "left",
        disablePadding: false,
        label: "Street 4",
        sort: true,
    },
    {
        id: "City",
        align: "left",
        disablePadding: false,
        label: "City",
        sort: true,
    },
    {
        id: "State",
        align: "left",
        disablePadding: false,
        label: "State",
        sort: true,
    },
    {
        id: "Zip5",
        align: "left",
        disablePadding: false,
        label: "PostCode",
        sort: true,
    },
    {
        id: "PostCodeEx",
        align: "left",
        disablePadding: false,
        label: "PostCodeEx",
        sort: true,
    },
    {
        id: "LAT_Y",
        align: "left",
        disablePadding: false,
        label: "Latitude",
        sort: true,
    },
    {
        id: "LON_X",
        align: "left",
        disablePadding: false,
        label: "Longitude",
        sort: true,
    },
    {
        id: "Elevation",
        align: "left",
        disablePadding: false,
        label: "Elevation",
        sort: true,
    },
    {
        id: "ESN",
        align: "left",
        disablePadding: false,
        label: "ESN",
        sort: true,
    },
    {
        id: "Community",
        align: "left",
        disablePadding: false,
        label: "Community",
        sort: true,
    },
    {
        id: "County",
        align: "left",
        disablePadding: false,
        label: "County",
        sort: true,
    },
    {
        id: "Municipality",
        align: "left",
        disablePadding: false,
        label: "Municipality",
        sort: true,
    },
    {
        id: "PoliceZone",
        align: "left",
        disablePadding: false,
        label: "Police Zone",
        sort: true,
    },
    {
        id: "FireZone",
        align: "left",
        disablePadding: false,
        label: "Fire Zone",
        sort: true,
    },
    {
        id: 'action',
        align: 'left',
        disablePadding: false,
        label: 'action',
        sort: true
    }

];


IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const IntersectionImportErrorLogs = () => {
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const gridRef = useRef(null);
    let colorCode = getNavbarTheme();
    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    const rowsPerPageOptions = getRowsPerPageOptions();

    const intersectionSummaryId = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.intersectionSummaryId);
    const intersectionPointErrorLogs = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.intersectionPointErrorLogs);
    const intersectionPointErrorLogsCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.intersectionPointErrorLogsCount);
    const isLoading = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.isLoading);
    const selectedCounty = useSelector(({ administration }) => administration.masterAddressSlice.selectedCounty)
    const selectedCountyStateCode = useSelector(({ administration }) => administration.masterAddressSlice.selectedCountyStateCode)
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "City",
    });
    const [errorLogs, setErrorLogs] = useState([]);
    const [county, setCounty] = useState(null);
    const [countyState, setCountyState] = useState(null);
    const [totalCount, setTotalCount] = useState(intersectionPointErrorLogsCount);
    const [pageIndex, setPageIndex] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);

    const gridSize = "Large";

    // Set the grid size on component mount
    useEffect(() => {
        const grid = document.getElementById("grid");
        if (grid) {
            grid.style.setProperty('--ig-size', `var(--ig-size-${gridSize.toLowerCase()})`);
        }
    }, [gridSize]);

    useEffect(() => {
        if (intersectionPointErrorLogs !== null && intersectionPointErrorLogs !== undefined) {
            setErrorLogs(intersectionPointErrorLogs);
            setTotalCount(intersectionPointErrorLogsCount);
        }
    }, [intersectionPointErrorLogs, intersectionPointErrorLogsCount]);

    useEffect(() => {
        if (selectedCounty !== null && selectedCountyStateCode !== null) {
            setCounty(selectedCounty);
            setCountyState(selectedCountyStateCode);
        }
    }, [selectedCounty]);

    useEffect(() => {
        if (intersectionSummaryId !== null && intersectionSummaryId !== undefined) {
            dispatch(getIntersectionPointErrorLogsById(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, intersectionSummaryId));
        }
    }, [order, pageIndex, rowsPerPage])

    const ActionIcons = (n) => {
        let x = checkData(n);
        return (
            <div className="flex">
                {/* <Button variant="contained" color="primary">{t("View Details")}</Button> */}
                <div className="flex">
                    <Tooltip title={t("viewDetails")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            // onClick={() => viewSummaryError(x)}
                            size="large"
                        >
                            <ErrorRoundedIcon />
                        </IconButton>
                    </Tooltip>
                </div>
            </div >
        );
    };

    const rowData = (errorLogs !== null && errorLogs !== undefined) && errorLogs.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id];
            row["_id"] = item._id;
            row["City"] = item.City ? item.City : '';
            row["Community"] = item.Community ? item.Community : '';
            row["Zip5"] = item.Zip5 ? item.Zip5 : '';
            row["State"] = item.State ? item.State : '';
            row["County"] = item.County ? item.County : '';
            row["LAT_Y"] = item.LAT_Y ? item.LAT_Y : '';
            row["LON_X"] = item.LON_X ? item.LON_X : '';
            row["StreetNames"] = item.StreetNames ? item.StreetNames : '';
            row["Str1"] = item.Str1 ? item.Str1 : '';
            row["Str2"] = item.Str2 ? item.Str2 : '';
            row["Str3"] = item.Str3 ? item.Str3 : '';
            row["Str4"] = item.Str4 ? item.Str4 : '';
            row["Municipality"] = item.Municipality ? item.Municipality : '';
            row["PoliceZone"] = item.PoliceZone ? item.PoliceZone : '';
            row["FireZone"] = item.FireZone ? item.FireZone : '';
            // row["errors"] = item.errors ? item.errors : '';
            // row["action"] = ActionIcons(item);
        });
        return row;
    });

    const navigateToIntersectionList = () => {
        history.push('/admin/intersectionImportLogs');
    }

    const handleChangePage = (event, value) => {
        setPageIndex(value);
    }

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(event.target.value);
        setPageIndex(0);
    }

    const errorTemplate = (props) => {
        const data = props.dataContext.implicit.errors;

        return (
            <div className="contact-container">
                {
                    data.map(error => <div style={{ marginTop: '10px', fontSize: '15px' }}>{error}</div>)
                }
            </div>
        );
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    return (
        <div>
            {isLoading && <CircularProgressLoader loading={isLoading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%", marginBottom: "50px" }}>
                        <div className="flex flex-1 items-center justify-between">
                            <div className="flex items-center">
                                <Tooltip title="Back to Import Logs" style={{ float: "right" }}>
                                    <Stack direction="row" spacing={2}>
                                        <Button
                                            className="backButton"
                                            variant="contained"
                                            startIcon={<ArrowBackOutlinedIcon />}
                                            onClick={() => navigateToIntersectionList()}
                                        >
                                            {t("back")}
                                        </Button>
                                    </Stack>
                                </Tooltip>
                            </div>
                        </div>
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={{ paddingTop: "20px" }}
                        >
                            <div className="flex items-center">
                                <DnsIcon style={{ fontSize: '40px' }} />
                                <div className="flex flex-col">
                                    <Typography
                                        component={motion.span}
                                        initial={{ x: -20 }}
                                        animate={{ x: 0, transition: { delay: 0.2 } }}
                                        delay={300}
                                        className="hidden sm:flex mx-0 sm:mx-12"
                                        variant="h6"
                                    >
                                        {t("intersectionErrorLogs")}
                                    </Typography>
                                    <Typography
                                        component={motion.span}
                                        initial={{ x: -20 }}
                                        animate={{ x: 0, transition: { delay: 0.2 } }}
                                        delay={300}
                                        className="hidden sm:flex mx-0 sm:mx-12"
                                        variant="body2"
                                    >
                                        County: {county}, State: {countyState}
                                    </Typography>
                                </div>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={/*searchText*/ ''}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                            // onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                        </div>
                    </div>
                }
                content={
                    < div className="w-full flex flex-col" >
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        'aria-label': 'Previous Page'
                                    }}
                                    nextIconButtonProps={{
                                        'aria-label': 'Next Page'
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={errorLogs}
                                    primaryKey="_id"
                                    detailTemplate={errorTemplate}
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="City"
                                        header={t("cityPostalComm")}
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("community")}
                                        field="Community"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("streetName")}
                                        field="StreetNames"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("zipCode")}
                                        field="Zip5"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("state")}
                                        field="State"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("longitude")}
                                        field="LON_X"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("latitude")}
                                        field="LAT_Y"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                </IgrGrid>
                            </div>
                        </div>
                    </div >
                }
            />
        </div>
    )
}


export default IntersectionImportErrorLogs;