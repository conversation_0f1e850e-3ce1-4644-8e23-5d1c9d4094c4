import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import Button from '@mui/material/Button';
import { DialogActions, TextField, } from "@mui/material";
import { IsCheckMongooseObjectId } from '../../utils/utils';
import { createNibrs, getNibrsList } from '../store/nibrsCodeSlice';

const defaultValues = {
    code: '',
    group: '',
    title: '',
    description: ''
};

const NibrsCodeAdd = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [title, setTitle] = React.useState("");
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [PagingDetails, setPagingDetails] = React.useState();
    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues
    });
    const { isValid, dirtyFields, errors } = formState;

    useImperativeHandle(ref, () => ({
        handleOpen(flag, title, pagingDetails, data) {
            setIsUpdate(flag);
            setTitle(title);
            setNibrsvalue(data);
            setPagingDetails(pagingDetails)
            handleClickOpen();
        },
    }));

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        reset();
        setOpen(false);
        defaultValues;
    };

    const setNibrsvalue = async (data) => {
        if (data !== undefined) {
            setValue('_id', data._id);
            setValue('code', data.code);
            setValue('group', data.group);
            setValue('title', data.title);
            setValue('description', data.description);
        }
    };

    function onSubmit(model) {
        let body = {}
        const items = {
            code: model.code,
            group: model.group,
            title: model.title,
            description: model.description,
        }
        if (isUpdate) {
            body.isUpdate = true;
            items._id = model._id;
        } else {
            body.isUpdate = false;
        }
        body.data = items;
        dispatch(createNibrs(body));
        handleClose();
        dispatch(getNibrsList(
            PagingDetails.sortField, PagingDetails.sortDirection, PagingDetails.pageIndex,
            PagingDetails.pageLimit, PagingDetails.searchText
        ));
    }

    return (
        <div>
            <Dialog
                fullWidth={true} maxWidth='md' open={open} onClose={handleClose} aria-labelledby="responsive-dialog-title" >
                <DialogTitle style={{ display: "flex", justifyContent: "space-between" }} id="responsive-dialog-title">
                    <div>
                        {t(`${title}`)} {t("nibrsCode")}
                    </div>
                    <div>
                        <IconButton onClick={handleClose} aria-label="show more">
                            <Icon>close</Icon>
                        </IconButton>
                    </div>
                </DialogTitle>
                <form name="registerForm" noValidate className="flex flex-col justify-center w-full pb-16" onSubmit={handleSubmit(onSubmit)} autoSave={false} >
                    <DialogContent dividers>
                        <Controller
                            name="code"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("code")}
                                    type="text"
                                    error={!!errors.type}
                                    helperText={errors?.type?.message}
                                    variant="outlined"
                                    required
                                    onChange={(e) => {
                                        field.onChange(e);
                                        const value = e.target.value;
                                        setValue("group", value.startsWith("90") ? "B" : "A");
                                    }}
                                />
                            )}
                        />

                        <Controller
                            name="group"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("group")}
                                    type="text"
                                    error={!!errors.type}
                                    helperText={errors?.type?.message}
                                    variant="outlined"
                                    disabled
                                />
                            )}
                        />

                        <Controller
                            name="title"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("title")}
                                    type="text"
                                    error={!!errors.name}
                                    helperText={errors?.name?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="description"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("description")}
                                    type="text"
                                    error={!!errors.description}
                                    helperText={errors?.description?.message}
                                    variant="outlined"
                                />
                            )}
                        />
                    </DialogContent>
                    <DialogActions>
                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>
                    </DialogActions>
                </form>
            </Dialog>
        </div>
    );

});

export default NibrsCodeAdd;