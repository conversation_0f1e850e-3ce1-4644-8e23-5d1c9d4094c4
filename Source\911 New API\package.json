{"name": "reality-web-api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js", "prod": "node dist/api.bundle.js", "build": "webpack"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@hapi/joi": "^17.1.1", "@mapbox/leaflet-pip": "^1.1.0", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "circular-json": "^0.5.9", "cors": "^2.8.5", "crypto": "^1.0.1", "cryptr": "^6.0.2", "dotenv": "^8.2.0", "express": "^4.17.1", "express-locale": "^2.0.0", "formidable": "^1.2.2", "jsonwebtoken": "^8.5.1", "leaflet": "^1.7.1", "i18next": "^22.4.9", "i18next-fs-backend": "^2.1.1", "i18next-http-middleware": "^3.2.2", "loadash": "^1.0.0", "lodash": "^4.17.20", "mongoose": "^7.4.1", "node-polyglot": "^2.4.0", "nodemailer": "^6.4.8", "nodemon": "^2.0.4", "proj4": "^2.6.3", "socket.io": "^3.0.4", "transform-coordinates": "^1.0.0", "axios": "^0.21.1"}, "devDependencies": {"@babel/core": "^7.21.0", "babel-loader": "^9.1.2", "webpack": "^5.52.0", "webpack-cli": "^4.8.0", "webpack-node-externals": "^3.0.0"}}