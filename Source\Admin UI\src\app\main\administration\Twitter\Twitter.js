import React, { useEffect } from 'react';
import { getTwitterOAuthUrl } from '../../utils/utils';

const Twitter = () => {

    useEffect(() => {
        handleClickTwitterAuth();
    }, []);

    const handleClickTwitterAuth = () => {
        const twitterData = JSON.parse(localStorage.getItem("twitterData"));
        if (twitterData !== null && twitterData !== undefined) {
            let url = getTwitterOAuthUrl();
            if (url !== null) {
                window.location.replace(url);
            }
        }
    };

    return (
        <div>
            <h1>Twitter Authentication</h1>
            <button onClick={handleClickTwitterAuth}>Authenticate with Twitter</button>
        </div>
    );
};

export default Twitter;
