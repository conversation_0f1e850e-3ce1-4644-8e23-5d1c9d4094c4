import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import TextField from '@mui/material/TextField';
import { useDispatch, useSelector } from 'react-redux';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import { useTranslation } from "react-i18next";
import CircularProgressLoader from '../CircularProgressLoader/CircularProgressLoader';
import Backdrop from '@mui/material/Backdrop';
import { UpdateRPSAuthentication } from '../../administration/store/usersSlice';
import { showMessage } from 'app/store/fuse/messageSlice';
import { Typography } from '@mui/material';

const defaultValues = {
    rpsusername: '',
    password: ''
};

function ChangeRPSCredentials(props) {
    const { control, formState, handleSubmit, reset, setValue } = useForm({
        mode: 'onChange',
        defaultValues,
        // resolver: yupResolver(schema),
    });

    console.log('props.data', props.data);

    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();
    const [rpsusername, setUsername] = useState(props.data.userAgencies !== undefined ? props.data.userAgencies.length > 0 ? props.data.userAgencies[0].RPSUserName : "" : "");
    const [rpspassword, setPassword] = useState(props.data.userAgencies !== undefined ? props.data.userAgencies.length > 0 ? props.data.userAgencies[0].RPSPassword : "" : "");
    const [showPassword, setShowPassword] = useState(false);
    const isloading = useSelector(({ administration }) => administration.user.isloading);

    useEffect(() => {
    }, [isloading]);

    useEffect(() => {
    }, [props.data])

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    function onSubmit() {
        if (rpsusername !== "" && rpspassword !== "") {
            dispatch(UpdateRPSAuthentication({ userName: rpsusername, password: rpspassword, deviceID: "test", deviceType: "webadmin", }, props.data.defaultAgency, props.data._id))
            props.handleClose();
        }
        else {
            ShowErroMessage(t("pleaseEnterUsernameAndPassword"))
        }
    }

    function onUsernameChange(e) {
        setUsername(e.target.value);
    }

    function onPasswordChange(e) {
        setPassword(e.target.value);
    }

    return (
        <>
            {isloading && <CircularProgressLoader loading={isloading} />}
            <div className="w-full">
                <form
                    id="rpsform"
                    name="registerForm"
                    className="flex flex-col justify-center w-full pb-16"
                    autoSave={false}
                >
                    {props.data?.lastRpsChange &&
                        <Typography className="font-semibold text-16 mb-6">{t("lastRpsChange")}: {new Date(props.data?.lastRpsChange)?.toLocaleString() ?? null}</Typography>
                    }
                    <TextField
                        className="mb-16"
                        name='rpsusername'
                        label={t('userName')}
                        type="text"
                        variant="outlined"
                        onChange={onUsernameChange}
                        autoComplete="new-password"
                        value={rpsusername || ""}
                        InputProps={{
                            className: 'pr-16',
                            endAdornment: (
                                <InputAdornment position="end">
                                    <Icon className="text-20" color="action" size="large">
                                        person
                                    </Icon>
                                </InputAdornment>
                            ),
                        }}
                    />

                    <TextField
                        name='rpspassword'
                        className="mb-16"
                        label={t('password')}
                        type="text"
                        variant="outlined"
                        autoComplete="new-password"
                        value={rpspassword || ""}
                        onChange={onPasswordChange}
                        InputProps={{
                            className: 'pr-2',
                            type: showPassword ? 'text' : 'password',
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton onClick={() => setShowPassword(!showPassword)} size="large">
                                        <Icon className="text-20" color="action">
                                            {showPassword ? 'visibility' : 'visibility_off'}
                                        </Icon>
                                    </IconButton>
                                </InputAdornment>
                            ),
                        }}
                    />

                    <Button
                        variant="contained"
                        color="primary"
                        className=" w-full mx-auto mt-8"
                        aria-label="Register"
                        type="button"
                        size="large"
                        onClick={() => onSubmit()}
                    >
                        {t("save")}
                    </Button>
                </form>
            </div>

        </>
    )
}

export default ChangeRPSCredentials;