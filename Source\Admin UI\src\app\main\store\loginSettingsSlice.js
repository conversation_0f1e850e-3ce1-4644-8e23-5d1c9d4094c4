import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import { encrypt, decrypt } from '../../security/crypto';
import axios from 'axios';

export const getisOtp = () => async dispatch => {
    try {
        await axios.get(`admin/api/settings/GetSettings`)
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    return dispatch(GetIsOtpSuccess(response.data));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            })
            .catch(error => {
                dispatch(GetIsOtpSuccess(error));
                return dispatch(
                    showMessage({
                        message: error.message, //text or html
                        autoHideDuration: 2000, //ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    })
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
};

export const updateSettings = (data) => async dispatch => {
    try {
        await axios.post(`admin/api/settings/updatesettings`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    return dispatch(getisOtp());
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    isOtpsuccess: false,
    isOtpdata: [],
};

const loginSettingsSlice = createSlice({
    name: 'loginSettings',
    initialState,
    reducers: {
        GetIsOtpSuccess: (state, action) => {
            state.isOtpsuccess = true;
            state.isOtpdata = action.payload;
        },

    },
    extraReducers: {}
});

export const { GetIsOtpSuccess,
} = loginSettingsSlice.actions;

export default loginSettingsSlice.reducer;
