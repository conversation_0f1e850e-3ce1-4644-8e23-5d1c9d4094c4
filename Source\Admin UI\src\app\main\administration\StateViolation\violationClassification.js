import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useEffect, useRef, useState } from 'react';
import { Button, Icon, Input, Paper, StyledEngineProvider, ThemeProvider, Typography, Tooltip, Stack, InputAdornment, IconButton, } from '@mui/material';
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import { useSelector, useDispatch } from 'react-redux';
import history from '@history';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import CorporateFareIcon from '@mui/icons-material/CorporateFare';
import { setSelectedStateCode } from '../store/stateViolationSlice';
import { setViolationClassification, setViolationClassificationTotalCount, } from '../store/violationClassificationSlice';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import ClassificationTable from './ClassificationTable';
import CancelIcon from '@mui/icons-material/Cancel';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import AddClassification from './AddClassification';
import CommonButton from '../../SharedComponents/ReuseComponents/CommonButton';

function ViolationClassification(props) {
    const { t } = useTranslation("laguageConfig");
    const mainTheme = useSelector(selectMainTheme);
    const dispatch = useDispatch();
    const classificationRef = useRef(null);

    const [state, setState] = React.useState(null);
    const [loading, setloading] = useState(false);
    const [searchText, setSearchText] = React.useState("");

    const user = useSelector(({ auth }) => auth.user);
    const isloading = useSelector(({ administration }) => administration.violationClassificationSlice.isloading);
    const selectedStateCode = useSelector(({ administration }) => administration.stateViolationSlice.selectedStateCode);

    useEffect(() => {
        setloading(isloading);
    }, [isloading]);

    useEffect(() => {
        if (selectedStateCode !== null) {
            setState(selectedStateCode);
        }
    }, [selectedStateCode]);

    const navigateToState = () => {
        history.push(`/admin/states`);
        dispatch(setSelectedStateCode(null));
        dispatch(setViolationClassification([]));
        dispatch(setViolationClassificationTotalCount(0));
    };

    const createNewClassification = () => {
        classificationRef.current.handleOpen(false, "add");
    };

    const handleDefaultSelected = () => {
    };

    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {user.data.isSuperAdmin && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => navigateToState()}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={user.data.isSuperAdmin ? {} : { paddingTop: "29px" }}
                        >
                            <div className="flex items-center">
                                <CorporateFareIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {state} - {t("violationClassification")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText("")}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div className="flex items-center justify-between">
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addclassification" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addClassification")} parentCallback={createNewClassification}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ClassificationTable" />} onReset={() => window.location.reload()} >
                            <ClassificationTable showAction={true} searchText={searchText} handleDefaultSelected={handleDefaultSelected} classificationSelected={handleDefaultSelected}></ClassificationTable>
                        </ErrorBoundary>
                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="AddClassification" />} onReset={() => window.location.reload()} >
                            <AddClassification ref={classificationRef} />
                        </ErrorBoundary>
                    </>
                }
            />
        </>
    );
}

export default ViolationClassification;