import React, { forwardRef, useRef, useImperativeHandle, useEffect, } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { useDispatch } from "react-redux";
import IconButton from "@mui/material/IconButton";
import { useSelector } from "react-redux";
import Icon from "@mui/material/Icon";
import { clearSearchDataTable, GetPersonAlerts } from "../../store/personSlice";
import DisplayIndividualPersonSearchData from "./DisplayIndividualPersonSearchData";
import DisplayMasterPersonSearchData from "./DisplayMasterPersonSearchData";
import ErrorPage from "../ErrorPage/ErrorPage";
import { ErrorBoundary } from "react-error-boundary";
import FuseLoading from "@fuse/core/FuseLoading";
import { Backdrop } from "@mui/material";
import { makeStyles } from '@mui/styles';
import PersonAlerts from "../../Dailog/PersonAlerts";
import PersonAlertsNew from "../../Dailog/PersonAlertsNew";

const useStyles = makeStyles((theme) => ({
    backdrop: { zIndex: theme.zIndex.drawer + 1, color: '#fff', },
}))

const DisplaySearchData = forwardRef((props, ref) => {
    const classes = useStyles(props);
    const personAlertsRef = useRef(null);

    const [open, setOpen] = React.useState(false);
    const [IndividualData, setIndividualData] = React.useState([]);
    const [MatserData, setMatserData] = React.useState([]);
    const dispatch = useDispatch();

    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.person.isloading);
    const personDetails = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.person.personDetails);

    const handleClickOpen = () => {
        setOpen(true);
    };

    useImperativeHandle(ref, () => ({
        openSearch(individualData, MasterData, title) {
            setIndividualData(individualData)
            setMatserData(MasterData)
            handleClickOpen();
        },
    }));

    const handleClose = () => {
        setOpen(false);
        dispatch(clearSearchDataTable());
    };

    useEffect(() => {
        if (!props.isLocalSearch) {
            handleClose();
        } else {
            if (props.isLocalSearch && personDetails !== null && personDetails !== undefined) {
                handleInfo(personDetails);
            }
        }
    }, [personDetails]);

    const handleInfo = () => {
        const payload = {
            "firstname": personDetails.fName,
            "middlename": personDetails.mName,
            "lastname": personDetails.lName,
            "dob": personDetails.dob,
            "sex": personDetails.sex,
            "dlNumber": personDetails.dlNumber,
            "dlState": personDetails.dlState,
            "ssn": personDetails.ssn
        }
        dispatch(GetPersonAlerts(payload));
        personAlertsRef.current.openPersonAlerts();
    };

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth="xxl"
                open={open}
                aria-labelledby="responsive-dialog-title"
            >
                <div className="flex flex-row-reverse mt-16 mr-16">

                    <IconButton
                        onClick={handleClose}
                        aria-label="show more"
                    >
                        <Icon>close</Icon>
                    </IconButton>
                </div>
                <DialogContent dividers>
                    <Backdrop className={classes.backdrop} open={isloadingvalue}>
                        <FuseLoading variant="determinate" />
                    </Backdrop>
                    <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="DisplayMasterPersonSearchData" />} onReset={() => { }}>
                        <DisplayMasterPersonSearchData MatserData={MatserData} isLocalSearch={props.isLocalSearch} />
                    </ErrorBoundary>
                    <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="DisplayIndividualPersonSearchData" />} onReset={() => { }}>
                        <DisplayIndividualPersonSearchData className="mb-80" isLocalSearch={props.isLocalSearch} IndividualData={IndividualData} />
                    </ErrorBoundary>
                </DialogContent>
            </Dialog>
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="PersonAlerts" />} onReset={() => { }}>
                {/* <PersonAlerts ref={personAlertsRef} isLocalSearch={props.isLocalSearch} /> */}
                <PersonAlertsNew ref={personAlertsRef} isLocalSearch={props.isLocalSearch} />
            </ErrorBoundary>
        </div>
    );
});
export default DisplaySearchData;
