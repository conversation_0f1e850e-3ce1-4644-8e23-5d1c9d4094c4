import React from 'react';
import {
    FormControl,
    FormLabel,
    FormGroup,
    FormControlLabel,
    Checkbox
} from '@mui/material';

const OtherAccessRightGroup = ({
    t,
    accessRights,
    handleChange,
    showAccessRight,
    isSuperAdmin,
    defaultApp,
    doesNotUseOldRps // ⬅️ Added as a prop
}) => {
    let accessItems = [
        { key: 'Mugshot', label: t('mugshot') },
        { key: 'DispatchApp', label: t('dispatchApp') },
        { key: 'DispatchWebsite', label: t('dispatchWebsite') },
        { key: 'IncidentApp', label: t('incidentApp') },
        { key: 'IncidentWebsite', label: t('incidentWebsite') }
    ];

    // Filter out DispatchApp and DispatchWebsite if doesNotUseOldRps is true
    if (doesNotUseOldRps) {
        accessItems = accessItems.filter(item =>
            item.key !== 'DispatchApp' && item.key !== 'DispatchWebsite'
        );
    }

    const isDisabled = !(defaultApp === 'mobile' || defaultApp === 'incident') && !isSuperAdmin;

    return (
        <div className="col-span-1 w-full">
            <FormControl component="fieldset">
                <FormLabel component="legend">{t('others')}</FormLabel>
                <FormGroup>
                    {accessItems.map(({ key, label }) => {
                        const canShow = showAccessRight[key] === key || isSuperAdmin;
                        return canShow && (
                            <FormControlLabel
                                key={key}
                                control={
                                    <Checkbox
                                        checked={accessRights[key] || false}
                                        onChange={handleChange}
                                        name={key}
                                    />
                                }
                                label={label}
                            // disabled={isDisabled}
                            />
                        );
                    })}
                </FormGroup>
            </FormControl>
        </div>
    );
};

export default OtherAccessRightGroup;
