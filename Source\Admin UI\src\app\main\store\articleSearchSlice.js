import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';

export const getArticleDetails = data => async dispatch => {
	
	await axios
		.post(process.env.REACT_APP_GateWayAPI_URL_URL.concat(`admin/api/articlesearch/getarticlebysearch`), { data })
		.then(response => {
			if (response.data.length > 0) {
				return dispatch(articleListSuccess(response.data));
			}
			else {
				return dispatch(articleListSuccess([]));
			}
		})
		.catch(error => {
			return dispatch(articleListSuccess([]));
		});
};

export const clearArticleSearchData = () => dispatch => {
	return dispatch(articleSearchListSuccess([]));
};


export const getArticleDetailsCopy = data => async dispatch => {

	await axios
		.post(process.env.REACT_APP_GateWayAPI_URL_URL.concat(`admin/api/articlesearch/getarticlebysearchCopy`), { data })
		.then(response => {
			if (response.data.length > 0) {
				return dispatch(articleListSuccessCopy(response.data));
			}
			else {
				return dispatch(articleListSuccessCopy([]));
			}
		})
		.catch(error => {
			return dispatch(articleListSuccessCopy([]));
		});
};



export const clearArticleSearchDataCopy = () => dispatch => {
	return dispatch(articleSearchListSuccessCopy([]));
};

const initialState = {
	articleSuccess: false,
	articleData: [],
	searchhData: [],
	articleSuccessCopy: false,
	articleDataCopy: []
};

const articleSlice = createSlice({
	name: 'article',
	initialState,
	reducers: {
		articleListSuccess: (state, action) => {
			
			state.articleSuccess = true;
			state.articleData = action.payload;
		},
		articleSearchListSuccess: (state, action) => {
			state.articleSuccess = true;
			state.articleData = [];
		},

		articleListSuccessCopy: (state, action) => {
			
			state.articleSuccessCopy = true;
			state.articleDataCopy = action.payload;
		},
		articleSearchListSuccessCopy: (state, action) => {
			state.articleSuccessCopy = true;
			state.articleDataCopy = [];
		},

	},
	extraReducers: {}
});

export const { articleListSuccess, articleSearchListSuccess, articleListSuccessCopy, articleSearchListSuccessCopy } = articleSlice.actions;

export default articleSlice.reducer;
