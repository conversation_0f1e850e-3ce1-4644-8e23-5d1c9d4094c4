import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from 'react-redux';
import { motion } from "framer-motion";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import FusePageCarded from "@fuse/core/FusePageCarded";
import DnsIcon from '@mui/icons-material/Dns';
import {
    StyledEngineProvider,
    ThemeProvider,
    Typography,
    Paper,
    Icon,
    Input,
    Button,
    TablePagination,
    Tooltip,
    Stack,
    IconButton
} from "@mui/material";
import { checkData, getNavbarTheme, getRowsPerPageOptions } from "src/app/main/utils/utils";
import { getInterSectionPointDetailsLogs, getIntersectionPointErrorLogsById, setIntersectionSelectedSummaryId } from "src/app/main/store/importFromExcelSlice";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import ErrorRoundedIcon from '@mui/icons-material/ErrorRounded';
import history from "@history";
import CircularProgressLoader from "src/app/main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import { useWindowResizeHeight } from "../../../utils/utils";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: '_id',
        align: 'left',
        disablePadding: false,
        label: 'ID',
        sort: true
    },
    {
        id: 'user',
        align: 'left',
        disablePadding: false,
        label: 'User',
        sort: true
    },
    {
        id: 'filePath',
        align: 'left',
        disablePadding: false,
        label: 'FilePath',
        sort: true
    },
    {
        id: 'fileName',
        align: 'left',
        disablePadding: false,
        label: 'FileName',
        sort: true
    },
    {
        id: 'createdOn',
        align: 'left',
        disablePadding: false,
        label: 'Date',
        sort: true
    },
    {
        id: 'updateCount',
        align: 'left',
        disablePadding: false,
        label: 'UpdateCount',
        sort: true
    },
    {
        id: 'insertCount',
        align: 'left',
        disablePadding: false,
        label: 'InsertCount',
        sort: true
    },
    {
        id: 'matchCount',
        align: 'left',
        disablePadding: false,
        label: 'MatchCount',
        sort: true
    },
    {
        id: 'failedCount',
        align: 'left',
        disablePadding: false,
        label: 'FailedCount',
        sort: true
    },
    {
        id: 'totalCount',
        align: 'left',
        disablePadding: false,
        label: 'TotalCount',
        sort: true
    },
    {
        id: 'status',
        align: 'left',
        disablePadding: false,
        label: 'County',
        sort: true
    },
];

const IntersectionImportLogs = () => {
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const gridRef = useRef(null);
    let colorCode = getNavbarTheme();
    const navbarTheme = useSelector(selectNavbarTheme);
    const rowsPerPageOptions = getRowsPerPageOptions();


    const intersectionPointDetailSummary = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.intersectionPointDetailSummary);
    const intersectionPointDetailSummaryCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.intersectionPointDetailSummaryCount);
    const isLoading = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.isLoading);
    const selectedCounty = useSelector(({ administration }) => administration.masterAddressSlice.selectedCounty)
    const selectedCountyStateCode = useSelector(({ administration }) => administration.masterAddressSlice.selectedCountyStateCode)

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "User",
    });
    const [logs, setLogs] = useState([]);
    const [county, setCounty] = useState(null);
    const [countyState, setCountyState] = useState(null);
    const [totalCount, setTotalCount] = useState(intersectionPointDetailSummaryCount);
    const [pageIndex, setPageIndex] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    useEffect(() => {
        if (intersectionPointDetailSummary !== null && intersectionPointDetailSummary !== undefined) {
            setLogs(intersectionPointDetailSummary);
            setTotalCount(intersectionPointDetailSummaryCount);
        }
    }, [intersectionPointDetailSummary, intersectionPointDetailSummaryCount]);

    useEffect(() => {
        if (selectedCounty !== null && selectedCountyStateCode !== null) {
            setCounty(selectedCounty);
            setCountyState(selectedCountyStateCode);
            // dispatch(getInterSectionPointDetailsLogs(selectedCounty, selectedCountyStateCode));
        }
    }, [selectedCounty]);

    useEffect(() => {
        dispatch(getInterSectionPointDetailsLogs(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, selectedCounty, selectedCountyStateCode));
    }, [order, pageIndex, rowsPerPage]);

    const viewSummaryError = (_id) => {
        dispatch(setIntersectionSelectedSummaryId(_id));
        history.push("/admin/intersectionImportErrorLogs");
    }

    const ActionIcons = (n) => {
        // let x = checkData(n);
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div className="flex">
                    {/* <Button variant="contained" color="primary">{t("View Details")}</Button> */}
                    <div className="flex">
                        <Tooltip title={t("viewDetails")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => viewSummaryError(x._id)}
                                size="large"
                            >
                                <ErrorRoundedIcon />
                            </IconButton>
                        </Tooltip>
                    </div>
                </div >
            );
        }
    };

    const rowData = (logs !== null && logs !== undefined) && logs.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["user"] = item.user ?? "";
            row["filePath"] = item.filePath ?? "";
            row["fileName"] = item.fileName ?? "";
            row["createdOn"] = new Date(item.createdOn).toLocaleDateString("en-US") ?? "";
            row["updateCount"] = item.updateCount ?? "";
            row["insertCount"] = item.insertCount ?? "";
            row["failedCount"] = item.failedCount ?? "";
            row["totalCount"] = item.totalCount ?? "";
            row["status"] = item.status ?? "";
            row["Action"] = ActionIcons(item)
        });
        return row;
    });


    const navigateToIntersectionList = () => {
        history.push('/admin/intersectionPoint');
    }

    const handleChangePage = (event, value) => {
        setPageIndex(value);
    }

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(event.target.value);
        setPageIndex(0);
    }

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    return (
        <div>
            {isLoading && <CircularProgressLoader loading={isLoading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%", marginBottom: "50px" }}>
                        <div className="flex flex-1 items-center justify-between">
                            <div className="flex items-center">
                                <Tooltip title="Back to Intersection List" style={{ float: "right" }}>
                                    <Stack direction="row" spacing={2}>
                                        <Button
                                            className="backButton"
                                            variant="contained"
                                            startIcon={<ArrowBackOutlinedIcon />}
                                            onClick={() => navigateToIntersectionList()}
                                        >
                                            {t("back")}
                                        </Button>
                                    </Stack>
                                </Tooltip>
                            </div>
                        </div>
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={{ paddingTop: "20px" }}
                        >
                            <div className="flex items-center">
                                <DnsIcon style={{ fontSize: '40px' }} />
                                <div className="flex flex-col">
                                    <Typography
                                        component={motion.span}
                                        initial={{ x: -20 }}
                                        animate={{ x: 0, transition: { delay: 0.2 } }}
                                        delay={300}
                                        className="hidden sm:flex mx-0 sm:mx-12"
                                        variant="h6"
                                    >
                                        {t("importLogsIntersectionPoint")}
                                    </Typography>
                                    <Typography
                                        component={motion.span}
                                        initial={{ x: -20 }}
                                        animate={{ x: 0, transition: { delay: 0.2 } }}
                                        delay={300}
                                        className="hidden sm:flex mx-0 sm:mx-12"
                                        variant="body2"
                                    >
                                        County: {county}, State: {countyState}
                                    </Typography>
                                </div>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={/*searchText*/ ''}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                            // onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                        </div>
                    </div>
                }
                content={
                    < div className="w-full flex flex-col" >
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        'aria-label': 'Previous Page'
                                    }}
                                    nextIconButtonProps={{
                                        'aria-label': 'Next Page'
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>


                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="user"
                                        header={t("user")}
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("filePath")}
                                        field="filePath"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("fileName")}
                                        field="fileName"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("importDate")}
                                        field="createdOn"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("updatedCount")}
                                        field="updateCount"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("insertedCount")}
                                        field="insertCount"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("failedCount")}
                                        field="failedCount"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("totalCount")}
                                        field="totalCount"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("status")}
                                        field="status"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>

                    </div >
                }
            />
        </div>
    )
}


export default IntersectionImportLogs;