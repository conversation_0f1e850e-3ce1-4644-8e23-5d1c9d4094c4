import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const getNfirsDetails = (searchText, code) => async dispatch => {
    const data = { searchText, code }
    try {
        await axios
            .post(`admin/api/nfirs/NFIRSDetails`, encrypt(JSON.stringify(data)))
            .then(response => {
                dispatch(nfirsListSuccess(JSON.parse(decrypt(response.data))));
                if (response.status == 200) {
                    dispatch(
                        showMessage({
                            message: 'NFIRS Equipment Type details found.',
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        })
                    );
                } else {
                    dispatch(
                        showMessage({
                            message: 'NFIRS Equipment Type details not found.',
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'warning'
                        })
                    );
                }
            })
            .catch(error => {
                dispatch(nfirsListError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
};

export const getNFIRS = (sortField, sortDirection, pageIndex, pageLimit) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/nfirs/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setNfirsTotalCount(listData.totalCount));
                    dispatch(setLoading(false));
                    return dispatch(setNfirs(listData.nfirsList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const saveNFIRS = (data) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/nfirs`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(
                        showMessage({
                            message: data.isUpdate ? response.message : response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    if (data.isUpdate) {
                        dispatch(setNFIRSEditData(response.newData))
                        dispatch(setNfirsTotalCount(response.totalCount));
                    }
                    else {
                        dispatch(setNFIRSAddData(response.newData))
                        dispatch(setNfirsTotalCount(response.totalCount));
                    }
                    return dispatch(setNfirsResponse(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const removeNFIRS = (ID) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/nfirs/${ID}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    dispatch(removeNFIRSData(ID));
                    return dispatch(setNfirsResponse(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//for searching
export const searchNFIRS = (searchText) => async dispatch => {
    dispatch(setLoading(true));
    try {
        axios.get(`admin/api/nfirs/nfirsSearchList/${searchText}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchnfirs(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    nfirssuccess: false,
    nfirsdata: [],
    data: [],
    searchnfirs: [],
    success: false,
    isloading: false,
    totalCount: 0
}

const nfirsSlice = createSlice({
    name: 'nfirs',
    initialState,
    reducers: {
        nfirsListSuccess: (state, action) => {
            state.nfirssuccess = true;
            state.nfirsdata = action.payload;
        },
        setSearchnfirs: (state, action) => {
            state.searchnfirs = action.payload;
        },
        nfirsListError: (state, action) => {
            state.nfirssuccess = false;
            state.nfirsdata = [];
        },
        setNfirs: (state, action) => {
            state.data = action.payload;
        },
        setNfirsResponse: (state, action) => {
            state.success = action.payload;
        },
        setNfirsTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setNFIRSlist: (state, action) => {
            state.nfirsdata = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        setNFIRSEditData: (state, action) => {
            const index = state.data.findIndex(obj => obj._id === action.payload._id);
            if (index !== -1) {
                state.data[index] = action.payload;
            }
        },
        setNFIRSAddData: (state, action) => {
            state.data = [...state.data, action.payload];
        },
        removeNFIRSData: (state, action) => {
            state.data = state.data.filter(x => x._id !== action.payload);
        },
    },
    extraReducers: {}
});

export const {
    nfirsListSuccess,
    setNfirs,
    setNfirsResponse,
    setNfirsTotalCount,
    setNFIRSlist,
    setLoading,
    setSearchnfirs,
    nfirsListError,
    setNFIRSEditData,
    removeNFIRSData,
    setNFIRSAddData
} = nfirsSlice.actions;

export default nfirsSlice.reducer;