import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import _ from "@lodash";
import history from "@history";
import TablePagination from "@mui/material/TablePagination";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import CircularProgressLoader from "../../SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { checkData, dateDiffInHoursAndMinutes, getLocalOffset, getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils";
import AgencyShiftDialog from "./AgencyShiftDialog";
import { getShiftTime, removeShift, searchShift } from "../../store/shiftTimeSlice";
import { ErrorBoundary } from "react-error-boundary";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import HistoryIcon from '@mui/icons-material/History';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "ShiftName",
        align: "left",
        disablePadding: false,
        label: "ShiftName",
        sort: true,
    },
    {
        id: "StartTime",
        align: "left",
        disablePadding: false,
        label: "StartTime",
        sort: true,
    },
    {
        id: "EndTime",
        align: "left",
        disablePadding: false,
        label: "EndTime",
        sort: true,
    },

    {
        id: "ShiftDuration",
        align: "left",
        disablePadding: false,
        label: "ShiftDuration",
        sort: true,
    },
    {
        id: "department",
        align: "left",
        disablePadding: false,
        label: "department",
        sort: true,
    },
    {
        id: "departmentID",
        align: "left",
        disablePadding: false,
        label: "departmentID",
        sort: true,
    },
    {
        id: "shiftcode",
        align: "left",
        disablePadding: false,
        label: "shiftcode",
        sort: true,
    },
    {
        id: "color",
        align: "left",
        disablePadding: false,
        label: "color",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

let Data = []
function AgencyShiftDetails() {
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");

    const gridRef = useRef(null);
    const ShiftTimeRef = useRef();
    const dispatch = useDispatch();
    const routeParams = useParams();

    const ShiftTimeData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shift.data);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shift.isloading);
    const ShiftTimeTotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shift.totalCount);

    const [newCallViolation, setNewCallViolation] = React.useState(false);
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [searchText, setSearchText] = React.useState("");
    // let filteredData = routeParams.id !== '0' ? ShiftTimeData.filter(x => x.department[0]._id === routeParams.id) : ShiftTimeData
    // let searchfilteredData = routeParams.id !== '0' ? searchshiftTimes.filter(x => x.department[0]._id === routeParams.id) : searchshiftTimes
    const [data, setData] = React.useState(ShiftTimeData);
    const [countData, setCountData] = React.useState(ShiftTimeTotalCount);
    //   const callViolationRef = useRef(null);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "ShiftName",
    });
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    let colorCode = getNavbarTheme();

    const deleteCallViolation = (n) => {
        setOpen(true);
        setRemoveID(n._id);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeShift(removeID, routeParams.code, pageIndex, rowsPerPage, order.id, order.direction));
            setCountData(countData - 1)
        }
    };

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const search = useDebounce((search, pageIndex, rowsPerPage) => {
        dispatch(getShiftTime(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, search === '' ? null : search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, pageIndex, rowsPerPage);
        } else {
            dispatch(getShiftTime(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, searchText, pageIndex, rowsPerPage, order, routeParams.code]);

    useEffect(() => {
        setData(ShiftTimeData);
        setCountData(ShiftTimeTotalCount)
    }, [ShiftTimeData]);

    useEffect(() => { }, [isUpdate, newCallViolation]);

    const [loading, setLoading] = useState();
    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    let PagingDetails = {
        pageIndex: pageIndex,
        rowsPerPage: rowsPerPage,
        id: order.id,
        direction: order.direction,
    }

    const ActionIcons = (n) => {
        // let x = checkData(n)
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    // onClick={() => editRow(x)}
                                    onClick={() => ShiftTimeRef.current.handleClickOpen(x, routeParams.code, PagingDetails, true, routeParams.id)}
                                    size="large"
                                >
                                    <EditIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("delete")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    // disabled={n.isActive}
                                    onClick={() => deleteCallViolation(x)}
                                    size="large"
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                    }
                </>
            );
        }
    };

    const ColorCode = (n) => {
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div style={{ display: "flex", paddingLeft: "7.2rem", margin: "1.2rem" }}>
                    <FiberManualRecordIcon style={{ color: x.color }} />
                </div>
            )
        }
    };

    const rowData = data.map(item => {

        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["ShiftName"] = item.ShiftName
            row["department"] = item.department[0].name
            row["StartTime"] = item.StartTime
            row["EndTime"] = item.EndTime
            row["ShiftDuration"] = item.ShiftDuration
            row["color"] = item.color
            row["departmentID"] = item.department[0]._id
            row["action"] = ActionIcons(item)
        });
        return row;
    });

    // useEffect(() => {
    //     if (routeParams.id == '0') {
    //         // add the group description to the grid's groupDescriptions collection
    //         const peopleGroup = new IgrColumnGroupDescription();
    //         peopleGroup.field = "department";
    //         peopleGroup.displayName = "";
    //         // Add the group description to the groupDescriptions property of the grid's data source
    //         if (gridRef.current) {
    //             gridRef.current.groupDescriptions.add(peopleGroup);
    //             // Call refresh to update the grid with the grouping
    //             // gridRef.current.refresh();
    //         }
    //     }
    // });


    const nevigateBack = () => {
        if (routeParams.id == '0') {
            history.push(`/admin/agencyOptionsList/${routeParams.code}`)
        }
        else {
            history.push(`/admin/Department/${routeParams.code}`)
        }
    };

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => nevigateBack()}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <HistoryIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("shift")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addShift" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addShift")} parentCallback={() => ShiftTimeRef.current.handleClickOpen(Data, routeParams.code, PagingDetails, false, routeParams.id)}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={countData}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{
                                    "aria-label": "Previous Page",
                                }}
                                nextIconButtonProps={{
                                    "aria-label": "Next Page",
                                }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />
                        </div>
                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="department"
                                        field="department"
                                        header={t("department")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="ShiftName"
                                        header={t("shiftName")}
                                        field="ShiftName"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="StartTime"
                                        header={t("startTime")}
                                        field="StartTime"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="EndTime"
                                        header={t("endTime")}
                                        field="EndTime"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="ShiftDuration"
                                        header={t("shiftDuration")}
                                        field="ShiftDuration"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="color"
                                        header={t("color")}
                                        field="color"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                        bodyTemplate={ColorCode}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="200px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteShiftText")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="AgencyShiftDialog" />} onReset={() => { }} >
                                <AgencyShiftDialog ref={ShiftTimeRef} />
                            </ErrorBoundary>
                        </div>
                    </div>
                }
            />
        </>
    );
}

export default AgencyShiftDetails;
