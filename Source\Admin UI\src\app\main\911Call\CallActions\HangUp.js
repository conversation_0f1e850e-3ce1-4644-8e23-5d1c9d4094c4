import React from 'react';
import { makeStyles } from '@mui/styles';
import { withStyles } from '@mui/styles';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import MuiDialogTitle from '@mui/material/DialogTitle';
import MuiDialogContent from '@mui/material/DialogContent';
import MuiDialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import ButtonGroup from '@mui/material/ButtonGroup';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';


import HangUpCallBackNoAnswerComponent from './HangUpCallBackNoAnswer';
import HangUpCallBackDeadCallComponent from './HangUpCallBackDeadCall';
import HangUpAccidentalCallComponent from './HangUpAccidentalCall';
import HangUpNonAccidentalCallComponent from './HangUpNonAccidentalCall';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';

const useStyles = makeStyles((theme) => ({
  root: {
    '& > *': {
      margin: theme.spacing(1),
      width: '95%'
    },
  },
}));

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle disableTypography className={classes.root} {...other}>
      <Typography variant="h6">{children}</Typography>
      {onClose ? (
        <IconButton aria-label="close" className={classes.closeButton} onClick={onClose}>
          <CloseIcon />
        </IconButton>
      ) : null}
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
    width: 500,
    height: 500
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);


const fw_8 = {
  fontWeight: '600'
}

export default function HangUpCallComponent(props) {
  const { t } = useTranslation('laguageConfig');
  const [open, setOpen] = React.useState(false);
  const [formattedOpen, setformattedOpen] = React.useState(true);
  const [rawDataOpen, setrawDataOpen] = React.useState(false);
  const [callBackAnswer, setCallBackAnswer] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
    setformattedOpen(true);
    setrawDataOpen(false);
    setCallBackAnswer(false);
  };
  const handleClose = () => {
    setOpen(false);
  };


  const handleFormattedOpen = () => {
    setformattedOpen(true);
    setrawDataOpen(false);
  };

  const handleRawDataOpen = () => {
    setrawDataOpen(true);
    setformattedOpen(false);
  };

  const handleCallBackAnswer = () => {
    setCallBackAnswer(true);
  };



  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Button variant="contained" color="primary" onClick={handleClickOpen}>
        {t('hangUp911')}
      </Button>
      <Dialog onClose={handleClose} aria-labelledby="customized-dialog-title" open={open}>
        <DialogTitle id="customized-dialog-title" onClose={handleClose}>
          {t('hangUp911')}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container>
            <ButtonGroup className="mb-8" size="small" color="secondary" variant="contained" fullWidth={true} aria-label="small contained  secondary  button group">
              <Button style={{ margin: '1px' }} onClick={handleFormattedOpen}>{t('formatted')}</Button>
              <Button style={{ margin: '1px' }} onClick={handleRawDataOpen}>{t('rawData')}</Button>
            </ButtonGroup>
            <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
              <CardContent>
                {formattedOpen &&
                  <div>
                    {(props.value.PacketClassofService === "WRLS" || props.value.PacketClassofService === "WPH1") &&
                      <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                        <Grid item xs={4} style={{ padding: '0px' }}>
                          <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                        </Grid>
                        <Grid item xs={4} style={{ padding: '0px' }}>
                          <span style={fw_8}>  {props.value.PacketCallTime}:{new Date(props.value.PacketCallReceivedDT).getSeconds()}  {props.value.PacketCallDate}</span>
                        </Grid>
                        <Grid item xs={2} style={{ padding: '0px' }}>
                          <span style={fw_8}> {props.value.PacketClassofService}</span>
                        </Grid>
                        <Grid item xs={2} style={{ padding: '0px' }}>
                          <span style={fw_8}>{props.value.SubCall && props.value.SubCall.filter(call => call.PacketCustomerName.trim() !== props.value.PacketCustomerName.trim()).length > 0 && "Moving"}</span>
                          {/* <span style={fw_8}>{props.count > 1 && "Moving"}</span> */}
                        </Grid>
                        <Grid item xs={12} style={{ padding: '0px' }}>
                          {t('towerInfo')}: <span style={fw_8}>{props.value.PacketCustomerName}</span>
                        </Grid>
                        <Grid item xs={12} style={{ padding: '0px' }}>
                          <span style={fw_8}>{props.value.PacketStreetAddress}, {props.value.PacketCity}, {props.value.PacketState}, {props.value.PacketState} </span>
                        </Grid>
                        <Grid item xs={6} style={{ padding: '0px' }}>
                          {t('lat')}: <span style={fw_8}>{props.value.Packety}</span>
                        </Grid>
                        <Grid item xs={6} style={{ padding: '0px' }}>
                          {t('long')}: <span style={fw_8}>{props.value.Packetx}</span>
                        </Grid>
                      </Grid>
                    }

                    {(props.value.PacketClassofService === "WPH2" || props.value.PacketClassofService === "TLMA" || props.value.PacketClassofService === "TELM") &&
                      <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                        <Grid item lg={4}>
                          <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                        </Grid>
                        <Grid item lg={4}>
                          <span style={fw_8}>  {props.value.PacketCallTime}:{new Date(props.value.PacketCallReceivedDT).getSeconds()}  {props.value.PacketCallDate}</span>
                        </Grid>
                        <Grid item lg={2}>
                          <span style={fw_8}> {props.value.PacketClassofService}</span>
                        </Grid>
                        <Grid item lg={2}>
                          <span style={fw_8}>{props.value.SubCall && props.value.SubCall.filter(call => call.Packety !== props.value.Packety || call.Packetx !== props.value.Packetx).length > 0 && "Moving"}</span>
                          {/* <span style={fw_8}>{props.count > 1 && "Moving"}</span> */}
                        </Grid>
                        <Grid item lg={12}>
                          {t('towerInfo')}: <span style={fw_8}>{props.value.PacketCustomerName}</span>
                        </Grid>
                        <Grid item lg={12}>
                          <span style={fw_8}>{props.value.PacketStreetAddress}, {props.value.PacketCity}, {props.value.PacketState}</span>
                        </Grid>
                        <Grid item lg={6}>
                          {t('lat')}: <span style={fw_8}>{props.value.Packety}</span>
                        </Grid>
                        <Grid item lg={6}>
                          {t('long')}: <span style={fw_8}>{props.value.Packetx}</span>
                        </Grid>
                      </Grid>
                    }


                    {(props.value.PacketClassofService !== "WRLS" && props.value.PacketClassofService !== "WPH1" && props.value.PacketClassofService !== "WPH2" && props.value.PacketClassofService !== "TLMA" && props.value.PacketClassofService !== "TELM") &&
                      <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                        <Grid item xs={4}>
                          <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                        </Grid>
                        <Grid item xs={4}>
                          <span style={fw_8}>  {props.value.PacketCallTime}:{new Date(props.value.PacketCallReceivedDT).getSeconds()}  {props.value.PacketCallDate}</span>
                        </Grid>
                        <Grid item xs={2}>
                          <span style={fw_8}> {props.value.PacketClassofService}</span>
                        </Grid>
                        <Grid item xs={12}>
                          {t('caller')}: <span style={fw_8}>{props.value.PacketCustomerName}</span>
                        </Grid>
                        <Grid item xs={12}>
                          {t('location')}: <span style={fw_8}>{props.value.PacketStreetNumber}, {props.value.PacketStreetAddress}, {props.value.PacketCity}, {props.value.PacketState}, {props.value.PacketLocationInfo}</span>
                        </Grid>
                        <Grid item xs={6}>
                          {t('lat')}: <span style={fw_8}>{props.value.Packety}</span>
                        </Grid>
                        <Grid item xs={6}>
                          {t('long')}: <span style={fw_8}>{props.value.Packetx}</span>
                        </Grid>
                      </Grid>
                    }
                  </div>
                }
                {rawDataOpen &&
                  <div style={{ fontSize: '1.15rem', padding: '0px', fw_8 }}>
                    {props.value.RawInputString}
                  </div>
                }
              </CardContent>
            </Card>

            <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
              <CardContent>
                <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                  <Grid item lg={6}>
                    <span style={fw_8}>Previous calls from this number</span>
                  </Grid>
                  <Grid item lg={6}>
                    <span style={fw_8}>xxxx</span>
                  </Grid>

                </Grid>

                <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                  <Grid item lg={6}>
                    <span style={fw_8}>Previous dropped/hangup calls</span>
                  </Grid>
                  <Grid item lg={6}>
                    <span style={fw_8}>xxxx</span>
                  </Grid>
                </Grid>

                <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                  <Grid item lg={6}>
                    <span style={fw_8}>Last previous call was</span>
                  </Grid>
                  <Grid item lg={6}>
                    <span style={fw_8}>1 hr. 30 minutes ago</span>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>


            <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
              <CardContent>
                <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                  <Grid item lg={4} fullWidth={true}>
                    <Button color="primary" size="small" variant="contained" onClick={handleCallBackAnswer}>{t('callBackAnswer')}</Button>
                  </Grid>
                  <Grid item lg={4} fullWidth={true}>
                    <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="HangUpCallBackNoAnswerComponent" />} onReset={() => { }} >
                      <HangUpCallBackNoAnswerComponent value={props.value}></HangUpCallBackNoAnswerComponent>
                    </ErrorBoundary>
                  </Grid>
                  <Grid item lg={4} fullWidth={true}>
                    <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="HangUpCallBackDeadCallComponent" />} onReset={() => { }} >
                      <HangUpCallBackDeadCallComponent value={props.value}></HangUpCallBackDeadCallComponent>
                    </ErrorBoundary>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {callBackAnswer &&
              <Card className="mt-8 mb-8" variant="outlined" style={{ width: "100%" }}>
                <CardContent>
                  <Grid container xs={12} sm={12} md={12} lg={12} xl={12} style={{ fontSize: '1.15rem', padding: '0px' }}>
                    <Grid item lg={4} fullWidth={true} className="mt-8 mb-8">
                      <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="HangUpAccidentalCallComponent" />} onReset={() => { }} >
                        <HangUpAccidentalCallComponent value={props.value}></HangUpAccidentalCallComponent>
                      </ErrorBoundary>
                    </Grid>
                    <Grid item lg={8} fullWidth={true} className="mt-8 mb-8">
                    </Grid>
                    <Grid item lg={4} fullWidth={true} className="mt-8 mb-8">
                      <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="HangUpNonAccidentalCallComponent" />} onReset={() => { }} >
                        <HangUpNonAccidentalCallComponent value={props.value}></HangUpNonAccidentalCallComponent>
                      </ErrorBoundary>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            }
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button autoFocus onClick={handleClose} color="primary">
            Back
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

