import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import TextField from '@mui/material/TextField';
import { useDispatch, useSelector } from 'react-redux';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import { useTranslation } from "react-i18next";
import CircularProgressLoader from '../CircularProgressLoader/CircularProgressLoader';
import { showMessage } from 'app/store/fuse/messageSlice';
import { UpdateMugshotAuthentication } from '../../administration/store/usersSlice';

const defaultValues = {
    mugshotusername: '',
    mugshotpassword: ''
};

function ChangeMugshotCredentials(props) {
    const { control, formState, handleSubmit, reset, setValue } = useForm({
        mode: 'onChange',
        defaultValues,
        // resolver: yupResolver(schema),
    });

    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();
    const [mugshotusername, setMugshotUsername] = useState(props.data.mugshotusername !== undefined ? props.data.mugshotusername : "");
    const [mugshotpassword, setMugshotPassword] = useState(props.data.mugshotpassword !== undefined ? props.data.mugshotpassword : "");
    const [showPassword, setShowPassword] = useState(false);
    const isloading = useSelector(({ administration }) => administration.user.isloading);

    useEffect(() => {

    }, [isloading])

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    function onSubmit() {
        if (mugshotusername !== "" && mugshotpassword !== "") {
            dispatch(UpdateMugshotAuthentication({ UserName: mugshotusername, Password: mugshotpassword, DeviceID: "testdev", DeviceType: "android" }, props.data.defaultAgency, props.data._id))
            props.newFunc();
        }
        else {
            ShowErroMessage(t("pleaseEnterUsernameAndPassword"))
        }
    }

    function onUsernameChange(e) {
        setMugshotUsername(e.target.value);

    }

    function onPasswordChange(e) {
        setMugshotPassword(e.target.value);

    }

    return (
        <>
            {isloading && <CircularProgressLoader loading={isloading} />}
            <div className="w-full">

                <form
                    id="rpsform"
                    name="registerForm"
                    // noValidate
                    className="flex flex-col justify-center w-full pb-16"
                    // onSubmit={handleSubmit(onSubmit)}
                    // autoComplete="off"
                    autoSave={false}
                >
                    <TextField
                        className="mb-16"
                        name='mugshotusername'
                        label={t('userName')}
                        type="text"
                        variant="outlined"
                        onChange={onUsernameChange}
                        autoComplete="new-password"
                        value={mugshotusername || ""}
                        InputProps={{
                            className: 'pr-16',
                            endAdornment: (
                                <InputAdornment position="end">
                                    <Icon className="text-20" color="action" size="large">
                                        person
                                    </Icon>
                                </InputAdornment>
                            ),
                        }}
                    />

                    <TextField
                        name='mugshotpassword'
                        className="mb-16"
                        label={t('password')}
                        type="text"
                        variant="outlined"
                        autoComplete="new-password"
                        value={mugshotpassword || ""}
                        onChange={onPasswordChange}
                        InputProps={{
                            className: 'pr-2',
                            type: showPassword ? 'text' : 'password',
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton onClick={() => setShowPassword(!showPassword)} size="large">
                                        <Icon className="text-20" color="action">
                                            {showPassword ? 'visibility' : 'visibility_off'}
                                        </Icon>
                                    </IconButton>
                                </InputAdornment>
                            ),
                        }}
                    />

                    <Button
                        variant="contained"
                        color="primary"
                        className=" w-full mx-auto mt-8"
                        aria-label="Register"
                        type="button"
                        size="large"
                        onClick={() => onSubmit()}
                    >
                        {t("save")}
                    </Button>
                </form>
            </div>

        </>
    )
}


export default ChangeMugshotCredentials;
