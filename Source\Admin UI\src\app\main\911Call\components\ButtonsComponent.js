import Switch from "@mui/material/Switch";
import { useTranslation } from "react-i18next";
import FormControlLabel from "@mui/material/FormControlLabel";
import { archiveData } from "../store/call911Slice";
import Dialog from "@mui/material/Dialog";
import MuiDialogTitle from "@mui/material/DialogTitle";
import MuiDialogContent from "@mui/material/DialogContent";
import MuiDialogActions from "@mui/material/DialogActions";
import { withStyles } from "@mui/styles";
import ReactCardFlip from "react-card-flip";
import { Player } from "video-react";
import CardHeader from "@mui/material/CardHeader";
import CardMedia from "@mui/material/CardMedia";
import Divider from "@mui/material/Divider";
import IconButton from "@mui/material/IconButton";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import CloseIcon from "@mui/icons-material/Close";
import { useDispatch, useSelector } from "react-redux";
import React, { useState, useEffect, useRef } from "react";
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import CommonSwitch from "../../SharedComponents/ReuseComponents/CommonSwitch";

const styles = (theme) => ({
    root: {
        margin: 0,
        padding: theme.spacing(2),
    },
    closeButton: {
        position: "absolute",
        right: theme.spacing(1),
        top: theme.spacing(1),
        color: theme.palette.grey[500],
    },
});

const DialogTitle = withStyles(styles)((props) => {
    const { children, classes, onClose, ...other } = props;
    return (
        <MuiDialogTitle disableTypography className={classes.root} {...other}>
            <Typography variant="h6">{children}</Typography>
            {onClose ? (
                <IconButton
                    aria-label="close"
                    className={classes.closeButton}
                    onClick={onClose}
                >
                    <CloseIcon />
                </IconButton>
            ) : null}
        </MuiDialogTitle>
    );
});

const DialogContent = withStyles((theme) => ({
    root: {
        padding: theme.spacing(2),
        width: "auto",
        height: "auto",
    },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
    root: {
        margin: 0,
        padding: theme.spacing(1),
    },
}))(MuiDialogActions);

function ButtonComponent(props) {
    const classes = props.classes;
    const { t } = useTranslation("laguageConfig");
    const rapidSoSList = useSelector(({ call911 }) => call911.call911.rapidSoS);
    const selectedCallData = useSelector(({ call911 }) => call911.call911.selectedCall);
    const callsByNumber = useSelector(({ call911 }) => call911.call911.callsByNumber);
    const navbarTheme = useSelector(selectNavbarTheme);

    const [openRawData, setRawDataOpen] = React.useState(false);
    const [open, setOpen] = React.useState(false);
    const [isFlipped, setFlipped] = React.useState(false);
    const [toggleShowRectangle, setShowRectangle] = React.useState(false)
    const nav_css = {
        backgroundColor: navbarTheme.palette.primary.main,
        color: navbarTheme.palette.primary.contrastText,
    };

    const handleRawDataClose = () => {
        setRawDataOpen(false);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const handleReactCardFlipClick = () => {
        setFlipped(!isFlipped);
    };

    function archiveCall() {
        dispatch(archiveData());
        props.setCounty(0);
    }

    function rapidDialog() {
        setOpen(true);
    }

    function rawDataDialog() {
        setRawDataOpen(true);
    }

    const handleToggleChange = (event) => {
        setShowRectangle(event.target.checked)
        props.parentCallbackMap(event.target.checked)
    }

    return (
        <>
            <div style={{ display: 'flex' }}>
                <ErrorBoundary
                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_archiveCalls" />}
                    onReset={() => window.location.reload()} >
                    <CommonButton styleClass="m-4" btnName={t("archiveCalls")} parentCallback={archiveCall}></CommonButton>
                </ErrorBoundary>
                <ErrorBoundary
                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonSwitch_showAllCalls" />}
                    onReset={() => window.location.reload()} >
                    <CommonSwitch switchName={t("showAllCalls")} parentCallback={props.handleToggleChange} toggleValue={props.toggleValue} ></CommonSwitch>
                </ErrorBoundary>
                <ErrorBoundary
                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_rapidSOS" />}
                    onReset={() => window.location.reload()} >
                    <CommonButton styleClass="m-4" btnName={t("rapidSoS")} parentCallback={rapidDialog}></CommonButton>
                </ErrorBoundary>
                <ErrorBoundary
                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_rowData" />}
                    onReset={() => window.location.reload()} >
                    <CommonButton styleClass="m-4" btnName={t("rawData")} parentCallback={rawDataDialog}></CommonButton>
                </ErrorBoundary>
                <ErrorBoundary
                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonSwitch_showRectangle" />}
                    onReset={() => window.location.reload()} >
                    <CommonSwitch switchName={t("showRectangle")} parentCallback={handleToggleChange} toggleValue={toggleShowRectangle} ></CommonSwitch>
                </ErrorBoundary>
            </div>

            <Dialog
                onClose={handleRawDataClose}
                maxWidth="true"
                style={{ width: "70%", marginLeft: "15%" }}
                aria-labelledby="customized-dialog-title"
                open={openRawData}
            >
                <DialogTitle
                    id="customized-dialog-title"
                    onClose={handleRawDataClose}
                    style={nav_css}
                >
                    {t("rawData")}
                </DialogTitle>
                <DialogContent dividers style={{ width: "100%" }}>

                    {t("selectedCall")}
                    <Card
                        variant="outlined"
                        style={{ marginBottom: "2px" }}
                        className="font-bold"
                        color="inherit"
                    >
                        <div style={{ padding: "5px", fontSize: "15px" }}>
                            {selectedCallData.PacketClassofService}{", "}
                            <pre style={{ whiteSpace: "pre-wrap", wordBreak: "break-word" }}
                            >{selectedCallData.RawInputString}</pre>
                        </div>
                    </Card>
                    {selectedCallData.SubCall &&
                        selectedCallData.SubCall.length > 0 &&
                        selectedCallData.SubCall.map((call) => (
                            <Card variant="outlined" style={{ marginBottom: "2px" }}>
                                <div style={{ padding: "5px", fontSize: "15px" }}>
                                    {call.PacketClassofService}{", "}
                                    <pre style={{ whiteSpace: "pre-wrap", wordBreak: "break-word" }}
                                    >{call.RawInputString}</pre>
                                </div>
                            </Card>
                        )).reverse()}

                    {callsByNumber.length > 0 &&
                        callsByNumber.length + " Moving Call/s"}
                    {callsByNumber.length > 0 &&
                        callsByNumber.map((call) => (
                            <>
                                <Card
                                    variant="outlined"
                                    style={{ marginBottom: "2px" }}
                                    className="font-bold"
                                    color="inherit"
                                >
                                    <pre style={{
                                        padding: "5px", fontSize: "15px",
                                        whiteSpace: "pre-wrap", wordBreak: "break-word"
                                    }}
                                    >{call.RawInputString}</pre>
                                </Card>
                                {call.SubCall.map((callDetail) => (
                                    <Card
                                        variant="outlined"
                                        style={{ marginBottom: "2px" }}
                                    >
                                        <pre style={{
                                            padding: "5px", fontSize: "15px",
                                            whiteSpace: "pre-wrap", wordBreak: "break-word"
                                        }}
                                        >{callDetail.RawInputString}</pre>
                                    </Card>
                                )).reverse()}
                            </>
                        ))}
                </DialogContent>
                <DialogActions>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton" />}
                        onReset={() => window.location.reload()} >
                        <CommonButton styleClass="" btnName={t("Close")} parentCallback={handleRawDataClose}></CommonButton>
                    </ErrorBoundary>
                </DialogActions>
            </Dialog>

            <Dialog
                onClose={handleClose}
                maxWidth="false"
                aria-labelledby="customized-dialog-title"
                open={open}
            >
                <DialogTitle
                    id="customized-dialog-title"
                    onClose={handleClose}
                    style={nav_css}
                >
                    {t("rapidSoS")}
                </DialogTitle>
                <DialogContent dividers>
                    <div>
                        {rapidSoSList.map((ele) => (
                            <ReactCardFlip
                                infinite="true"
                                flipSpeedBackToFront="1.5"
                                flipSpeedFrontToBack="1.5"
                                isFlipped={isFlipped}
                                flipDirection="horizontal"
                            >
                                <Card className="mt-8">
                                    <CardHeader
                                        onClick={handleReactCardFlipClick}
                                        style={{ cursor: "pointer" }}
                                        subheader={ele.testNumber}
                                        title={t("medicAlertTest")}
                                    />
                                    <CardContent>
                                        {ele.data.map((ele1) => (
                                            <Typography
                                                variant="body2"
                                                color="textPrimary"
                                                component="p"
                                                className="p-2"
                                            >
                                                {ele1[0]}:
                                                {(() => {
                                                    if (ele1[1].includes("image-url")) {
                                                        return (
                                                            <CardMedia
                                                                className={classes.media}
                                                                image={ele1[1].replace("image-url:", "")}
                                                                title={t("contemplativeReptile")}
                                                            />
                                                        );
                                                    }
                                                    // eslint-disable-next-line
                                                    else if (ele1[1].includes("web-url")) {
                                                        return (
                                                            <a
                                                                style={{ fontWeight: 600 }}
                                                                target="_blank"
                                                                href={ele1[1].replace("web-url:", "")}
                                                            >
                                                                {ele1[1].replace("web-url:", "")}
                                                            </a>
                                                        );
                                                    } else if (ele1[1].includes("video-stream")) {
                                                        return (
                                                            <Player className={classes.root}>
                                                                <source
                                                                    src={ele1[1].replace(
                                                                        "video-stream:",
                                                                        ""
                                                                    )}
                                                                />
                                                            </Player>
                                                        );
                                                    } else {
                                                        return (
                                                            <span style={{ fontWeight: 600 }}>
                                                                {ele1[1]}
                                                            </span>
                                                        );
                                                    }
                                                })()}
                                            </Typography>
                                        ))}
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardHeader
                                        onClick={handleReactCardFlipClick}
                                        style={{ cursor: "pointer" }}
                                        title={t("locationHistory")}
                                    />
                                    {rapidSoSList[0].EmergencyData !== undefined
                                        ? rapidSoSList[0].EmergencyData.map(
                                            (emergencyData, index) => (
                                                <CardContent dividers>
                                                    <Typography
                                                        variant="body2"
                                                        color="textPrimary"
                                                        component="h4"
                                                        className="p-4"
                                                    >
                                                        <h4 style={{ fontWeight: "bolder" }}>
                                                            {new Date(
                                                                emergencyData.SafeZone.data.shot_alert.location.timestamp
                                                            ).toLocaleDateString()}{" "}
                                                            |
                                                            {new Date(
                                                                emergencyData.SafeZone.data.shot_alert.location.timestamp
                                                            ).toLocaleTimeString()}
                                                        </h4>
                                                    </Typography>
                                                    <Typography
                                                        variant="body2"
                                                        color="textPrimary"
                                                        component="p"
                                                        className="p-4"
                                                    >
                                                        {t("estimatedaddress")}
                                                    </Typography>
                                                    <Typography
                                                        variant="body2"
                                                        color="textPrimary"
                                                        component="p"
                                                        className="p-4"
                                                        style={{ fontWeight: 800 }}
                                                    >
                                                        {t("indoorLocation")}
                                                    </Typography>
                                                    <Typography
                                                        variant="body2"
                                                        color="textPrimary"
                                                        component="p"
                                                        className="p-4"
                                                    >
                                                        {t("longitude")}
                                                        {
                                                            emergencyData.SafeZone.data.shot_alert
                                                                .location.longitude
                                                        }
                                                    </Typography>
                                                    <Typography
                                                        variant="body2"
                                                        color="textPrimary"
                                                        component="p"
                                                        className="p-4"
                                                    >
                                                        {t("latitude")}
                                                        {
                                                            emergencyData.SafeZone.data.shot_alert
                                                                .location.latitude
                                                        }
                                                    </Typography>
                                                    <Typography
                                                        variant="body2"
                                                        color="textPrimary"
                                                        component="p"
                                                        className="p-4"
                                                    >
                                                        {t("uncertaintyRadius")}
                                                        {
                                                            emergencyData.SafeZone.data.shot_alert
                                                                .location.uncertainty
                                                        }
                                                    </Typography>
                                                    <Divider component="p" />
                                                    <span>
                                                        <Typography
                                                            className={classes.dividerFullWidth}
                                                            color="textSecondary"
                                                            display="block"
                                                            variant="caption"
                                                        ></Typography>
                                                    </span>
                                                </CardContent>
                                            )
                                        )
                                        : null}
                                </Card>
                            </ReactCardFlip>
                        ))}
                    </div>
                </DialogContent>
                <DialogActions>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton" />}
                        onReset={() => window.location.reload()} >
                        <CommonButton styleClass="" btnName={t("close")} parentCallback={handleClose}></CommonButton>
                    </ErrorBoundary>
                </DialogActions>
            </Dialog>
        </>
    );
}

export default ButtonComponent;