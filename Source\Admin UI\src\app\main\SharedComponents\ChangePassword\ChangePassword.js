import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../ErrorPage/ErrorPage';
import * as yup from 'yup';
import TextField from '@mui/material/TextField';
import { ChangedPassword, updateConfirmationDlg } from 'src/app/auth/store/userSlice';
import { useDispatch, useSelector } from 'react-redux';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Checkbox from '@mui/material/Checkbox';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import JwtService from 'src/app/services/jwtService';
import ConfirmationMessageDlg from '../ConfirmationMessageDialog/ConfirmationMessageDlg';
import { useTranslation } from "react-i18next";

const schema = yup.object().shape({
    Password: yup
        .string()
        .required('Please enter your password.')
        .min(8, 'Password is too short - should be 8 chars minimum.'),
    confirmPassword: yup.string().oneOf([yup.ref('Password'), null], 'Passwords must match'),
});

const defaultValues = {
    currentPassword: '',
    Password: '',
    confirmPassword: '',
};

function ChangePassword(props) {
    const { control, formState, handleSubmit, reset, setValue } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });

    const { t } = useTranslation("laguageConfig");
    const { isValid, dirtyFields, errors } = formState;
    const dispatch = useDispatch();
    const userName = useSelector(({ auth }) => auth.login);
    const [showOldPassword, setShowOldPassword] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [checked, setChecked] = useState(true);

    const ChangePasswordConfirmationDlg = useSelector(({ auth }) => auth.user.ChangePasswordConfirmationDlg);
    const ChangePassword = useSelector(({ auth }) => auth.user.ChangePassword);

    const [open, setOpen] = useState(false);

    useEffect(() => {
        if (ChangePasswordConfirmationDlg) {
            setOpen(true)
        }

    }, [ChangePasswordConfirmationDlg]);

    useEffect(() => {
        if (ChangePassword) {
            props.newFunc();
        }

    }, [ChangePassword]);

    function onSubmit(model) {
        let data = {
            currentPassword: model.currentPassword,
            password: model.Password,
            confirmPassword: model.confirmPassword,
            email: props.data.email,
            sendEmail: checked
        }
        dispatch(ChangedPassword(data, props.isDialog))
    }

    const ClearAll = () => {
        setValue("currentPassword", "");
        setValue("Password", "");
        setValue("confirmPassword", "");
    };

    const handleChange = (event) => {
        setChecked(event.target.checked);
    };

    const handleClose = () => {
        setOpen(false);
        ClearAll();
        dispatch(updateConfirmationDlg());
        JwtService.logout()
    };

    return (
        <div className="w-full">
            <form
                name="registerForm"
                noValidate
                className="flex flex-col justify-center w-full pb-16"
                onSubmit={handleSubmit(onSubmit)}
                // autoComplete="off"
                autoSave={false}
            >
                {props.isDialog == false &&
                    <Controller
                        name="currentPassword"
                        control={control}
                        render={({ field }) => (
                            <TextField
                                {...field}
                                className="mb-16"
                                label={t("currentPassword")}
                                type="password"
                                error={!!errors.password}
                                helperText={errors?.password?.message}
                                variant="outlined"
                                InputProps={{
                                    className: 'pr-2',
                                    type: showOldPassword ? 'text' : 'password',
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton onClick={() => setShowOldPassword(!showOldPassword)} size="large">
                                                <Icon className="text-20" color="action">
                                                    {showOldPassword ? 'visibility' : 'visibility_off'}
                                                </Icon>
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                                required
                                fullWidth
                            />
                        )}
                    />

                }

                <Controller
                    name="Password"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            className="mb-16"
                            label={t("password")}
                            type="password"
                            error={!!errors.password}
                            helperText={errors?.password?.message}
                            variant="outlined"
                            InputProps={{
                                className: 'pr-2',
                                type: showPassword ? 'text' : 'password',
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton onClick={() => setShowPassword(!showPassword)} size="large">
                                            <Icon className="text-20" color="action">
                                                {showPassword ? 'visibility' : 'visibility_off'}
                                            </Icon>
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            }}
                            required
                            fullWidth
                        />
                    )}
                />

                <Controller
                    name="confirmPassword"
                    control={control}
                    render={({ field }) => (
                        <TextField
                            {...field}
                            className="mb-16"
                            label={t("confirmPassword")}
                            type="password"
                            error={!!errors.confirmPassword}
                            helperText={errors?.confirmPassword?.message}
                            variant="outlined"
                            InputProps={{
                                className: 'pr-2',
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton>
                                            <Icon className="text-20" color="action">
                                                vpn_key
                                            </Icon>
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            }}
                            required
                            fullWidth
                        />
                    )}
                />

                {props.isDialog == true && <FormGroup>
                    <FormControlLabel onChange={handleChange} control={<Checkbox defaultChecked />} label={t("sendEmailToUser")} />
                </FormGroup>}

                <Button
                    variant="contained"
                    color="primary"
                    className=" w-full mx-auto mt-8"
                    aria-label="Register"
                    disabled={_.isEmpty(dirtyFields) || !isValid}
                    type="submit"
                    size="large"
                >
                    {t("changePassword")}
                </Button>
            </form>
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationMessageDlg" />} onReset={() => { }}>
                <ConfirmationMessageDlg
                    id="ringtone-menu"
                    keepMounted
                    open={open}
                    onClose={handleClose}
                    text={t("passwordConfirm")}
                />
            </ErrorBoundary>
        </div>
    )
}


export default ChangePassword;
