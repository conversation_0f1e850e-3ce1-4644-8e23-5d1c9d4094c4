import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const getViolationDetails = (searchText, code) => async dispatch => {
	const data = {
		searchText,
		code
	}
	try {
		await axios
			.post(`admin/api/callViolation/violationDetails`, encrypt(JSON.stringify(data)))
			.then(response => {
				
				dispatch(violationListSuccess(JSON.parse(decrypt(response.data))));
				if (response.status == 200) {
					dispatch(
						showMessage({
							message: 'Violation details found.',
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'success'
						})
					);
				} else {
					dispatch(
						showMessage({
							message: 'Violation details not found.',
							autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						})
					);
				}
			})
			.catch(error => {
				dispatch(violationListError(error));
			});
	} catch (e) {
		return console.error(e.message);
	}
};

const initialState = {
	violationsuccess: false,
	violationdata: []
};

const violationSlice = createSlice({
	name: 'violation',
	initialState,
	reducers: {
		violationListSuccess: (state, action) => {
			state.violationsuccess = true;
			state.violationdata = action.payload;
		},

		violationListError: (state, action) => {
			state.violationsuccess = false;
			state.violationdata = [];
		}
	},
	extraReducers: {}
});

export const {
	violationListSuccess,

	violationListError
} = violationSlice.actions;

export default violationSlice.reducer;
