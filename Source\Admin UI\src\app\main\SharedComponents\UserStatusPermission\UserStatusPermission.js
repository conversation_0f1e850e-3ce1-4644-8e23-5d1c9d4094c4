import React from 'react';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import FormGroup from '@mui/material/FormGroup';
import { useTranslation } from "react-i18next";

function UserStatusPermission(props) {

    const { t } = useTranslation('laguageConfig');
    const handleUserStatusPermissionChange = (event) => {
        props.userStatusRequiredValueChange(event.currentTarget.checked)
    }

    return (
        <div className="flex flex-col justify-center w-full">
            <FormGroup className='col-span-1'>
                <FormControlLabel
                    control={<Checkbox checked={props.userStatusPermission}
                        onChange={handleUserStatusPermissionChange} name="userStatus" />}
                    label={t('userStatus')}
                />
            </FormGroup>
        </div>
    );

}

export default UserStatusPermission;
