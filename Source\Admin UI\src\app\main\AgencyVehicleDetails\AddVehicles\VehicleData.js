import React, { useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Controller, useForm } from "react-hook-form";
import { Grid, TextField } from "@mui/material";
import { useTranslation } from "react-i18next";
import { checkValueEmptyOrNull } from "../../utils/utils";
import Button from "@mui/material/Button";
import { useLocation, useParams } from "react-router";
import history from "@history";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { SaveVehicle, UpdateVehicle } from "../../administration/store/vehicleMasterSlice";
import { ErrorBoundary } from "react-error-boundary";
import CommonAddressReactAutoComplete from "../../SharedComponents/SharedFormFields/CommonAddressReactAutoComplete";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { getAgencyDetailsWithCode } from "../../agencyPage/store/agencySlice";


const schema = yup.object().shape({
  Year: yup.string().required('Please enter Year.'),
  Make: yup.string().required('Please enter Make Description.'),
  Model: yup.string().required('Please enter Model Description.'),
  Color1: yup.string().required('Please enter Primary Color.'),
  Color2: yup.string().required('Please enter Secondary Color.'),
  state: yup.string().required('Please enter Style Description.'),
  TagNumber: yup.string().required('Please enter Tag.'),
  FirstName: yup.string().required('Please enter First Name.'),
  MiddleName: yup.string().required('Please enter Middle Name.'),
  LastName: yup.string().required('Please enter Last Name.'),
  DFirstName: yup.string().required('Please enter Driver First Name.'),
  DMiddleName: yup.string().required('Please enter Driver Middle Name.'),
  DLastName: yup.string().required('Please enter Driver Last Name.')
});

const defaultValues = {
  id: '',
  Year: '',
  Make: '',
  Model: '',
  Color1: '',
  Color2: '',
  state: '',
  vin: '',
  TagNumber: '',
  FirstName: '',
  MiddleName: '',
  LastName: '',
  DFirstName: '',
  DMiddleName: '',
  DLastName: ''
};

function VehicleData(props) {
  const { t } = useTranslation("laguageConfig");
  const routeParams = useParams();
  const dispatch = useDispatch();
  const location = useLocation();
  const { tempData } = location.state || {};
  const yearInputRef = useRef(null);

  const { control, setValue, formState: { errors }, handleSubmit, reset, trigger, setError, } = useForm({
    mode: "onChange",
    defaultValues,
    resolver: yupResolver(schema),
  });


  useEffect(() => {
    if (routeParams.id !== "0" && tempData?.data !== null) {
      setControlValue(tempData?.data);
    }
    //For Address state and county to search address
    dispatch(getAgencyDetailsWithCode(routeParams.code));
  }, [routeParams, tempData]);

  const setControlValue = (data) => {
    setValue("id", data._id);
    setValue("Year", data?.modelYear ?? '');
    setValue("Make", data?.makeDesc ?? '');
    setValue("Model", data?.modelDesc ?? '');
    setValue("Color1", data?.vehicleColor1 ?? '');
    setValue("Color2", data?.vehicleColor2 ?? '');
    setValue("state", data?.tagState ?? '');
    setValue("Vin", data.vin ?? '');
    setValue("TagNumber", data?.tagNumber ?? '');
    setValue("LastName", data.ownerInformation?.LName);
    setValue("MiddleName", data.ownerInformation?.MName);
    setValue("FirstName", data.ownerInformation?.FName);
    setValue("Email", data.ownerInformation?.Email);
    setValue("Number", data.ownerInformation?.PhoneNumber);
    setValue("Suffix", data.ownerInformation?.Suffix);
    setValue("DLNumber", data.ownerInformation?.DLNumber);
    setOwnerAddressValues(data.ownerInformation);
    setValue("DLastName", data.driverInformation?.DLName);
    setValue("DMiddleName", data.driverInformation?.DMName);
    setValue("DFirstName", data.driverInformation?.DFName);
    setValue("DEmail", data.driverInformation?.DEmail);
    setValue("DNumber", data.driverInformation?.DPhoneNumber);
    setValue("DSuffix", data.driverInformation?.DSuffix);
    setValue("DDLNumber", data.driverInformation?.DDLNumber);
    setDriverAddressValues(data.driverInformation);
  };

  const ClearAll = () => {
    setValue("id", "");
    setValue("Year", "");
    setValue("Make", "");
    setValue("Model", "");
    setValue("Color1", "");
    setValue("Color2", "");
    setValue("state", "");
    setValue("Vin", "");
    setValue("TagNumber", "");
    setValue("LastName", "");
    setValue("MiddleName", "");
    setValue("FirstName", "");
    setValue("Email", "");
    setValue("Number", "");
    setValue("Suffix", "");
    setValue("DLNumber", "");
    setValue("St_PreDir", "");
    setValue("St_Name", "");
    setValue("St_PosTyp", "");
    setValue("St_PosDir", "");
    setValue("Building", "");
    setValue("PlaceType", "");
    setValue("Unit", "");
    setValue("Post_Comm", "");
    setValue("State", "");
    setValue("County", "");
    setValue("Post_Code", "");
    setValue("Involvement", "");
    setValue("Notes", "");
    setValue("DLastName", "");
    setValue("DMiddleName", "");
    setValue("DFirstName", "");
    setValue("DEmail", "");
    setValue("DNumber", "");
    setValue("DSuffix", "");
    setValue("DDLNumber", "");
    setValue("DSt_PreDir", "");
    setValue("DSt_Name", "");
    setValue("DSt_PosTyp", "");
    setValue("DSt_PosDir", "");
    setValue("DBuilding", "");
    setValue("DPlaceType", "");
    setValue("DUnit", "");
    setValue("DPost_Comm", "");
    setValue("DState", "");
    setValue("DCounty", "");
    setValue("DPost_Code", "");
  };

  const onSubmit = (model) => {
    const ownerInfo = {
      FName: checkValueEmptyOrNull(model.FirstName),
      MName: checkValueEmptyOrNull(model.MiddleName),
      LName: checkValueEmptyOrNull(model.LastName),
      Email: checkValueEmptyOrNull(model.Email),
      PhoneNumber: checkValueEmptyOrNull(model.Number),
      Suffix: checkValueEmptyOrNull(model.Suffix),
      St_PreDir: checkValueEmptyOrNull(model.St_PreDir),
      St_Name: checkValueEmptyOrNull(model.St_Name),
      St_PosTyp: checkValueEmptyOrNull(model.St_PosTyp),
      St_PosDir: checkValueEmptyOrNull(model.St_PosDir),
      Building: checkValueEmptyOrNull(model.Building),
      PlaceType: checkValueEmptyOrNull(model.PlaceType),
      Unit: checkValueEmptyOrNull(model.Unit),
      Post_Comm: checkValueEmptyOrNull(model.Post_Comm),
      State: checkValueEmptyOrNull(model.State),
      County: checkValueEmptyOrNull(model.County),
      Post_Code: checkValueEmptyOrNull(model.Post_Code),
      DLNumber: checkValueEmptyOrNull(model.DLNumber),
    };
    const driverInfo = {
      DFName: checkValueEmptyOrNull(model.DFirstName),
      DMName: checkValueEmptyOrNull(model.DMiddleName),
      DLName: checkValueEmptyOrNull(model.DLastName),
      DEmail: checkValueEmptyOrNull(model.DEmail),
      DPhoneNumber: checkValueEmptyOrNull(model.DNumber),
      DSuffix: checkValueEmptyOrNull(model.DSuffix),
      DSt_PreDir: checkValueEmptyOrNull(model.DSt_PreDir),
      DSt_Name: checkValueEmptyOrNull(model.DSt_Name),
      DSt_PosTyp: checkValueEmptyOrNull(model.DSt_PosTyp),
      DSt_PosDir: checkValueEmptyOrNull(model.DSt_PosDir),
      DBuilding: checkValueEmptyOrNull(model.DBuilding),
      DPlaceType: checkValueEmptyOrNull(model.DPlaceType),
      DUnit: checkValueEmptyOrNull(model.DUnit),
      DPost_Comm: checkValueEmptyOrNull(model.DPost_Comm),
      DState: checkValueEmptyOrNull(model.DState),
      DCounty: checkValueEmptyOrNull(model.DCounty),
      DPost_Code: checkValueEmptyOrNull(model.DPost_Code),
      DDLNumber: checkValueEmptyOrNull(model.DDLNumber),
    };
    let data = {
      modelYear: checkValueEmptyOrNull(model.Year),
      makeDesc: checkValueEmptyOrNull(model.Make),
      modelDesc: checkValueEmptyOrNull(model.Model),
      tagState: checkValueEmptyOrNull(model.state),
      vehicleColor1: checkValueEmptyOrNull(model.Color1),
      vehicleColor2: checkValueEmptyOrNull(model.Color2),
      tagNumber: checkValueEmptyOrNull(model.TagNumber),
      vin: checkValueEmptyOrNull(model.Vin),
      ownerInformation: ownerInfo,
      driverInformation: driverInfo,
      code: routeParams.code,
    };
    if (model.id !== '') {
      data._id = model.id;
      dispatch(UpdateVehicle(data, tempData));
    } else {
      dispatch(SaveVehicle(data, tempData));
    }
    history.push(`/admin/vehicle/${routeParams.code}`);
    ClearAll();
  };

  const [isChecked, setChecked] = React.useState(false);

  const setDriverAddressValues = (data) => {
    debugger;
    setValue("DSt_PreDir", data?.DSt_PreDir ?? data?.St_PreDir ?? '');
    setValue("DSt_Name", data?.DSt_Name ?? data?.St_Name ?? '');
    setValue("DSt_PosTyp", data?.DSt_PosTyp ?? data?.St_PosTyp ?? '');
    setValue("DSt_PosDir", data?.DSt_PosDir ?? data?.St_PosDir ?? '');
    setValue("DBuilding", data?.DBuilding ?? data?.Building ?? '');
    setValue("DUnit", data?.DUnit ?? data?.Unit ?? '');
    setValue("DState", data?.DState ?? data?.State ?? '');
    setValue("DPost_Comm", data?.DPost_Comm ?? data?.Post_Comm ?? '');
    setValue("DPlaceType", data?.DPlaceType ?? data?.PlaceType ?? '');
    setValue("DCounty", data?.DCounty ?? data?.County ?? '');
    setValue("DPost_Code", data?.DPost_Code ?? data?.Post_Code ?? '');
  }

  const setOwnerAddressValues = (data) => {
    debugger;
    setValue("St_PreDir", data?.St_PreDir ?? '');
    setValue("St_Name", data?.St_Name ?? '');
    setValue("St_PosTyp", data?.St_PosTyp ?? '');
    setValue("St_PosDir", data?.St_PosDir ?? '');
    setValue("Building", data?.Building ?? '');
    setValue("PlaceType", data?.PlaceType ?? '');
    setValue("Unit", data?.Unit ?? '');
    setValue("Post_Comm", data?.Post_Comm ?? '');
    setValue("State", data?.State ?? '');
    setValue("County", data?.County ?? '');
    setValue("Post_Code", data?.Post_Code ?? '');
  }

  const handleChange = (event) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (yearInputRef.current) {
      yearInputRef.current.focus();
    }
  }, [yearInputRef]);

  const handleOnOwnerSelectAddress = (data) => {
    setOwnerAddressValues(data);
  };

  const handleOnOwnerClearAddress = () => {
    setOwnerAddressValues({});
  };

  const handleOnDriverSelectAddress = (data) => {
    setDriverAddressValues(data);
  };

  const handleOnDriverClearAddress = () => {
    setDriverAddressValues({});
  };

  return (
    <div className="w-full p-16 ">
      <form
        className="flex flex-col justify-center w-full"
        onSubmit={handleSubmit(onSubmit)}
        autoSave={false}
        autoComplete={false}
      >
        <div className="flex -mx-4">
          <Controller
            name="Year"
            control={control}
            rules={{
              required: true,
              pattern: {
                value: /^[0-9]{4}$/, // Regex for a 4-digit year
                message: t("Please enter a valid year"), // Error message
              },
            }}
            //Add rules for year
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                inputRef={yearInputRef}
                label={t("year")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
                error={!!errors.Year} // Check for error
                helperText={errors.Year ? errors.Year.message : null} // Display error message
              />
            )}
          />
          <Controller
            name="Make"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("make")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Model"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full mt-8 mb-16 mx-4"
                type="text"
                label={t("model")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Color1"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("primaryColor")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Color2"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("secondaryColor")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="state"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("st")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="TagNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("tag")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Vin"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("vin")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <span> {t("ownerInformation")}:</span>
        <div className="flex -mx-4">
          <Controller
            name="FirstName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("firstName")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="MiddleName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("middleName")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="LastName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("lastName")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <div className="flex -mx-4">
          <Controller
            name="Email"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("email")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Number"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("number")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Suffix"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-10 mx-4"
                type="text"
                label={t("suffix")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <Grid container spacing={1} className="mb-8">
          <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="ReactAutoCompleteSearch" />} onReset={() => { }}>
              <CommonAddressReactAutoComplete
                onSelect={handleOnOwnerSelectAddress}
                handleClear={handleOnOwnerClearAddress}
              />
            </ErrorBoundary>
          </Grid>
        </Grid>
        <div className="flex -mx-4">
          <Controller
            name="St_PreDir"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("preDir")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="St_Name"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("streetName")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="St_PosTyp"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("streetType")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="St_PosDir"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("postDir")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <div className="flex -mx-4">
          <Controller
            name="Building"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("building")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="PlaceType"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("unitType")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Unit"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("unitID")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Post_Comm"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("city")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="State"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("state")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <div className="flex -mx-4">
          <Controller
            name="County"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("county")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="Post_Code"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("zip")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DLNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("dlNumber")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <div className="flex -mx-4">
          <span>
            {t("driverInformation")}: &nbsp;
            <FormControlLabel
              control={
                <Checkbox
                  checked={isChecked}
                  onChange={handleChange}
                  name="checkedA"
                />
              }
              label={t("sameAsOwner")}
            />
          </span>
        </div>
        <div className="flex -mx-4">
          <Controller
            name="DFirstName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("firstName")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DMiddleName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("middleName")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DLastName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("lastName")}
                variant="outlined"
                required
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <div className="flex -mx-4">
          <Controller
            name="DEmail"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("email")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("number")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DSuffix"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-10 mx-4"
                type="text"
                label={t("suffix")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <Grid container spacing={1} className="mb-8">
          <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
            <ErrorBoundary
              FallbackComponent={(props) => <ErrorPage {...props} componentName="ReactAutoCompleteSearch" />} onReset={() => { }}>
              <CommonAddressReactAutoComplete
                onSelect={handleOnDriverSelectAddress}
                handleClear={handleOnDriverClearAddress}
              />
            </ErrorBoundary>
          </Grid>
        </Grid>
        <div className="flex -mx-4">
          <Controller
            name="DSt_PreDir"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("preDir")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DSt_Name"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("streetName")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DSt_PosTyp"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("streetType")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DSt_PosDir"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("postDir")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <div className="flex -mx-4">
          <Controller
            name="DBuilding"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("building")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DPlaceType"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("unitType")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DUnit"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("unitID")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DPost_Comm"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("city")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DState"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("state")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <div className="flex -mx-4">
          <Controller
            name="DCounty"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("county")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DPost_Code"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("zip")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
          <Controller
            name="DDLNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                className="w-full  mt-8 mb-16 mx-4"
                type="text"
                label={t("dlNumber")}
                variant="outlined"
                InputLabelProps={{
                  shrink: field.value ? true : undefined,
                }}
              />
            )}
          />
        </div>
        <div className="flex justify-center">
          <Button
            type="submit"
            variant="contained"
            color="primary"
            className="normal-case m-16"
            aria-label="REGISTER"
            value="legacy"
          >
            {t("save")}
          </Button>
          <Button
            type="button"
            variant="contained"
            color="secondary"
            onClick={() => history.push(`/admin/vehicle/${routeParams.code}`)}
            className="normal-case m-16"
            aria-label="UPDATE"
            value="legacy"
          >
            {t("back")}
          </Button>
        </div>
      </form>
    </div>
  );
}
export default VehicleData;
