import axios from 'axios';
import express, { Request, NextFunction } from 'express';
import gateway from 'fast-gateway';
import dotenv from 'dotenv';
import jwt from "jsonwebtoken";
import toArray from 'stream-to-array';

const app: express.Application = express();

dotenv.config();

interface VerifiedMongo {
    client: any;
    socketApiUrl: any;
    mugshotapitoken: any;
    isSuperAdmin: boolean;
    userdeviceid: string | null;
    IsDeviceLicenseCodeRequired: boolean;
    agencyCode: string;
    id: string;
    mobileapiToken: string;
    mobileapiUrl: string;
    ncicApiUrl?: string | null;
    mugshotapiURL?: string | null;
}

const CheckAuthValidDevice = async (req: any, res: any, next: NextFunction): Promise<void> => {
    try {
        req.timeout = 100000;
        next();
    } catch (error) { }
}

const setHeader = async (req: any, res: any): Promise<Request['headers'] | void> => {
    if (req.headers['authorization'] === undefined || req.headers['authorization'] === null) {
        return;
    }

    const token: string = req.headers['authorization'].split(' ')[1];

    try {
        if (token !== "null") {
            const verifiedMongo = await jwt.verify(token, process.env.JWT_PRIVATEKEY as string) as VerifiedMongo;

            if (verifiedMongo) {
                if (!verifiedMongo.isSuperAdmin) {
                    if (verifiedMongo.userdeviceid !== null && verifiedMongo.userdeviceid !== undefined && verifiedMongo.userdeviceid !== "") {
                        if (verifiedMongo.IsDeviceLicenseCodeRequired) {
                            const data = await axios.get(`${process.env.LOGIN_API}api/auth/getUserDeviceLoginDetails/${verifiedMongo.id}/${verifiedMongo.userdeviceid}/${verifiedMongo.agencyCode}`, {
                                headers: { 'Content-Type': 'application/json', Authorization: ` ${req.headers['authorization']}` }
                            });
                            if (data.data.message === false) {
                                res.status(401).end(JSON.stringify("InvalidDevice"));
                                return;
                            }
                        }
                    }
                }

                req.headers.agencycode = verifiedMongo.agencyCode;
                req.headers.isSuperAdmin = verifiedMongo.isSuperAdmin.toString();
                req.headers.mobileapitoken = verifiedMongo.mobileapiToken;
                req.headers.mobileapiurl = verifiedMongo.mobileapiUrl;
                req.headers['accept-language'] = req.headers['accept-language'] === undefined ? 'en' : req.headers['accept-language'];
                req.headers.userdeviceid = verifiedMongo.userdeviceid!;
                req.headers.id = verifiedMongo.id;
                req.headers.ncicApiUrl = verifiedMongo.ncicApiUrl ?? "";
                req.headers.mugshotapiurl = verifiedMongo.mugshotapiURL ?? "";
                req.headers.IsDeviceLicenseCodeRequired = verifiedMongo.IsDeviceLicenseCodeRequired.toString();
                req.headers.mugshotapitoken = verifiedMongo.mugshotapitoken!;
                req.headers.socketApiUrl = verifiedMongo.socketApiUrl!;
                req.headers.client = verifiedMongo.client!;

            } else {
                res.status(401).end(JSON.stringify("TokenExpire"));
            }
            return req.headers;
        }
        return req.headers;
    } catch (error) {
        res.status(401).end(JSON.stringify("TokenExpire"));
    }
}

const server = gateway({
    middlewares: [
        CheckAuthValidDevice,
        require('cors')(),

    ],
    routes: [
        {
            prefix: '/gateway/adminqrcode',
            target: process.env.RELATIVITY_ADMIN_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
        },
        {
            prefix: '/gateway/admin',
            target: process.env.RELATIVITY_ADMIN_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    req.headers = await setHeader(req, res)


                },
                async onResponse(req: any, res: any, stream: NodeJS.ReadableStream) {
                    const resBuffer = Buffer.concat(await toArray(stream))
                    res.end(resBuffer)

                }
            },
        },
        {
            prefix: '/gateway/fileupload',
            target: process.env.FILEUPLOAD_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    //await setHeader(req, res)

                },
            },
        },
        {
            prefix: '/gateway/incident',
            target: process.env.INCIDENT_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    await setHeader(req, res)

                },
                async onResponse(req: any, res: any, stream: NodeJS.ReadableStream) {

                    const resBuffer = Buffer.concat(await toArray(stream))
                    res.end(resBuffer)

                }
            },
        },
        {
            prefix: '/gateway/dispatch',
            target: process.env.DISPATCH_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    //console.log("request")
                    await setHeader(req, res)

                },
                async onResponse(req: any, res: any, stream: NodeJS.ReadableStream) {
                    const resBuffer = Buffer.concat(await toArray(stream))
                    res.end(resBuffer)

                }
            },
        },
        {
            prefix: '/gateway/911',
            target: process.env.CALL911_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    await setHeader(req, res)

                },
                async onResponse(req: any, res: any, stream: NodeJS.ReadableStream) {
                    const resBuffer = Buffer.concat(await toArray(stream))
                    res.end(resBuffer)
                }
            },
        },
        {
            prefix: '/gateway/quiktipClient',
            target: process.env.QUIKTIPAPI_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    //await setHeader(req, res)
                },
                async onResponse(req: any, res: any, stream: NodeJS.ReadableStream) {
                    const resBuffer = Buffer.concat(await toArray(stream))
                    res.end(resBuffer)
                }
            },
        },
        {
            prefix: '/gateway/quiktipAdmin',
            target: process.env.QUIKTIPAPI_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    await setHeader(req, res)
                },
                async onResponse(req: any, res: any, stream: NodeJS.ReadableStream) {
                    const resBuffer = Buffer.concat(await toArray(stream))
                    res.end(resBuffer)
                }
            },
        },
        {
            prefix: '/gateway/mugshot',
            target: process.env.MUGSHOT_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    console.log("hhh")
                    await setHeader(req, res)
                },
                async onResponse(req: any, res: any, stream: NodeJS.ReadableStream) {
                    const resBuffer = Buffer.concat(await toArray(stream))
                    res.end(resBuffer)
                }
            },
        },
        // This method is used for EventSource
        {
            prefix: '/gateway/admineventsource',
            target: process.env.RELATIVITY_ADMIN_API || '',
            methods: ['GET', 'POST', 'DELETE', 'PUT'],
            hooks: {
                async onRequest(req: any, res: any) {
                    try {
                        console.log("admineventsource  onRequest")
                        req.headers['accept'] = 'text/event-stream';
                        req.headers['cache-control'] = 'no-cache';
                        req.headers['connection'] = 'keep-alive';
                        req.headers = await setHeader(req, res)

                    } catch (e) {
                        console.log('onRequest', e)
                    }

                },
                async onResponse(req: any, res: any, stream: NodeJS.ReadableStream) {
                    console.log("admineventsource  onResponse")
                    res.setHeader('Content-Type', 'text/event-stream');
                    res.setHeader('Cache-Control', 'no-cache');
                    res.setHeader('Connection', 'keep-alive');
                    stream.pipe(res);
                }
            },
        },

    ]
})

server.start(Number(process.env.PORT) || 8003).then((server: any) => {
    console.log(`Gateway listing on port: ${process.env.PORT || 8003}`);
});
