import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';

export const getCallViolations = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/callViolation/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setCallViolationsTotalCount(listData.totalCount));
                    return dispatch(setCallViolations(listData.callViolationList));
                }
                else {
                    dispatch(setLoading(false));
                    response = JSON.parse(decrypt(response.response.data));
                    return dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: error.message,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'

                }))
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return dispatch(showMessage({
            message: error.message,
            autoHideDuration: 2000,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
            },
            variant: 'warning'

        }))
    }
};

export const saveCallViolation = (data,tempData) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/callViolation`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    //get request to load updated data
                    dispatch(getCallViolations(tempData.orderId, tempData.orderDirection, tempData.pageIndex, tempData.rowsPerPage, tempData.searchText, data.code));
                    dispatch(
                        showMessage({
                            message: data.isUpdate ? response.message : response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    return dispatch(setViolationResponse(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const removeViolation = (ID, code, pageIndex, pageLimit, sortField, sortDirection, searchText) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/callViolation/${ID}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(getCallViolations(sortField,sortDirection,pageIndex,pageLimit,searchText,code));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    dispatch(
                        getCallViolations(sortField, sortDirection, pageIndex, pageLimit, searchText, code)
                    );
                    return dispatch(setViolationResponse(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),


                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//for searching
export const searchViolation = (searchText, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        axios.get(`admin/api/callViolation/searchViolation/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchViolation(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

export const getSelectedViolation = (id) => async dispatch => {
    dispatch(setSelectedViolation(id))
}

const initialState = {
    data: [],
    selectedViolation: {},
    searchViolation: [],
    success: false,
    isloading: false,
};

const callViolationsSlice = createSlice({
    name: 'administration/callViolations',
    initialState,
    reducers: {
        setCallViolations: (state, action) => {
            state.data = action.payload;
        },
        setViolationResponse: (state, action) => {
            state.success = action.payload;
        },
        setSearchViolation: (state, action) => {
            state.searchViolation = action.payload;
        },
        setSelectedViolation: (state, action) => {
            state.selectedViolation = state.data.find(x => x.ViolationID === parseInt(action.payload))
        },
        setCallViolationsTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
    },
    extraReducers: {}
});

export const {
    setCallViolations,
    setViolationResponse,
    setSelectedViolation,
    setSearchViolation,
    setCallViolationsTotalCount,
    setLoading
} = callViolationsSlice.actions;

export default callViolationsSlice.reducer;