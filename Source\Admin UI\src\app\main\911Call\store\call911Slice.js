import { createSlice } from '@reduxjs/toolkit';
import axios from "axios";
import { decrypt } from '../../../security';
import { showMessage } from 'app/store/fuse/messageSlice';

const initialState = {
    success: false,
    error: {
        contact: {}
    },
    data: [],
    group: [[], [], []],
    selectedCall: [],
    countyList: [],
    cityList: [],
    zoneList: [],
    pdZoneList: [],
    rapidSoS: [],
    callsByNumber: [],
    loader: false,
    currentData: [],
    update: false,
    updatedData: [],
    hoverID: null,
    callTypesData: []

};

const call911Slice = createSlice({
    name: 'call911',
    initialState,
    reducers: {
        setRAPIDSOS: (state, action) => {
            state.rapidSoS = action.payload;
        },
        setCALLTYPESDATA: (state, action) => {
            state.callTypesData = action.payload;
        },
        setFAILCALLTYPESDATA: (state, action) => {
            state.callTypesData = [];
        },
        setPACKET911CALL: (state, action) => {
            state.update = false
            state.success = true
            state.data = action.payload;
            state.loader = false

        },
        setSuccessFalse: (state, action) => {
            state.success = false
        },
        setPACKET911SELECTEDCALL: (state, action) => {
            state.selectedCall = action.payload.length > 0 ? action.payload[0] : state.selectedCall
            state.loader = false
        },
        setPACKET911CALLTotalCount: (state, action) => {
            state.packet911calltotalCount = action.payload
        },
        setPACKET911CALLCITY: (state, action) => {
            state.data = action.payload;
            state.selectedCall = action.payload.length > 0 ? action.payload[0] : state.selectedCall
            state.loader = false
        },
        setPACKET911CALLZONE: (state, action) => {
            state.data = action.payload;
            state.selectedCall = action.payload.length > 0 ? action.payload[0] : state.selectedCall
        },
        setPACKET911CALLGROUP: (state, action) => {
            state.group = action.payload;
        },
        setCOUNTYLIST: (state, action) => {
            state.countyList = action.payload;
        },
        setSELECTEDPACKET911CALL: (state, action) => {
            state.callsByNumber = action.payload
        },
        setCITYLIST: (state, action) => {
            state.cityList = action.payload;
            state.loader = false
        },
        setZONELIST: (state, action) => {
            state.zoneList = action.payload;
        },
        setPDZONELIST: (state, action) => {
            state.pdZoneList = action.payload;
        },

        setCURRENTPACKET911CALL: (state, action) => {
            state.selectedCall = action.payload;
        },
        setLOADERON: (state, action) => {
            state.loader = true
        },
        setCurrentData: (state, action) => {
            state.currentData = action.payload;
            state.currentFlag = true
        },
        clearUpdatedData: (state, action) => {
            state.updatedData = [];

        },
        UpdatePACKET911CALL: (state, action) => {
            if (action.payload.CAD911CallID !== undefined) {
                let x = state.data.filter(x => x.CAD911CallID == action.payload.CAD911CallID);
                let y = state.currentData.filter(x => x.CAD911CallID == action.payload.CAD911CallID);
                if (x.length == 0 && y.length == 0) {
                    state.updatedData = state.currentData.length !== 0 ? [action.payload, ...state.currentData] : [action.payload, ...state.data]
                    state.currentData = state.updatedData
                    state.data = state.updatedData
                    state.update = true
                }
            }
            state.loader = false
        },
        setHoverID: (state, action) => {
            state.hoverID = action.payload;

        },
    },
    extraReducers: {}
});

export default call911Slice.reducer;

// Actions
export const {
    setRAPIDSOS,
    setPACKET911CALL,
    setPACKET911SELECTEDCALL,
    setPACKET911CALLCITY,
    setPACKET911CALLZONE,
    setPACKET911CALLGROUP,
    setCOUNTYLIST,
    setCITYLIST,
    setZONELIST,
    setPDZONELIST,
    setCURRENTPACKET911CALL,
    setSELECTEDPACKET911CALL,
    setLOADERON,
    setPACKET911CALLTotalCount,
    setCurrentData,
    UpdatePACKET911CALL,
    setSuccessFalse,
    clearUpdatedData,
    setHoverID,
    setCALLTYPESDATA,
    setFAILCALLTYPESDATA
} = call911Slice.actions;


export const setPacket911SelectedCall = (data) => async dispatch => {
    return dispatch(setPACKET911SELECTEDCALL(JSON.parse(data)));
}

// Added 14-12-2023
export const getCallTypes = () => async dispatch => {
    try {
        await axios.get(`admin/api/callType/getCallTypes`, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${localStorage.getItem('jwt_access_token')}`
            }
        })
            .then((response) => {
                if (response.status == 200) {
                    var callTypes = JSON.parse(decrypt(response.data));
                    if (callTypes.length !== 0) {
                        dispatch(setCALLTYPESDATA(callTypes))
                    }
                    else {
                        // dispatch(setFAILCALLTYPESDATA([]))
                    }
                }
                else {
                    // dispatch(setFAILCALLTYPESDATA([]))
                }
            })
    }
    catch (e) {
        dispatch(setFAILCALLTYPESDATA([]))
        return console.error(e.message);
    }

}

// To get Data From Rapid SoS
export const getRapidSoSData = () => async dispatch => {
    try {
        await axios.get(`911/api/call/getRapidSoSData`)
            .then(response => {
                return dispatch(setRAPIDSOS(JSON.parse(decrypt(response.data))));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

//To set selected call to store
export const setSelectedCallData = (call) => async dispatch => {
    try {
        //dispatch(getPacket911CallListByNumber(call))
        return dispatch(setCURRENTPACKET911CALL(call));
    } catch (e) {
        return console.error(e.message);
    }
}

export const getPacket911CallListByNumber = (call) => async dispatch => {
    try {
        await axios.get(`911/api/call/callListByCallNumber/` + call.PacketCallingPhone)
            .then(response => {
                const result = JSON.parse(decrypt(response.data))
                return dispatch(setSELECTEDPACKET911CALL(result.filter(x => x._id !== call._id)));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

// To get 911 Call List
export const getPacket911CallList = (selectedCountyID, pageIndex, pageLimit, selectedCity, selectedZone, selectedPDZone, value) => async dispatch => {
    try {
        dispatch(setLOADERON());
        // let pageIndex = 0;
        console.log(pageIndex)
        let lastDate = pageIndex != 0 ? localStorage.getItem("infiniteScrollLastDate") : new Date();
        await axios.get(`911/api/call/callList1/${lastDate}?countyID=${selectedCountyID}&pageIndex=${pageIndex}&pageLimit=${pageLimit}&selectedCity=${selectedCity}&selectedZone=${selectedZone}&selectedPDZone=${selectedPDZone}`)
            .then(response => {
                //
                response.data = JSON.parse(decrypt(response.data));
                // if (response.data.length > 0) {
                //     dispatch(getPacket911CallListByNumber(response.data[0]));
                // }
                if (response.data.callList.length > 0) {
                    let lastElement = response.data.callList.reduce((acc, current) => current);
                    localStorage.setItem("infiniteScrollLastDate", lastElement.PacketCallReceivedDT)
                }
                dispatch(setPACKET911CALLTotalCount(response.data.totalCount))
                if (value === 0) {
                    dispatch(setPACKET911SELECTEDCALL(response.data.callList));
                }
                return dispatch(setPACKET911CALL(response.data.callList));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

export const getRefereshPacket911CallList = (selectedCountyID, selectedCity, selectedZone, selectedPDZone, value) => async dispatch => {
    try {
        dispatch(setLOADERON());
        // let pageIndex = 0;
        // let pageLimit = 10
        await axios.get(`911/api/call/refreshcallList?countyID=${selectedCountyID}&selectedCity=${selectedCity}&selectedZone=${selectedZone}&selectedPDZone=${selectedPDZone}`)
            .then(response => {
                response.data = JSON.parse(decrypt(response.data));
                // if (response.data.length > 0) {
                //     dispatch(getPacket911CallListByNumber(response.data[0]));
                // }
                dispatch(setPACKET911CALLTotalCount(response.data.totalCount))
                if (value === 0) {
                    dispatch(setPACKET911SELECTEDCALL(response.data.callList));
                }

                return dispatch(setPACKET911CALL(response.data.callList));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

export const SetCurrrentData = (data) => async dispatch => {
    return dispatch(setCurrentData(data))
}
export const updatePacket911Call = (data) => async dispatch => {
    if (data !== undefined && data !== null) {
        dispatch(UpdatePACKET911CALL(data));

    }
    // if (data.length > 0) {
    //     // data = [{_id: '655308a71d6ca56101a3bced', PacketCallReceivedDT: '2023-11-14T05:42:18.870Z', PacketCallProcessed: false, PacketCallProcessedBy: '', PacketCountyID: 60},
    //     // {_id: '655308a71d6ca56101a3bced', PacketCallReceivedDT: '2023-11-14T05:42:18.870Z', PacketCallProcessed: false, PacketCallProcessedBy: '', PacketCountyID: 60}]
    //     data.map(x => dispatch(UpdatePACKET911CALL(x)))
    //     // dispatch(UpdatePACKET911CALL(data))

    // }
}

//Get PACKET911CALL City List
export const getPacket911CityCallList = (selectedCity, selectedCounty) => async dispatch => {
    try {
        await axios.get(`911/api/call/getCityWiseList?selectedCity=` + selectedCity + `&selectedCounty=` + selectedCounty)
            .then(response => {
                response.data = JSON.parse(decrypt(response.data));
                // if (response.data.length > 0) {
                //     dispatch(getPacket911CallListByNumber(response.data[0]));
                // }
                return dispatch(setPACKET911CALLCITY(response.data));
                // return dispatch(setPACKET911CALLCITY(JSON.parse(decrypt(response.data))));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

//Get PACKET911CALL Zone List
export const getPacket911ZoneCallList = (selectedZone, selectedPDZone, selectedCity) => async dispatch => {
    try {
        await axios.get(`911/api/call/getZoneAndPDZoneWiseList?selectedZone=` + selectedZone + `&selectedPDZone=` + selectedPDZone + `&selectedCity=` + selectedCity)
            .then(response => {
                response.data = JSON.parse(decrypt(response.data));
                // if (response.data.length > 0) {
                //     dispatch(getPacket911CallListByNumber(response.data[0]));
                // }
                return dispatch(setPACKET911CALLZONE(response.data));
                //return dispatch(setPACKET911CALLZONE(JSON.parse(decrypt(response.data))));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

//Get PACKET911CALL Group List
export const getPacket911GroupCallList = (call) => async dispatch => {
    try {
        await axios.post(`911/api/call/groupCallList`, call)
            .then(async response => {
                return dispatch(setPACKET911CALLGROUP(JSON.parse(decrypt(await response.data))));
                // const result = JSON.parse(decrypt(await response.data));             
                // return dispatch(setPACKET911CALLGROUP(result.map((ele)=>ele.filter(x=>x.PacketCallingPhone!==call.PacketCallingPhone))));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

//Get County List
export const getCountyList = () => async dispatch => {
    try {
        await axios.get(`911/api/call/getAllCounties`)
            .then(response => {
                response.data = JSON.parse(decrypt(response.data));
                var counties = [];
                response.data.forEach(function (line, index) {
                    counties.push({ county: line.County_Name, id: line.County_ID });
                });
                counties.sort();
                counties.unshift({ county: 'All County', id: 0 });
                return dispatch(setCOUNTYLIST(counties));
            }
            );
    } catch (e) {
        return console.error(e.message);
    }
}

//Get City List
export const getCityList = (countyID) => async dispatch => {
    try {
        await axios.get(`911/api/call/getAllCities/` + countyID)
            .then(response => {
                response.data = JSON.parse(decrypt(response.data))
                var cities = [];
                response.data.forEach(function (line, index) {
                    cities.push({ city: line.City_Name, id: line.City_ID });
                });
                cities.sort();
                cities.unshift({ city: 'All City', id: 0 });

                return dispatch(setCITYLIST(cities));
            }
            );
        // else {
        //     var cities = [];
        //     cities.unshift({ city: 'All City', id: 0 });
        //     return dispatch(setCITYLIST(cities));
        // }
    } catch (e) {
        return console.error(e.message);
    }
}

//Get Zone List
export const getZoneList = (countyID, city) => async dispatch => {
    try {
        if (parseInt(countyID) === 60 && city === 10) {

            await axios.get(`911/api/call/getZones`)
                .then(response => {
                    response.data = JSON.parse(decrypt(response.data));
                    var zones = [];
                    response.data.forEach(function (line, index) {
                        zones.push({ LABEL: line.PDZone_Name, id: line.PDZone_ID });
                    });
                    zones.sort();
                    zones.unshift({ LABEL: 'All Zone', id: 0 });

                    return dispatch(setZONELIST(zones));
                }
                );
        }
        else {
            var zones = [];
            zones.unshift({ LABEL: 'All Zone', id: 0 });
            return dispatch(setZONELIST(zones));
        }
    } catch (e) {
        return console.error(e.message);
    }
}

//Get PD Zone List
export const getPDZoneList = (countyID, city) => async dispatch => {
    try {
        if (parseInt(countyID) === 60 && city === 10) {

            await axios.get(`911/api/call/getPoliceZones`)
                .then(response => {
                    response.data = JSON.parse(decrypt(response.data));
                    var PDzones = [];
                    response.data.forEach(function (line, index) {
                        PDzones.push({ LABEL: line.JPD_Name, id: line.JPD_ID });
                    });
                    PDzones.sort();
                    PDzones.unshift({ LABEL: 'All PD Zone', id: 0 });

                    return dispatch(setPDZONELIST(PDzones));
                }
                );
        }
        else {
            var PDzones = [];
            PDzones.unshift({ LABEL: 'All PD Zone', id: 0 });
            return dispatch(setPDZONELIST(PDzones));
        }
    } catch (e) {
        return console.error(e.message);
    }
}

export const archiveData = () => async dispatch => {
    try {
        await axios.get(`911/api/call/setProcessData`)
            .then(async res => {
                if (res.data) {
                    dispatch(getPacket911CallList(0))
                    dispatch(showMessage({
                        message: 'Data processed successfully.', autoHideDuration: 1500,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                }
            })
    } catch (error) {
        return console.error(error.message);
    }
}
