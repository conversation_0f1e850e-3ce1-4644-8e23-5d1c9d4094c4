import React, { forwardRef } from "react";
import { Autocomplete, createFilterOptions, TextField } from "@mui/material";
import { makeStyles } from "@mui/styles";

const useStyles = makeStyles((theme) => ({
    paperHeight: {
        maxHeight: 'none',
        overflowY: 'visible',
    },
    listbox: {
        maxHeight: 230,
        overflowY: 'auto',
    },
}));

export const handleSelectKeyDownFreeForm = (event, data, optionLabel, handleChange) => {
    if (event.key !== 'Tab') return;

    const listboxNode = document.querySelector('[role="listbox"]');
    if (!listboxNode) return;

    const activeOption = listboxNode.querySelector('.MuiAutocomplete-option.Mui-focused');
    if (!activeOption) return;

    const selectedText = activeOption.innerText.trim();

    // Try to find matching option from data
    const matchedItem = data.find(option =>
        option[optionLabel]?.toString().trim() === selectedText
    );

    if (matchedItem) {
        handleChange(null, matchedItem);
    } else {
        // Match string inside quotes in 'Add "Some Text"' using RegExp
        const match = selectedText.match(/Add\s+"(.+?)"/);
        const value = match ? match[1] : selectedText;
        handleChange(null, { _id: 'local', [optionLabel]: value });
    }
};


const filter = createFilterOptions();

const FreeFormAutocomplete = forwardRef((props, ref) => {
    const classes = useStyles();

    const handleOnChange = (event, newValue) => {
        if (!newValue) return;

        // Check if newValue exists in options
        const isExisting = props.options?.some(
            (option) => option[props.optionLabel] === newValue[props.optionLabel]
        );

        let cleanValue = newValue;

        // If it's not an existing option but includes `Add "..."`, clean it
        if (!isExisting && typeof newValue[props.optionLabel] === 'string' && newValue[props.optionLabel].startsWith('Add "')) {
            const inputText = newValue[props.optionLabel];
            const extractedValue = inputText.slice(5, -1); // Extract text inside quotes
            cleanValue = { _id: 'local', [props.optionLabel]: extractedValue };
        }

        props.parentCallback(event, cleanValue);
    };


    const handleInputChange = (event, inputValue) => {
        if (!inputValue) return;

        // Convert inputValue to string to ensure compatibility
        const inputStr = inputValue.toString().toLowerCase();

        // Find all matching options
        const matchedOptions = props.options.filter(option => {
            const optionLabel = option[props.optionLabel];

            // Convert to string safely before comparison
            return optionLabel.toString().toLowerCase().startsWith(inputStr);
        });

        // Only autofill if there's exactly one unique match
        if (Array.isArray(matchedOptions) && matchedOptions.length === 1) {
            handleOnChange(null, matchedOptions[0]); // Autofill with the unique match
        }
    };

    return (
        <Autocomplete
            fullWidth={true}
            disabled={props.disabled}
            id="freeFormAutocomplete"
            disablePortal
            onChange={handleOnChange}
            onInputChange={handleInputChange}
            options={props.options ? props.options : []}
            value={props.value ? props.value : null}
            classes={{ paper: classes.paperHeight, listbox: classes.listbox }}
            getOptionLabel={(option) => option[props.optionLabel]}
            filterOptions={(options, params) => {
                const filtered = filter(options, params);
                const { inputValue } = params;
                const isExisting = options.some((option) => inputValue === option[props.optionLabel]);
                if (inputValue !== '' && !isExisting) {
                    filtered.push({
                        inputValue,
                        [props.optionLabel]: `Add "${inputValue}"`,
                    });
                }
                return filtered;
            }}
            onKeyDown={(event) => {
                props.onKeyDown(event, props.options, props.optionLabel, handleOnChange);
            }}
            freesolo
            onOpen={props.onOpen} // Handle dropdown open
            onClose={props.onClose} // Handle dropdown close
            open={props.open}
            renderInput={(params) =>
                <TextField
                    {...params}
                    label={props.fieldName}
                    ref={ref}
                    InputProps={{
                        ...params.InputProps,
                        disableUnderline: true, // Optionally customize the input's underline
                        tabIndex: 0
                    }}
                    required={props.required}
                />
            }
        />
    )
})


export default FreeFormAutocomplete;