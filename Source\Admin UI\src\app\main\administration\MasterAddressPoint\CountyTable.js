import FusePageCarded from '@fuse/core/FusePageCarded';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from "react-router";
import { Icon, Input, InputAdornment, Paper, StyledEngineProvider, TablePagination, ThemeProvider, Typography } from '@mui/material';
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useSelector, useDispatch } from 'react-redux';
import history from '@history';
import { useDebounce } from '@fuse/hooks';
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from '../../utils/utils';
import { getMasterCounty, setLoading, setSelectedCounty, setSelectedCountyStateCode, } from '../store/masterAddressSlice';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import DnsIcon from '@mui/icons-material/Dns';
import ImportFromExcelDialog from '../../SharedComponents/ImportFromExcelDialog/ImportFromExcelDialog';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import {
    saveExcelIntersectionData,
    saveExcelMasterAddressData,
    uploadFile,
    clearFileUploadResponse,
    getMasterAddressAndInterSectionColumn
} from '../../store/importFromExcelSlice';
import LocationCityIcon from '@mui/icons-material/LocationCity';
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import AddLocationAltIcon from '@mui/icons-material/AddLocationAlt';
import SyncAltIcon from '@mui/icons-material/SyncAlt'
import CancelIcon from '@mui/icons-material/Cancel';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function CountyTable(props) {
    const { t } = useTranslation("laguageConfig");
    const mainTheme = useSelector(selectMainTheme);
    const dispatch = useDispatch();
    const routeParams = useParams();
    const gridRef = useRef(null);
    const rowsPerPageOptions = getRowsPerPageOptions();
    let colorCode = getNavbarTheme();
    const importExcelRef = useRef();

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [page, setPage] = useState(0);
    const [importButtonClick, setImportButtonClick] = useState('');
    const [importCounty, setImportCounty] = useState('');
    const [importState, setImportState] = useState('');
    const [finalData, setFinalData] = useState(null);

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "CountyName",
    });

    const [searchText, setSearchText] = React.useState("");
    const user = useSelector(({ auth }) => auth.user);
    const MasterAddress = useSelector(({ administration }) => administration.masterAddressSlice.MasterCounty)
    const TotalCount = useSelector(({ administration }) => administration.masterAddressSlice.countyTotalCount)
    const isloading = useSelector(({ administration }) => administration.masterAddressSlice.isloading);
    const fileUploadResponse = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.fileUploadResponseData);
    const masterAddressColumn = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.masterAddressColumnList);
    const intersectionColumn = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.interSectionColumnList);
    const isLoadingExcel = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.ImportFromExcel.isLoading);

    const [loading, setloading] = useState(false);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);

    useEffect(() => {
        dispatch(getMasterAddressAndInterSectionColumn());
    }, []);


    useEffect(() => {
        setloading(isloading);
    }, [isloading]);

    useEffect(() => {
        setloading(isLoadingExcel);
    }, [isLoadingExcel])

    const viewMasterAddress = async (value) => {
        dispatch(setLoading(true));
        await dispatch(setSelectedCounty(value.CountyName));
        await dispatch(setSelectedCountyStateCode(value.StateCode));
        dispatch(setLoading(false));
        history.push(`/admin/masterAddress`);
    };

    const viewIntersection = async (value) => {
        dispatch(setLoading(true));
        await dispatch(setSelectedCounty(value.CountyName));
        await dispatch(setSelectedCountyStateCode(value.StateCode));
        dispatch(setLoading(false));
        history.push(`/admin/intersectionPoint`);
    };

    function handleChangePage(event, value) {
        setPage(value);
        setloading(true);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
        setloading(true);
    }

    const handleMasterAddressClick = async (value) => {
        setImportButtonClick('MasterAddress');
        setImportCounty(value.CountyName);
        setImportState(value.StateCode);
        importExcelRef.current.handleOpen(`${t("importMasterAddress")} - ${value.CountyName}`, masterAddressColumn.Fields, masterAddressColumn.DefaultFields ? masterAddressColumn.DefaultFields : null);
    }

    const handleIntersectionPointClick = async (value) => {
        setImportButtonClick('IntersectionPoint');
        setImportCounty(value.CountyName);
        setImportState(value.StateCode);
        importExcelRef.current.handleOpen(`${t("importIntersectionPoint")} - ${value.CountyName}`, intersectionColumn.Fields, intersectionColumn.DefaultFields ? intersectionColumn.DefaultFields : null);
    }

    const setChildData = (mappedData, fileDetails, destinationColumns) => {
        if (fileDetails !== null) {
            setFinalData((prev) => ({ ...prev, fileName: fileDetails.name }));
            var formData = new FormData();
            formData.append("file", fileDetails);
            dispatch(uploadFile(formData, importButtonClick, importCounty));
        }

        if (importButtonClick === 'IntersectionPoint') {
            setFinalData((prev) => ({ ...prev, intersectionPoint: mappedData, lastMapedColums: destinationColumns, excelFileData: null, masterAddressColumnId: intersectionColumn._id, user: user.data.email }));
        } else {
            setFinalData((prev) => ({ ...prev, masterAddress: mappedData, lastMapedColums: destinationColumns, excelFileData: null, masterAddressColumnId: masterAddressColumn._id, user: user.data.email }));
            // setFinalData({ masterAddress: mappedData, lastMapedColums: destinationColumns, excelFileData: null, masterAddressColumnId: masterAddressColumn._id });
        }

    }


    useEffect(() => {
        if (fileUploadResponse !== null) {
            setFinalData((prevData) => ({
                ...prevData, // Spread the previous 
                excelFileData: fileUploadResponse, // Update only excelFileData
            }));
        }
    }, [fileUploadResponse]);

    useEffect(() => {
        if (finalData !== null && fileUploadResponse !== null && fileUploadResponse !== undefined) {
            if (importButtonClick === 'IntersectionPoint') {
                dispatch(saveExcelIntersectionData(finalData, importCounty, importState));
            } else {
                dispatch(saveExcelMasterAddressData(finalData, importCounty, importState));
            }

            dispatch(clearFileUploadResponse());
            // setImportButtonClick('');
            // setImportCounty('');
        }
    }, [finalData]);

    const ActionIcons = (n) => {
        // let x = checkData(n);

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div className="flex">
                    <Tooltip title={t("masterAddressList")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            onClick={() => viewMasterAddress(x)}
                            size="large"
                        >
                            <LocationCityIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={t("masterIntersectionList")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            onClick={() => viewIntersection(x)}
                            size="large"
                        >
                            <FullscreenExitIcon />
                        </IconButton>
                    </Tooltip>



                    <Tooltip title={t("importMasterAddress")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            onClick={() => handleMasterAddressClick(x)}
                            size="large"
                        >
                            <AddLocationAltIcon />
                        </IconButton>
                    </Tooltip>

                    <Tooltip title={t("importIntersectionPoint")}>
                        <IconButton
                            aria-label="edit"
                            color="inherit"
                            onClick={() => handleIntersectionPointClick(x)}
                            size="large"
                        >
                            <SyncAltIcon />
                        </IconButton>
                    </Tooltip>
                </div >
            );
        }
    };


    useEffect(() => {
        if (MasterAddress !== null && MasterAddress !== undefined) {
            setData(MasterAddress);
            setTotalCount(TotalCount);
        }
    }, [MasterAddress]);

    const search = useDebounce((search, page, rowsPerPage) => {
        dispatch(getMasterCounty(order.id, order.direction, page * rowsPerPage, rowsPerPage,
            search === '' ? null : search, routeParams.code));
    }, 500);

    useEffect(() => {
        if (searchText !== '') {
            search(searchText, page, rowsPerPage);
        } else {
            dispatch(getMasterCounty(order.id, order.direction, page * rowsPerPage, rowsPerPage,
                searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, searchText, page, rowsPerPage, order, routeParams.code]);

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 0 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    return (
        <>
            {loading && <CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={{ paddingTop: "50px" }}
                        >
                            <div className="flex items-center">
                                <DnsIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("counties")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                // defaultValue={searchText}
                                                value={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText("")}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>


                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="CountyName"
                                        header={t("name")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("stateCode")}
                                        field="StateCode"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("stateName")}
                                        field="StateName"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        width="300px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}

                                    />
                                </IgrGrid>
                            </div>

                        </div>
                    </div>
                }
            />

            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="ImportFromExcelDialog" />} onReset={() => { }} >
                <ImportFromExcelDialog ref={importExcelRef} passChildData={setChildData} />
            </ErrorBoundary>
        </>
    );
}

export default CountyTable;