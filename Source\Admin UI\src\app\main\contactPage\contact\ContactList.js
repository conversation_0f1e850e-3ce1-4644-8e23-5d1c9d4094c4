import FusePageCarded from '@fuse/core/FusePageCarded';
import <PERSON><PERSON>istHeader from './ContactListHeader';
import ContactListCards from './ContactListCards';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { newUserAudit } from '../../../main/userAuditPage/store/userAuditSlice';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import ContactNewListTable from './ContactNewListTable';

function ContactList() {
	const user = useSelector(({ auth }) => auth.user);
	const dispatch = useDispatch();

	useEffect(() => {
		dispatch(newUserAudit({
			activity: "Access Contact",
			user: user,
			appName: "Admin",
		}));
		// eslint-disable-next-line
	}, []);

	return (
		<FusePageCarded
			classes={{
				content: 'flex',
				header: 'min-h-72 h-72 sm:h-136 sm:min-h-136'
			}}
			header={
				<ErrorBoundary
					FallbackComponent={(props) => <ErrorPage {...props} componentName="ContactListHeader" />} onReset={() => { }}>
					<ContactListHeader />
				</ErrorBoundary>
			}
			content={user.data.cardView ?
				<ErrorBoundary
					FallbackComponent={(props) => <ErrorPage {...props} componentName="ContactListCards" />} onReset={() => { }}>
					<ContactListCards />
				</ErrorBoundary>
				:
				<ErrorBoundary
					FallbackComponent={(props) => <ErrorPage {...props} componentName="ContactNewListTable" />} onReset={() => { }}>
					<ContactNewListTable />
				</ErrorBoundary>
			}
			
		/>
	);
}

export default ContactList;
