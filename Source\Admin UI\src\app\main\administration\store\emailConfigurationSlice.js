import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';

export const getEmailConfigurations = () => async dispatch => {
    try {
        await axios.get(`admin/api/emailConfiguration/GetEmailConfiguration`)
            .then(response => {
                if (response.status == 200) {

                    return dispatch(setEmailConfigurations(JSON.parse(decrypt(response.data))))
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
}



export const saveEmailConfiguration = (data) => async dispatch => {
    try {
        await axios.post(`admin/api/emailConfiguration/emailConfigurationAdd`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}




// To update Server Configuration
export const updateEmailConfiguration = (data) => async dispatch => {
    try {
        await axios.post(`admin/api/emailConfiguration/emailConfigurationEdit`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    // history.push("/admin/serverconfiguration");
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    data: [],
    searchText: '',
    emailConfigurationID: "0",
    emailConfigurationData: []
};

const emailConfigurationSlice = createSlice({
    name: 'administration/emailConfigurations',
    initialState,
    reducers: {
        setEmailConfigurations: (state, action) => {
            state.data = action.payload;
        }
    },
    extraReducers: {}
});

export const { setEmailConfigurations,
} = emailConfigurationSlice.actions;

export default emailConfigurationSlice.reducer;
