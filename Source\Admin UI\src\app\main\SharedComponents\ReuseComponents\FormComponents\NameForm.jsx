import Grid from "@mui/material/Grid";
import { useTranslation } from "react-i18next";
import FormTextField from "../../SharedFormFields/FormTextField";

const NameForm = ({ namespace = "name" }) => {
  const { t } = useTranslation("laguageConfig");

  return (
    <Grid container spacing={2}>
      <FormTextField
        name={`${namespace}.FirstName`}
        label={t("firstName")}
        gridProps={{ xs: 12, sm: 12, md: 2 }}
      />

      <FormTextField
        name={`${namespace}.LastName`}
        label={t("lastName")}
        gridProps={{ xs: 12, sm: 12, md: 2 }}
      />
    </Grid>
  );
};

export default NameForm;
