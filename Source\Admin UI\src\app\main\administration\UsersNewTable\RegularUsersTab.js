import React, { useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from "react-redux";
import {
    enableUser,
    hardDeleteUser,
    removeUser,
    setEditUser,
    getRegularUsers,
    setLoading,
    SetPageValue,
    SetRowsPerPageValue,
    setUsersDelete,
    setUsersHardDelete,
    setUsersEnable
} from '../store/usersSlice';
import { Tooltip, IconButton, Switch, Checkbox, Button } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import LockResetIcon from '@mui/icons-material/LockReset';
import LockIcon from '@mui/icons-material/Lock';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import './UserNewTable.css';
import TablePagination from '@mui/material/TablePagination';
import history from '@history';
import { showMessage } from 'app/store/fuse/messageSlice';
import ChangePasswordDlg from '../../Dialog/ChangePasswordDilalog/ChangePasswordDlg';
import ChangeRPSCredentialsDialog from '../../Dialog/ChangeRPSCredentialsDialog/ChangeRPSCredentialsDialog';
import { getAgencyByAgencyID, getNavbarTheme, getRowsPerPageOptions, getUserID, useWindowResizeHeight } from '../../utils/utils';
import ConfirmationDialog from '../../components/ConfirmationDialog/ConfirmationDialog';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import ChangeMugshotCreadentialsDialog from '../../Dialog/ChangeMugshotCredentialsDialog/ChangeMugshotCreadentialsDialog';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { useDebounce } from '@fuse/hooks';
import ChangeAgencyForMultipleUserDialog from '../../Dialog/ChangeAgencyForMultipleUser/ChangeAgencyForMultipleUser';
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import { getAgencyData } from '../../agencyPage/store/agencySlice';

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: '_id',
        align: 'left',
        disablePadding: false,
        label: 'ID',
        sort: true
    },
    {
        id: 'fname',
        align: 'left',
        disablePadding: false,
        label: 'NAME',
        sort: true
    },
    {
        id: 'profilePic',
        align: 'left',
        disablePadding: false,
        label: 'Photo',
        sort: true
    },
    {
        id: 'email',
        align: 'left',
        disablePadding: false,
        label: 'EMAIL',
        sort: true
    },
    {
        id: 'userAgenciesArray',
        align: 'left',
        disablePadding: false,
        label: 'AGENCIES',
        sort: true
    },
    {
        id: 'userAgencies',
        align: 'left',
        disablePadding: false,
        label: 'userAgencies',
        sort: true
    },
    {
        id: 'phone',
        align: 'left',
        disablePadding: false,
        label: 'PHONE',
        sort: true
    },
    {
        id: 'isSuperAdmin',
        align: 'left',
        disablePadding: false,
        label: 'IsSuperAdmin',
        sort: true
    },
    {
        id: 'isActive',
        align: 'left',
        disablePadding: false,
        label: 'IsActive',
        sort: true
    },
    {
        id: 'isEnabled',
        align: 'left',
        disablePadding: false,
        label: 'IsEnabled',
        sort: true
    },
    {
        id: 'remove',
        align: 'left',
        disablePadding: false,
        label: 'Remove',
        sort: true
    },
    {
        id: 'mugshotusername',
        align: 'left',
        disablePadding: false,
        label: 'mugshotusername',
        sort: true
    },
    {
        id: 'mugshotpassword',
        align: 'left',
        disablePadding: false,
        label: 'mugshotpassword',
        sort: true
    },
    {
        id: 'lastLoginDateTime',
        align: 'left',
        disablePadding: false,
        label: 'lastLoginDateTime',
        sort: true
    },
    {
        id: 'source',
        align: 'left',
        disablePadding: false,
        label: 'source',
        sort: true
    },
    {
        id: 'action',
        align: 'left',
        disablePadding: false,
        label: 'ACTIONLBL',
        sort: true
    }
];

function RegularUsersTab(props) {
    const searchText = props.searchText;
    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const ActionIconColor = navbarTheme.palette.primary.light;
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    const RowsSelectedBackgroundColor = navbarTheme.palette.secondary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const RegularUsers = useSelector(({ administration }) => administration.user.RegularUsers);
    const isloading = useSelector(({ administration }) => administration.user.isloading);
    const selectedAgency = useSelector(({ agency }) => agency.agency.filterAgencyValue);
    const userTotalCount = useSelector(({ administration }) => administration.user.totalCount);
    const user = useSelector(({ auth }) => auth.user);
    const userDelete = useSelector(({ administration }) => administration.user.userDelete);
    const userHardDelete = useSelector(({ administration }) => administration.user.userHardDelete);
    const userEnable = useSelector(({ administration }) => administration.user.userEnable);
    //const searchText = useSelector(({ administration }) => administration.user.searchText);
    const selectedUser = useSelector(({ administration }) => administration.user.selectedUser);
    const agencyList = useSelector(({ agency }) => agency.agency.data);
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();
    const [value, setValue] = React.useState(0);
    const gridRef = useRef(null);
    const gridContainerRef = useRef(null);
    const noteRef = useRef();
    const rpsAuthRef = useRef(null);
    const mugshotAuthRef = useRef(null);
    const changeMultipleAgenciesRef = useRef(null);
    const [order, setOrder] = useState({
        direction: "asc",
        id: "fname",
    });
    const [open, setOpen] = useState(false);
    const [data, setData] = useState(RegularUsers);
    const [totalCount, setTotalCount] = useState(userTotalCount);
    const [valueEnableUser, setValueEnableUser] = useState(false);
    const [openEnableUserDialog, setEnableUserDialog] = useState(false);
    const [openHardDeleteDialog, setHardDeleteDialog] = useState(false);
    const [valueHardDelete, setHardDeleteValue] = useState(false);
    const [selectedId, setSelectedId] = React.useState([]);
    const rowsPerPage = useSelector(({ administration }) => administration.user.rowsPerPage);
    const page = useSelector(({ administration }) => administration.user.page);
    let colorCode = getNavbarTheme();
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    const [gridWidth, setGridWidth] = useState(1200);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 375);
    function handleChangePage(event, value) {
        dispatch(SetPageValue(value))
    }

    function handleChangeRowsPerPage(event) {
        dispatch(SetRowsPerPageValue(event.target.value))
        dispatch(SetPageValue(0));
    }

    const search = useDebounce((search, page, rowsPerPage) => {
        if (selectedAgency !== "") {
            dispatch(getRegularUsers(order.id, order.direction, page * rowsPerPage, rowsPerPage, selectedAgency, search === '' ? null : search));
        }
    }, 200);

    useEffect(() => {
        if (selectedAgency !== '') {
            if (searchText !== '') {
                search(searchText, page, rowsPerPage);
            } else {
                dispatch(getRegularUsers(order.id, order.direction, page * rowsPerPage, rowsPerPage, selectedAgency, searchText === '' ? null : searchText));
            }
        }
    }, [dispatch, rowsPerPage, page, selectedAgency, searchText]);

    useEffect(() => {
        if (userDelete || userHardDelete || userEnable) {
            if (searchText !== '') {
                search(searchText, page, rowsPerPage);
            } else {
                dispatch(getRegularUsers(order.id, order.direction, page * rowsPerPage, rowsPerPage, selectedAgency, searchText === '' ? null : searchText));
            }
            dispatch(setUsersDelete(false));
            dispatch(setUsersHardDelete(false));
            dispatch(setUsersEnable(false));
        }
    }, [dispatch, userDelete, userHardDelete, userEnable, rowsPerPage, page, searchText]);

    let PagingDetails = {
        pageIndex: page * rowsPerPage,
        pageLimit: rowsPerPage,
        selectedAgency: selectedAgency,
        searchText: searchText === '' ? null : searchText,
    };

    const handleClickEdit = async (user) => {
        dispatch(setLoading(true))
        const selectedUserData = await getUserID(user._id)
        if (selectedUserData !== null) {
            dispatch(setEditUser(selectedUserData));
            dispatch(setLoading(false))
        }
        else {
            ShowErroMessage(t("userNotFound"))
        }
    };

    useEffect(() => {
        if (selectedUser) {
            history.push("/admin/create_New_User");
        }
    }, [selectedUser]);

    useEffect(() => {
        if (RegularUsers !== undefined && RegularUsers !== null) {
            setSelectedId([]);
            setData(RegularUsers);
            setTotalCount(userTotalCount);
        }
    }, [RegularUsers, userTotalCount]);

    useEffect(() => {
        //For clearing the selectedId array for multiple error resolve
        setSelectedId([]);
    }, []);

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeUser(value));
        }
    }

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const handleClickDelete = async (userID, n) => {
        debugger;
        await dispatch(getAgencyData());
        if (n.isActive) {
            setValue(userID);
            setOpen(true);
        }
        else {
            const UserLicensesCount = agencyList.find(y => y.code === n.defaultAgency)?.noOfUserLicenses || 0;
            const countuser = data.filter(y => y.defaultAgency === n.defaultAgency && y.isActive === true)
            if (countuser.length >= parseInt(UserLicensesCount) && n.isSuperAdmin !== true) {
                ShowErroMessage(t('userLimitReachedMsg'));
            }
            else {
                setValueEnableUser(n)
                setEnableUserDialog(true);
            }
        }
    };

    const handleClickEnableUserClose = (n) => {
        setEnableUserDialog(false);
        if (n) {
            dispatch(enableUser(valueEnableUser._id,));
        }
    }

    const handleClickHardDeleteClose = (n) => {
        setHardDeleteDialog(false);
        if (n) {
            dispatch(hardDeleteUser(valueHardDelete.email, valueHardDelete._id));
        }
    }

    const handleClickHardDelete = (n) => {
        setHardDeleteValue(n)
        setHardDeleteDialog(true);
    }

    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {(x.isActive) &&
                        <Tooltip title={t("edit")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => handleClickEdit(x)}

                                size="large">
                                <EditIcon />
                            </IconButton>
                        </Tooltip>
                    }

                    {(user.data.email !== (x.email)) &&
                        <Tooltip title={t("resetPassword")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => noteRef.current.handleClickOpen(x)}
                                size="large">
                                <LockResetIcon />
                            </IconButton>
                        </Tooltip>
                    }

                    {(user.data.email !== (x.email)) &&
                        <Tooltip title={t("rpsCredentials")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => rpsAuthRef.current.handleClickOpen(x, false)}
                                size="large">
                                <ManageAccountsIcon />
                            </IconButton>
                        </Tooltip>
                    }

                    {(user.data.email !== (x.email)) &&
                        <Tooltip title={t("mugshotCredentials")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => mugshotAuthRef.current.handleClickOpen(x, false)}
                                size="large">
                                <LockIcon />
                            </IconButton>
                        </Tooltip>
                    }
                    {
                        (user.data.isSuperAdmin) && (x.isActive) &&
                        // <Tooltip title="Change Agency">
                        <Checkbox
                            color="primary"
                            id={x._id}
                            checked={checkValue(x)}
                            onClick={handleCheckBoxChange}
                            inputProps={{ 'aria-label': 'controlled' }}
                        ></Checkbox>
                        // </Tooltip>
                    }
                </>
            );
        }
    };

    function checkValue(item) {
        if (selectedId.length > 0)
            return selectedId.map(x => x).includes(item._id)
        else
            return selectedId.includes(item._id);
    };

    const handleCheckBoxChange = (event) => {
        const id = event.target.id;
        // Create a copy of the selected id array
        const updatedSelectedTags = [...selectedId];

        if (event.target.checked) {
            // If the checkbox is checked, add the id to the array
            updatedSelectedTags.push(id);
        } else {
            // If the checkbox is unchecked, remove the id from the array
            const index = updatedSelectedTags.indexOf(id);
            if (index !== -1) {
                updatedSelectedTags.splice(index, 1);
            }
        }
        // Update the selected id state
        setSelectedId(updatedSelectedTags);
    };

    const editTemplate = (n) => {
        // let x = checkData(n)
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        (user.data.isSuperAdmin) &&
                        <>
                            <Tooltip title={x.isActive ? t("disableUser") : t("enableUser")}>
                                <Switch checked={x.isActive}
                                    onClick={() => handleClickDelete(x._id, x)}
                                    color="info" />
                            </Tooltip>
                        </>
                    }
                    {(!x.isActive) && user.data.isSuperAdmin &&
                        <Tooltip title={t("delete")}>
                            <IconButton
                                aria-label="Delete"
                                color="info"
                                onClick={() => handleClickHardDelete(x)}
                                size="large">
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>
                    }
                    {
                        (x.isActive) && user.data.agencyAdmin &&
                        <Tooltip title={t("delete")}>
                            <IconButton
                                aria-label="Delete"
                                color="info"
                                onClick={() => handleClickDelete(x._id, x)}
                                size="large">
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>
                    }
                </>
            );
        }
    };

    const rowData = data !== undefined && data !== null ? data.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["key"] = item._id;
            row["userAgenciesArray"] = item.userAgencies.length > 0 ? item.userAgencies[0].label : "";
            row["fname"] = item.fname + ' ' + item.lname;
            if (item.profilePic == undefined || item.profilePic == null || item.profilePic == "") {
                row["profilePic"] = require('../../../asset/img/No-Image.png');
            }
            else {
                row["profilePic"] = item.profilePic
            }
            row["action"] = ActionIcons(item)
            row["isEnabled"] = editTemplate(item)
            row["isActive"] = item.isActive;
            row["defaultAgency"] = item.defaultAgency;
            row["agencyAdmin"] = item.agencyAdmin;
            row["lastLoginDateTime"] = item.lastLoginDateTime !== undefined ? new Date(item.lastLoginDateTime).toLocaleString() : "";
            row["source"] = item.source;
            let filterAgecny = item.userAgencies.filter(x => x.agencyCode === item.defaultAgency);
            row["agencyID"] = filterAgecny.length > 0 ? filterAgecny[0].value : "0";
        });
        return row;
    }) : {};

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;
        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            // Update the order state
            setOrder({ id: fieldName, direction: direction });
            // Call the sort handler with the field name and direction
            dispatch(getRegularUsers(fieldName, direction, page * rowsPerPage, rowsPerPage, selectedAgency, searchText === '' ? null : searchText))
        } else {
            console.log("No sorting applied.");
        }
    };

    //method for grouping row
    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    const rowsPerPageOptions = getRowsPerPageOptions();

    useEffect(() => {
        const updateGridWidth = () => {
            if (gridContainerRef.current) {
                const containerWidth = gridContainerRef.current.offsetWidth;
                setGridWidth(containerWidth);
            }
        };

        // Initial calculation
        updateGridWidth();

        // Add event listener for window resizing
        window.addEventListener('resize', updateGridWidth);
        return () => window.removeEventListener('resize', updateGridWidth);
    }, []);

    //method for rendering image in grid
    const renderImage = (ctx) => {
        const item = ctx.dataContext;
        return (
            <>
                {(item.implicit.includes("static/media/No-Image.33e7df0963941edbeb70.png")) ?
                    <AccountCircleIcon /> :
                    <img
                        src={item.implicit}
                        alt={item.name}
                        style={{ height: '30px', width: "30px", objectFit: 'contain' }}
                    />
                }
            </>

        );
    };


    return (
        <div className="w-full flex flex-col">
            {isloading && <CircularProgressLoader loading={isloading} />}
            <div className="sticky flex justify-end mt-4" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                <TablePagination
                    className="tablePaging"
                    component="div"
                    count={totalCount}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    backIconButtonProps={{
                        'aria-label': 'Previous Page'
                    }}
                    nextIconButtonProps={{
                        'aria-label': 'Next Page'
                    }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    rowsPerPageOptions={rowsPerPageOptions}
                ></TablePagination>
                {selectedId.length > 0 &&
                    <Button className="ml-4 mt-4" color="primary" autoFocus variant="contained"
                        onClick={() => changeMultipleAgenciesRef.current.handleClickOpen(selectedId, PagingDetails)}
                    >
                        {t('changeAgency')}
                    </Button>
                }
            </div>
            <div className="flex-grow overflow-y-auto">
                <div className="igrGridClass">

                    <div>
                        <IgrGrid
                            id="grid"
                            autoGenerate="false"
                            data={rowData}
                            primaryKey="_id"
                            ref={gridRef}
                            height={`${gridHeight}px`}
                            rowHeight={60}
                            groupRowTemplate={groupByRowTemplate}
                            allowFiltering={false}
                            allowAdvancedFiltering={true}
                            filterMode="ExcelStyleFilter"
                            moving={true}
                            allowPinning={true}
                            pinning={pinningConfig}
                        >
                            <IgrGridToolbar>
                                <IgrGridToolbarActions>
                                    <IgrGridToolbarAdvancedFiltering />
                                    <IgrGridToolbarHiding />
                                    <IgrGridToolbarPinning />
                                </IgrGridToolbarActions>
                            </IgrGridToolbar>
                            <IgrColumn
                                field="profilePic"
                                header={t("photo")}
                                resizable={true}
                                width="80px"
                                horizontalAlignment="stretch"
                                bodyTemplate={renderImage}
                            />
                            <IgrColumn
                                key="Name"
                                field="fname"
                                header={t("name")}
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="email"
                                header={t("email")}
                                field="email"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="agencies"
                                header={t("agencies")}
                                field="userAgenciesArray"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="phone"
                                header={t("phone")}
                                field="phone"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="lastLoginDate"
                                header={t("lastLoginDate")}
                                field="lastLoginDateTime"
                                width="270px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="source"
                                header={t("source")}
                                field="source"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="isEnabled"
                                header={t("isEnabled")}
                                field="isActive"
                                width="130px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                                pinned={true}
                                bodyTemplate={editTemplate}
                            />
                            <IgrColumn
                                key="action"
                                field="action"
                                width="250px"
                                header={t("action")}
                                resizable={true}
                                pinned={true}
                                bodyTemplate={ActionIcons}
                            />
                        </IgrGrid>
                    </div>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                        <ConfirmationDialog
                            id="ringtone-menu"
                            keepMounted
                            open={open}
                            text={t("disableUserMsg")}
                            onClose={handleClose}
                            value={value}
                        >
                        </ConfirmationDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                        <ConfirmationDialog
                            id="ringtone-menu"
                            keepMounted
                            open={openHardDeleteDialog}
                            text={t("deleteUserMsg")}
                            onClose={handleClickHardDeleteClose}
                            value={valueHardDelete}
                        >
                        </ConfirmationDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                        <ConfirmationDialog
                            id="ringtone-menu"
                            keepMounted
                            open={openEnableUserDialog}
                            text={t("enableUserMsg")}
                            onClose={handleClickEnableUserClose}
                            value={valueEnableUser}
                        >
                        </ConfirmationDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangePasswordDlg" />} onReset={() => { }} >
                        <ChangePasswordDlg ref={noteRef} ></ChangePasswordDlg>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangeRPSCredentialsDialog" />} onReset={() => { }} >
                        <ChangeRPSCredentialsDialog ref={rpsAuthRef} ></ChangeRPSCredentialsDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangeMugshotCreadentialsDialog" />} onReset={() => { }} >
                        <ChangeMugshotCreadentialsDialog ref={mugshotAuthRef} ></ChangeMugshotCreadentialsDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangeAgencyForMultipleUserDialog" />} onReset={() => { }} >
                        <ChangeAgencyForMultipleUserDialog ref={changeMultipleAgenciesRef} ></ChangeAgencyForMultipleUserDialog>
                    </ErrorBoundary>

                </div>
            </div>
        </div>
    )
}

export default RegularUsersTab;