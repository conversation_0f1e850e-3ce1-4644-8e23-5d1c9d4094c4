import Typography from "@mui/material/Typography";
import Icon from "@mui/material/Icon";
import React, { useState, useEffect, useRef } from "react";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import {
    ThemeProvider, StyledEngineProvider, Paper, Input, TablePagination, Button, Tooltip, Stack,
    InputAdornment
} from "@mui/material";
import { selectMainTheme, selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { useTranslation } from "react-i18next";
import _ from "@lodash";
import history from "@history";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import CancelIcon from '@mui/icons-material/Cancel';
import CircularProgressLoader from "src/app/main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { ErrorBoundary } from "react-error-boundary";
import ConfirmationDialog from "src/app/main/components/ConfirmationDialog/ConfirmationDialog";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import { getNavbarTheme, getRowsPerPageOptions } from "src/app/main/utils/utils";
import CommonButton from "src/app/main/SharedComponents/ReuseComponents/CommonButton";
import { useDebounce } from '@fuse/hooks';
import {
    getIntersectionPointData, removeIntersectionPointData, getIntersectionPointDetailById, setIntersectionPointDetailById,
} from "../../store/intersectionPointSlice";
import SelectAllIcon from '@mui/icons-material/SelectAll';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import { getAgencyByCode } from "src/app/main/agencyPage/store/agencySlice";
import { setSelectedCounty, setSelectedCountyStateCode } from "../../store/masterAddressSlice";
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";
import { useWindowResizeHeight } from "../../../utils/utils";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const IntersectionList = () => {
    const gridRef = useRef(null);
    const dispatch = useDispatch();
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");

    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const ActionIconColor = navbarTheme.palette.primary.light;
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    const RowsSelectedBackgroundColor = navbarTheme.palette.secondary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const user = useSelector(({ auth }) => auth.user);
    const intersectionPointData = useSelector(({ administration }) => administration.intersectionPointSlice.intersectionPointData);
    const isLoading = useSelector(({ administration }) => administration.intersectionPointSlice.isLoading);
    const intersectionTotalCount = useSelector(({ administration }) => administration.intersectionPointSlice.totalCount);
    const intersectionSuccess = useSelector(({ administration }) => administration.intersectionPointSlice.intersectionSuccess);
    const selectedCounty = useSelector(({ administration }) => administration.masterAddressSlice.selectedCounty)
    const selectedCountyStateCode = useSelector(({ administration }) => administration.masterAddressSlice.selectedCountyStateCode)
    const agencyByCode = useSelector(({ agency }) => agency.agency.agencyByCode);
    let colorCode = getNavbarTheme();

    const [removeData, setRemoveData] = React.useState(null);
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState([]);
    const [totalCount, setTotalCount] = useState(intersectionTotalCount);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [pageIndex, setPageIndex] = useState(0);
    const [searchText, setSearchText] = useState('');
    const [county, setCounty] = React.useState(null);
    const [countyState, setCountyState] = React.useState(null);
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);

    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "Country",
    });

    useEffect(() => {
        if (selectedCounty !== null && selectedCountyStateCode !== null) {
            setCounty(selectedCounty);
            setCountyState(selectedCountyStateCode);
            // dispatch(getInterSectionPointDetailsLogs(selectedCounty, selectedCountyStateCode));
        }
    }, [selectedCounty]);

    const getCounty = async () => {
        await dispatch(getAgencyByCode(user.data.defaultAgency));
    }

    useEffect(() => {
        if (user.data.agencyAdmin) {
            getCounty();
        }
    }, [user]);

    useEffect(() => {
        if (agencyByCode !== undefined && agencyByCode !== null &&
            agencyByCode.county !== undefined && agencyByCode.county !== null &&
            agencyByCode.county !== "") {
            setCounty(agencyByCode.county);
        }
    }, [agencyByCode]);

    const ActionIcons = (n) => {
        // let x = checkData(n);
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <div style={{ display: "flex" }}>
                    <Tooltip title={t("edit")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => handleEditClick(x)}
                            size="large"
                        >
                            <EditIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={t("delete")}>
                        <IconButton
                            variant="contained"
                            className="normal-case m-4"
                            aria-label="Back"
                            color="inherit"
                            onClick={() => handleDeleteClick(x)}
                            size="large"
                        >
                            <DeleteIcon />
                        </IconButton>
                    </Tooltip>
                </div>
            )
        }

        return null;

    };

    const search = useDebounce((search, page, rowsPerPage, county, order, countyState) => {
        dispatch(getIntersectionPointData(order.id, order.direction, page * rowsPerPage,
            rowsPerPage, search, county, countyState));
    }, 500);

    useEffect(() => {
        if (county !== null && countyState !== null) {
            if (searchText !== '') {
                search(searchText, pageIndex, rowsPerPage, county, order, countyState);
            } else {
                dispatch(getIntersectionPointData(order.id, order.direction, pageIndex * rowsPerPage,
                    rowsPerPage, searchText === '' ? null : searchText, county, countyState));
            }
        }
    }, [order, pageIndex, rowsPerPage, searchText, county, removeData, countyState, intersectionSuccess]);

    useEffect(() => {
        if (intersectionPointData !== null && intersectionPointData !== undefined) {
            setData(intersectionPointData);
            setTotalCount(intersectionTotalCount);
        }
    }, [intersectionPointData, intersectionTotalCount]);

    const handleChangePage = (event, value) => {
        setPageIndex(value);
    }

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(event.target.value);
        setPageIndex(0);
    }

    const handleEditClick = (n) => {
        dispatch(setIntersectionPointDetailById(n));
        history.push(`/admin/intersectionPointAdd`);
    }

    const navigateToCounty = () => {
        history.push(`/admin/counties`);
        dispatch(setSelectedCounty(null));
        dispatch(setSelectedCountyStateCode(null));
    };

    const createNewIntersectionPoint = () => {
        dispatch(setIntersectionPointDetailById(null));
        history.push('/admin/intersectionPointAdd');
    }

    const viewIntersectionLogs = () => {
        history.push('/admin/intersectionImportLogs');
    }

    const handleDeleteClick = (intersectionPoint) => {
        let data = {
            id: intersectionPoint._id,
            county: intersectionPoint.County,
            stateCode: intersectionPoint.State,
        }
        setRemoveData(data);
        setOpen(true);
    }

    const handleConfimationDialogClick = async (newValue) => {
        if (newValue) {
            await dispatch(removeIntersectionPointData(removeData));
            setRemoveData(null);
        }
        setOpen(false);
    }

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            const direction = sortDesc.sortDirection === 0 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            // Call the sort handler with the field name and direction
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    return (
        <>
            {isLoading && <CircularProgressLoader loading={isLoading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {user.data.isSuperAdmin && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title="Back to agency" style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => navigateToCounty()}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={user.data.isSuperAdmin ? {} : { paddingTop: "29px" }}
                        >
                            <div className="flex items-center">
                                <SelectAllIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {county}, {countyState} - {t("viewIntersection")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                // defaultValue={searchText}
                                                value={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                                endAdornment={
                                                    <InputAdornment position='end'>
                                                        <IconButton
                                                            onClick={e => setSearchText("")}
                                                        >
                                                            <CancelIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div className="flex items-center justify-between">
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addIntersection" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("viewImportLogs")} parentCallback={viewIntersectionLogs}></CommonButton>
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addIntersection")} parentCallback={createNewIntersectionPoint}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="igrGridClass">
                            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                                <TablePagination
                                    className="tablePaging"
                                    component="div"
                                    count={totalCount}
                                    rowsPerPage={rowsPerPage}
                                    page={pageIndex}
                                    backIconButtonProps={{
                                        "aria-label": "Previous Page",
                                    }}
                                    nextIconButtonProps={{
                                        "aria-label": "Next Page",
                                    }}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    rowsPerPageOptions={rowsPerPageOptions}
                                />
                            </div>


                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={data}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}
                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        field="County"
                                        header={t("county")}
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("street1")}
                                        field="Str1"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("street2")}
                                        field="Str2"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("street3")}
                                        field="Str3"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("street4")}
                                        field="Str4"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("cityPostalComm")}
                                        field="City"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("state")}
                                        field="State"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("zipPostCode")}
                                        field="Zip5"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("latitude")}
                                        field="LAT_Y"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("longitude")}
                                        field="LON_X"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("msagCommunity")}
                                        field="Community"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("municipality")}
                                        field="Municipality"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("policeZone")}
                                        field="PoliceZone"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("fireZone")}
                                        field="FireZone"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        header={t("streetNames")}
                                        field="StreetNames"
                                        width="250px"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        field="action"
                                        header={t("action")}
                                        width="250px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>
                        </div>

                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                            <ConfirmationDialog
                                id="ringtone-menu"
                                keepMounted
                                open={open}
                                text={t("deleteRecord")}
                                onClose={handleConfimationDialogClick}
                                value={removeData}
                            >
                            </ConfirmationDialog>
                        </ErrorBoundary>
                    </div>
                }
            />
        </>
    )
}

export default IntersectionList;