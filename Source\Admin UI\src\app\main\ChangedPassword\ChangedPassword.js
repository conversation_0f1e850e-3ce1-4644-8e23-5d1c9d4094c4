import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import React from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { styled, darken } from '@mui/material/styles';
import { motion } from 'framer-motion';
import ChangePassword from '../SharedComponents/ChangePassword/ChangePassword';
import Paper from '@mui/material/Paper';
import { useTranslation } from "react-i18next";
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../SharedComponents/ErrorPage/ErrorPage';

const Root = styled('div')(({ theme }) => ({
    background: `linear-gradient(to right, ${theme.palette.primary.dark} 0%, ${darken(
        theme.palette.primary.dark,
        0.5
    )} 100%)`,
    color: theme.palette.primary.contrastText,

    '& .Login-leftSection': {},

    '& .Login-rightSection': {
        background: `linear-gradient(to right, ${theme.palette.primary.dark} 0%, ${darken(
            theme.palette.primary.dark,
            0.5
        )} 100%)`,
        color: theme.palette.primary.contrastText,
    },
}));

function ChangedPassword(props) {
    const { t } = useTranslation("laguageConfig");
    const { flag } = props;
    const login = useSelector(({ auth }) => auth.login);
    const user = useSelector(({ auth }) => auth.user);

    const handledata = () => { }
    return (
        <div className="flex flex-col flex-auto items-center sm:justify-center min-w-0">
            <Paper className="w-full sm:w-auto min-h-full sm:min-h-auto rounded-0 py-32 px-16 sm:p-48 sm:rounded-2xl sm:shadow">
                <div className="w-full max-w-320 sm:w-320 mx-auto sm:mx-0">

                    <div className="flex items-center mb-32">
                        <img className="logo-icon w-48" src="assets/images/logo/fuse.svg" alt="logo" />
                        <div className="border-l-1 mr-4 w-1 h-40" />
                        <div>
                            <Typography className="text-24 font-800 logo-text pl-8" color="inherit">
                                {t("changePassword")}
                            </Typography>
                        </div>
                    </div>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangePassword" />} onReset={() => { }}>
                        <ChangePassword data={user.data} isDialog={false} newFunc={handledata} />
                    </ErrorBoundary>

                </div>
            </Paper>
        </div>

    );
}

export default ChangedPassword;
