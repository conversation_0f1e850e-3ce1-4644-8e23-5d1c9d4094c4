import React, { useRef } from "react";
import { makeStyles } from "@mui/styles";
import { withStyles } from "@mui/styles";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import MuiDialogTitle from "@mui/material/DialogTitle";
import MuiDialogContent from "@mui/material/DialogContent";
import MuiDialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "react-i18next";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import { Icon } from "@mui/material";
// import Formsy from 'formsy-react';
import InputAdornment from "@mui/material/InputAdornment";
// import { TextFieldFormsy } from '@fuse/core/formsy';
import AutoSearch from "../../../theme-layouts/shared-components/SearchBox/AutoSearch";
import { Controller, useForm } from "react-hook-form";
import { TextField } from "@mui/material";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

const useStyles = makeStyles((theme) => ({
  root: {
    "& > *": {
      // margin: theme.spacing(1),
      width: "95%",
    },
  },
}));

const styles = (theme) => ({
  root: {
    margin: 0,
    // padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle disableTypography className={classes.root} {...other}>
      <Typography variant="h6">{children}</Typography>
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles((theme) => ({
  root: {
    // padding: theme.spacing(2),
    width: 500,
    height: 500,
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);

export default function HangUpAccidentalCallComponent(props) {
  const { t } = useTranslation("laguageConfig");
  const [open, setOpen] = React.useState(false);
  const formRef = useRef(null);
  const [CallerNumber, setCallerNumber] = React.useState("");
  const [CallerName, setCallerName] = React.useState("");
  const [Location, setLocation] = React.useState("");
  const [LocationInfo, setLocationInfo] = React.useState("");

  const {
    control,
    setValue,
    formState,
    handleSubmit,
    reset,
    trigger,
    setError,
  } = useForm({
    mode: "onChange",
    // defaultValues,
    // resolver: yupResolver(schema),
  });

  setValue("CallerName", CallerName);
  setValue("CallerNumber", CallerNumber);
  setValue("CallerLocation", Location);
  setValue("LocationInfo", LocationInfo);

  const handleClickOpen = () => {
    setOpen(true);
    setCallerNumber(props.value.PacketCallingPhone.trim());
    setCallerName("");
    setLocation("");
    setLocationInfo("");

    if (
      props.value.PacketClassofService === "VOIP" ||
      props.value.PacketClassofService === "RESD" ||
      props.value.PacketClassofService === "BUSN"
    ) {
      setCallerName(props.value.PacketCustomerName.trim());
      setLocation(
        props.value.PacketStreetNumber.trim() +
        ", " +
        props.value.PacketStreetAddress.trim() +
        ", " +
        props.value.PacketCity.trim() +
        ", " +
        props.value.PacketState.trim() +
        ", " +
        props.value.PacketLocationInfo.trim()
      );
    }

    if (props.value.PacketClassofService === "WPH2") {
      setLocation(
        "Lat:" +
        props.value.Packety.trim() +
        ", Long:" +
        props.value.Packetx.trim()
      );
    }
  };
  const handleClose = () => {
    setOpen(false);
  };

  const callerNameChanged = () => {
    setCallerName("");
    document.getElementById("callerNameText").focus();
  };
  const callerNumberChanged = () => {
    setCallerNumber("");
    document.getElementById("callerNumberText").focus();
  };
  const callerLocationText = () => {
    setLocation("");
    document.getElementById("callerLocationText").focus();
  };
  const copyLocationText = () => {
    setLocationInfo(Location);
  };

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Button
        variant="contained"
        color="secondary"
        size="small"
        onClick={handleClickOpen}
      >
        {t("nonAccidentalCallNeedOfficer")}
      </Button>
      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
      >
        <DialogTitle id="customized-dialog-title" onClose={handleClose}>
          {t("callForService")}
        </DialogTitle>
        <DialogContent dividers style={{ width: "100%" }}>
          <Card variant="outlined">
            <CardContent>
              <form
                // onValidSubmit={handleSubmit}
                // onValid={enableButton}
                // onInvalid={disableButton}
                ref={formRef}
              // className="flex justify-center w-full"
              >
                <Grid container spacing={1} className="mb-4 mt-4">
                  {(props.value.PacketClassofService === "VOIP" ||
                    props.value.PacketClassofService === "RESD" ||
                    props.value.PacketClassofService === "BUSN") && (
                      <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Controller
                          name="CallerName"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              className="w-full"
                              type="text"
                              id="callerNameText"
                              label={t("callerName")}
                              variant="outlined"
                              InputProps={{
                                className: "pr-2",
                                type: "text",
                                endAdornment: (
                                  <InputAdornment position="end">
                                    <IconButton
                                      onClick={() => callerNameChanged()}
                                    >
                                      <Icon className="text-20" color="action">
                                        highlight_off
                                      </Icon>
                                    </IconButton>
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Grid>
                    )}
                  <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                    <Controller
                      name="CallerNumber"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          className="w-full"
                          type="text"
                          id="callerNumberText"
                          label={t("callerNumber")}
                          variant="outlined"
                          InputProps={{
                            className: "pr-2",
                            type: "text",
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={() => callerNumberChanged()}
                                >
                                  <Icon className="text-20" color="action">
                                    highlight_off
                                  </Icon>
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
                <Grid container spacing={1} className="mb-4 mt-4">
                  <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                    <Controller
                      name="CallerLocation"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          className="w-full"
                          type="text"
                          id="callerLocationText"
                          label={t("callerLocation")}
                          variant="outlined"
                          InputProps={{
                            className: "pr-2",
                            type: "text",
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={() => callerLocationText()}
                                >
                                  <Icon className="text-20" color="action">
                                    highlight_off
                                  </Icon>
                                </IconButton>
                                <IconButton onClick={() => copyLocationText()}>
                                  <Icon className="text-20" color="action">
                                    arrow_circle_down
                                  </Icon>
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                    <Controller
                      name="LocationInfo"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          className="w-full"
                          type="text"
                          label={t("locationInfo")}
                          variant="outlined"
                        />
                      )}
                    />
                  </Grid>
                </Grid>
                <Grid container spacing={1} className="mb-4 mt-4">
                  <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                    <AutoSearch lable={t("callType")} type="callType" />
                  </Grid>
                </Grid>
              </form>
            </CardContent>
          </Card>
        </DialogContent>
        <DialogActions>
          <Button autoFocus onClick={handleClose} color="primary">
            Back
          </Button>
          <Button autoFocus onClick={handleClose} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
