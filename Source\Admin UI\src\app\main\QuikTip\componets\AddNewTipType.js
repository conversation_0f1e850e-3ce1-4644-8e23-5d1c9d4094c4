import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Grid from '@mui/material/Grid';
import DepartmentDropdownSetting from '../../SharedComponents/DepartmentDropdownSettings/DepartmentDropdownSetting'
import { isEmptyOrNull } from '../../utils/utils';
import { showMessage } from 'app/store/fuse/messageSlice';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { AddUpdateTipType } from '../../store/quikTipSlice';


const defaultValues = { name: '', description: '' };

const schema = yup.object().shape({});


const AddNewTipType = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [code, setCode] = React.useState("");
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [dataValue, setData] = React.useState("");
    const [id, setID] = React.useState(0);
    const [departmentId, setDepartmentId] = React.useState("");
    const [loading, setLoading] = React.useState();
    const nameRef = useRef(null);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues
    });


    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            nameRef.current?.focus();
        }, 0);

    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, isUpdate) {
            handleClickOpen1();
            setCode(code)
            setData(data)
            setIsUpdate(isUpdate)
        },
    }));

    // useEffect(() => {
    //     setLoading(isloadingvalue)
    // }, [isloadingvalue]);


    useEffect(() => {
        if (!isEmptyOrNull(dataValue)) {
            setID(dataValue._id);
            setValue('name', dataValue.name);
            setValue('description', dataValue.description);
            setDepartmentId(dataValue.departmentId);
        }
        else {
            setValue('name', "");
            setValue('description', "");
            setDepartmentId("")
            setID(0)
        }

    }, [open]);


    const handleClose = () => {
        setOpen(false);
        setValue('name', "");
        setValue('description', "");
        setID(0)
    };


    function onSubmit(model) {
        let data = {
            id: id,
            name: model.name,
            description: model.description,
            departmentId: departmentId,
            code: code,
            isUpdate: isUpdate
        }

        dispatch(AddUpdateTipType(data))
        handleClose();


    }

    const departmentValueChange = (departmentId) => {
        setDepartmentId(departmentId);
    }


    return (
        <div>
            {/* {loading && < CircularProgressLoader loading={loading} />} */}
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("tipType")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        autoSave={false}
                    >
                        <Controller
                            name="name"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mt-16 w-full"
                                    label={t("name")}
                                    type="text"
                                    variant="outlined"
                                    required
                                    inputRef={nameRef}
                                />
                            )}
                        />

                        <Controller
                            name="description"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mt-16 mb-16 w-full"
                                    label={t("description")}
                                    type="text"
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <ErrorBoundary
                            FallbackComponent={(props) => <ErrorPage {...props} componentName="DepartmentDropdownSetting" />} onReset={() => { }}>
                            <DepartmentDropdownSetting code={code} departmentId={departmentValueChange} departmentIdValue={departmentId}>
                            </DepartmentDropdownSetting>
                        </ErrorBoundary>



                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-16"
                                aria-label="Register"
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-16"
                                aria-label="Register"
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>

        </div>
    );
});
export default AddNewTipType;

