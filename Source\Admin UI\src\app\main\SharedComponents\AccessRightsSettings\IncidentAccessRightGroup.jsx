import React from 'react';
import {
    FormControl,
    FormLabel,
    FormControlLabel,
    Checkbox
} from '@mui/material';

const IncidentAccessRightGroup = ({
    t,
    accessRights,
    handleChange,
    showAccessRight,
    isSuperAdmin,
    defaultApp
}) => {
    const accessItems = [
        { key: 'Incident', label: t('incident') },
        { key: 'Citation', label: t('citation') }
    ];

    const isDisabled = !(defaultApp === "mobile" || defaultApp === "incident") && !isSuperAdmin;

    return (
        <div className="col-span-1 w-full">
            <FormControl component="fieldset">
                <FormLabel component="legend">{t('incident')}</FormLabel>
                {accessItems.map(({ key, label }) => {
                    const canShow = showAccessRight[key] === key || isSuperAdmin;
                    return canShow && (
                        <FormControlLabel
                            key={key}
                            control={
                                <Checkbox
                                    checked={accessRights[key] || false}
                                    onChange={handleChange}
                                    name={key}
                                />
                            }
                            label={label}
                        //disabled={isDisabled}
                        />
                    );
                })}
            </FormControl>
        </div>
    );
};

export default IncidentAccessRightGroup;
