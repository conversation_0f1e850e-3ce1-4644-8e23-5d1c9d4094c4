import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { Autocomplete, TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { saveShiftTime } from '../../store/shiftTimeSlice';
import moment from "moment";
import { calculateHours, dateDiffInHoursAndMinutes, parseDateString } from '../../utils/utils';
import { showMessage } from 'app/store/fuse/messageSlice';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import FormControl from '@mui/material/FormControl';
import { getDepartmentDetails, getMasterDepartmentDetails } from '../../store/departmentSlice';
import { ChromePicker } from 'react-color';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorMessage from '../../SharedComponents/ErrorMessage/ErrorMessage';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { useParams } from 'react-router-dom';
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';

const controlStyle = { padding: 0, width: '850px', height: '500px' };
const controlStyleAudio = { padding: 0, width: '305px', height: '50px', };

const defaultValues = {
    ShiftName: '',
    ShiftDuration: '',

};

const schema = yup.object().shape({
    // ShiftName: yup.string().required('Please enter Shift Name.'),
    // ShiftDuration: yup.string().required('Shift Duration not be empty.'),
});

let update;
let deptId;
let displayHoursMessage;
let shiftNameFlag = false
const AgencyShiftDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const routeParams = useParams();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState(null);
    const [code, setCode] = React.useState("");
    const [PagingDetails, setPagingDetails] = React.useState();
    const [closeDta, setcloseDta] = React.useState(null);
    const [color, setColor] = React.useState('#000000');
    const [departmentValue, setDepartmentValue] = React.useState([]);
    const [shiftValue, setShiftValue] = React.useState("");
    const [shiftNameValue, setShiftNameValue] = React.useState([]);
    const ShiftTimeData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shift.data);
    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
    const dtFrom = new Date();
    dtFrom.setHours(dtFrom.getHours() - 2);
    const [selectedStartTime, setselectedStartTime] = React.useState(new Date());
    const [selectedEndTime, setselectedEndTime] = React.useState(new Date());
    const [messageFlag, setMessageFlag] = React.useState(false);
    const departmentRef = useRef(null);
    const [dropdownOpen, setDropdownOpen] = React.useState(false);

    // const [selectedStartTime, setselectedStartTime] = React.useState(new Date());
    // const [selectedEndTime, setselectedEndTime] = React.useState(new Date());

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        resolver: yupResolver(schema),
        defaultValues
    });

    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            if (departmentRef.current) {
                departmentRef.current.focus(); // Focus on the Department field
                setDropdownOpen(true); // Open the dropdown
            }
        }, 0);
    };
    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, PagingDetails, flag, deptID) {

            setData(data)
            setCode(code)
            update = flag
            deptId = deptID
            setPagingDetails(PagingDetails)
            setShiftTimeValue(data)
            handleClickOpen1();

        },
    }));
    useEffect(() => {
        // dispatch(getDepartmentDetails("name", "asc", 0, 1000, code));
        dispatch(getMasterDepartmentDetails("name", "asc", 0, 10000, null, routeParams.code));
    }, []);


    function setDisplayHoursMessage(ID) {
        let datas = DepartmentData.filter(x => x._id === ID)
        if (datas.length > 0) {
            displayHoursMessage = datas[0].shiftType[0].shifthour
            setMessageFlag(true)
        }
    }

    const setShiftTimeValue = data => {


        if (deptId !== '0') {
            setDepartmentValue(deptId)
            setDisplayHoursMessage(deptId)
            let datas = DepartmentData.filter(x => x._id === deptId)[0]
            setShiftNameValue(datas.shiftType[0].shiftnames)
            shiftNameFlag = true
        }
        if (data._id !== null && data._id !== undefined) {
            shiftNameFlag = true
            let [d1hrs, d1mins] = data.StartTime.split(':')
            let [d2hrs, d2mins] = data.EndTime.split(':')
            let x = new Date()
            let y = new Date()
            setselectedStartTime(new Date(new Date(x.setHours(d1hrs)).setMinutes(d1mins)))
            setselectedEndTime(new Date(new Date(y.setHours(d2hrs)).setMinutes(d2mins)))
            setValue('ShiftName', data.ShiftName);
            setValue('ShiftDuration', data.ShiftDuration);
            setDepartmentValue(data.departmentID)
            setDisplayHoursMessage(data.departmentID)
            setShiftValue(data.shiftcode)
            let datas = DepartmentData.filter(x => x._id === data.departmentID)
            if (datas.length > 0) {
                setShiftNameValue(datas[0].shiftType[0].shiftnames)
            }
            // displayHoursMessage = datas[0].shiftType[0].shifthour
            setColor(data.color)
            // setMessageFlag(true)

        }
        else {
            setValue('ShiftName', "");
            setValue('ShiftDuration', "");
            setColor('#000000');
        }
    };

    const handleClose = () => {
        setOpen(false);
        setValue('ShiftName', "");
        setValue('ShiftDuration', "");
        setselectedStartTime(new Date());
        setselectedEndTime(new Date());
        setColor('#000000');
        setDepartmentValue([])
        setMessageFlag(false)
        displayHoursMessage = 0
        shiftNameFlag = false
        setShiftValue('')

    };
    const formRef = useRef(null);

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const checkAlreadyExits = (StartTime, EndTime, id) => {

        let data = ShiftTimeData.filter(x => x.StartTime == StartTime && x.EndTime == EndTime && x._id !== id)
        return (data.length > 0)
    };


    function onSubmit(model) {

        let id = update ? data._id : 0
        let a = new Date(selectedStartTime)
        let b = new Date(selectedEndTime)
        let starttime = a.getHours() + ':' + a.getMinutes();
        let endtime = b.getHours() + ':' + b.getMinutes();
        let shiftData = ShiftTimeData.filter(x => x._id !== id && x.department[0]._id == departmentValue)
        let deptData = DepartmentData.filter(x => x._id === departmentValue)[0]
        let TotalHours = deptData.shiftType[0].shifthour * deptData.shiftType[0].noofshifts + 1;
        let totalHrs = calculateHours(shiftData, model.ShiftDuration, TotalHours);
        let filterDepartmetnData = {};

        if (totalHrs) {
            if (deptData) {
                filterDepartmetnData = {
                    _id: deptData._id,
                    name: deptData.name
                }
            }
            if (shiftData.length < deptData.shiftType[0].noofshifts) {
                dispatch(
                    saveShiftTime(
                        {
                            _id: id,
                            ShiftDuration: model.ShiftDuration,
                            StartTime: starttime,
                            EndTime: endtime,
                            ShiftName: model.ShiftName,
                            shiftcode: shiftValue,
                            department: filterDepartmetnData,
                            isUpdate: update,
                            code: code,
                            color: color
                        },
                        PagingDetails.pageIndex,
                        PagingDetails.rowsPerPage,
                        PagingDetails.id,
                        PagingDetails.direction
                    )
                );
                handleClose();
            }
            else {
                ShowErroMessage(t("shiftLimitExceedsForDepartment"));
            }

        }
        else {
            ShowErroMessage(t('dateAlreadyExistMsg'));
        }

    }

    const handleFromTimeChange = (event) => {

        setselectedStartTime(event);
        let SHR = new Date(event).getHours();
        setselectedEndTime(new Date(new Date(event).setHours(SHR + displayHoursMessage)));
        // selectedEndTime = new Date(event).setHours(SHR + 10);
        let diff = dateDiffInHoursAndMinutes(new Date(event), new Date(new Date(new Date(event).setHours(SHR + displayHoursMessage))))
        setValue('ShiftDuration', diff);
        // setselectedEndTime(event);

    };
    const handleToTimeChange = (event) => {

        // setselectedEndTime(event);
        // let diff = dateDiffInHoursAndMinutes(new Date(selectedStartTime), new Date(event))
        // setValue('ShiftDuration', diff);
    };


    const handleDepartmentChange = (newValue) => {
        if (!newValue) return;

        let datas = DepartmentData.filter(x => x._id === newValue._id);
        let z = ShiftTimeData.filter(x => x._id !== data._id && x.department[0]._id === newValue._id);

        displayHoursMessage = datas[0].shiftType[0].shifthour;

        if (update) {
            let selectedData = DepartmentData.filter(x => x._id === departmentValue);
            setShiftNameValue(selectedData[0].shiftType[0].shiftnames);
            setShiftValue(data.shiftcode);
        } else {
            setShiftNameValue(datas[0].shiftType[0].shiftnames);
        }

        if (z.length < datas[0].shiftType[0].noofshifts) {
            shiftNameFlag = true;
            setDepartmentValue(newValue._id);
            setMessageFlag(true);
        } else {
            ShowErroMessage("shiftLimitExceedsForDepartment");
            setDepartmentValue(departmentValue);
        }
    };


    const handleShiftChange = (newValue) => {
        if (!newValue) return;  // Avoid errors if no shift is selected

        let datas = ShiftTimeData.filter(x => x.department[0]._id === departmentValue);
        let checkShiftName = datas.filter(x => x.shiftcode === newValue);

        if (checkShiftName.length > 0) {
            ShowErroMessage(t("shiftAlreadyExists"));
        } else {
            setShiftValue(newValue);
            setValue("ShiftName", newValue);
        }
    };


    const handleColorChange = (newColor) => {
        setColor(newColor.hex);
    };

    const handleInputChange = (event, inputValue) => {
        if (!inputValue) return;

        // Convert inputValue to string to ensure compatibility
        const inputStr = inputValue.toString().toLowerCase();

        // Find all matching options
        const matchedOptions = shiftNameValue.filter(option => {
            const optionLabel = option;

            // Convert to string safely before comparison
            return optionLabel.toString().toLowerCase().startsWith(inputStr);
        });

        // Only autofill if there's exactly one unique match
        if (Array.isArray(matchedOptions) && matchedOptions.length === 1) {
            handleShiftChange(matchedOptions[0]); // Autofill with the unique match
        }
    };


    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("shiftDetails")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        // autoComplete="off"
                        autoSave={false}
                    >


                        <FormControl fullWidth className="mt-16 w-full">
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsCriminalValue" />} onReset={() => { }}>

                                <CommonAutocomplete
                                    parentCallback={(event, newValue) => handleDepartmentChange(newValue)}
                                    options={DepartmentData || []}
                                    value={DepartmentData.find((dept) => dept._id === departmentValue) || null}
                                    fieldName={t("department")}
                                    optionLabel={"name"}
                                    onKeyDown={handleSelectKeyDown}
                                />
                            </ErrorBoundary>
                        </FormControl>
                        {shiftNameFlag &&
                            <>
                                <FormControl fullWidth className="mt-16 w-full">
                                    <Autocomplete
                                        options={shiftNameValue}
                                        value={shiftValue || null} // Check for selected shift value
                                        onChange={(event, newValue) => handleShiftChange(newValue)}
                                        renderInput={(params) => (
                                            <TextField {...params} label={t("shiftCode")} variant="outlined" required />
                                        )}
                                        onInputChange={handleInputChange}
                                        disabled={update}
                                        getOptionLabel={(option) => option} // Since the shift names are strings
                                        isOptionEqualToValue={(option, value) => option === value} // Ensure correct comparison of selected value
                                        onKeyDown={(event) => {
                                            // Only handle Tab key specifically
                                            if (event.key === 'Tab') {
                                                const listboxNode = document.querySelector('.MuiAutocomplete-listbox');

                                                // Ensure the dropdown is open and get the active option
                                                if (listboxNode) {
                                                    const activeOption = listboxNode.querySelector('[class="MuiAutocomplete-option Mui-focused Mui-focusVisible"]');

                                                    if (activeOption) {
                                                        // Find the selected option
                                                        const selectedItem = shiftNameValue.find(option =>
                                                            option === activeOption.innerText.trim() // Use trim() to avoid leading/trailing spaces
                                                        );

                                                        if (selectedItem) {
                                                            // Trigger selection
                                                            handleShiftChange(selectedItem);
                                                        }
                                                    }
                                                }
                                                // Allow the default behavior (move to the next input field)
                                            }
                                        }}
                                    />

                                </FormControl>

                                <Controller
                                    name="ShiftName"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-16 w-full"
                                            label={t("shiftName")}
                                            type="text"
                                            // error={!!errors.ShiftName}
                                            // helperText={errors?.ShiftName?.message}
                                            variant="outlined"
                                            required
                                        />
                                    )}
                                />
                            </>
                        }
                        {messageFlag &&
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ErrorMessage" />} onReset={() => { }}>
                                <ErrorMessage message={`This department need ${displayHoursMessage} hours shift format.`} />
                            </ErrorBoundary>
                        }

                        <TimePicker
                            renderInput={(params) => (
                                <TextField {...params} sx={{ mt: 2 }} />
                            )}
                            label={t("selectTime")}
                            // ampm={false}
                            openTo="hours"
                            format="HH:mm"
                            value={selectedStartTime}
                            onChange={handleFromTimeChange}
                            sx={{marginTop: 2}}
                        />

                        <TimePicker
                            renderInput={(params) => (
                                <TextField {...params} sx={{ mt: 2 }} />
                            )}
                            label={t("selectTime")}
                            value={selectedEndTime}
                            onChange={handleToTimeChange}
                            // ampm={false}
                            openTo="hours"
                            format="HH:mm"
                            disabled={true}
                            sx={{marginTop: 2}}
                        />

                        <Controller
                            name="ShiftDuration"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mt-16 w-full"
                                    label={t("shiftDuration")}
                                    type="text"
                                    // error={!!errors.ShiftDuration}
                                    // helperText={errors?.ShiftDuration?.message}
                                    variant="outlined"
                                    required
                                    disabled={true}
                                />
                            )}
                        />

                        <label style={{ marginBottom: "2%", marginTop: "2%" }}>{t("colorCode")}</label>
                        <ChromePicker color={color} onChange={handleColorChange} styles={{
                            // Set background of the entire picker container to transparent
                            default: {
                                picker: {
                                    background: 'transparent', // Transparent background
                                },
                                body: {
                                    background: 'transparent', // Transparent background
                                },
                            },
                        }} />

                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
});
export default AgencyShiftDialog;

