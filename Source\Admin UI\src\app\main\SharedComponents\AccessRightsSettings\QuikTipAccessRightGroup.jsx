import React from 'react';
import {
    FormControl,
    FormLabel,
    FormControlLabel,
    Checkbox
} from '@mui/material';

const QuikTipAccessRightGroup = ({
    t,
    accessRights,
    handleChange,
    showAccessRight,
    isSuperAdmin,
    defaultApp
}) => {
    const accessItems = [
        { key: 'QuikTip', label: t('quikTip') },
        { key: 'TipType', label: t('tipType') },
        { key: 'ViewTip', label: t('viewTip') },
        { key: 'Notification', label: t('notification') },
    ];

    const isDisabled = defaultApp !== "quiktip" && !isSuperAdmin;

    return (
        <div className="col-span-1 w-full">
            <FormControl component="fieldset">
                <FormLabel component="legend">{t('quikTip')}</FormLabel>
                {accessItems.map(({ key, label }) => {
                    const canShow = showAccessRight[key] === key || isSuperAdmin;
                    return canShow && (
                        <FormControlLabel
                            key={key}
                            control={
                                <Checkbox
                                    checked={accessRights[key] || false}
                                    onChange={handleChange}
                                    name={key}
                                />
                            }
                            label={label}
                            disabled={isDisabled}
                        />
                    );
                })}
            </FormControl>
        </div>
    );
};

export default QuikTipAccessRightGroup;
