import { authRoles } from '../../auth';
import React from 'react';

const ChangedPassword = React.lazy(() => import('./ChangedPassword'))

const LoginConfig = {
	settings: {
		layout: {
			config: {
				navbar: {
					display: false
				},
				toolbar: {
					display: false
				},
				footer: {
					display: false
				},
				leftSidePanel: {
					display: false
				},
				rightSidePanel: {
					display: false
				}
			}
		}
	},
	auth: authRoles.onlyGuest,
	routes: [
		{
			path:'/ChangePassword/:id',
			element: <ChangedPassword />
		}
	]
};

export default LoginConfig;
