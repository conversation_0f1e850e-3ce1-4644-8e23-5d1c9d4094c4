

exports.translateMessages = (errObj, req) => {
    const errArr = Object.entries(errObj)
    const errMessage={};
    errArr.forEach(err => {
        Object.keys(req.polyglot.phrases).forEach(phrase => {
            if (phrase == err[1].message) {
                err[1].message = req.polyglot.t(phrase)
                errMessage[err[1].path]=err[1].message
            }
        })
    })

    return errMessage
}
