import React, { useEffect } from "react";
import { useDispatch, useSelector } from 'react-redux';
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Grid from "@mui/material/Grid";
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import Divider from '@mui/material/Divider';
import ListItemText from '@mui/material/ListItemText';
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import "leaflet/dist/leaflet.css";
import { useTranslation } from "react-i18next";
import "./UserRecentEntities.css";
import Typography from "@mui/material/Typography";
import { getRecentSelectedEntityData } from "../../store/incidentSlice";
import { Box } from "@mui/system";
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import PropTypes from 'prop-types';
import { Accordion, AccordionDetails, AccordionSummary } from "@mui/material";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const UserRecentUsage = React.memo(({ person, address, vehicle, personSelected, addressSelected, vehicleSelected }) => {
    const dispatch = useDispatch();
    const navbarTheme = useSelector(selectNavbarTheme);
    const { t } = useTranslation("laguageConfig");

    const [id, setId] = React.useState("");
    const [value, setValue] = React.useState(0);
    const [expanded, setExpanded] = React.useState(true);

    function a11yProps(index) {
        return {
            id: `simple-tab-${index}`,
            'aria-controls': `simple-tabpanel-${index}`,
        };
    }

    function TabPanel(props) {
        const { children, value, index, disabled, ...other } = props;
        return (
            <div
                disabled={disabled}
                role="tabpanel"
                hidden={value !== index}
                id={`simple-tabpanel-${index}`}
                aria-labelledby={`simple-tab-${index}`}
                {...other}
            >
                {value === index && (
                    <Box sx={{ p: 3 }}>
                        <Typography>{children}</Typography>
                    </Box>
                )}
            </div>
        );
    }

    TabPanel.propTypes = {
        children: PropTypes.node,
        index: PropTypes.number.isRequired,
        value: PropTypes.number.isRequired,
    };

    const getSearchDetails = async (searchText) => {
        await setId(searchText._id);
        const data = searchText?.data?.entity ?? null;
        if (searchText.type === "Person") {
            const personData = {
                ...data,
                from: "person",
                itemFrom: "Recent"
            };
            personSelected(personData);
            setValue(0);
        }
        if (searchText.type === "Address") {
            const addressData = {
                ...data,
                from: "address",
                itemFrom: "Recent"
            };
            addressSelected(addressData);
            setValue(1);
        }
        if (searchText.type === "Vehicle") {
            const vehicleData = {
                ...data,
                from: "vehicle",
                itemFrom: "Recent"
            }
            vehicleSelected(vehicleData);
            dispatch(getRecentSelectedEntityData(searchText._id, searchText.type));
        }
    }

    const handleChange = (event, newValue) => {
        event.stopPropagation(); // Stop event from bubbling up to accordion
        setValue(newValue);
    };

    const entityCard = (entities) => {
        return <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
            <List sx={{
                width: '100%',
                bgcolor: 'background.paper',
                position: 'relative',
                overflow: 'auto',
                maxHeight: 350,
                '& ul': { padding: 0 },
            }}
            >
                {entities && entities.map((item) =>
                    <ListItem className='pointer'
                        style={item._id === id
                            ? { backgroundColor: navbarTheme.palette.primary.main, color: navbarTheme.palette.primary.contrastText }
                            : {}
                        }
                        alignItems="flex-start">
                        <ListItemText
                            primary={item.userRecentEntity !== undefined ? item.userRecentEntity : ""}
                            onClick={() => getSearchDetails(item)}
                        />
                        <Divider />
                    </ListItem>
                )}
            </List>
        </Grid>
    }

    const handleChangePanel = () => {
        setExpanded(!expanded);
    };

    return (
        <div className="cardPadding mt-16">
            {vehicle?.length > 0 ?
                <Card>
                    <CardContent>
                        <Typography className="font-semibold text-16 text-center">{t("recentVehicle")}</Typography>
                        {entityCard(vehicle)}
                    </CardContent>
                </Card>
                :
                <Accordion
                    expanded={expanded}
                    onChange={handleChangePanel}
                    TransitionProps={{ unmountOnExit: true }}
                >
                    <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        aria-controls="panel-content"
                        id="panel-header"
                    >
                        {
                            vehicle?.length > 0 &&
                            <Typography className="font-semibold text-16 text-center">{t("recent")}</Typography>
                        }
                        <Tabs id="entityTab" value={value} onChange={handleChange} aria-label="basic tabs example">
                            {
                                person?.length > 0 &&
                                <Tab label={t("recentPerson")} {...a11yProps(0)} />
                            }
                            {
                                address?.length > 0 &&
                                <Tab label={t("recentAddress")} {...a11yProps(1)} />
                            }
                        </Tabs>
                    </AccordionSummary>
                    <AccordionDetails
                        sx={{
                            maxHeight: '250px',
                            overflowY: 'clip',
                        }}
                        expandIcon={<ExpandMoreIcon />}>
                        {
                            person?.length > 0 &&
                            <TabPanel id="entityPanel" value={value} index={0}>
                                {entityCard(person)}
                            </TabPanel >
                        }
                        {
                            address?.length > 0 &&
                            <TabPanel id="entityPanel" value={value} index={1}>
                                {entityCard(address)}
                            </TabPanel >
                        }
                    </AccordionDetails>
                </Accordion>
            }
        </div >
    );

}, (prevProps, nextProps) => {
    return prevProps.person === nextProps.person &&
        prevProps.address === nextProps.address;
});

export default UserRecentUsage;
