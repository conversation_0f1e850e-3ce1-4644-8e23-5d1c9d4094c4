import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import Typography from "@mui/material/Typography";
import MuiDialogTitle from "@mui/material/DialogTitle";
import MuiDialogContent from "@mui/material/DialogContent";
import MuiDialogActions from "@mui/material/DialogActions";
import axios from "axios";
import React, { useState, useEffect, useRef } from "react";
import { Controller, useForm } from "react-hook-form";
import { TextField } from "@mui/material";
import AutoSearch from "app/theme-layouts/shared-components/SearchBox/AutoSearch";
import "../911Call.css"
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { useTranslation } from "react-i18next";
import InputAdornment from "@mui/material/InputAdornment";
import { useDispatch, useSelector } from "react-redux";
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { withStyles } from "@mui/styles";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { Icon, Backdrop } from "@mui/material";
import jsonp from 'jsonp';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import { isEmptyOrNull } from "../../utils/utils";
import Autocomplete from '@mui/material/Autocomplete';
import { ReactSearchAutocomplete } from 'react-search-autocomplete'
import ReactSearchAutocompleteComponents from '../../SharedComponents/ReactSearchAutocompleteComponents/ReactSearchAutocompleteComponents';
import moment from "moment";

const fw_8 = {
    fontWeight: "600",
};

const styles = (theme) => ({
    root: {
        margin: 0,
        padding: theme.spacing(2),
    },
    closeButton: {
        position: "absolute",
        right: theme.spacing(1),
        top: theme.spacing(1),
        color: theme.palette.grey[500],
    },
});

const DialogTitle = withStyles(styles)((props) => {
    const { children, classes, onClose, ...other } = props;
    return (
        <MuiDialogTitle disableTypography className={classes.root} {...other}>
            <Typography variant="h6">{children}</Typography>
            {onClose ? (
                <IconButton
                    aria-label="close"
                    className={classes.closeButton}
                    onClick={onClose}
                >
                    <CloseIcon />
                </IconButton>
            ) : null}
        </MuiDialogTitle>
    );
});

const DialogContent = withStyles((theme) => ({
    root: {
        padding: theme.spacing(2),
        width: "auto",
        height: "auto",
    },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
    root: {
        margin: 0,
        padding: theme.spacing(1),
    },
}))(MuiDialogActions);

function SelectedCallInfo(props) {
    const { t } = useTranslation("laguageConfig");
    const [newCall, setNewCallOpen] = React.useState(false);
    const [address, setAddressOpen] = React.useState(false);
    const [revrseGeocodeAddresses, setRevrseGeocodeAddresses] = React.useState([]);
    const navbarTheme = useSelector(selectNavbarTheme);
    const packet911CallsGroup = useSelector(({ call911 }) => call911.call911.group);
    const callTypesData = useSelector(({ call911 }) => call911.call911.callTypesData);

    const handleNewCallClose = () => {
        props.onClose();
        setNewCallOpen(false);
    };

    const handleNewCallClick = () => {
        setNewCallOpen(!newCall);
    };

    const [heightDynamic, setHeightFynamic] = React.useState("auto");

    const handleAddressClick = () => {
        if (
            props.value.PacketClassofService === "WPH2" ||
            props.value.PacketClassofService === "VOIP" ||
            props.value.PacketClassofService === "TLMA" ||
            props.value.PacketClassofService === "TELM" ||
            props.value.PacketClassofService === "RESD" ||
            props.value.PacketClassofService === "BUSN"
        ) {
            // axios
            //   .get(
            //     `https://geocode.arcgis.com/arcgis/rest/services/World/GeocodeServer/findAddressCandidates?singleLine=&category=Residence,Shops%20and%20Service,Food,Education,POI,Professional%20and%20Other%20Places,Nightlife%20Spot&outFields=LongLabel,Distance&maxLocations=5&location=${props.value.Packetx.trim()},${props.value.Packety.trim()}&f=pjson`
            //   )
            //   .then((result) => {
            //     setRevrseGeocodeAddresses(result.data.candidates);
            //   })
            //   .catch((ex) => {
            //   });
            jsonp(`https://geocode.arcgis.com/arcgis/rest/services/World/GeocodeServer/findAddressCandidates?singleLine=&category=Residence,Shops%20and%20Service,Food,Education,POI,Professional%20and%20Other%20Places,Nightlife%20Spot&outFields=LongLabel,Distance&maxLocations=5&location=${props.value.Packetx.trim()},${props.value.Packety.trim()}&f=pjson`,
                null,
                (err, result) => {
                    if (err) {
                        console.log(err.message);
                    } else {
                        //setNearAddresses(result.candidates);
                        setRevrseGeocodeAddresses(result.candidates);
                    }
                });
            setAddressOpen(!address);
        }
    };

    const handleNext = () => {
        setAddressOpen(false);
        setNewCallOpen(!newCall);
    };

    const handleAddressClose = () => {
        setAddressOpen(false);
    };

    const nav_css = {
        backgroundColor: navbarTheme.palette.primary.main,
        color: navbarTheme.palette.primary.contrastText,
    };

    const { control, setValue, } = useForm({ mode: "onChange", });
    const formRef = useRef(null);
    const [CallerNumber, setCallerNumber] = React.useState("");
    const [CallerName, setCallerName] = React.useState("");
    const [Location, setLocation] = React.useState("");
    const [LocationInfo, setLocationInfo] = React.useState("");

    setValue("CallerName", CallerName);
    setValue("CallerNumber", CallerNumber);
    setValue("CallerLocation", Location);
    setValue("LocationInfo", LocationInfo);

    useEffect(() => {
        if (newCall) {
            setCallerNumber(props.value.PacketCallingPhone.trim());
            setCallerName("");
            setLocation("");
            setLocationInfo("");

            if (
                props.value.PacketClassofService === "VOIP" ||
                props.value.PacketClassofService === "RESD" ||
                props.value.PacketClassofService === "BUSN"
            ) {
                setCallerName(props.value.PacketCustomerName.trim());
                setLocation(
                    props.value.PacketStreetNumber.trim() +
                    " " +
                    props.value.PacketStreetAddress.trim() +
                    ", " +
                    props.value.PacketCity.trim() +
                    ", " +
                    props.value.PacketState.trim() +
                    ", " +
                    props.value.PacketLocationInfo.trim()
                );
            }

            if (props.value.PacketClassofService === "WPH2") {
                setLocation(
                    props.value.PacketStreetNumber.trim() +
                    " " +
                    props.value.PacketStreetAddress.trim() +
                    ", " +
                    props.value.PacketCity.trim() +
                    ", " +
                    props.value.PacketState.trim() +
                    ", " +
                    props.value.PacketLocationInfo.trim()
                );
            }
        }
        // eslint-disable-next-line
    }, [newCall]);

    useEffect(() => {
        setNewCallOpen(props.newCall);
        // eslint-disable-next-line
    }, [props.newCall, revrseGeocodeAddresses]);

    const callerNameChanged = () => {
        setCallerName("");
        document.getElementById("callerNameText").focus();
    };

    const callerNumberChanged = () => {
        setCallerNumber("");
        document.getElementById("callerNumberText").focus();
    };

    const callerLocationText = () => {
        setLocation("");
        document.getElementById("callerLocationText").focus();
    };

    const copyLocationText = () => {
        setLocationInfo(Location);
    };

    const passChildSeachChange = (value) => {
        if (value !== "") {
            setHeightFynamic("400px")
        }
        else {
            setHeightFynamic("auto")
        }
    };

    const handleOnHover = (result) => {
        console.log(result);
    };

    const handleOnSelect = (item) => {
        setHeightFynamic("auto")
    };

    const handleOnFocus = () => {
        console.log("Focused");
    };

    const handleOnClear = (vale) => {
        setHeightFynamic("auto")
    };



    return (
        <div>
            <Card
                className="cardborderclass"
                variant="outlined"
                style={
                    props.selectedCard === props.id
                        ? {
                            backgroundColor: props.backColor,
                            border: "solid 3px",
                            margin: "1px",
                        }
                        : { backgroundColor: props.backColor, cursor: "pointer" }
                }
            >
                <CardContent>
                    <Grid container>
                        {(props.value.PacketClassofService === "WRLS" ||
                            props.value.PacketClassofService === "WPH1") && (
                                <Grid container
                                    xs={10} sm={10} md={10} lg={10} xl={10} style={{ fontSize: "1.15rem", padding: "0px" }}
                                >
                                    <Grid item xs={4} style={{ padding: "0px" }}>
                                        <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                                    </Grid>
                                    <Grid item xs={4} style={{ padding: "0px" }}>
                                        <span style={fw_8}>
                                            {" "}
                                            {/* {props.value.PacketCallTime}:
                                            {new Date(props.value.PacketCallReceivedDT).getSeconds()}{" "}
                                            {props.value.PacketCallDate} */}
                                            {moment(new Date(props.value.PacketCallReceivedDT)).format("HH:mm:ss MM/DD/YY")}
                                        </span>
                                    </Grid>
                                    <Grid item xs={2} style={{ padding: "0px" }}>
                                        <span style={fw_8}> {props.value.PacketClassofService}</span>
                                    </Grid>
                                    <Grid item xs={2} style={{ padding: "0px" }}>
                                        <span style={fw_8}>
                                            {props.value.SubCall &&
                                                props.value.SubCall.filter(
                                                    (call) =>
                                                        call.PacketCustomerName.trim() !==
                                                        props.value.PacketCustomerName.trim()
                                                ).length > 0 &&
                                                "Moving"}
                                        </span>
                                    </Grid>
                                    <Grid item xs={12} style={{ padding: "0px" }}>
                                        {t("towerInfo")}:{" "}
                                        <span style={fw_8}>{props.value.PacketCustomerName}</span>
                                    </Grid>
                                    <Grid item xs={12} style={{ padding: "0px" }}>
                                        <span style={fw_8}>
                                            {props.value.PacketStreetAddress}, {props.value.PacketCity},{" "}
                                            {props.value.PacketState}, {props.value.PacketState}{" "}
                                        </span>
                                    </Grid>
                                    <Grid item xs={6} style={{ padding: "0px" }}>
                                        {t("lat")}: <span style={fw_8}>{!isEmptyOrNull(props.value.Packety) ? props.value.Packety : "0"}</span>
                                    </Grid>
                                    <Grid item xs={6} style={{ padding: "0px" }}>
                                        {t("long")}:{" "}
                                        <span style={fw_8}>{!isEmptyOrNull(props.value.Packetx) ? props.value.Packetx : "0"}</span>
                                    </Grid>
                                </Grid>
                            )}

                        {(props.value.PacketClassofService === "WPH2" ||
                            props.value.PacketClassofService === "TLMA" ||
                            props.value.PacketClassofService === "TELM") && (
                                <Grid container xs={10} sm={10} md={10} lg={10} xl={10}
                                    style={{ fontSize: "1.15rem", padding: "0px" }}
                                >
                                    <Grid item lg={4}>
                                        <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                                    </Grid>
                                    <Grid item lg={4}>
                                        <span style={fw_8}>
                                            {" "}
                                            {/* {props.value.PacketCallTime}:
                                            {new Date(props.value.PacketCallReceivedDT).getSeconds()}{" "}
                                            {props.value.PacketCallDate} */}
                                            {moment(new Date(props.value.PacketCallReceivedDT)).format("HH:mm:ss MM/DD/YY")}
                                        </span>
                                    </Grid>
                                    <Grid item lg={2}>
                                        <span style={fw_8}> {props.value.PacketClassofService}</span>
                                    </Grid>
                                    <Grid item lg={2}>
                                        <span style={fw_8}>
                                            {props.value.SubCall &&
                                                props.value.SubCall.filter(
                                                    (call) =>
                                                        call.Packety !== props.value.Packety ||
                                                        call.Packetx !== props.value.Packetx
                                                ).length > 0 &&
                                                "Moving"}
                                        </span>
                                    </Grid>
                                    <Grid item lg={12}>
                                        {t("towerInfo")}:{" "}
                                        <span style={fw_8}>{props.value.PacketCustomerName}</span>
                                    </Grid>
                                    <Grid item lg={12}>
                                        <span style={fw_8}>
                                            {props.value.PacketStreetAddress}, {props.value.PacketCity},{" "}
                                            {props.value.PacketState}
                                        </span>
                                    </Grid>
                                    <Grid item lg={6}>
                                        {t("lat")}: <span style={fw_8}>{props.value.Packety}</span>
                                    </Grid>
                                    <Grid item lg={6}>
                                        {t("long")}:{" "}
                                        <span style={fw_8}>{props.value.Packetx}</span>
                                    </Grid>
                                </Grid>
                            )}

                        {props.value.PacketClassofService !== "WRLS" &&
                            props.value.PacketClassofService !== "WPH1" &&
                            props.value.PacketClassofService !== "WPH2" &&
                            props.value.PacketClassofService !== "TLMA" &&
                            props.value.PacketClassofService !== "TELM" && (
                                <Grid container xs={10} sm={10} md={10} lg={10} xl={10}
                                    style={{ fontSize: "1.15rem", padding: "0px" }}
                                >
                                    <Grid item xs={4}>
                                        <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                                    </Grid>
                                    <Grid item xs={4}>
                                        <span style={fw_8}>
                                            {" "}
                                            {/* {props.value.PacketCallTime}:
                                            {new Date(props.value.PacketCallReceivedDT).getSeconds()}{" "}
                                            {props.value.PacketCallDate} */}
                                            {moment(new Date(props.value.PacketCallReceivedDT)).format("HH:mm:ss MM/DD/YY")}
                                        </span>
                                    </Grid>
                                    <Grid item xs={2}>
                                        <span style={fw_8}>
                                            {" "}
                                            {props.value.PacketClassofService}
                                        </span>
                                    </Grid>
                                    <Grid item xs={12}>
                                        {t("caller")}:{" "}
                                        <span style={fw_8}>{props.value.PacketCustomerName}</span>
                                    </Grid>
                                    <Grid item xs={12}>
                                        {t("location")}:{" "}
                                        <span style={fw_8}>
                                            {props.value.PacketStreetNumber}{" "}
                                            {props.value.PacketStreetAddress},{" "}
                                            {props.value.PacketLocationInfo},{" "}
                                            {props.value.PacketCity}, {props.value.PacketState}
                                        </span>
                                    </Grid>
                                    <Grid item xs={6}>
                                        {t("lat")}:{" "}
                                        <span style={fw_8}>{props.value.Packety}</span>
                                    </Grid>
                                    <Grid item xs={6}>
                                        {t("long")}:{" "}
                                        <span style={fw_8}>{props.value.Packetx}</span>
                                    </Grid>
                                </Grid>
                            )}

                        <Grid item xs={12} sm={2} md={2} lg={2} xl={2}
                            style={{ fontSize: "1.15rem", padding: "0px" }}
                        >
                            <Grid item xs={12} sm={2} md={2} lg={2} xl={2}
                                style={{ padding: "0px" }}
                            >
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_newCall" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="m-0" btnName={t("newCall")} parentCallback={handleNewCallClick}></CommonButton>
                                </ErrorBoundary>
                                {(props.value.PacketClassofService === "WPH2" ||
                                    props.value.PacketClassofService === "VOIP" ||
                                    props.value.PacketClassofService === "TLMA" ||
                                    props.value.PacketClassofService === "TELM" ||
                                    props.value.PacketClassofService === "RESD" ||
                                    props.value.PacketClassofService === "BUSN") && (
                                        <ErrorBoundary
                                            FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_RG" />} onReset={() => window.location.reload()} >
                                            <CommonButton styleClass="mt-5" btnName={t("rg")} parentCallback={handleAddressClick}></CommonButton>
                                        </ErrorBoundary>
                                    )}
                            </Grid>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>

            <Dialog
                onClose={handleNewCallClose}
                maxWidth="true"
                style={
                    packet911CallsGroup[0].length > 1
                        ? { width: "90%", marginLeft: "5%" }
                        : { width: "70%", marginLeft: "15%" }
                }
                aria-labelledby="customized-dialog-title"
                open={newCall || props.newCallOpen}
            >
                <DialogTitle
                    id="customized-dialog-title"
                    onClose={handleNewCallClose}
                    style={nav_css}
                >
                    {t("newCall")}
                </DialogTitle>
                <DialogContent dividers style={{ width: "100%" }}>
                    <Typography className="text-18 mx-12 font-bold" color="inherit">
                        {t("selectedCall")}
                    </Typography>
                    <Grid container xs={12}
                        style={{ padding: "0px", paddingLeft: "12px" }}
                    >
                        <Grid item xs={4}>
                            {t("callingPhone")}{" "}
                            <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                        </Grid>
                        <Grid item xs={4}>
                            {t("receivedOn")}
                            <span style={fw_8}>
                                {" "}
                                {/* {props.value.PacketCallTime}:
                                {new Date(props.value.PacketCallReceivedDT).getSeconds()}{" "}
                                {props.value.PacketCallDate} */}
                                {moment(new Date(props.value.PacketCallReceivedDT)).format("HH:mm:ss MM/DD/YY")}
                            </span>
                        </Grid>
                        <Grid item xs={4}>
                            {t("classofService")}
                            <span style={fw_8}> {props.value.PacketClassofService}</span>
                        </Grid>
                        <Grid item xs={4}>
                            <span style={fw_8}>{props.value.PacketCustomerName}</span>
                        </Grid>
                        <Grid item xs={8}>
                            {t("location")}{" "}
                            <span style={fw_8}>
                                {props.value.PacketClassofService === "VOIP" ? props.value.PacketStreetNumber + " " + props.value.PacketStreetAddress
                                    + " ," + props.value.PacketLocationInfo + ", " + props.value.PacketCity + ", " + props.value.PacketState
                                    : props.value.PacketStreetAddress},{" "}
                                {props.value.PacketLocationInfo}, {props.value.PacketCity}, {props.value.PacketState}

                            </span>
                        </Grid>
                        <Grid item xs={4}>
                            {t("lat")}: <span style={fw_8}>{props.value.Packety}</span>
                        </Grid>
                        <Grid item xs={4}>
                            {t("long")}: <span style={fw_8}>{props.value.Packetx}</span>
                        </Grid>
                    </Grid>
                    {callTypesData.length &&
                        <Card variant="outlined"
                            style={{ height: heightDynamic }}
                        >
                            <CardContent>
                                <form ref={formRef} >
                                    <div>
                                        <Grid container spacing={1} className="mb-4 mt-4">
                                            {(props.value.PacketClassofService === "VOIP" ||
                                                props.value.PacketClassofService === "RESD" ||
                                                props.value.PacketClassofService === "BUSN") && (
                                                    <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                                                        <Controller
                                                            name="CallerName"
                                                            control={control}
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    className="w-full"
                                                                    type="text"
                                                                    id="callerNameText"
                                                                    label={t("callerName")}
                                                                    variant="outlined"
                                                                    InputProps={{
                                                                        className: "pr-2",
                                                                        type: "text",
                                                                        endAdornment: (
                                                                            <InputAdornment position="end">
                                                                                <IconButton
                                                                                    onClick={() => callerNameChanged()}
                                                                                >
                                                                                    <Icon className="text-20" color="action">
                                                                                        highlight_off
                                                                                    </Icon>
                                                                                </IconButton>
                                                                            </InputAdornment>
                                                                        ),
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                )}
                                            <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                                                <Controller
                                                    name="CallerNumber"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <TextField
                                                            {...field}
                                                            className="w-full"
                                                            type="text"
                                                            id="callerNumberText"
                                                            label={t("callerNumber")}
                                                            variant="outlined"
                                                            InputProps={{
                                                                className: "pr-2",
                                                                type: "text",
                                                                endAdornment: (
                                                                    <InputAdornment position="end">
                                                                        <IconButton
                                                                            onClick={() => callerNumberChanged()}
                                                                        >
                                                                            <Icon className="text-20" color="action">
                                                                                highlight_off
                                                                            </Icon>
                                                                        </IconButton>
                                                                    </InputAdornment>
                                                                ),
                                                            }}
                                                        />
                                                    )}
                                                />
                                            </Grid>
                                        </Grid>
                                    </div>
                                    <div>
                                        <Grid container spacing={1} className="mb-4 mt-4">
                                            <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                                                <Controller
                                                    name="CallerLocation"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <TextField
                                                            {...field}
                                                            className="w-full"
                                                            type="text"
                                                            id="callerLocationText"
                                                            label={t("callerLocation")}
                                                            variant="outlined"
                                                            InputProps={{
                                                                className: "pr-2",
                                                                type: "text",
                                                                endAdornment: (
                                                                    <InputAdornment position="end">
                                                                        <IconButton
                                                                            onClick={() => callerLocationText()}
                                                                        >
                                                                            <Icon className="text-20" color="action">
                                                                                highlight_off
                                                                            </Icon>
                                                                        </IconButton>
                                                                        <IconButton onClick={() => copyLocationText()}>
                                                                            <Icon className="text-20" color="action">
                                                                                arrow_circle_down
                                                                            </Icon>
                                                                        </IconButton>
                                                                    </InputAdornment>
                                                                ),
                                                            }}
                                                        />
                                                    )}
                                                />
                                            </Grid>
                                            <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
                                                <Controller
                                                    name="LocationInfo"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <TextField
                                                            {...field}
                                                            className="w-full"
                                                            type="text"
                                                            label={t("locationInfo")}
                                                            variant="outlined"
                                                        />
                                                    )}
                                                />
                                            </Grid>
                                        </Grid>
                                    </div>
                                    {/* <div>
                                        <Grid container spacing={1} className="mb-4 mt-4" >
                                            <Grid item className="w-full">
                                                <Autocomplete
                                                    freeSolo
                                                    id="free-solo-2-demo"
                                                    disableClearable
                                                    options={callTypesData.map((option) => option.CallTypeName)}
                                                    renderInput={(params) => (
                                                        <TextField
                                                            {...params}
                                                            label="Call T"
                                                            InputProps={{
                                                                ...params.InputProps,
                                                                type: 'search',
                                                            }}
                                                        />
                                                    )}
                                                />

                                            </Grid>
                                        </Grid>
                                    </div> */}
                                    <div>
                                        <Grid container spacing={1} className="mb-4 mt-4" >
                                            <Grid item className="w-full">
                                                <ErrorBoundary FallbackComponent={(props) => <ErrorPage {...props} componentName="ReactSearchAutocompleteComponents" />} onReset={() => { }} >
                                                    <ReactSearchAutocompleteComponents pashandleOnSelect={handleOnSelect} passHandleOnClear={handleOnClear} passChildOnsearch={passChildSeachChange} items={callTypesData} keys={"CallTypeName"} resultStringKeyName={"CallTypeName"} placeholder={t("callType")} />
                                                </ErrorBoundary>
                                            </Grid>
                                        </Grid>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    }
                    {packet911CallsGroup[0].length > 1 && (
                        <Grid container>
                            <Typography className="text-18 mx-12 font-bold" color="inherit">
                                {("groupCalls")}
                            </Typography>
                            <Grid item xs={12} className="pb-8">
                                <div>
                                    <TableContainer component={Paper}>
                                        <Table aria-label="simple table">
                                            <TableHead>
                                                <TableRow>
                                                    <TableCell>{t("callingPhone")}</TableCell>
                                                    <TableCell>{t("receivedOn")}</TableCell>
                                                    <TableCell>{t("classofService")}</TableCell>
                                                    <TableCell>{t("towerInfo")}</TableCell>
                                                    <TableCell>{t("address")}</TableCell>
                                                    <TableCell>{t("suppliment")}</TableCell>
                                                    <TableCell>{t("create")}</TableCell>
                                                </TableRow>
                                            </TableHead>
                                            <TableBody>
                                                {packet911CallsGroup[0]
                                                    .filter(
                                                        (value) =>
                                                            value.PacketCallingPhone !==
                                                            props.value.PacketCallingPhone
                                                    )
                                                    .map((row) => (
                                                        <TableRow key={row.officerName}>
                                                            <TableCell component="th" scope="row">
                                                                {row.PacketCallingPhone}
                                                            </TableCell>
                                                            <TableCell>
                                                                {/* {row.PacketCallTime}:
                                                                {new Date(
                                                                    row.PacketCallReceivedDT
                                                                ).getSeconds()}{" "}
                                                                {row.PacketCallDate} */}
                                                                {moment(new Date(props.value.PacketCallReceivedDT)).format("HH:mm:ss MM/DD/YY")}
                                                            </TableCell>
                                                            <TableCell>{row.PacketClassofService}</TableCell>
                                                            <TableCell>{row.PacketCustomerName}</TableCell>
                                                            <TableCell>
                                                                {row.PacketClassofService === "VOIP" ? row.PacketStreetNumber + " " + row.PacketStreetAddress
                                                                    + ", " + row.PacketLocationInfo + ", " + row.PacketCity + ", " + row.PacketState
                                                                    : row.PacketStreetAddress},{" "}
                                                                {row.PacketLocationInfo}
                                                                , {row.PacketCity}, {row.PacketState}
                                                            </TableCell>
                                                            <TableCell>
                                                                <Button variant="contained" color="secondary">
                                                                    {t("add")}
                                                                </Button>
                                                            </TableCell>
                                                            <TableCell>
                                                                <Button variant="contained" color="primary">
                                                                    {t("new")}
                                                                </Button>
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                            </TableBody>
                                        </Table>
                                    </TableContainer>
                                </div>
                            </Grid>
                        </Grid>
                    )}
                </DialogContent>
                <DialogActions>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_Close" />} onReset={() => window.location.reload()} >
                        <CommonButton styleClass="mr-8" btnName={t("close")} parentCallback={handleNewCallClose}></CommonButton>
                    </ErrorBoundary>
                    <Button
                        autoFocus
                        onClick={handleNewCallClose}
                        color="primary"
                        style={nav_css}
                    >
                        {t("createCall")}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog
                onClose={handleAddressClose}
                maxWidth="true"
                style={
                    packet911CallsGroup[0].length > 1
                        ? { width: "90%", marginLeft: "5%" }
                        : { width: "70%", marginLeft: "15%" }
                }
                aria-labelledby="customized-dialog-title"
                open={address}
            >
                <DialogTitle
                    id="customized-dialog-title"
                    onClose={handleAddressClose}
                    style={nav_css}
                >
                    {t("closest5addresses")}
                </DialogTitle>
                <DialogContent dividers style={{ width: "100%" }}>
                    {revrseGeocodeAddresses.map((address, index) => (
                        <Typography
                            className="text-18 mx-12 font-bold p-16 m-16"
                            style={{ border: "2px solid", borderColor: "#393939" }}
                            color="inherit"
                        >
                            {index + 1}. {address.attributes.LongLabel}
                            <div style={{ marginLeft: "16px" }}>
                                {t("distance")}:{" "}
                                {(Math.round(address.attributes.Distance * 100) / 100).toFixed(2)}
                            </div>
                        </Typography>
                    ))}
                </DialogContent>
                <DialogActions>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_Close" />} onReset={() => window.location.reload()} >
                        <CommonButton styleClass="" btnName={t("close")} parentCallback={handleAddressClose}></CommonButton>
                    </ErrorBoundary>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_Next" />} onReset={() => window.location.reload()} >
                        <CommonButton styleClass={nav_css} btnName={t("next")} parentCallback={handleNext}></CommonButton>
                    </ErrorBoundary>
                </DialogActions>
            </Dialog>
        </div >
    );
}

export default SelectedCallInfo;