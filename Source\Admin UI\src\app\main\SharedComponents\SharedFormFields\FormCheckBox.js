import { useState } from "react";
import { Checkbox, FormControl, FormControlLabel } from "@mui/material";

const FormCheckbox = ({
    label,
    checked,
    onChange,
    gridProps = {},
}) => {

    return (
        <FormControl component="fieldset" {...gridProps}>
            <FormControlLabel
                control={
                    <Checkbox
                        checked={checked}
                        onChange={onChange}
                    />
                }
                label={label}
            />
        </FormControl>
    );
};

export default FormCheckbox;
