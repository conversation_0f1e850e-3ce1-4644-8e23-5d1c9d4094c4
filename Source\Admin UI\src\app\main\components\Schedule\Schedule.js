import React, { useState, useEffect, useRef } from "react";
import _ from "@lodash";
import "./Schedule.css";
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { useParams } from "react-router";
import { Scheduler } from 'smart-webcomponents-react/scheduler';
import { clearData, getShiftAllocationSchedule, saveShiftAllocationSchedule, updateShiftAllocationSchedule } from "../../store/shiftAllocationScheduleSlice";
import { getUsers } from "../../administration/store/usersSlice";

const Schedule = (props) => {
    const { t } = useTranslation('laguageConfig');


    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
    });


    const routeParams = useParams();
    const [dropFlag, setDropFlag] = useState(false);

    const scheduler = useRef(null);
    const calendar = useRef(null);
    const treeref = useRef(null);
    const primaryContainer = useRef(null);
    const dispatch = useDispatch();




    const today = new Date(props.todayDate);



    useEffect(() => {
        dispatch(clearData())
    }, []);

    const view = 'month';
    const views = ['day',
        {
            type: 'week',
            hideWeekend: true,
        },
        {
            type: 'month',
            hideWeekend: false,
        }, 'agenda',
        {
            label: '4 days',
            value: 'workWeek',
            type: 'week',
            shortcutKey: 'X',
            hideWeekend: false,
            hideNonworkingWeekdays: true,
        }
    ];
    const firstDayOfWeek = 0;
    const scrollButtonsPosition = 'far';




    useEffect(() => {
        if (scheduler.current !== null) {
        }
        else {
            console.log("Scheduler not found")
        }

    }, [primaryContainer, scheduler, calendar, treeref]);

    return (
        <>
            <Scheduler style={{ height: "1600px" }}
                dataSource={props.data}
                view={view}
                dateCurrent={today}
                views={views}
                firstDayOfWeek={firstDayOfWeek}
                scrollButtonsPosition={scrollButtonsPosition}
                onDragEnd={props.onDragEnd}
                ref={scheduler}
                disableDrag={props.isDragAndDrop}
                disableDrop={props.dropFlag}
                onEditDialogOpen={props.onItemClick}
            >
            </Scheduler>

        </>

    );
}
export default Schedule;