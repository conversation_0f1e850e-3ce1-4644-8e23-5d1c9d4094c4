import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { InsertErrorLog } from '../../ErrorLog/store/errorLogSlice';

const callType = {
	fontWeight: '800',
	fontSize: '12px'
}

function ErrorPage({ error, resetErrorBoundary, componentName }) {
	const user = useSelector(({ auth }) => auth.user);
	const dispatch = useDispatch();
	const userId = localStorage.getItem("userId");
	const agencyCode = localStorage.getItem("agencyCode");

	function callErrorApi(error) {
		const body = {
			Message: error.message,
			StackTrace: error.stack,
			Method: componentName,
			UserID: user.data.id === "" ? userId : user.data.id,
			URL: "",
			Source: "ADMIN_UI",
			AgencyCode: user.data.defaultAgency === undefined ? agencyCode : user.data.defaultAgency,
		}
		dispatch(InsertErrorLog(body));
	}

	return (
		<div className="flex flex-col flex-1 items-center justify-center p-16" style={{ backgroundColor: "aliceblue", color: 'red' }}>
			<div className="max-w-512 text-center">
				<span style={callType}>	{error.message}</span>
				{callErrorApi(error)}
			</div>
		</div>
	);


}


export default ErrorPage;
