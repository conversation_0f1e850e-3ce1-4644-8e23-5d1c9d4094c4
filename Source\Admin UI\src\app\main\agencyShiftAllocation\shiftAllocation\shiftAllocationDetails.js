import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import { ThemeProvider, StyledEngineProvider, Paper, Input, } from "@mui/material";
import { useTranslation } from "react-i18next";
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import _ from "@lodash";
import history from "@history";
import TablePagination from "@mui/material/TablePagination";
import { useParams } from "react-router";
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from '@mui/icons-material/Delete';
import ReplayIcon from '@mui/icons-material/Replay';
import NoteAddIcon from '@mui/icons-material/NoteAdd';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CircularProgressLoader from "../../SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from "../../utils/utils";
import { getShiftTime } from "../../store/shiftTimeSlice";
import { ErrorBoundary } from "react-error-boundary";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import moment from "moment";
import "./shiftAllocation.css"
import { getShiftAllocation, removeShiftAllocation, searchshitAllocation } from "../../store/shiftAllocationSlice";
import ShiftAllocationDialog from "./shiftAllocationDialog";
import AssignmentIcon from '@mui/icons-material/Assignment';
import { getTeams } from "../../store/teamSlice";
import { getMasterDepartmentDetails } from "../../store/departmentSlice";
import { showMessage } from "app/store/fuse/messageSlice";
import { DeleteShiftAllocationSchedule } from "../../store/shiftAllocationScheduleSlice";
import CommonButton from "../../SharedComponents/ReuseComponents/CommonButton";
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End

const rows = [
    {
        id: "_id",
        align: "left",
        disablePadding: false,
        label: "ID",
        sort: true,
    },
    {
        id: "StartDate",
        align: "left",
        disablePadding: false,
        label: "StartDate",
        sort: true,
    },
    {
        id: "EndDate",
        align: "left",
        disablePadding: false,
        label: "EndDate",
        sort: true,
    },
    {
        id: "department",
        align: "left",
        disablePadding: false,
        label: "department",
        sort: true,
    },
    {
        id: "departmentID",
        align: "left",
        disablePadding: false,
        label: "departmentID",
        sort: true,
    },
    {
        id: "Action",
        align: "left",
        disablePadding: false,
        label: "Action",
        sort: true,
    },
];

let Data = []

function shiftAllocationDetails() {
    const mainTheme = useSelector(selectMainTheme);
    const { t } = useTranslation("laguageConfig");
    const gridRef = useRef(null);
    const ShiftAllocationRef = useRef();
    const dispatch = useDispatch();
    const routeParams = useParams();
    const user = useSelector(({ auth }) => auth.user);
    const agencyCode = routeParams.code !== "list" ? routeParams.code : user.data.defaultAgency
    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
    const ShiftTimeData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shift.data);
    const ShiftAllocationData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shiftallocation.data);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shiftallocation.isloading);
    const ShiftAllocationTotalCount = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shiftallocation.totalCount);
    const [newCallViolation, setNewCallViolation] = React.useState(false);
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [open, setOpen] = React.useState(false);
    const [removeID, setRemoveID] = React.useState(0);
    const [searchText, setSearchText] = React.useState("");
    let filteredData = routeParams.id !== '0' ? ShiftAllocationData.filter(x => x.department[0]._id === routeParams.id) : ShiftAllocationData
    const [data, setData] = React.useState(filteredData);
    const [countData, setCountData] = React.useState(ShiftAllocationTotalCount);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const [pageIndex, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(100);
    const [order, setOrder] = React.useState({
        direction: "asc",
        id: "StartDate",
    });
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");
    const [openRescheduleConfirmation, setReScheduleConfirmationOpen] = useState(false);
    const [shiftAllocationData, setshiftAllocationData] = useState(null);
    let colorCode = getNavbarTheme();
    useEffect(() => {
        dispatch(getShiftAllocation(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, routeParams.code));
        dispatch(getTeams("_id", "desc", 0, 10000, null, routeParams.code));
        dispatch(getShiftTime("_id", "desc", 0, 10000, null, routeParams.code));
        dispatch(getMasterDepartmentDetails("_id", "desc", 0, 10000, null, routeParams.code));
    }, []);

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeShiftAllocation(removeID, routeParams.code, pageIndex, rowsPerPage, order.id, order.direction));
            setCountData(countData - 1)
        }
    };

    const [loading, setLoading] = useState();
    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    const handleClose1 = (value) => {
        setLoading(true);
        setReScheduleConfirmationOpen(false);
        if (value) {
            setLoading(false);
            dispatch(DeleteShiftAllocationSchedule(routeParams.code, shiftAllocationData._id))
            let x = getValidationData(shiftAllocationData)
            // history.push(`/admin/ShiftAllocation/${routeParams.code}/${n._id}`);
            if (x) {
                history.push(`/admin/ShiftAllocationSchedule/${routeParams.code}/${routeParams.id}/${shiftAllocationData._id}`);
            }
            else {
                ShowErroMessage("Please fullfill the requirements related to shift type.");
            }
        }
        else {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (searchText.length !== 0) {
            setData(
                _.filter(filteredData, (item) =>
                    (item.department[0] ? item.department[0].name : '')
                        .toLowerCase()
                        .includes(searchText.toString().toLowerCase())
                )
            );
            setCountData(_.filter(filteredData, (item) =>
                (item.department[0] ? item.department[0].name : '')
                    .toLowerCase()
                    .includes(searchText.toString().toLowerCase())
            ).length)
        } else {
            setData(filteredData);
            setCountData(ShiftAllocationTotalCount)
        }
    }, [ShiftAllocationData, searchText, ShiftAllocationTotalCount]);

    useEffect(() => {
        dispatch(getShiftAllocation(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage, routeParams.code));
        dispatch(getTeams(order.id, order.direction, 0, 10000, null, routeParams.code));
        dispatch(getShiftTime(order.id, order.direction, 0, 10000, null, routeParams.code));
    }, [dispatch, pageIndex, rowsPerPage, searchText, countData, order]);

    useEffect(() => { }, [isUpdate, newCallViolation]);

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    let PagingDetails = {
        pageIndex: pageIndex,
        rowsPerPage: rowsPerPage,
        id: order.id,
        direction: order.direction,
    }
    const deleteCallViolation = (n) => {

        setOpen(true);
        //setRemoveID(n.ViolationID);
        setRemoveID(n._id);

    };

    const getValidationData = (n) => {
        let y = DepartmentData.filter(x => x._id === n.departmentID)
        let z = ShiftTimeData.filter(x => x.department[0]._id === n.departmentID)

        if (y[0].shiftType[0].noofteams === y[0].teams.filter(x => x.code == agencyCode).length && z.length === y[0].shiftType[0].noofshifts) {
            return true
        }
        else {
            return false
        }


    };

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const generateShift = (n) => {

        //Here we need to generate the shift...
        // history.push(`/admin/ShiftAllocationSchedule/${routeParams.code}/${routeParams.id}/${n._id}`);
        let x = getValidationData(n)
        // history.push(`/admin/ShiftAllocation/${routeParams.code}/${n._id}`);
        if (x) {
            history.push(`/admin/ShiftAllocationSchedule/${routeParams.code}/${routeParams.id}/${n._id}`);
        }
        else {
            ShowErroMessage("Please fullfill the requirements related to shift type.");
        }
    };

    const reSchedule = (n) => {
        setReScheduleConfirmationOpen(true);
        setshiftAllocationData(n);

    };

    const redirectViewAndEdit = (n) => {
        history.push(`/admin/ViewAndEditSchedule/${routeParams.code}/${routeParams.id}/${n._id}`);
    };

    const ActionIcons = (n) => {
        // let x = checkData(n)
        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => ShiftAllocationRef.current.handleClickOpen(x, routeParams.code, PagingDetails, true, routeParams.id)}
                                    size="large"
                                >
                                    <EditIcon />
                                </IconButton>
                            </Tooltip>
                            <Tooltip title={t("delete")}>
                                <IconButton
                                    aria-label="Back"
                                    color="inherit"
                                    onClick={() => deleteCallViolation(x)}
                                    size="large"
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                            {x.isSchedulerButton &&
                                <Tooltip title={t("generateShift")}>
                                    <IconButton
                                        aria-label="Back"
                                        color="inherit"
                                        onClick={() => generateShift(x)}
                                        size="large"
                                    >
                                        <NoteAddIcon />
                                    </IconButton>

                                </Tooltip>
                            }

                            {!x.isSchedulerButton &&
                                <>
                                    <IconButton
                                        className="Muibutton"
                                        aria-label="Back"
                                        color="inherit"
                                        size="large"
                                        onClick={() => redirectViewAndEdit(x)}
                                        title={t("viewAndEdit")}
                                    >
                                        <VisibilityIcon />
                                    </IconButton>

                                    <IconButton
                                        aria-label="Back"
                                        color="inherit"
                                        onClick={() => reSchedule(x)}
                                        size="large"
                                        title={t("reSchedule")}
                                    >
                                        <ReplayIcon />
                                    </IconButton>
                                </>
                            }

                        </div>
                    }
                </>
            );
        }
    };

    const rowData = data.map(item => {

        let StartDate = moment(item.StartDate).format('MM/DD/YYYY')
        let EndDate = moment(item.EndDate).format('MM/DD/YYYY')
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id]
            row["department"] = item.department[0] ? item.department[0].name : ''
            row["StartDate"] = StartDate
            row["EndDate"] = EndDate
            row["departmentID"] = item.department[0] ? item.department[0]._id : ''
            row["isSchedulerButton"] = item.ShiftAllocationSchedulerList.length === 0 ? true : false;
            row["Action"] = ActionIcons(item)
        });
        return row;
    });

    const nevigateBack = () => {
        if (routeParams.id == '0') {
            history.push(`/admin/agencyOptionsList/${routeParams.code}`)
        }
        else {
            history.push(`/admin/Department/${routeParams.code}`)
        }
    };

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        {routeParams.code !== "list" && (
                            <div className="flex flex-1 items-center justify-between">
                                <div className="flex items-center">
                                    <Tooltip title={t("backtoAgency")} style={{ float: "right" }}>
                                        <Stack direction="row" spacing={2}>
                                            <Button
                                                className="backButton"
                                                variant="contained"
                                                startIcon={<ArrowBackOutlinedIcon />}
                                                onClick={() => nevigateBack()}
                                            >
                                                {t("back")}
                                            </Button>
                                        </Stack>
                                    </Tooltip>
                                </div>
                            </div>
                        )}
                        <div
                            className="flex flex-1 items-center justify-between"
                            style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center">
                                <AssignmentIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    {t("shiftAllocation")}
                                </Typography>
                            </div>
                            <div className="flex flex-1 items-center justify-center px-12">
                                <StyledEngineProvider injectFirst>
                                    <ThemeProvider theme={mainTheme}>
                                        <Paper
                                            component={motion.div}
                                            sx={{ background: mainTheme.palette.mode === 'light' ? 'white' : mainTheme.palette.primary.main }}
                                            initial={{ y: -20, opacity: 0 }}
                                            animate={{ y: 0, opacity: 1, transition: { delay: 0.2 } }}
                                            className="flex items-center w-full max-w-512 px-8 py-4 rounded-8"
                                            elevation={1}
                                        >
                                            <Icon color="action">search</Icon>

                                            <Input
                                                placeholder={t("search")}
                                                className="flex flex-1 mx-8"
                                                disableUnderline
                                                fullWidth
                                                defaultValue={searchText}
                                                inputProps={{
                                                    "aria-label": "Search",
                                                }}
                                                onChange={(ev) => setSearchText(ev.target.value)}
                                            />
                                        </Paper>
                                    </ThemeProvider>
                                </StyledEngineProvider>
                            </div>
                            <div>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="CommonButton_addShiftAllocation" />} onReset={() => window.location.reload()} >
                                    <CommonButton styleClass="normal-case m-16" btnName={t("addShiftAllocation")} parentCallback={() => ShiftAllocationRef.current.handleClickOpen(Data, routeParams.code, PagingDetails, false, routeParams.id)}></CommonButton>
                                </ErrorBoundary>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                            <TablePagination
                                className="tablePaging"
                                component="div"
                                count={countData}
                                rowsPerPage={rowsPerPage}
                                page={pageIndex}
                                backIconButtonProps={{
                                    "aria-label": "Previous Page",
                                }}
                                nextIconButtonProps={{
                                    "aria-label": "Next Page",
                                }}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={rowsPerPageOptions}
                            />
                        </div>

                        <div className="igrGridClass">

                            <div>
                                <IgrGrid
                                    id="grid"
                                    autoGenerate="false"
                                    data={rowData}
                                    primaryKey="_id"
                                    ref={gridRef}
                                    height={`${gridHeight}px`}
                                    rowHeight={60}
                                    groupRowTemplate={groupByRowTemplate}
                                    filterMode="ExcelStyleFilter"
                                    moving={true}
                                    allowFiltering={false}
                                    allowAdvancedFiltering={true}
                                    allowPinning={true}
                                    pinning={pinningConfig}

                                >
                                    <IgrGridToolbar>
                                        <IgrGridToolbarActions>
                                            <IgrGridToolbarAdvancedFiltering />
                                            <IgrGridToolbarHiding />
                                            <IgrGridToolbarPinning />
                                        </IgrGridToolbarActions>
                                    </IgrGridToolbar>
                                    <IgrColumn
                                        key="department"
                                        field="department"
                                        header={t("department")}
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="StartDate"
                                        header={t("startDate")}
                                        field="StartDate"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="EndDate"
                                        header={t("endDate")}
                                        field="EndDate"
                                        resizable={true}
                                        groupable={true}
                                        sortable={true}
                                    />
                                    <IgrColumn
                                        key="action"
                                        field="action"
                                        header={t("action")}
                                        width="250px"
                                        resizable={true}
                                        pinned={true}
                                        bodyTemplate={ActionIcons}
                                    />
                                </IgrGrid>
                            </div>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                                <ConfirmationDialog
                                    id="ringtone-menu"
                                    keepMounted
                                    open={open}
                                    text={t("deleteScheduleText")}
                                    onClose={handleClose}
                                    value={removeID}
                                >
                                </ConfirmationDialog>
                            </ErrorBoundary>

                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ShiftAllocationDialog" />} onReset={() => { }} >
                                <ShiftAllocationDialog ref={ShiftAllocationRef} />
                            </ErrorBoundary>
                        </div>
                    </div>
                }
            />
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                <ConfirmationDialog
                    id="ringtone-menu"
                    keepMounted
                    open={openRescheduleConfirmation}
                    onClose={handleClose1}
                    text={t("reScheduleConfirmation")}
                />
            </ErrorBoundary>
        </>
    );
}

export default shiftAllocationDetails;
