import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Grid from "@mui/material/Grid";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setHoverID } from "../store/call911Slice";
import "../911Call.css";
import moment from "moment";

const fw_8 = {
    fontWeight: "600",
};

const fw_10 = {
    fontWeight: "1000",
    fontSize: "16px",
};

function CallInfo(props) {
    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();
    let movingValue = ""
    let subCalls = props.value.SubCall;
    const setMovingLogic = () => {
        if (subCalls.length > 0 && subCalls !== undefined) {
            for (let i = 0; i < subCalls.length; i++) {
                for (let j = i + 1; j < subCalls.length; j++) {
                    if (subCalls[i].PacketClassofService === "WPH2" && subCalls[j].PacketClassofService === "WPH2") {
                        if (subCalls[i].Packety !== subCalls[j].Packety || subCalls[i].Packetx !== subCalls[j].Packetx) {
                            movingValue = "Moving"
                            break;
                        }
                    }
                }
            }
        }
        return movingValue;
    }
    const handleCardHover = (id) => {
        dispatch(setHoverID(id))
    };
    return (
        <div>
            <Card
                className="cardborderclass"
                variant="outlined"
                style={
                    props.selectedCard === props.id
                        ? {
                            backgroundColor: props.backColor,
                            border: "solid 3px",
                            margin: "1px",
                        }
                        : { backgroundColor: props.backColor, cursor: "pointer" }
                }
                onMouseEnter={() => handleCardHover(props.id)}
                onMouseLeave={() => handleCardHover(null)}
            >
                <CardContent>
                    {(props.value.PacketClassofService === "WRLS" ||
                        props.value.PacketClassofService === "WPH1") && (
                            <Grid container style={{ fontSize: "1.15rem" }}>
                                <Grid item xs={4}>
                                    <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                                </Grid>
                                <Grid item xs={4}>
                                    <span style={{ fw_8 }}>
                                        {" "}
                                        {moment(new Date(props.value.PacketCallReceivedDT)).format("HH:mm:ss MM/DD/YY")}
                                    </span>
                                </Grid>
                                <Grid item xs={2}>
                                    <span style={fw_8}> {props.value.PacketClassofService}</span>
                                </Grid>
                                <Grid item xs={2}>
                                    <span style={fw_8}>
                                        {/* {props.value.SubCall &&
                      props.value.SubCall.filter(
                        (call) =>
                          call.PacketCustomerName.trim() !==
                          props.value.PacketCustomerName.trim()
                      ).length > 0 &&
                      "Moving"} */}
                                        {setMovingLogic()}
                                    </span>
                                </Grid>
                                <Grid item xs={12}>
                                    {t("towerInfo")}:{" "}
                                    <span style={fw_8}>{props.value.PacketCustomerName}</span>
                                </Grid>
                                <Grid item xs={12}>
                                    <span style={fw_8}>
                                        {props.value.PacketStreetAddress}, {props.value.PacketCity},{" "}
                                        {props.value.PacketState}
                                    </span>
                                </Grid>
                                <Grid item xs={6}>
                                    {t("lat")}: <span style={fw_8}>{props.value.Packety}</span>
                                </Grid>
                                <Grid item xs={6}>
                                    {t("long")}: <span style={fw_8}>{props.value.Packetx}</span>
                                </Grid>
                            </Grid>
                        )}
                    {(props.value.PacketClassofService === "WPH2" ||
                        props.value.PacketClassofService === "TLMA" ||
                        props.value.PacketClassofService === "TELM") && (
                            <Grid container style={{ fontSize: "1.15rem" }}>
                                <Grid item xs={4}>
                                    <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                                </Grid>
                                <Grid item xs={4}>
                                    <span style={fw_8}>
                                        {" "}
                                        {/* {props.value.PacketCallTime}:
                                        {new Date(props.value.PacketCallReceivedDT).getSeconds()}{" "}
                                        {props.value.PacketCallDate} */}
                                        {moment(new Date(props.value.PacketCallReceivedDT)).format("HH:mm:ss MM/DD/YY")}
                                    </span>
                                </Grid>
                                <Grid item xs={2}>
                                    <span style={fw_8}> {props.value.PacketClassofService}</span>
                                </Grid>
                                <Grid item xs={2}>
                                    <span style={fw_8}>
                                        {/* {props.value.SubCall &&
                      props.value.SubCall.filter(
                        (call) =>
                          call.Packety !== props.value.Packety ||
                          call.Packetx !== props.value.Packetx
                      ).length > 0 &&
                      "Moving"} */

                                        }
                                        {setMovingLogic()}
                                    </span>
                                </Grid>
                                <Grid item xs={12}>
                                    {t("towerInfo")}:{" "}
                                    <span style={fw_8}>{props.value.PacketCustomerName}</span>
                                </Grid>
                                <Grid item xs={12}>
                                    <span style={fw_8}>
                                        {props.value.PacketStreetAddress}, {props.value.PacketCity},{" "}
                                        {props.value.PacketState}
                                    </span>
                                </Grid>
                                <Grid item xs={6}>
                                    {t("lat")}: <span style={fw_10}>{props.value.Packety}</span>
                                </Grid>
                                <Grid item xs={6}>
                                    {t("long")}: <span style={fw_10}>{props.value.Packetx}</span>
                                </Grid>
                                <Grid item xs={6}>
                                    {t("confidence")}:{" "}
                                    <span
                                        style={
                                            parseFloat(props.value.PacketCNF) >= 90
                                                ? { fontWeight: "1000" }
                                                : { fontWeight: "600" }
                                        }
                                    >
                                        {parseFloat(props.value.PacketCNF)}%
                                    </span>
                                </Grid>
                                <Grid item xs={6}>
                                    {t("radius")}:{" "}
                                    <span style={fw_8}> {props.value.PacketUNC + " Meters"}</span>
                                </Grid>
                            </Grid>
                        )}
                    {props.value.PacketClassofService !== "WRLS" &&
                        props.value.PacketClassofService !== "WPH1" &&
                        props.value.PacketClassofService !== "WPH2" &&
                        props.value.PacketClassofService !== "TLMA" &&
                        props.value.PacketClassofService !== "TELM" && (
                            <Grid container style={{ fontSize: "1.15rem" }}>
                                <Grid item xs={4}>
                                    <span style={fw_8}>{props.value.PacketCallingPhone} </span>
                                </Grid>
                                <Grid item xs={4}>
                                    <span style={fw_8}>
                                        {" "}
                                        {/* {props.value.PacketCallTime}:
                                        {new Date(props.value.PacketCallReceivedDT).getSeconds()}{" "}
                                        {props.value.PacketCallDate} */}
                                        {moment(new Date(props.value.PacketCallReceivedDT)).format("HH:mm:ss MM/DD/YY")}
                                    </span>
                                </Grid>
                                <Grid item xs={2}>
                                    <span style={fw_8}> {props.value.PacketClassofService}</span>
                                </Grid>
                                <Grid item xs={12}>
                                    {t("caller")}:{" "}
                                    <span style={fw_8}>{props.value.PacketCustomerName}</span>
                                </Grid>
                                <Grid item xs={12}>
                                    {t("location")}:{" "}
                                    <span style={fw_8}>
                                        {props.value.PacketStreetNumber}{" "}
                                        {props.value.PacketStreetAddress},{" "}
                                        {props.value.PacketLocationInfo},{" "}
                                        {props.value.PacketCity}, {props.value.PacketState}
                                    </span>
                                </Grid>
                                <Grid item xs={6}>
                                    {t("lat")}: <span style={fw_8}>{props.value.Packety}</span>
                                </Grid>
                                <Grid item xs={6}>
                                    {t("long")}:{" "}
                                    <span style={fw_8}>{props.value.Packetx}</span>
                                </Grid>
                            </Grid>
                        )}
                </CardContent>
                {props.value.PacketClassofService !== "WRLS" &&
                    props.value.PacketClassofService !== "WPH1" &&
                    props.children}
            </Card>
        </div>
    );
}

export default CallInfo;