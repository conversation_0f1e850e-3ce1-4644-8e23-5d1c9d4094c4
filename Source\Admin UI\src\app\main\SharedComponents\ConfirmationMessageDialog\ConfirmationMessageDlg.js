import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>utton, <PERSON><PERSON>, DialogContent, DialogTitle, Dialog, DialogActions, Switch } from '@mui/material';
import { makeStyles } from '@mui/styles';
import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
function ConfirmationMessageDlg(props) {
    const { onClose, value: valueProp, text, open, ...other } = props;
    const { t } = useTranslation("laguageConfig");
   
    const handleOk = () => {
        onClose(true);
    };


    return (
        <Dialog
            disableEscapeKeyDown
            maxWidth="xs"
            aria-labelledby="confirmation-dialog-title"
            open={open}
            {...other}>
            <DialogTitle id="confirmation-dialog-title" color="primary">{t("confirm")}</DialogTitle>
            <DialogContent dividers>
                <div className='m-16'>
                    {props.text}
                </div>
            </DialogContent>
            <DialogActions>
                <Button onClick={handleOk} variant="contained" color="primary">
                    {t("ok")}
                </Button>
            </DialogActions>
        </Dialog>
    );
}

export default ConfirmationMessageDlg;
