import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Card, CardContent, FormControl, Grid, TextField } from "@mui/material";
import AppHeaderBar from "../../components/AppHeaderBar";
import { Controller, useForm } from "react-hook-form";
import { Box } from "@mui/system";
import history from '@history';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import { makeStyles } from "@mui/styles";
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from "react-redux";
import { addIntersectionPointData, setIntersectionPointDetailById, updateIntersectionPointData, } from "../../store/intersectionPointSlice";
import CircularProgressLoader from "src/app/main/SharedComponents/CircularProgressLoader/CircularProgressLoader";
import { getCountryList, getCityCountyStateZipCode, setCountyList } from "../../store/masterAddressSlice";
import { isEmptyOrNull } from "src/app/main/utils/utils";
import { yupResolver } from "@hookform/resolvers/yup";
import CommonAutocomplete, { handleSelectKeyDown } from "src/app/main/SharedComponents/ReuseComponents/CommonAutocomplete";
import { showMessage } from "app/store/fuse/messageSlice";

const useStyles = makeStyles((theme) => ({
    formControl: {
        minWidth: 120,
        width: "100%",
    },
    selectEmpty: {
        marginTop: theme.spacing(2),
    },
    paperHeight: {
        maxHeight: 'none',
        overflowY: 'visible',
    },
    listbox: {
        maxHeight: 230,
        overflowY: 'auto',
    },
    backdrop: { zIndex: theme.zIndex.drawer + 1, color: '#fff', },
}));

const defaultValues = {
    country: [],
    rowOneStreet1: '',
    rowOneStreet2: '',
    rowOneStreet3: '',
    rowOneStreet4: '',
    city: [],
    state: [],
    zip: [],
    zipPlus: '',
    latitude: '',
    longitude: '',
    elevation: '',
    esn: '',
    msagCommunity: '',
    county: [],
    Municipality: '',
    PoliceZone: '',
    FireZone: ''
}

const IntersectionAdd = () => {

    const { t } = useTranslation("laguageConfig");
    const classes = useStyles();
    const dispatch = useDispatch();
    const { control, setValue, formState, handleSubmit, } = useForm({
        mode: 'onChange',
        defaultValues,
        // resolver: yupResolver(schema),
    });
    const { errors } = formState;

    const textFieldRef = useRef(null); // Ref for the third text field
    const individualIntersectionPointDetails = useSelector(({ administration }) => administration.intersectionPointSlice.intersectionPointDetailById);
    const isLoading = useSelector(({ administration }) => administration.intersectionPointSlice.isLoading);
    const countryList = useSelector(({ administration }) => administration.masterAddressSlice.countryList);
    const zipCodeList = useSelector(({ administration }) => administration.masterAddressSlice.zipcodeList);
    const cityList = useSelector(({ administration }) => administration.masterAddressSlice.cityList);
    const countyList = useSelector(({ administration }) => administration.masterAddressSlice.countyList);
    const stateList = useSelector(({ administration }) => administration.masterAddressSlice.stateList);
    const selectedcounty = useSelector(({ administration }) => administration.masterAddressSlice.selectedCounty)
    const selectedCountyStateCode = useSelector(({ administration }) => administration.masterAddressSlice.selectedCountyStateCode);

    const [pageType, setPageType] = useState("Add");
    const [country, setCountry] = useState(null);
    const [city, setCity] = useState(null);
    const [state, setState] = useState(null);
    const [zip, setZip] = useState(null);
    const [county, setCounty] = useState(null);
    const [filteredCityList, setFilteredCityList] = useState(cityList ?? []);
    const [filteredCountyList, setFilteredCountyList] = useState(countyList ?? []);
    const [filteredZipCodeList, setFilteredZipCodeList] = useState(zipCodeList ?? []);
    const [countyState, setCountyState] = React.useState(null);
    const [dropdownOpen, setDropdownOpen] = useState(false);

    const [selectedCounty, setSelectedCounty] = React.useState(null);
    const countryAutocompleteRef = useRef(null);

    useEffect(() => {
        if (selectedcounty !== null && selectedCountyStateCode !== null) {
            setSelectedCounty(selectedcounty);
            setCountyState(selectedCountyStateCode);
            dispatch(getCityCountyStateZipCode(selectedcounty, selectedCountyStateCode));
        }
    }, [selectedcounty, selectedCountyStateCode]);

    useEffect(() => {
        if (individualIntersectionPointDetails !== null) {
            setPageType("update");
            setValue("rowOneStreet1", individualIntersectionPointDetails.Str1);
            setValue("rowOneStreet2", individualIntersectionPointDetails.Str2);
            setValue("rowOneStreet3", individualIntersectionPointDetails.Str3);
            setValue("rowOneStreet4", individualIntersectionPointDetails.Str4);
            setValue("zipPlus", individualIntersectionPointDetails.PostCodeEx);
            setValue("latitude", individualIntersectionPointDetails.LAT_Y);
            setValue("longitude", individualIntersectionPointDetails.LON_X);
            setValue("elevation", individualIntersectionPointDetails.Elevation);
            setValue("esn", individualIntersectionPointDetails.ESN);
            setValue("msagCommunity", individualIntersectionPointDetails.Community);
            setValue("Municipality", individualIntersectionPointDetails.Municipality);
            setValue("PoliceZone", individualIntersectionPointDetails.PoliceZone);
            setValue("FireZone", individualIntersectionPointDetails.FireZone);
            let Country = countryList.find((x) => x.name === individualIntersectionPointDetails.Country)
            setCountry(Country);
            let city = cityList.find((x) => x.CityName === individualIntersectionPointDetails.City)
            setCity(city);
            let State = stateList.find((x) => x.StateCode === individualIntersectionPointDetails.State)
            setState(State);
            let Post_Code = zipCodeList.find((x) => x.ZipCode === individualIntersectionPointDetails.Zip5)
            setZip(Post_Code);
            let county = countyList.find((x) => x.CountyName === individualIntersectionPointDetails.County)
            setCounty(county);

        }
    }, [individualIntersectionPointDetails, countryList, cityList, stateList, zipCodeList, countyList])

    const handleCountryChange = (event, newValue) => setCountry(newValue);
    const handleCityChange = (event, newValue) => setCity(newValue);
    const handleStateChange = (event, newValue) => {
        if (newValue !== null) {
            setState(newValue);
            //Set city and county list according to state
            setFilteredCityList(cityList.filter((x) => x.StateCode === newValue.StateCode));
        } else {
            setState(null);
            setFilteredCityList(cityList);
            setFilteredCountyList(countyList);
        }
    }
    const handleZipChange = (event, value) => {
        if (value !== null) {
            setZip(value);
            let city = cityList.find((x) => x.CityName === value.CityName);
            setCity(city);
        } else {
            setZip(null);
            setCity(null);
        }
    }
    const handleCountyChange = (event, newValue) => setCounty(newValue);

    const navigateBack = () => {
        history.push('/admin/intersectionPoint');
    }

    useEffect(() => {
        dispatch(getCountryList());
    }, []);

    useEffect(() => {
        setFilteredCityList(cityList);
        setFilteredCountyList(countyList);
        setFilteredZipCodeList(zipCodeList);
        var selectedAddressCounty = countyList.find((x) => x.CountyName === selectedCounty);
        var selectedAddressCountyState = stateList.find((x) => x.StateCode === countyState);
        if (selectedAddressCounty !== undefined && selectedAddressCounty !== null) {
            setCounty(selectedAddressCounty);
        }
        if (selectedAddressCountyState !== undefined && selectedAddressCountyState !== null) {
            setFilteredCityList(cityList.filter((x) => x.StateCode === countyState));
            setState(selectedAddressCountyState);
        }
    }, [cityList, countyList, zipCodeList, stateList]);

    const handleKeyDown = (event) => {
        if (event.key === 'Tab') {
            event.preventDefault(); // Prevent default tab behavior
            textFieldRef.current.children[1].children[0].focus(); // Focus the third text field
        }
    };

    const backButton = (
        <Button
            type="button"
            variant="contained"
            color='secondary'
            className="normal-case m-16"
            aria-label="Back"
            value="legacy"
            onClick={navigateBack}>
            {t('back')}
        </Button>
    )

    const submitButton = (
        <Button
            type="submit"
            variant="contained"
            color="secondary"
            className="normal-case m-16"
            aria-label="REGISTER"
            value="legacy">
            {pageType === "Add" ? t('save') : t('update')}
        </Button>
    )

    const cleanStreetNames = (street1, street2, street3, street4) => {
        const streets = [street1, street2, street3, street4];
        const cleanedStreets = streets
            .filter(street => street && street.trim() !== '')
            .map(street => street.trim());

        const duplicateStreetsRemoved = [...new Set(cleanedStreets)].sort();

        if(duplicateStreetsRemoved.length > 1) {
            return duplicateStreetsRemoved;
        } else {
            dispatch(showMessage({
                message: t("intersectionUniqueStreetName"), autoHideDuration: 3000,
                anchorOrigin: {
                    vertical: 'top',
                    horizontal: 'center'
                },
                variant: 'warning'
            }));
            return null;
        }
    }

    const onSubmit = (model) => {

        const streets = cleanStreetNames(model.rowOneStreet1, model.rowOneStreet2, model.rowOneStreet3, model.rowOneStreet4);
        if(!streets) return;

        const data = {
            _id: "0",
            Country: isEmptyOrNull(country) ? "" : country.name,
            Str1: isEmptyOrNull(streets[0]) ? "" : streets[0],
            Str2: isEmptyOrNull(streets[1]) ? "" : streets[1],
            Str3: isEmptyOrNull(streets[2]) ? "" : streets[2],
            Str4: isEmptyOrNull(streets[3]) ? "" : streets[3],
            City: isEmptyOrNull(city) ? "" : city.CityName,
            State: isEmptyOrNull(countyState) ? null : countyState,
            Zip5: isEmptyOrNull(zip) ? "" : zip.ZipCode,
            PostCodeEx: isEmptyOrNull(model.zipPlus) ? "" : model.zipPlus,
            LAT_Y: isEmptyOrNull(model.latitude) ? "" : model.latitude,
            LON_X: isEmptyOrNull(model.longitude) ? "" : model.longitude,
            Elevation: isEmptyOrNull(model.elevation) ? "" : model.elevation,
            ESN: isEmptyOrNull(model.esn) ? "" : model.esn,
            Community: isEmptyOrNull(model.msagCommunity) ? "" : model.msagCommunity,
            County: isEmptyOrNull(selectedCounty) ? "" : selectedCounty,
            Municipality: isEmptyOrNull(model.Municipality) ? "" : model.Municipality,
            PoliceZone: isEmptyOrNull(model.PoliceZone) ? "" : model.PoliceZone,
            FireZone: isEmptyOrNull(model.FireZone) ? "" : model.FireZone,
            StreetNames: Array.isArray(streets) && streets.length > 1 ? streets.join("/") : "",
        }

        if (individualIntersectionPointDetails !== null) {
            data._id = model._id !== undefined ? model._id : individualIntersectionPointDetails._id;
            dispatch(updateIntersectionPointData(data));
        } else {
            dispatch(addIntersectionPointData(data));
        }
        navigateBack();
    }

    const handleDropdownClose = () => {
        setDropdownOpen(false);
    };

    const handleDropdownOpen = () => {
        setDropdownOpen(true);
    };

    useEffect(() => {
        setTimeout(() => {
            const countryInput = countryAutocompleteRef.current?.querySelector('input');
            if (countryInput) {
                countryInput.focus();
                setDropdownOpen(true);
            }
        }, 100);
    }, []);

    return (
        <div className="p-16">
            {isLoading && <CircularProgressLoader loading={isLoading} />}
            <Card className=" m-16 rounded-8 shadow">
                <AppHeaderBar headerText={`${t(pageType)} ${t('viewIntersection')}`} />
                <CardContent style={{ overflowX: "scroll", height: "90%" }}>
                    <div className="w-full p-16">
                        <form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)} autoSave={false} autoComplete={false} >
                            <Box component="fieldset" sx={{ border: '1px solid #ccc', padding: 2, borderRadius: '5px', marginBottom: 2 }}>
                                <legend style={{ padding: '0 10px', fontSize: '1.3rem' }}>{t("intersectionPointDetail")}</legend>
                                <Grid container spacing={1} style={{ paddingLeft: 8 }}>
                                    <Grid container spacing={1} className="mt-0 flex flex-row" style={{ marginBottom: 18 }}>
                                        <Grid item xs={12} sm={1.7} md={1.7} lg={1.7} xl={1.7}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_Country" />} onReset={() => { }}>
                                                <div ref={countryAutocompleteRef}>
                                                    <CommonAutocomplete
                                                        parentCallback={handleCountryChange}
                                                        options={countryList}
                                                        value={country}
                                                        fieldName={t("country")}
                                                        optionLabel={"name"}
                                                        onKeyDown={handleSelectKeyDown}
                                                        open={dropdownOpen}
                                                        onClose={handleDropdownClose}
                                                        onOpen={handleDropdownOpen}
                                                    />
                                                </div>
                                            </ErrorBoundary>
                                        </Grid>
                                    </Grid>
                                    <Grid container spacing={1} className="mb-4 mt-0 flex flex-row">
                                        <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                            <Controller
                                                name="rowOneStreet1"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t('street1')}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                            <Controller
                                                name="rowOneStreet2"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("street2")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                            <Controller
                                                name="rowOneStreet3"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("street3")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                            <Controller
                                                name="rowOneStreet4"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("street4")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        onKeyDown={handleKeyDown} // Add key down event handler
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                    </Grid>
                                    <Grid container spacing={1} className="mb-4 mt-0 flex flex-row">
                                        <Grid item xs={12} sm={3} md={3} lg={3} xl={3}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_City" />} onReset={() => { }}>
                                                <CommonAutocomplete parentCallback={handleCityChange} options={filteredCityList} value={city} fieldName={t("cityPostalComm")} optionLabel={"CityName"} onKeyDown={handleSelectKeyDown} />
                                            </ErrorBoundary>
                                        </Grid>
                                        <Grid item xs={12} sm={1} md={1} lg={1} xl={1}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_State" />} onReset={() => { }}>
                                                <CommonAutocomplete disabled={true} parentCallback={handleStateChange} options={stateList} value={state} fieldName={t("state")} optionLabel={"StateCode"} onKeyDown={handleSelectKeyDown} />
                                            </ErrorBoundary>
                                        </Grid>
                                        <Grid item xs={12} sm={1.5} md={1.5} lg={1.5} xl={1.5}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_Zip" />} onReset={() => { }}>
                                                <CommonAutocomplete parentCallback={handleZipChange} options={filteredZipCodeList} value={zip} fieldName={t("zipPostCode")} optionLabel={"ZipCode"} onKeyDown={handleSelectKeyDown} ref={textFieldRef} />
                                            </ErrorBoundary>
                                        </Grid>
                                        <Grid item xs={12} sm={1.6} md={1.5} lg={1.5} xl={1.5}>
                                            <Controller
                                                name="zipPlus"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("zipPostCodeEx")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                    </Grid>
                                    <Grid container spacing={1} className="mb-4 mt-0 flex flex-row">
                                        <Grid item xs={12} sm={1.5} md={1.5} lg={1.5} xl={1.5}>
                                            <Controller
                                                name="latitude"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("latitude")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={1.5} md={1.5} lg={1.5} xl={1.5}>
                                            <Controller
                                                name="longitude"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("longitude")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={1.5} md={1.5} lg={1.5} xl={1.5}>
                                            <Controller
                                                name="elevation"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("elevation")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={1} md={1} lg={1} xl={1}>
                                            <Controller
                                                name="esn"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("esn")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
                                            <Controller
                                                name="msagCommunity"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("msagCommunity")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={1.5} md={1.5} lg={1.5} xl={1.5}>
                                            <ErrorBoundary
                                                FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_County" />} onReset={() => { }}>
                                                <CommonAutocomplete disabled={true} parentCallback={handleCountyChange} options={filteredCountyList} value={county} fieldName={t("COUNTY")} optionLabel={"CountyName"} onKeyDown={handleSelectKeyDown} />
                                            </ErrorBoundary>
                                        </Grid>
                                    </Grid>
                                    <Grid container spacing={1} className="mb-4 mt-0 flex flex-row">
                                        <Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
                                            <Controller
                                                name="Municipality"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("municipality")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
                                            <Controller
                                                name="PoliceZone"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("policeZone")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
                                            <Controller
                                                name="FireZone"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        className="mb-16  w-full"
                                                        label={t("fireZone")}
                                                        type="text"
                                                        error={!!errors.name}
                                                        helperText={errors?.name?.message}
                                                        variant="outlined"
                                                        fullWidth
                                                        sx={{ mb: 2 }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </Box>
                            <div className="mx-auto">
                                {submitButton}
                                {backButton}
                            </div>
                        </form>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default IntersectionAdd;