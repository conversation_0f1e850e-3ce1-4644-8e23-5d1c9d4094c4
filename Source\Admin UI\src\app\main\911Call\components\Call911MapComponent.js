import SelectedCallInfo from "./SelectedCallInfo";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import { useTranslation } from "react-i18next";
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { setSelectedCallData, setSuccessFalse } from "../store/call911Slice";
import { useDispatch, useSelector } from "react-redux";
import { Icon } from "@mui/material";
import React, { useState, useEffect, useRef } from "react";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import "video-react/dist/video-react.css";
import IconButton from "@mui/material/IconButton";
import { isMobile } from "react-device-detect";
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ButtonComponent from "./ButtonsComponent";
import VectorTileLayer from "react-leaflet-vector-tile-layer";
import { getUniqueArray, isEmptyOrNull, isEmptyOrNullWithTrim, useWindowResizeHeight } from "../../utils/utils";
import '../911Call.css'
import { MapContainer, TileLayer, Marker, CircleMarker, Popup, Tooltip, Polyline, Rectangle, useMap } from 'react-leaflet';


const mobilemapMode = {
    position: "absolute",
    padding: 6,
    zIndex: 401,
    top: 225,
    border: "2px solid rgba(0,0,0,0.2)",
    left: "5.5%",
    background: "white",
};

let defaultlatlan = {
    MaxX: "-90.064005271999974",
    MaxY: "35.441653765000069",
    MinX: "-90.506531612999936",
    MinY: "34.826566366000065"
}

function Call911MapComponent(props) {
    const classes = props.classes;
    const dispatch = useDispatch();
    const mapRef = useRef();
    const { t } = useTranslation("laguageConfig");
    const callsByNumber = useSelector(({ call911 }) => call911.call911.callsByNumber);
    const selectedCallData = useSelector(({ call911 }) => call911.call911.selectedCall);
    const packet911CALLTotalCount = useSelector(({ call911 }) => call911.call911.packet911calltotalCount);
    const packet911CallsGroup = useSelector(({ call911 }) => call911.call911.group);
    const codeList = useSelector(({ classOfService }) => classOfService.classOfService.data);
    const update = useSelector(({ call911 }) => call911.call911.update);
    const updatedData = useSelector(({ call911 }) => call911.call911.updatedData);
    const [satelliteView, setSatelliteView] = React.useState(false);
    const [nearestAddressMarkers1, setNearestAddressMarkers1] = React.useState([]);
    const navbarTheme = useSelector(selectNavbarTheme);
    const ShowAllCalls = localStorage.getItem("ShowAllCalls");
    const [toggleValue, setToggleValue] = React.useState((ShowAllCalls !== null && ShowAllCalls !== undefined) ? JSON.parse(ShowAllCalls) : false);
    const [expanded, setExpanded] = React.useState("currentCall");
    const handlePanelChange = (panel) => (event, newExpanded) => { setExpanded(newExpanded ? panel : false); };
    const [toggleShowRectangle, setShowRectangle] = React.useState(false)
    const [mapType, setMapType] = React.useState(0)
    const windowHeight = useWindowResizeHeight(window.innerHeight, 130);
    const mapHeight = useWindowResizeHeight(window.innerHeight, 275);

    const divstyle = {
        overflow: "auto",
        height: windowHeight,
    };


    const mapdivstyle = {
        overflow: "auto",
        height: mapHeight,
    };



    const [styleUrl, setStyleUrl] = useState(require('../../../stadiaMapJson/osm_bright.json'));

    // // Commented Since there is no dark mode in call 911 maps
    // useEffect(() => {
    //     if (darkMode) {
    //         setStyleUrl(require('../../../stadiaMapJson/osm_bright_dark.json'));
    //     } else {
    //         setStyleUrl(require('../../../stadiaMapJson/osm_bright.json'));
    //     }
    // }, [darkMode]);

    const nav_css = {
        backgroundColor: navbarTheme.palette.primary.main,
        color: navbarTheme.palette.primary.contrastText,
    };

    useEffect(() => {
        if (selectedCallData._id !== undefined && codeList.length > 0) {
            if (toggleValue) {
                setMapType(1);
            } else {
                setMapType(0);
            }
        }
    }, [satelliteView, packet911CallsGroup, codeList]);

    useEffect(() => {
        if (update) {
            if (props.callData.length > 0) {
                let x = updatedData;
                let data = [...x].sort((a, b) => b.CAD911CallID - a.CAD911CallID);
                props.setNewCurrentValue(data);
                if (toggleValue) {
                    setMapType(1);
                }
                if (props.callData.length >= packet911CALLTotalCount) {
                    props.setHasMore(false);
                }
            }
        }
        else {
            if (props.callData.length > 0) {
                let uniqueArray = getUniqueArray([...props.current, ...props.callData])
                // Update the state with the unique array
                props.setNewCurrentValue(uniqueArray);
                if (toggleValue) {
                    setMapType(1);
                }
                if (props.callData.length >= packet911CALLTotalCount) {
                    props.setHasMore(false);
                }
            }
        }
    }, [update, props.callData]);

    useEffect(() => {
        setNearestAddressMarkers1(props.selectedAddress)
    }, [props.selectedAddress]);

    function getDefaultIcon() {
        let url = require("./otherCallIcon.png")
        var customIcon = L.icon({
            iconUrl: url,
            iconSize: [30, 40]
        });
        return customIcon;
    }

    var customIcon = L.icon({
        iconUrl: require("./white-red.png"),
        iconSize: [30, 40]
    });

    useEffect(() => {
        setNearestAddressMarkers1([]);
    }, [selectedCallData]);

    const handleToggleChange = (event) => {
        setToggleValue(event.target.checked);
        localStorage.setItem("ShowAllCalls", event.target.checked);
        if (event.target.checked) {
            setMapType(1);
            createRect();
        } else {
            setMapType(0);
        }
    };

    const handleRectangleChangeChange = (event) => {
        setShowRectangle(event);
    };

    useEffect(() => {
        if (selectedCallData.length !== 0) {
            if (toggleValue) {
                setMapType(1);
            }
            else {
                setMapType(0);
            }
        }
    }, [toggleShowRectangle]);

    // 03 June 2024 - Use New React Leaflet Map for Call 911 Map...
    const lineNewStyle = { color: 'blue', dashArray: '5, 10', weight: 6 };

    useEffect(() => {
        if (!isEmptyOrNull(selectedCallData.Packety) && !isEmptyOrNull(selectedCallData.Packetx)) {
            const map = mapRef.current;
            if (map) {
                map.setView([selectedCallData.Packety, selectedCallData.Packetx], 10);
            }
        }
    }, [selectedCallData]);

    useEffect(() => {
        if (!isEmptyOrNull(props.current) && mapType == 1) {
            createRect();
        }
    }, [mapType]);

    function createRect() {
        const bounds = props.current.map((item) => [item.Packety, item.Packetx]);
        if (bounds.length > 1) {
            const uniqueBounds = Array.from(new Set(bounds.map(JSON.stringify))).map(JSON.parse);
            if (uniqueBounds.length > 1) {
                const map = mapRef.current;
                map.fitBounds(uniqueBounds, { padding: [10, 10] });
            }
            return uniqueBounds;
        }
    }

    function returnFlag(selectedCall) {
        if (
            selectedCall.PacketClassofService === "WPH2" ||
            selectedCall.PacketClassofService === "TLMA" ||
            selectedCall.PacketClassofService === "WPH1" ||
            selectedCall.PacketClassofService === "VMBL" ||
            selectedCall.PacketClassofService === "TELM") {
            return true;
        }
        else {
            return false;
        }
    }

    function returnDisplayString(name) {
        let address = name.PacketClassofService === "VOIP"
            ? name.PacketStreetNumber + " " + name.PacketStreetAddress
            : name.PacketStreetAddress;
        let string = `${name.PacketCallingPhone}, ${address}, ${name.PacketCity}, ${name.PacketState}`;
        return string || "";
    }

    function createLineDraw(call) {
        var latlngs = [];
        call.SubCall.map((loc) => {
            // Added RD for line draw 19-10-2023
            if (call.PacketClassofService === "WRLS") {
                if (loc.PacketClassofService === "WPH2") {
                    if (!isEmptyOrNullWithTrim(loc.Packety) && !isEmptyOrNullWithTrim(loc.Packetx)) {
                        latlngs.push([parseFloat(loc.Packety), parseFloat(loc.Packetx)]);
                    }
                }
            }
        });
        latlngs.push([parseFloat(call.Packety), parseFloat(call.Packetx)]);
        return latlngs;
    }

    function createIcon(selectedCall) {
        let icon =
            codeList.find((x) => x.code === selectedCall.PacketClassofService) !== undefined
                ?
                codeList.find((x) => x.code === selectedCall.PacketClassofService).iconPic
                : getDefaultIcon()
        return icon;
    }

    function getCustomIcon(selectedCall) {
        return L.icon({
            iconUrl: createIcon(selectedCall),
            iconSize: [30, 40], // Customize the size of the icon as needed
        });
    }

    function createShowAllCallsLineDraw() {
        const lines = [];
        let towerCalls = props.current.filter(name => name.PacketClassofService === "WRLS");
        towerCalls.forEach(tower => {
            const mainCallLatitude = parseFloat(tower.Packety);
            const mainCallLongitude = parseFloat(tower.Packetx);

            const wph2Calls = tower.SubCall.filter(subCall => subCall.PacketClassofService === "WPH2");
            let isFirstWPH2 = true;
            wph2Calls.forEach((wph2Call, index) => {
                if (!isEmptyOrNullWithTrim(wph2Call.Packety) && !isEmptyOrNullWithTrim(wph2Call.Packetx)) {
                    const wph2CallLatitude = parseFloat(wph2Call.Packety);
                    const wph2CallLongitude = parseFloat(wph2Call.Packetx);
                    // Create a line connecting the tower call to the 1st WPH2 subcall location
                    if (isFirstWPH2) {
                        lines.push([
                            [mainCallLatitude, mainCallLongitude],
                            [wph2CallLatitude, wph2CallLongitude]
                        ]);
                        isFirstWPH2 = false;
                    }
                    // If there is another WPH2 subcall after the current one, create a line to connect them
                    if (index < wph2Calls.length - 1) {
                        const nextWPH2Call = wph2Calls[index + 1];
                        const nextWPH2CallLatitude = parseFloat(nextWPH2Call.Packety);
                        const nextWPH2CallLongitude = parseFloat(nextWPH2Call.Packetx);

                        // Create a line connecting the current WPH2 subcall location to the next one
                        lines.push([
                            [wph2CallLatitude, wph2CallLongitude],
                            [nextWPH2CallLatitude, nextWPH2CallLongitude]
                        ]);
                    }
                }
            });
        });

        return lines;
    }

    return (
        <div>
            {isMobile ? (
                <>
                    <Accordion
                        expanded={expanded === "currentCall"}
                        onChange={handlePanelChange("currentCall")}
                    >
                        <AccordionSummary
                            expandIcon={<ExpandMoreIcon style={nav_css} />}
                            aria-controls="panel2a-content"
                            id="panel2a-header"
                            style={nav_css}
                        >
                            <Typography className={classes.title} variant="h3">
                                {t("selectedCall")} -{" "}
                                {selectedCallData.PacketCallingPhone}
                            </Typography>
                        </AccordionSummary>
                        <AccordionDetails >
                            <Grid item xs={12}>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectedCallInfo" />}
                                    onReset={() => { }} >
                                    <SelectedCallInfo
                                        value={selectedCallData}
                                        newCall={props.newCallOpen}
                                        selectedCard={props.selectedCard}
                                        onClose={() => props.setNewCallOpen(false)}
                                    ></SelectedCallInfo>
                                </ErrorBoundary>
                                <IconButton
                                    aria-label="Satellite View"
                                    style={mobilemapMode}
                                    onClick={() => setSatelliteView(!satelliteView)}
                                >
                                    <Icon>satellite</Icon>
                                </IconButton>
                                <ErrorBoundary
                                    FallbackComponent={(props) => <ErrorPage {...props} componentName="ButtonComponent" />}
                                    onReset={() => { }} >
                                    <ButtonComponent setCounty={props.setCounty} handleToggleChange={handleToggleChange}
                                        toggleValue={toggleValue} classes={classes}
                                    ></ButtonComponent>
                                </ErrorBoundary>
                            </Grid>
                        </AccordionDetails>
                    </Accordion>
                </>
            ) : (
                <>
                    <Card className={classes.cardRoot} variant="outlined">
                        <CardContent style={nav_css} className="p-13">
                            <Typography className={classes.title} variant="h3">
                                {t("currentSelectedCall")} -{" "}
                                {selectedCallData.PacketCallingPhone}
                            </Typography>
                        </CardContent>
                    </Card>
                    <div style={divstyle}>
                        <div>
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="SelectedCallInfo" />}
                                onReset={() => { }} >
                                <SelectedCallInfo
                                    value={selectedCallData}
                                    newCall={props.newCallOpen}
                                    selectedCard={props.selectedCard}
                                    onClose={() => props.setNewCallOpen(false)}
                                    current={props.current}
                                ></SelectedCallInfo>
                            </ErrorBoundary>
                            {!isEmptyOrNull(selectedCallData) &&
                                <MapContainer ref={mapRef} center={!isEmptyOrNull(selectedCallData.Packety) && !isEmptyOrNull(selectedCallData.Packetx) ? [selectedCallData.Packety, selectedCallData.Packetx] : [defaultlatlan.MinY, defaultlatlan.MinX]} zoom={10} style={mapdivstyle}>
                                    <VectorTileLayer
                                        styleUrl={styleUrl}
                                    />
                                    {mapType == 0 ?
                                        <>
                                            < Marker position={!isEmptyOrNull(selectedCallData.Packety) && !isEmptyOrNull(selectedCallData.Packetx) ? [selectedCallData.Packety, selectedCallData.Packetx] : [defaultlatlan.MinY, defaultlatlan.MinX]} icon={getCustomIcon(selectedCallData)}>
                                                <Tooltip direction="top" offset={[0, -20]} opacity={1} permanent>
                                                    <span>{`${selectedCallData.PacketCallingPhone}, ${selectedCallData.PacketStreetAddress}, ${selectedCallData.PacketCity}, ${selectedCallData.PacketState}`}</span>
                                                </Tooltip>
                                            </Marker>
                                            {returnFlag(selectedCallData) &&
                                                <CircleMarker center={[selectedCallData.Packety, selectedCallData.Packetx]} radius={10} color={(returnFlag(selectedCallData) && parseFloat(selectedCallData.PacketCNF) >= 90) ? "red" : "gray"}>
                                                </CircleMarker>
                                            }

                                            {nearestAddressMarkers1.length > 0 && nearestAddressMarkers1.map((name, index) => {
                                                return (
                                                    <Marker key={index} position={[name.location.y, name.location.x]} icon={customIcon} eventHandlers={{ click: () => props.setNewCallOpen(true) }}>
                                                        <Tooltip direction="top" offset={[0, -20]} opacity={1} permanent>
                                                            <span>{name.address}</span>
                                                        </Tooltip>
                                                    </Marker>
                                                );
                                            })}

                                            {packet911CallsGroup.length > 0 && !isEmptyOrNull(packet911CallsGroup[0]) && packet911CallsGroup[0].map((name, index) => {
                                                return (
                                                    <Marker key={index} position={[name.Packety, name.Packetx]} icon={getCustomIcon(selectedCallData)}>
                                                        <Tooltip direction="top" offset={[0, -20]} opacity={1}>
                                                            <span>{returnDisplayString(name)}</span>
                                                        </Tooltip>
                                                    </Marker>
                                                );
                                            })}

                                            {callsByNumber.map((name, index) => {
                                                return (
                                                    <Marker key={index} position={[name.Packety, name.Packetx]} icon={customIcon}>
                                                        <Tooltip direction="top" offset={[0, -20]} opacity={1}>
                                                            <span>{`${name.PacketCallingPhone}, ${name.PacketStreetAddress}, ${name.PacketCity}, ${name.PacketState}`}</span>
                                                        </Tooltip>
                                                    </Marker>
                                                );
                                            })}

                                            {selectedCallData.SubCall.length > 0 && selectedCallData.SubCall.map((loc, index) => {
                                                if (selectedCallData.PacketClassofService === "WRLS" && loc.PacketClassofService === "WPH2") {
                                                    return (
                                                        <>
                                                            <Marker key={index} position={!isEmptyOrNull(loc.Packety) && !isEmptyOrNull(loc.Packetx) ? [loc.Packety, loc.Packetx] : [defaultlatlan.MinY, defaultlatlan.MinX]} icon={getCustomIcon(loc)}>
                                                            </Marker>
                                                        </>
                                                    );
                                                }
                                            })}

                                            {selectedCallData.PacketClassofService === "WRLS" &&
                                                <Polyline positions={createLineDraw(selectedCallData)} {...lineNewStyle} />
                                            }
                                        </>
                                        :
                                        <>
                                            {props.current.map((name, index) => {
                                                return (
                                                    <Marker key={index} position={[name.Packety, name.Packetx]} icon={getCustomIcon(name)} eventHandlers={{ click: () => dispatch(setSelectedCallData(name), (props.setSelectedCard(name._id)), setToggleValue(false)) }}>
                                                        <Tooltip direction="top" offset={[0, -20]} opacity={1}>
                                                            <span>{`${name.PacketCallingPhone}, ${name.PacketCustomerName}`}</span>
                                                        </Tooltip>
                                                    </Marker>
                                                );
                                            })}

                                            {props.current.filter(name => name.PacketClassofService === 'WRLS').map((tower, towerIndex) => {
                                                const wph2Calls = tower.SubCall.filter(subCall => subCall.PacketClassofService === 'WPH2');
                                                return wph2Calls.map((wph2Call, index) => {
                                                    const position = [parseFloat(wph2Call.Packety), parseFloat(wph2Call.Packetx)];
                                                    return (
                                                        <Marker key={`${towerIndex}-${index}`} position={position} icon={getCustomIcon(wph2Call)}>
                                                            <Tooltip direction="top" offset={[0, -20]} opacity={1}>
                                                                <span>{`${wph2Call.PacketCallingPhone}, ${wph2Call.PacketCustomerName}`}</span>
                                                            </Tooltip>
                                                        </Marker>
                                                    );
                                                })
                                            })}

                                            {props.current.filter(name => name.PacketClassofService === 'WRLS').length > 0 &&
                                                <Polyline positions={createShowAllCallsLineDraw()} {...lineNewStyle} />
                                            }

                                            {toggleShowRectangle &&
                                                <Rectangle bounds={createRect()} pathOptions={{ color: "#ff7800" }} />
                                            }

                                            {nearestAddressMarkers1.length > 0 && nearestAddressMarkers1.map((name, index) => {
                                                return (
                                                    <Marker key={index} position={[name.location.y, name.location.x]} icon={customIcon} eventHandlers={{ click: () => props.setNewCallOpen(true) }}>
                                                        <Tooltip direction="top" offset={[0, -20]} opacity={1} permanent>
                                                            <span>{name.address}</span>
                                                        </Tooltip>
                                                    </Marker>
                                                );
                                            })}
                                        </>
                                    }
                                </MapContainer>
                            }
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ButtonComponent" />}
                                onReset={() => { }} >
                                <ButtonComponent setCounty={props.setCounty} handleToggleChange={handleToggleChange}
                                    toggleValue={toggleValue} classes={classes} parentCallbackMap={handleRectangleChangeChange}
                                ></ButtonComponent>
                            </ErrorBoundary>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
}

export default Call911MapComponent;