
import React, { useEffect, useRef, useState } from 'react';
import * as yup from 'yup';
import _ from '@lodash';
import { useDispatch, useSelector } from 'react-redux';
import { isEmptyOrNull } from '../../utils/utils';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';





function agencyUsersTwitter(props) {
    const user = useSelector(({ auth }) => auth.user);
    const agency = useSelector(({ agency }) => agency.agency.data);
    const [loading, setLoading] = useState(false);

    const getTwitterOAuthUrl = () => {
        const twitterData = JSON.parse(localStorage.getItem("twitterData"))
        if (twitterData !== null && twitterData !== undefined) {
            let string = getURLWithQueryParams(twitterData.authUrl, {
                response_type: "code",
                client_id: twitterData.clientID,
                redirect_uri: twitterData.redirectUrl,
                scope: ["tweet.read", "users.read", "tweet.write", "offline.access"].join(" "),
                state: "state",
                code_challenge: "challenge",
                code_challenge_method: "plain",
            })

            return string;
        }
        return null;
    }

    const getURLWithQueryParams = (authUrl, params) => {
        const query = Object.entries(params)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join("&")

        return `${authUrl}?${query}`
    }

    const handleClickTwitterAuth = () => {
        if (user.data.defaultAgency !== undefined) {
            localStorage.setItem('AgencyCode', user.data.defaultAgency)
            let url = getTwitterOAuthUrl();
            if (url !== null) {
                window.location.replace(url)
            }
        }
    }

    useEffect(() => {
        handleClickTwitterAuth()
        setLoading(true)
    }, []);

    useEffect(() => {
        setTimeout(() => {
            setLoading(false)
        }, 2000);

    }, [loading]);


    return (
        <div class="p-16 w-2/4">
            {loading && <CircularProgressLoader loading={loading} />}
        </div>
    );
}

export default agencyUsersTwitter;

