import styled from '@emotion/styled';
import { Button } from '@mui/material';
import React, { useRef } from 'react';
import ReactAudioPlayer from 'react-audio-player';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { useTranslation } from "react-i18next";

function FileUploadComponent({ accept, onChange, filePath }) {
    const fileUpload = useRef(null);
    const { t } = useTranslation("laguageConfig");

    const VisuallyHiddenInput = styled('input')({
        clip: 'rect(0 0 0 0)',
        clipPath: 'inset(50%)',
        height: 1,
        overflow: 'hidden',
        position: 'absolute',
        bottom: 0,
        left: 0,
        whiteSpace: 'nowrap',
        width: 1,
    });

    return (
        <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
            {/* <input
                type="file"
                className="mb-4"
                ref={fileUpload}
                accept={accept}
                style={{
                    padding: '13px',
                    border: '1px solid lightgray',
                    borderRadius: '4px',
                    cursor: 'pointer'
                }}
                onChange={onChange} // Attach the onChange handler here
            /> */}

            <div>
                <Button
                    component='label'
                    variant='contained'
                    startIcon={<CloudUploadIcon />}
                    color='primary'
                >
                    {t("selectFile")}
                    <VisuallyHiddenInput
                        id="docpicker"
                        type="file"
                        className="mb-4"
                        ref={fileUpload}
                        onChange={onChange}
                        accept={accept}
                    />
                </Button>
            </div>

            {/* Display previously uploaded file */}
            {filePath && (
                <>
                    <ReactAudioPlayer
                        src={filePath}
                        controls
                        width='90%'
                        height='90%'
                    />

                    {/* For images
                    {accept.startsWith("image/") ? (
                        <img
                            src={filePath}
                            alt="Uploaded"
                            style={{ maxWidth: '200px', margin: '10px 0' }}
                        />
                    ) : (
                        // For other file types
                        <a
                            href={filePath}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ color: 'blue', textDecoration: 'underline' }}
                        >
                            {filePath}
                        </a>
                    )} */}
                </>
            )}
        </div>
    );
}

export default FileUploadComponent;