import { TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { ErrorBoundary } from "react-error-boundary";
import { Controller, useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import ErrorPage from "src/app/main/SharedComponents/ErrorPage/ErrorPage";
import CommonAutocomplete, {
  handleSelectKeyDown,
} from "src/app/main/SharedComponents/ReuseComponents/CommonAutocomplete";

const AliasStreetNameFields = ({
  handleAutocompleteChange,
  aliasPostDir,
  aliasStreetType,
  aliasPreDir,
  streetNamePreTypesPostTypes,
  streetPredirectionAndPostDirectionList,
}) => {
  const { t } = useTranslation("laguageConfig");
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Box
      component="fieldset"
      sx={{
        border: "1px solid #ccc",
        padding: 2,
        borderRadius: "5px",
        marginBottom: 2,
      }}>
      <legend style={{ padding: "0 10px", fontSize: "1.3rem" }}>
        {t("aliasFields")}
      </legend>
      <Grid container spacing={1}>
        <Grid item xs={4} sm={4} md={2} lg={2} xl={2}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_aliasPreDir" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("aliasPreDir")}
              options={streetPredirectionAndPostDirectionList}
              value={aliasPreDir}
              fieldName={t("PreDir")}
              optionLabel="name"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
        <Grid item xs={4} sm={4} md={4} lg={4} xl={4}>
          <Controller
            name="AliasStreetName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label={t("streetName")}
                variant="outlined"
                fullWidth
                sx={{ mb: 2 }}
              />
            )}
          />
        </Grid>
        <Grid item xs={4} sm={4} md={2} lg={2} xl={2}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage
                {...props}
                componentName="Autocomplete_aliasStreetType"
              />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("aliasStreetType")}
              options={streetNamePreTypesPostTypes}
              value={aliasStreetType}
              fieldName={t("streetType")}
              optionLabel="Value"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
        <Grid item xs={4} sm={4} md={2} lg={2} xl={2}>
          <ErrorBoundary
            FallbackComponent={(props) => (
              <ErrorPage {...props} componentName="Autocomplete_aliasPostDir" />
            )}>
            <CommonAutocomplete
              parentCallback={handleAutocompleteChange("aliasPostDir")}
              options={streetPredirectionAndPostDirectionList}
              value={aliasPostDir}
              fieldName={t("postDir")}
              optionLabel="name"
              onKeyDown={handleSelectKeyDown}
            />
          </ErrorBoundary>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AliasStreetNameFields;
