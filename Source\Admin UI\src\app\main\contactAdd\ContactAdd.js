import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FusePageSimple from '@fuse/core/FusePageSimple';
import Typography from '@mui/material/Typography';
import history from '@history';
import { useTranslation } from 'react-i18next';
import { newContact, updateContact, setContactID, getContactByID, setLoading } from '../contactPage/store/contactSlice'
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import { TextField } from '@mui/material';
import { useParams } from "react-router";
import CircularProgressLoader from '../SharedComponents/CircularProgressLoader/CircularProgressLoader';

const schema = yup.object().shape({
	lName: yup.string()
		.required('Please enter last name code.'),
	fName: yup.string()
		.required('Please enter first name.'),
	address: yup.string()
		.required('Please enter last name.'),
	mName: yup.string()
		.required('Please enter middle name.'),
	city: yup.string()
		.required('Please enter city.'),
	title: yup.string()
		.required('Please enter title.'),
	phone: yup.string()
		.required('Please enter phone.'),
	email: yup.string()
		.email('Please enter valid email.')
		.required('Please enter email.'),
});

const defaultValues = {
	lName: '',
	fName: '',
	address: '',
	mName: '',
	city: '',
	title: '',
	phone: '',
	email: ''
};

function ContactAddPage() {
	const routeParams = useParams();
	const dispatch = useDispatch();
	const formRef = useRef(null);
	let pageType = "ADD";
	const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
		mode: 'onChange',
		defaultValues,
		resolver: yupResolver(schema),
	});
	const { isValid, dirtyFields, errors } = formState;

	const { t } = useTranslation('laguageConfig');

	const contactID = useSelector(({ contact }) => contact.contact.contactID);
	const contact = useSelector(({ contact }) => contact.contact.contactData);
	const isloadingvalue = useSelector(({ contact }) => contact.contact.isloading);
	const [loading, setLoading] = useState();
	const lastNameInputRef = useRef(null);

	useEffect(() => {
		setLoading(isloadingvalue)
	}, [isloadingvalue]);

	useEffect(() => {
		if (contactID !== "0") {
			dispatch(getContactByID(contactID, routeParams.code));
		}
	}, [dispatch, contactID]);

	useEffect(() => {
		if (contactID === "0") {
			pageType = "ADD";
			setValue('fName', '');
			setValue('lName', '');
			setValue('mName', '');
			setValue('title', '');
			setValue('address', '');
			setValue('phone', '');
			setValue('city', '');
			setValue('email', '');
		}
		else {
			pageType = "EDIT";
			setValue('fName', contact.fName);
			setValue('lName', contact.lName);
			setValue('mName', contact.mName);
			setValue('title', contact.title);
			setValue('address', contact.address);
			setValue('phone', contact.phone);
			setValue('city', contact.city);
			setValue('email', contact.email);
		}

	}, [contact])

	const navigateBack = () => {
		history.push(`/admin/contactList/${routeParams.code}`);
	};


	function onSubmit(contact) {
		const data = {
			_id: "0",
			fName: contact.fName,
			lName: contact.lName,
			mName: contact.mName,
			title: contact.title,
			address: contact.address,
			phone: contact.phone,
			city: contact.city,
			email: contact.email,
			code: routeParams.code
		};

		if (contactID === "0") {
			dispatch(newContact(data));
		}
		else {
			data._id = contact._id === undefined ? contactID : contact._id;
			dispatch(updateContact(data));
			dispatch(setContactID("0"));
		}
		// navigateBack();
	}

	useEffect(() => {
		if (lastNameInputRef.current) {
			lastNameInputRef.current.focus();
		}
	}, [lastNameInputRef]);

	return (
		<>
			{loading && < CircularProgressLoader loading={loading} />}

			<FusePageSimple
				header={
					<div className="flex flex-1 items-center justify-between p-24">
						<div className="flex flex-col">
							<Typography variant="h4">{t(pageType)} {t('contact')}</Typography>
						</div>
					</div>
				}
				content={
					<div className="w-full p-16">
						<form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)} autoSave={false}
							autoComplete={false}>
							<Controller
								type="number"
								name="lName"
								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label={t('lastName')}
										type="text"
										inputRef={lastNameInputRef}
										error={!!errors.lName}
										helperText={errors?.lName?.message}
										variant="outlined"
										required
									/>
								)}
							/>
							<Controller
								type="number"
								name="fName"
								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label={t('firstName')}
										type="text"
										error={!!errors.fName}
										helperText={errors?.fName?.message}
										variant="outlined"
										required
									/>
								)}
							/>
							<Controller
								name="mName"
								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label={t('middleName')}
										type="text"
										error={!!errors.mName}
										helperText={errors?.mName?.message}
										variant="outlined"
										required
									/>
								)}
							/>
							<Controller
								name="title"
								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label={t('title')}
										type="text"
										error={!!errors.title}
										helperText={errors?.title?.message}
										variant="outlined"
										required
									/>
								)}
							/>
							<Controller
								name="address"
								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label={t('address')}
										type="text"
										error={!!errors.address}
										helperText={errors?.address?.message}
										variant="outlined"
										required
									/>
								)}
							/>
							<Controller
								name="city"
								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label={t('city')}
										type="text"
										error={!!errors.mName}
										helperText={errors?.mName?.message}
										variant="outlined"
										required
									/>
								)}
							/>
							<Controller
								name="email"
								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label={t('email')}
										type="email"
										error={!!errors.email}
										helperText={errors?.email?.message}
										variant="outlined"
										required
									/>
								)}
							/>
							<Controller
								name="phone"
								control={control}
								render={({ field }) => (
									<TextField
										{...field}
										className="mb-16"
										label={t('phone')}
										type="text"
										error={!!errors.phone}
										helperText={errors?.phone?.message}
										variant="outlined"
										required
									/>
								)}
							/>



							<div className="mx-auto">
								<Button
									type="submit"
									variant="contained"
									color="primary"
									className="normal-case m-16"
									aria-label="REGISTER"
									value="legacy">
									{pageType === "ADD" ? t('save') : t('update')}
								</Button>
								<Button
									type="button"
									variant="contained"
									color="secondary"
									className="normal-case m-16"
									aria-label="Back"
									value="legacy"
									onClick={navigateBack}>
									{t('back')}
								</Button>
							</div>
						</form>
					</div>
				}
			/>
		</>
	);
}

export default ContactAddPage;
