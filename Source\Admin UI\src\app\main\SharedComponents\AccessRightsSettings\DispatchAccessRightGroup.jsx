import React from 'react';
import {
    FormControl,
    FormLabel,
    FormGroup,
    FormControlLabel,
    Checkbox
} from '@mui/material';

const DispatchAccessRightGroup = ({
    t,
    accessRights,
    handleChange,
    showAccessRight,
    isSuperAdmin,
    defaultApp,
    doesNotUseOldRps
}) => {

    const accessOptions = [
        { key: 'Dispatch', label: t('mobileDispatch') },
        { key: 'Watches', label: t('watches') },
        { key: 'Chat', label: t('chat') },
        { key: 'Current', label: t('currentMap') },
        { key: 'Replay', label: t('replayHistory') },
        { key: 'SearchFeature', label: t('searchFeature') },
        { key: 'BroadcastMessages', label: t('broadcastMessages') },
        { key: 'MySchedule', label: t('mySchedule') },

        { key: 'TrafficStop', label: t('trafficStop') },
        { key: 'SecurityCheck', label: t('securityCheck') },
        { key: 'TransportCall', label: t('transportCall') },
        { key: 'TransportMedicalCall', label: t('medicalTransport') }
    ];

    const isDisabled = !(defaultApp === "mobile" || defaultApp === "incident") && !isSuperAdmin;

    return (
        <div className="col-span-1 w-full">
            <FormControl component="fieldset">
                <FormLabel component="legend">{t('dispatch')}</FormLabel>
                <FormGroup>
                    {accessOptions.map(({ key, label }) => {
                        const canShow = showAccessRight[key] === key || isSuperAdmin;

                        return canShow && (
                            <FormControlLabel
                                key={key}
                                control={
                                    <Checkbox
                                        checked={doesNotUseOldRps === true ? false : accessRights[key] || false}
                                        onChange={handleChange}
                                        name={key}
                                    />
                                }
                                label={label}
                            //disabled={isDisabled}
                            />
                        );
                    })}
                </FormGroup>
            </FormControl>
        </div>
    );
};

export default DispatchAccessRightGroup;
