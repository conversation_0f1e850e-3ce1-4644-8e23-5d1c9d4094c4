import { Grid, TextField } from "@mui/material";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";

const FormDateTimePicker = ({
    label,
    value,
    onChange,
    required = false,
    gridProps = {},
    format = "MM/dd/yyyy HH:mm",
    ampm = false,
    inputVariant = "standard",
    size = "medium",
    autoOk = true,
    margin = "normal",
    id = "date-picker-inline"
}) => {
    return (
        <Grid item {...gridProps}>
            <DateTimePicker
                autoOk={autoOk}
                size={size}
                value={value}
                margin={margin}
                id={id}
                onChange={onChange}
                ampm={ampm}
                format={format}
                inputVariant={inputVariant}
                slotProps={{ field: { clearable: true } }}
                label={label}
                KeyboardButtonProps={{
                    "aria-label": "change date",
                }}
                renderInput={(params) => <TextField {...params} required={required} />}
            />
        </Grid>
    );
};

export default FormDateTimePicker;
