import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import "./ChangeRPSCredentialsDialog.css";
import { useDispatch, useSelector } from 'react-redux';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import ChangeRPSCredentials from '../../SharedComponents/ChangeRPSCredentials/ChangeRPSCredentials';

const ChangeRPSCredentialsDialog = forwardRef((props, ref) => {
    const { t } = useTranslation('laguageConfig');
    const [open, setOpen] = React.useState(false);
    const [flag, setflag] = React.useState(false);
    const [data, setData] = React.useState(null);

    const handleClickOpen1 = () => {
        setOpen(true);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, flag) {
            setData(data);
            setflag(flag);
            handleClickOpen1();
        },
    }));

    useEffect(() => {
    }, [data])

    const handleClose = () => {
        setOpen(false);
    };

    const formRef = useRef(null);

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <DialogTitle style={{ display: "flex", justifyContent: "space-between" }} id="responsive-dialog-title">
                    <div>
                        {t("rpsCredentialsFor")} {data === null ? "" : data.fname}
                    </div>
                    <div>
                        <IconButton onClick={handleClose} aria-label="show more" >
                            <Icon>close</Icon>
                        </IconButton>
                    </div>
                </DialogTitle>
                <DialogContent dividers>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangeRPSCredentials" />} onReset={() => { }}>
                        <ChangeRPSCredentials data={data} handleClose={handleClose} flag={flag}></ChangeRPSCredentials>
                    </ErrorBoundary>
                </DialogContent>
            </Dialog>
        </div>
    );
});
export default ChangeRPSCredentialsDialog;

