const router = require("express").Router();
const classOfService = require("../models/classOfService");
const { ClassOfServiceValidate } = require("../validations/classOfService");
const formidable = require("formidable");
const fs = require("fs");
const { encrypt, decrypt } = require('../security/crypto');
const ObjectID = require('mongodb').ObjectId;
const { authorize } = require("../businesslogic/authorize");

router.get("/classOfServiceList", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const classOfService = db.collection('classofservices');
        const classOfServiceList = await classOfService.find().toArray();
        res.status(200).send(encrypt(JSON.stringify(classOfServiceList)));

    } catch (error) {
        res.status(500).json(error);
    }
});

router.post("/classOfServiceAdd", authorize, async (req, res) => {
    try {
        // req.body = JSON.parse(decrypt(req.body));
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const classOfService = db.collection('classofservices');
        const codeExists = await classOfService.findOne({ code: req.body.code });
        if (codeExists) {
            return res.status(500).send(encrypt(JSON.stringify({
                result: {
                    code: "Error",
                    // message: "Class of Service code is already saved, Please use diffrent code."
                    message: req.t("CodeAlreadyExist")
                }
            })));
        }
        else {
            const { error } = ClassOfServiceValidate(req.body);
            let errors = [];
            if (error) {
                error.details.forEach((element) => {
                    errors.push({
                        key: element.context.key,
                        type: element.type,
                        message: element.message,
                    });
                });
                return res.status(500).send(encrypt(JSON.stringify({
                    result: {
                        code: "Error",
                        message: req.t("ErrorOccured")
                    }
                })));
            }
            else {
                const newType = {
                    code: req.body.code,
                    description: req.body.description,
                    color: req.body.color,
                    iconPic: req.body.iconPic
                };
                const newClassOfService = await classOfService.insertOne(newType);
                return res.status(200).send(encrypt(JSON.stringify({
                    result: {
                        code: "Success",
                        message: newClassOfService.insertedId
                    }
                })));
            }
        }
    }
    catch (error) {
        return res.status(500).send(encrypt(JSON.stringify({
            result: {
                code: "Error",
                message: error.message
            }
        })));
    }
});


router.post("/classOfServiceEdit", authorize, async (req, res) => {
    try {
        req.body = JSON.parse(decrypt(req.body));
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const classOfService = db.collection('classofservices');
        if (!req.body) {
            return res.status(400).send(encrypt((JSON.stringify({
                result: {
                    code: "Error",
                    // message: "Error Occured. Please try again."
                    message: req.t("ErrorOccured")
                }
            }))));
        }
        else {
            if (req.body.iconPic !== "") {
                await classOfService.updateOne(
                    { _id: new ObjectID(req.body._id) },
                    {
                        $set: {
                            description: req.body.description,
                            color: req.body.color,
                            iconPic: req.body.iconPic
                        }
                    }
                )
                return res.status(200).send(encrypt(JSON.stringify({
                    result: {
                        code: "Success",
                        // message: "Class Of Service Code Updated Successfully!"
                        message: req.t("RecordUpdate")
                    }
                })));
            }
            else {
                await classOfService.updateOne(
                    { _id: new ObjectID(req.body._id) },
                    {
                        $set: {
                            description: req.body.description,
                            color: req.body.color,
                        }
                    }
                )
                return res.status(200).send(encrypt(JSON.stringify({
                    result: {
                        code: "Success",
                        // message: "Class Of Service Code Updated Successfully!"
                        message: req.t("RecordUpdate")
                    }
                })));
            }
        }
    } catch (error) {
        res.status(500).send(encrypt(JSON.stringify(error.message)));
    }
});

router.delete("/classOfServiceDelete/:_id", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const classOfService = db.collection('classofservices');
        const result = await classOfService.deleteOne({ _id: new ObjectID(req.params._id) })
        if (result)
            res.status(200).send(encrypt(JSON.stringify({ "message": req.t("DeleteMessage") })));
        else
            return res.status(500).send(encrypt(JSON.stringify({ data: { message: error.message } })));
    } catch (error) {
        res.status(500).send(encrypt(JSON.stringify(error.message)));
    }
});


// Find a single classOfService with a ClassOfServiceId
router.get("/classOfServiceGetByID/:_id", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const classOfService = db.collection('classofservices');
        const classOfServiceList = await classOfService.findOne({ _id: new ObjectID(req.params._id) });
        if (!classOfServiceList) {
            return res.status(404).send(encrypt(JSON.stringify({
                message: req.t("NotFound") + req.params._id
            })));
        }
        else {
            res.status(200).send(encrypt(JSON.stringify(classOfServiceList)));
        }
    } catch (error) {
        return res.status(500).send(encrypt(JSON.stringify({
            message: error.message + " " + req.params._id
        })));
    }

});

//Get Icon Image to display in the list
router.get("/getIcon", authorize, async (req, res) => {
    try {

        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const classOfService = db.collection('classofservices');
        const image = await classOfService.findOne({ _id: new ObjectID(req.query.id) });
        if (image) {
            res.send(image.icon !== undefined && image.icon !== null ? image.icon : "Icon not found..");
            res.end();
        } else {
            res.status(404).send();
        }
    }
    catch (error) {
        res.status(500).send(error.message);
    }
});

module.exports = router;


