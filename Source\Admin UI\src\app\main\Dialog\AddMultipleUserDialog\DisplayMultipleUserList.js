import React, { forwardRef, useRef, useImperativeHandle, useEffect, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import "./AddMultipleUserDialog.css";
import Divider from '@mui/material/Divider';
import { useSelector } from 'react-redux';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
} from "@infragistics/igniteui-react-grids";
// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();


const rows = [
    {
        id: 'Email',
        align: 'left',
        disablePadding: false,
        label: 'Email',
        sort: true
    },
    {
        id: 'Fields',
        align: 'left',
        disablePadding: false,
        label: 'Fields',
        sort: true
    },
    {
        id: 'Status',
        align: 'left',
        disablePadding: false,
        label: 'Status',
        sort: true
    },


];

const DisplayMultipleUserList = forwardRef((props, ref) => {

    const [open, setOpen] = React.useState(false);
    var ImportUserData = useSelector(({ administration }) => administration.user.ImportUserData);
    const isloadingvalue = useSelector(({ administration }) => administration.user.isloading);
    const gridRef = useRef(null);
    const { t } = useTranslation('laguageConfig');

    const handleClickOpen1 = () => {
        setOpen(true);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen() {
            handleClickOpen1();
        },
    }));

    const handleClose = () => {
        setOpen(false);
    };


    function getUsers(n) {
        let data;
        if (n.User === undefined) {
            return ("");
        }
        else {
            if (n.User.length > 0) {
                return (
                    data = n.User.map(number => number.email)//.join(", ")
                );
            }
            else {
                return ("");
            }
        }

    }

    function getFields(n) {
        let data;
        if (n.Fields === undefined) {
            return ("");
        }
        else {
            if (n.Status === "Failed" || n.Status === "Failed sp") {
                return (
                    data = n.Fields.map(number => number).join(", ")
                );
            }
            else {
                return ("");
            }
        }

    }


    let rowData = ImportUserData.map(item => {
        const row = {};
        let Fields = getFields(item)
        rows.forEach(rowConfig => {
            row["Email"] = item.Email;
            row["Fields"] = Fields;
            row["Status"] = item.Status;
        });
        return row;
    });


    const [loading, setLoading] = useState();
    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    return (
        <>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"

            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%] float-right'
                    style={{ color: "black" }}
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("importedUsers")}</DialogTitle>
                <Divider style={{ backgroundColor: "black" }} />
                <DialogContent dividers>
                    {loading && < CircularProgressLoader loading={loading} />}

                    <div>
                        <IgrGrid
                            id="grid"
                            autoGenerate="false"
                            data={rowData}
                            primaryKey="_id"
                            ref={gridRef}
                            height="750px"
                            rowHeight={60}
                            groupRowTemplate={groupByRowTemplate}
                            filterMode="ExcelStyleFilter"
                            moving={true}
                            allowFiltering={false}
                            allowAdvancedFiltering={true}
                            allowPinning={true}
                        >
                            <IgrGridToolbar>
                                <IgrGridToolbarActions>
                                    <IgrGridToolbarAdvancedFiltering />
                                    <IgrGridToolbarHiding />
                                    <IgrGridToolbarPinning />
                                </IgrGridToolbarActions>
                            </IgrGridToolbar>
                            <IgrColumn
                                header={t("email")}
                                field="Email"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                field="Fields"
                                header={t("fields")}
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                        </IgrGrid>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
});
export default DisplayMultipleUserList;