const router = require("express").Router();
// const packet911 = require("../models/packet911");
const cityModel = require('../models/City.Model.js');
const pdZoneModel = require('../models/PDZone.model.js');
const jpdZoneModel = require('../models/JPDZone.model.js');
const countyModel = require('../models/County.model.js');
const CircularJSON = require("circular-json")
const { encrypt, decrypt } = require('../security/crypto.js');
const proj4 = require("proj4")
const leafletPip = require("@mapbox/leaflet-pip");
global.window = { screen: {} }
global.document = {
    documentElement: { style: {} },
    getElementsByTagName: () => { return [] },
    createElement: () => { return {} }
}
global.navigator = { userAgent: 'nodejs', platform: 'nodejs' }
const L = require('leaflet');
const { query } = require("express");
let MongoClient = require('mongodb').MongoClient;
const ObjectID = require('mongodb').ObjectId;
const axios = require('axios');
const { authorize } = require("../businesslogic/authorize");

router.get("/getRapidSoSData", authorize, async (req, res) => {
    try {
        var rapidSoSArr = [];
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const RapidSoS = db.collection('RapidSoS');
        const dispatchURL = await RapidSoS.find().toArray();
        await dispatchURL.forEach((ele) => {
            var arryObj = ObjectToArray(ele.data, true);
            ele.data = arryObj;
            rapidSoSArr.push(ele);
        });
        res.status(200).send(encrypt(JSON.stringify(rapidSoSArr)));
    }
    catch (err) {
        res.status(500).send(encrypt(JSON.stringify(err)));
    }
})

router.get("/refreshcallList", authorize, async (req, res) => {
    // For All County
    const dbConnection = await global.clientConnection;
    const db = dbConnection.db('RPSWeb');
    const packet911 = db.collection('packet911');
    try {
        let callList = [];
        if (parseInt(req.query.countyID) === 0) {
            if (req.query.maxdate !== undefined && req.query.maxdate !== 'undefined') {
                callList = await packet911.find({ PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $gt: new Date(req.query.maxdate) } }).sort({ PacketCallReceivedDT: -1 }).toArray()
            }
            // callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
        }
        else {
            if (parseInt(req.query.countyID) !== 0) {
                if (parseInt(req.query.selectedCity) !== 0) {
                    if (parseInt(req.query.selectedZone) == 0 && parseInt(req.query.selectedPDZone) == 0) {
                        if (req.query.maxdate !== undefined && req.query.maxdate !== 'undefined') {
                            callList = await packet911.find({ PacketCityID: parseInt(req.query.selectedCity), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $gt: new Date(req.query.maxdate) } }).toArray();
                            callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
                        }
                    }
                    else {
                        if (req.query.maxdate !== undefined && req.query.maxdate !== 'undefined') {
                            callList = req.query.selectedZone == 0
                                ? (req.query.selectedPDZone == 0
                                    ?
                                    await packet911.find({ PacketCityID: parseInt(req.query.selectedCity), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $gt: new Date(req.query.maxdate) } }).toArray()
                                    :
                                    await packet911.find({ PacketPDZoneID: parseInt(req.query.selectedPDZone), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $gt: new Date(req.query.maxdate) } })).toArray()
                                : (req.query.selectedPDZone == 0
                                    ?
                                    await packet911.find({ PacketZoneID: parseInt(req.query.selectedZone), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $gt: new Date(req.query.maxdate) } }).toArray()
                                    :
                                    await packet911.find({ PacketPDZoneID: parseInt(req.query.selectedZone), PacketZoneID: parseInt(req.query.selectedPDZone), Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallProcessed: false, PacketCallReceivedDT: { $gt: new Date(req.query.maxdate) } })).toArray();


                            callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
                        }
                    }

                }
                else {
                    if (req.query.maxdate !== undefined && req.query.maxdate !== 'undefined') {
                        const countyid = parseInt(req.query.countyID)
                        callList = await packet911.find({ PacketCountyID: countyid, PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $gt: new Date(req.query.maxdate) } }).sort({ PacketCallReceivedDT: -1 }).toArray();
                        //callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
                    }
                }
            }
        }
        //res.status(200).send(encrypt(JSON.stringify(callList)));
        res.status(200).send(JSON.stringify(callList));
    } catch (error) {
        res.status(500).send("something went wrong");
    }

});

router.get("/callList1/:lastDate?", authorize, async (req, res) => {
    const dbConnection = await global.clientConnection;
    const db = dbConnection.db('RPSWeb');
    const packet911 = db.collection('packet911');
    let callList;
    let totalCount = 0;

    if (parseInt(req.query.countyID) === 0) {
        callList = await packet911.find({ PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).skip(parseInt(req.query.pageIndex)).limit(parseInt(req.query.pageLimit)).sort({ PacketCallReceivedDT: -1 }).toArray()
        totalCount = await packet911.find({ PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).count()
    }
    else {
        //condition for check county id not zero and selected city would be zero
        if (parseInt(req.query.countyID) !== 0) {

            if (parseInt(req.query.selectedCity) !== 0) {
                if (parseInt(req.query.selectedZone) == 0 && parseInt(req.query.selectedPDZone) == 0) {
                    callList = await packet911.find({ PacketCityID: parseInt(req.query.selectedCity), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).skip(parseInt(req.query.pageIndex)).limit(parseInt(req.query.pageLimit)).toArray();
                    callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj);

                    totalCount = await packet911.find({ PacketCityID: parseInt(req.query.selectedCity), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).count()
                }
                else {
                    callList = req.query.selectedZone == 0
                        ? (req.query.selectedPDZone == 0
                            ? await packet911.find({ PacketCityID: parseInt(req.query.selectedCity), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).skip(parseInt(req.query.pageIndex)).limit(parseInt(req.query.pageLimit)).toArray()
                            : await packet911.find({ PacketPDZoneID: parseInt(req.query.selectedPDZone), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).skip(parseInt(req.query.pageIndex)).limit(parseInt(req.query.pageLimit))).toArray()
                        : (req.query.selectedPDZone == 0
                            ? await packet911.find({ PacketZoneID: parseInt(req.query.selectedZone), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).skip(parseInt(req.query.pageIndex)).limit(parseInt(req.query.pageLimit)).toArray()
                            : await packet911.find({ PacketPDZoneID: parseInt(req.query.selectedZone), PacketZoneID: parseInt(req.query.selectedPDZone), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).skip(parseInt(req.query.pageIndex)).limit(parseInt(req.query.pageLimit))).toArray();

                    totalCount = req.query.selectedZone == 0
                        ? (req.query.selectedPDZone == 0
                            ? await packet911.find({ PacketCityID: parseInt(req.query.selectedCity), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).count()
                            : await packet911.find({ PacketPDZoneID: parseInt(req.query.selectedPDZone), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).count())
                        : (req.query.selectedPDZone == 0
                            ? await packet911.find({ PacketZoneID: parseInt(req.query.selectedZone), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).count()
                            : await packet911.find({ PacketPDZoneID: parseInt(req.query.selectedZone), PacketZoneID: parseInt(req.query.selectedPDZone), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).count())

                    callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
                }

            }
            else {
                callList = await packet911.find({ PacketCountyID: parseInt(req.query.countyID), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).sort({ _id: -1 }).skip(parseInt(req.query.pageIndex)).limit(parseInt(req.query.pageLimit)).toArray()
                totalCount = await packet911.find({ PacketCountyID: parseInt(req.query.countyID), PacketCallProcessed: false, Packetx: { $regex: /[a-zA-Z0-9]/ }, Packety: { $regex: /[a-zA-Z0-9]/ }, PacketCallReceivedDT: { $lt: new Date(req.params.lastDate) } }).count()
                //callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
            }
        }

    }

    const callListData = {
        callList,
        totalCount
    }
    res.status(200).send(encrypt(JSON.stringify(callListData)));
});


//Find method call for get the data from MongoDb Atlas....
const getData = async (packet911, fieldsToQuery, fieldsToFind, pageIndex, pageLimit) => {
    const queryConditions = {};

    fieldsToQuery.forEach((field, index) => {
        const value = fieldsToFind[index];
        if (value !== undefined) {
            queryConditions[field] = value;
        }
    });

    const query = await packet911.find(queryConditions);

    if (pageIndex !== null && pageLimit !== null) {
        query.skip(parseInt(pageIndex)).limit(parseInt(pageLimit));
    }

    const result = await query.toArray();
    return result;
}

//Changes done for find data from single function call....
router.get("/callListByCallNumber/:callNumber", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const packet911 = db.collection('packet911');
        // var callList = await packet911.find({ PacketCallingPhone: req.params.callNumber, PacketCallProcessed: false }).toArray();
        var callList = await getData(packet911, ["PacketCallingPhone", "PacketCallProcessed"], [req.params.callNumber, false], null, null)
        res.status(200).send(encrypt(JSON.stringify(callList)));
    }
    catch (err) {
        res.status(500).send(encrypt(JSON.stringify(err.message)));
    }
});

router.get("/callList", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const packet911 = db.collection('packet911');
        // var callList = req.query.countyID == 0 ? await packet911.find({ PacketCallProcessed: false }).toArray() : await packet911.find({ PacketCountyID: parseInt(req.query.countyID), PacketCallProcessed: false }).toArray();
        var callList = req.query.countyID == 0 ? await getData(packet911, ["PacketCallProcessed"], [false], null, null) : await getData(packet911, ["PacketCountyID", "PacketCallProcessed"], [parseInt(req.query.countyID), false], null, null);
        callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
        res.status(200).send(encrypt(JSON.stringify(callList)));
    }
    catch (err) {
        res.status(500).send(encrypt(JSON.stringify(err.message)));
    }

});

router.get("/getCityWiseList", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const packet911 = db.collection('packet911');
        // var callList = req.query.selectedCity == 0 ? await packet911.find({ PacketCountyID: parseInt(req.query.selectedCounty), PacketCallProcessed: false }).toArray() : await packet911.find({ PacketCityID: parseInt(req.query.selectedCity), PacketCallProcessed: false }).toArray();
        var callList = req.query.selectedCity == 0 ? await getData(packet911, ["PacketCountyID", "PacketCallProcessed"], [parseInt(req.query.selectedCounty), false], null, null) : await getData(packet911, ["PacketCityID", "PacketCallProcessed"], [parseInt(req.query.selectedCity), false], null, null);
        callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
        res.status(200).send(encrypt(JSON.stringify(callList)));
    }
    catch (err) {
        res.status(500).send(encrypt(JSON.stringify(err.message)));
    }
})

router.get("/getZoneAndPDZoneWiseList", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const packet911 = db.collection('packet911');
        var callList = req.query.selectedZone == 0
            ? (req.query.selectedPDZone == 0
                ? await getData(packet911, ["PacketCityID", "PacketCallProcessed"], [parseInt(req.query.selectedCity), false], null, null)//await packet911.find({ PacketCityID: parseInt(req.query.selectedCity), PacketCallProcessed: false }).toArray()
                : await getData(packet911, ["PacketPDZoneID", "PacketCallProcessed"], [parseInt(req.query.selectedPDZone), false], null, null))//await packet911.find({ PacketPDZoneID: parseInt(req.query.selectedPDZone), PacketCallProcessed: false })).toArray()
            : (req.query.selectedPDZone == 0
                ? await getData(packet911, ["PacketZoneID", "PacketCallProcessed"], [parseInt(req.query.selectedZone), false], null, null)//await packet911.find({ PacketZoneID: parseInt(req.query.selectedZone), PacketCallProcessed: false }).toArray()
                : await getData(packet911, ["PacketPDZoneID", "PacketZoneID", "PacketCallProcessed"], [parseInt(req.query.selectedZone), parseInt(req.query.selectedPDZone), false], null, null))//await packet911.find({ PacketPDZoneID: parseInt(req.query.selectedZone), PacketZoneID: parseInt(req.query.selectedPDZone), PacketCallProcessed: false })).toArray();
        callList = callList.sort((a, b) => b.PacketCallReceivedDT.DateTimeObj - a.PacketCallReceivedDT.DateTimeObj)
        res.status(200).send(encrypt(JSON.stringify(callList)));
    }
    catch (err) {
        res.status(500).send(encrypt(JSON.stringify(err.message)));
    }
})

router.get("/getAllCounties", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const countyModel = db.collection('countydatas');
        var countiesData = await countyModel.find().sort({ County_Name: 1 }).toArray();

        res.status(200).send(encrypt(JSON.stringify(countiesData)));

    } catch (error) {
        res.status(500).send(encrypt(JSON.stringify(err.message)));
    }
})

router.get("/getAllCities/:_id", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const cityModel = db.collection('citydatas');
        var citiesData = await cityModel.find({ County_ID: parseInt(req.params._id) }).sort({ City_Name: 1 }).toArray();

        res.status(200).send(encrypt(JSON.stringify(citiesData)));
    } catch (error) {
        res.status(500).send(encrypt(JSON.stringify(error.message)));
    }
})

router.get("/getZones", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const pdZoneModel = db.collection('pdzonedatas');
        var ZonesData = await pdZoneModel.find().sort({ PDZone_Name: 1 }).toArray();
        res.status(200).send(encrypt(JSON.stringify(ZonesData)));
    } catch (error) {
        res.status(500).send(encrypt(JSON.stringify(error.message)));
    }
})

router.get("/getPoliceZones", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const jpdZoneModel = db.collection('jpddatas');
        var ZonesData = await jpdZoneModel.find().toArray();
        res.status(200).send(encrypt(JSON.stringify(ZonesData)));
    } catch (error) {
        res.status(500).send(encrypt(JSON.stringify(eerrorrr.message)));
    }
})

getLocation = (element, fileName) => {
    proj4.defs([
        ["urn:ogc:def:crs:EPSG::26915", "+proj=utm +zone=15 +ellps=GRS80 +datum=NAD83 +units=m +no_defs"],
        ["EPSG:102651", "+proj=lcc +lat_1=34.93333333333333 +lat_2=36.23333333333333 +lat_0=34.33333333333334 +lon_0=-92 +x_0=399999.9999999999 +y_0=0 +ellps=GRS80 +datum=NAD83 +to_meter=0.3048006096012192 no_defs"]

    ]);
    var result = leafletPip.pointInLayer(proj4('EPSG:4326', 'EPSG:102651', [element.loc.coordinates.longitude, element.loc.coordinates.latitude]), L.geoJSON(fileName), true)
    var serialized = JSON.parse(CircularJSON.stringify(result));
    if (serialized.length > 0)
        return serialized[0].feature.attributes;
    else
        return null;
}

//Need to Test properly..
router.post("/groupCallList", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const packet911 = db.collection('packet911');
        var callList = [];
        const startDate = new Date(req.body.dateTime); // Replace with your start date
        const endDate = new Date(req.body.dateTime);   // Replace with your end date

        const query = {
            "PacketCallDateTime.DateTimeObj": {
                $gte: new Date(startDate.getTime() - (parseInt(req.body.minutes) * 60000)),
                $lte: new Date(endDate.getTime() + (parseInt(req.body.minutes) * 60000)),
            },
            loc: {
                $nearSphere: {
                    $geometry: {
                        type: "Point",
                        coordinates: [parseFloat(req.body.Packetx), parseFloat(req.body.Packety)]
                    },
                    $maxDistance: (parseInt(req.body.geoRadius))
                }
            }
        };

        if (parseInt(req.body.countyID) == 0) {
            const callListGroup1 = await packet911.find({ query }).toArray();
            callList.push(callListGroup1);

        }
        else if (parseInt(req.body.cityName) == 0) {
            const callListGroup1 = await packet911.find({
                query,
                "PacketCountyID": {
                    $eq: parseInt(req.body.countyID)
                },
            }).toArray();
            callList.push([...new Map(callListGroup1.map(item =>
                [item["PacketCallingPhone"], item])).values()]);

        }
        else if (parseInt(req.body.zoneName) == 0 && parseInt(req.body.pdZoneName) == 0) {
            const callListGroup1 = await packet911.find({
                query,
                "PacketCityID": {
                    $eq: parseInt(req.body.cityName)
                },
            }).toArray();
            callList.push([...new Map(callListGroup1.map(item =>
                [item["PacketCallingPhone"], item])).values()]);

        }
        else {
            if (parseInt(req.body.zoneName) != 0 && parseInt(req.body.pdZoneName) != 0) {
                const callListGroup1 = await packet911.find({
                    query,
                    "PacketZoneID": {
                        $eq: parseInt(req.body.zoneName)
                    },
                    "PacketPDZoneID": {
                        $eq: parseInt(req.body.pdZoneName)
                    }

                }).toArray();
                callList.push([...new Map(callListGroup1.map(item =>
                    [item["PacketCallingPhone"], item])).values()]);
            }
            else if (parseInt(req.body.zoneName) != 0 && parseInt(req.body.pdZoneName) == 0) {
                const callListGroup1 = await packet911.find({
                    query,
                    "PacketZoneID": {
                        $eq: parseInt(req.body.zoneName)
                    }
                }).toArray();
                callList.push([...new Map(callListGroup1.map(item =>
                    [item["PacketCallingPhone"], item])).values()]);

            }
            else {
                const callListGroup1 = await packet911.find({
                    query,
                    "PacketPDZoneID": {
                        $eq: parseInt(req.body.pdZoneName)
                    }
                }).toArray();
                callList.push([...new Map(callListGroup1.map(item =>
                    [item["PacketCallingPhone"], item])).values()]);
            }
        }
        res.status(200).send(encrypt(JSON.stringify(callList)));
    }
    catch (error) {
        res.status(500).send(encrypt(JSON.stringify(error.message)));
    }

});

router.get("/setProcessData", authorize, async (req, res) => {
    try {
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const packet911 = db.collection('packet911');
        const dt = new Date();
        dt.setHours(dt.getHours() - 1)
        const result = await packet911.updateMany({ "PacketCallReceivedDT": { $lt: dt }, "PacketCallProcessed": false },
            {
                $set: {
                    PacketCallProcessed: true
                }
            })

        res.send(true)
    } catch (error) {
        throw (error)
    }
})

var res1 = [];
const ObjectToArray = (ele, isCallBack) => {
    if (isCallBack) {
        res1 = [];
    }
    for (key in ele) {
        if (ele[key].display_name) {

            if (ele[key].value.constructor === Array) {
                ele[key].value.forEach((ele2) => {
                    var ele2Res = '';
                    if (typeof ele2 === 'object' && ele2 !== null) {
                        switch (ele[key].type) {
                            case 'image-url':
                                ele2Res = 'image-url:' + ele2.url + ",";
                                break;
                            case 'web-url':
                                ele2Res = ele[key].type + ':' + ele2.url + ",";
                                break;
                            case 'video-stream':
                                ele2Res = ele[key].type + ':' + ele2.url + ",";
                                break;
                            default:
                                for (key2 in ele2) {
                                    ele2Res += ele2[key2] + ",";
                                }
                        }
                    }
                    else
                        ele2Res = ele2 + ",";
                    ele2Res = ele2Res.slice(0, -1);
                    res1.push([ele[key].display_name, ele2Res]);
                })
            }
            else
                res1.push([ele[key].display_name, ele[key].value]);
        }


        if (ele[key].constructor === Array) {
            ele[key].forEach((ele) => {
                ObjectToArray(ele, false)
            })
        }
        if (typeof ele[key] === 'object' && ele[key] !== null) {
            ObjectToArray(ele[key], false)
        }
    };

    var filtered = res1.filter(function (el) {
        return el != null;
    });
    return filtered;
}

router.get("/saveMasterData", authorize, async (req, res) => {
    const countyJson = require("../config/RPS911Counties.json");
    const cityJson = require("../config/RPS911Cities.json");
    //     countyJson.features.forEach(async element => {

    //     const countyObj = new countyModel({
    //         County_ID:element.id,
    //         County_Name:element.properties.COUNTY
    //     })
    //      await countyObj.save();
    // })
    // cityJson.features.forEach(async element => {
    //     const county_id= await countyModel.find({County_Name:element.attributes.COUNTY});
    //     const cityObj = new cityModel({
    //         City_ID: element.attributes.OBJECTID,
    //         County_ID : county_id[0].County_ID,
    //         City_Name: element.attributes.city_name      
    //     })
    //     await cityObj.save();
    // });

    res.send("ok")
})

const CovertDateTime = (PacketCallTime, PacketCallDate) => {
    var dateTimeStr = PacketCallDate.trim() + '/' + PacketCallTime.trim();
    dateTimeStr = dateTimeStr.replace(":", "/");
    var strArr = dateTimeStr.split('/');
    var dateTimeObj = new Date(20 + strArr[2], strArr[0] - 1, strArr[1], strArr[3], strArr[4]);
    return { DateTimeObj: dateTimeObj, offset: dateTimeObj.getTimezoneOffset() }
}

router.post("/SavePacket911Call", authorize, async (req, res) => {
    try {

        console.log("SavePacket911Call")
        //console.log(req.body)
        const dbConnection = await global.clientConnection;
        const db = dbConnection.db('RPSWeb');
        const packet911 = db.collection('packet911');

        var availableCall = [];

        availableCall = await packet911.find({ PacketCallingPhone: req.body.PacketCallingPhone }).toArray();
        //console.log(availableCall)

        var timeAfterAddingMinutes = 0;
        if (availableCall.length > 0) {
            console.log("availableCall found...")
            timeAfterAddingMinutes = new Date(availableCall[availableCall.length - 1].PacketCallDateTime.DateTimeObj.getTime() + 600000);
        }
        console.log("------------------------------------");
        console.log("old-", timeAfterAddingMinutes);
        console.log("New-", req.body.PacketCallDateTime.DateTimeObj);
        console.log("------------------------------------");

        console.log(availableCall)
        if (availableCall.length > 0 && new Date(req.body.PacketCallDateTime.DateTimeObj) <= new Date(timeAfterAddingMinutes)) {
            console.log("inside if");

            await packet911.updateOne({ _id: new ObjectID(availableCall[availableCall.length - 1]._id) },
                {
                    $set: {
                        PacketCallReceivedDT: new Date(),
                        PacketCallProcessed: false,
                        PacketCallProcessedBy: '',
                        PacketCountyID: req.body.PacketCountyID,
                        PacketCityID: req.body.PacketCityID,
                        PacketZoneID: req.body.PacketZoneID,
                        PacketPDZoneID: req.body.PacketPDZoneID,
                        JPDDistrict: req.body.PacketPDZoneID,
                        PacketCallDateTime: CovertDateTime(req.body.PacketCallTime, req.body.PacketCallDate),
                        PacketESN: req.body.PacketESN,
                        PacketPositionNumber: req.body.PacketPositionNumber,
                        PacketCallingPhone: req.body.PacketCallingPhone,
                        PacketCallTime: req.body.PacketCallTime,
                        PacketCallDate: req.body.PacketCallDate,
                        PacketStreetNumber: req.body.PacketStreetNumber,
                        PacketApt: req.body.PacketApt,
                        PacketStreetAddressDir: req.body.PacketStreetAddressDir,
                        PacketStreetAddress: req.body.PacketStreetAddress,
                        PacketLocationInfo: req.body.PacketLocationInfo,
                        PacketCity: req.body.PacketCity,
                        PacketState: req.body.PacketState,
                        PacketCustomerName: req.body.PacketCustomerName,
                        PacketPilot: req.body.PacketPilot,
                        PacketClassofService: req.body.PacketClassofService,
                        PacketAltNumber: req.body.PacketAltNumber,
                        PacketTelco: req.body.PacketTelco,
                        Packetx: req.body.Packetx,
                        Packety: req.body.Packety,
                        PacketCNF: req.body.PacketCNF,
                        PacketUNC: req.body.PacketUNC,
                        PacketPD: req.body.PacketPD,
                        PacketFD: req.body.PacketFD,
                        PacketEMS: req.body.PacketEMS,
                        RawInputString: req.body.InputString,
                        Status: 1,
                        // GoodOrBad: GoodOrBad,
                        loc: {
                            type: "Point", coordinates: { longitude: parseFloat(req.body.Packetx), latitude: parseFloat(req.body.packetLat) }
                        },
                    }
                    , $push: {
                        SubCall: {
                            PacketCallReceivedDT: availableCall[availableCall.length - 1].PacketCallReceivedDT,
                            PacketCallProcessed: availableCall[availableCall.length - 1].PacketCallProcessed,
                            PacketCallProcessedBy: '',
                            PacketCountyID: availableCall[availableCall.length - 1].PacketCountyID,
                            PacketCityID: availableCall[availableCall.length - 1].PacketCityID,
                            PacketZoneID: availableCall[availableCall.length - 1].PacketZoneID,
                            PacketPDZoneID: availableCall[availableCall.length - 1].PacketPDZoneID,
                            JPDDistrict: availableCall[availableCall.length - 1].JPDDistrict,
                            PacketCallDateTime: availableCall[availableCall.length - 1].PacketCallDateTime,
                            PacketESN: availableCall[availableCall.length - 1].PacketESN,
                            PacketPositionNumber: availableCall[availableCall.length - 1].PacketPositionNumber,
                            PacketCallingPhone: availableCall[availableCall.length - 1].PacketCallingPhone,
                            PacketCallTime: availableCall[availableCall.length - 1].PacketCallTime,
                            PacketCallDate: availableCall[availableCall.length - 1].PacketCallDate,
                            PacketStreetNumber: availableCall[availableCall.length - 1].PacketStreetNumber,
                            PacketApt: availableCall[availableCall.length - 1].PacketApt,
                            PacketStreetAddressDir: availableCall[availableCall.length - 1].PacketStreetAddressDir,
                            PacketStreetAddress: availableCall[availableCall.length - 1].PacketStreetAddress,
                            PacketLocationInfo: availableCall[availableCall.length - 1].PacketLocationInfo,
                            PacketCity: availableCall[availableCall.length - 1].PacketCity,
                            PacketState: availableCall[availableCall.length - 1].PacketState,
                            PacketCustomerName: availableCall[availableCall.length - 1].PacketCustomerName,
                            PacketPilot: availableCall[availableCall.length - 1].PacketPilot,
                            PacketClassofService: availableCall[availableCall.length - 1].PacketClassofService,
                            PacketAltNumber: availableCall[availableCall.length - 1].PacketAltNumber,
                            PacketTelco: availableCall[availableCall.length - 1].PacketTelco,
                            Packetx: availableCall[availableCall.length - 1].Packetx,
                            Packety: availableCall[availableCall.length - 1].Packety,
                            PacketCNF: availableCall[availableCall.length - 1].PacketCNF,
                            PacketUNC: availableCall[availableCall.length - 1].PacketUNC,
                            PacketPD: availableCall[availableCall.length - 1].PacketPD,
                            PacketFD: availableCall[availableCall.length - 1].PacketFD,
                            PacketEMS: availableCall[availableCall.length - 1].PacketEMS,
                            RawInputString: availableCall[availableCall.length - 1].RawInputString,
                            // GoodOrBad: GoodOrBad,
                            loc: availableCall[availableCall.length - 1].loc
                        }
                    }
                })

            let data = await packet911.find({ _id: new ObjectID(availableCall[availableCall.length - 1]._id) }).toArray();
            console.log(data[0])
            await axios.post(`${process.env.SOCKETAPIURL}/call911Alert/911appalert`, data[0]);
        }
        else {
            console.log("inside else");
            //Auto Increment Logic for CAD911CallID in Packet call 911...
            let ID;
            const result = await packet911.find().sort({ CAD911CallID: -1 }).skip(0).limit(1).toArray()
            if (result.length > 0) {
                ID = result[0].CAD911CallID !== undefined && result[0].CAD911CallID !== null ? result[0].CAD911CallID + 1 : 100
            }
            else {
                ID = 100
            }
            req.body.CAD911CallID = ID;
            let data = await packet911.insertOne(req.body)
            await axios.post(`${process.env.SOCKETAPIURL}/call911Alert/911appalert`, req.body);
        }
        //Call Socket Api for send Alert
        //await axios.get(`${process.env.SOCKETAPIURL}/call911Alert/911appalert`);

        res.status(200).send("ok");
    }
    catch (error) {
        console.log(error)
        res.status(500).send(encrypt(JSON.stringify(error.message)));
    }

});


module.exports = router;