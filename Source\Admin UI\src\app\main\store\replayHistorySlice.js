import { createSlice } from '@reduxjs/toolkit';
import axios from 'axios';
import moment from 'moment';


export const ReplaySearchByCalls = (startDate, endDate) => async dispatch => {

    const sDate = startDate.toLocaleString('sv');
    const eDate = endDate.toLocaleString('sv');
    try {
        await axios.get(localStorage.getItem("REACT_APP_MOBILE_API_URL").concat('dispatch/api/ReplaySearch/ListSearchCallsByDate?startDate=' + sDate + '&endDate=' + eDate))
            .then(response => {

                if (response.status === 200) {
                    return dispatch(replaySearchByCallsSuccess(response.data));
                }

            })
            .catch(error => {
                dispatch(replaySearchByCallsError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
};

function getLocalOffset() {
    var offset = new Date().toString().match(/([-\+][0-9]+)\s/)[1];
    return `${offset.slice(0, 3)}:${offset.slice(3, offset.length)}`
}


export const ReplaySearchByUnit = (startDate, endDate) => async dispatch => {
    const sDate = moment(startDate).subtract(getLocalOffset()).format('MM/DD/YYYY, HH:mm:ss');
    const eDate = moment(endDate).subtract(getLocalOffset()).format('MM/DD/YYYY, HH:mm:ss');
    try {
        await axios.get(localStorage.getItem("REACT_APP_MOBILE_API_URL").concat('dispatch/api/ReplaySearch/ListSearchUnitsByDate?startDate=' + sDate + '&endDate=' + eDate))
            .then(response => {

                if (response.status === 200) {
                    //;
                    return dispatch(replaySearchByUnitSuccess(response.data));
                }

            })
            .catch(error => {
                dispatch(replaySearchByUnitError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
};

export const ReplaySearchByOfficer = (startDate, endDate) => async dispatch => {
    const sDate = moment(startDate).subtract(getLocalOffset()).format('MM/DD/YYYY, HH:mm:ss');
    const eDate = moment(endDate).subtract(getLocalOffset()).format('MM/DD/YYYY, HH:mm:ss');
    try {
        await axios.get(localStorage.getItem("REACT_APP_MOBILE_API_URL").concat('dispatch/api/ReplaySearch/ListSearchOfficersByDate?startDate=' + sDate + '&endDate=' + eDate))
            .then(response => {

                if (response.status === 200) {
                    return dispatch(replaySearchByOfficerSuccess(response.data));
                }

            })
            .catch(error => {
                dispatch(replaySearchByOfficerError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
};


export const GetReplayHistoryByUnit = (startDate, endDate, lstUnits) => async dispatch => {
    const sDate = moment(startDate).subtract(getLocalOffset()).format('MM/DD/YYYY, HH:mm:ss');
    const eDate = moment(endDate).subtract(getLocalOffset()).format('MM/DD/YYYY, HH:mm:ss');
    try {
        await axios.get(localStorage.getItem("REACT_APP_MOBILE_API_URL").concat('dispatch/api/ReplayHistory/GetReplayHistoryByUnit?startDate=' + sDate + '&endDate=' + eDate + '&lstUnitID=' + lstUnits),
            {
                onDownloadProgress: progressEvent => {

                    dispatch(replayDownloadProgress(Math.floor(progressEvent.loaded / progressEvent.total * 100)));
                }
            })
            .then(response => {

                if (response.status === 200) {
                    return dispatch(replayHistoryByUnitSuccess(response.data));
                }

            })
            .catch(error => {
                dispatch(replaySearchByUnitError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
}
export const GetReplayHistoryByOfficers = (startDate, endDate, lstOfsID) => async dispatch => {
    const sDate = moment(startDate).subtract(getLocalOffset()).format('MM/DD/YYYY, HH:mm:ss');
    const eDate = moment(endDate).subtract(getLocalOffset()).format('MM/DD/YYYY, HH:mm:ss');
    try {
        await axios.get(localStorage.getItem("REACT_APP_MOBILE_API_URL").concat('dispatch/api/ReplayHistory/GetReplayHistoryByOfficer?startDate=' + sDate + '&endDate=' + eDate + '&lstPersID=' + lstOfsID))
            .then(response => {

                if (response.status === 200) {
                    return dispatch(replayHistoryByOfficerSuccess(response.data));
                }

            })
            .catch(error => {
                dispatch(replaySearchByOfficerError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
}



export const GetReplayHistoryByCall = (callID) => async dispatch => {
    try {
        await axios.get(localStorage.getItem("REACT_APP_MOBILE_API_URL").concat('dispatch/api/ReplayHistory/GetReplayHistoryByCall?CallID=' + callID))
            .then(response => {

                if (response.status === 200) {
                    ;
                    return dispatch(replayHistoryByCallSuccess(response.data));
                }

            })
            .catch(error => {
                dispatch(replayHistoryByCallError(error));
            });
    } catch (e) {
        return console.error(e.message);
    }
};

export const SetLineChartData = (data) => async dispatch => {
    try {
        return dispatch(setChartDataSuccess(data));
    } catch (e) {
        return console.error(e.message);
    }
};

export const updateLineData = (line, data) => {
    return
}
export const setSelectedUnit=(lstUnit)=>async dispatch=>{
    try {
        ;
        return dispatch(setSelectedUnitsSuccess(lstUnit));
    } catch (error) {
        
    }
}
export const SetSelectedUnitID = (unitID) =>async dispatch=>{
    try {
        return dispatch(setSelectedUnitIDSuccess(unitID));
    } catch (error) {
        
    }
}

const initialState = {
    replaySearchsuccess: false,
    replayHitoryByUnitsuccess: false,
    replaySearchByUnit: [],
    replayHistoryByUnit: [],
    replaySearchByCalls: [],
    replayHistoryByCall: [],
    replaySearchByOfficer: [],
    replayHistoryByOfficer: [],
    selectedUnits: [],
    selectedUnitID: 0,
    downloadProgress: 0,
    replaySearcherror: {
        startDate: null,
        endDate: null
    },
    lineChartData: {
        series: [{
            name: "Speed",
            data: []
        }],
        options: {
            annotations: {


                points: [
                    {
                        x: moment("2021-07-21T08:51:44").format('DD/MM	 hh:mm:ss'),
                        y: 28,
                        marker: {
                            size: 6,
                            fillColor: "#fff",
                            strokeColor: "#2698FF",
                            radius: 2
                        },
                        label: {
                            borderColor: "#FF4560",
                            offsetY: 0,
                            style: {
                                color: "#fff",
                                background: "#FF4560"
                            },

                            text: "Point Annotation (XY)"
                        }
                    }
                ]
            },
            chart: {
                height: 350,
                type: 'line',
            },

            stroke: {
                curve: 'straight'
            },
            title: {
                text: 'Speed by time',
                align: 'left'
            },
            grid: {
                row: {
                    colors: ['#f3f3f3', 'transparent'],
                    opacity: 0.5
                },
            },
            xaxis: {
                categories: [],
                labels: {
                    show: false,
                    format: 'dd/MM',
                }
            }
        },


    }
};


const replaySearchSlice = createSlice({
    name: 'replayHistory',
    initialState,
    reducers: {
        replaySearchByCallsSuccess: (state, action) => {
            state.replaySearchsuccess = true;
            state.replaySearchByCalls = action.payload != null ? action.payload : [];
        },
        replaySearchByCallsError: (state, action) => {
            state.replaySearchsuccess = false;
            state.replaySearchByCalls = [];
        },
        replayHistoryByCallSuccess: (state, action) => {
            state.replayHistoryByCall = action.payload != null ? action.payload : [];
        },
        replayHistoryByCallError: (state, action) => {
            state.replayHistoryByCall = [];
            state.replayHitoryByUnitsuccess = false;
        },
        replaySearchByUnitSuccess: (state, action) => {
            state.replaySearchsuccess = true;
            state.replaySearchByUnit = action.payload;
        },
        replaySearchByUnitError: (state, action) => {
            state.replaySearchsuccess = false;
            state.replaySearchByUnit = [];
            state.replayHitoryByUnitsuccess = false;
        },
        replayHistoryByUnitSuccess: (state, action) => {
            state.replayHistoryByUnit = action.payload;
            state.replayHitoryByUnitsuccess = false;

        },
        replayHistoryByOfficerSuccess: (state, action) => {
            state.replayHistoryByOfficer = action.payload;
            state.replayHitoryByUnitsuccess = false;
        },

        replaySearchByOfficerSuccess: (state, action) => {
            state.replaySearchByOfficer = action.payload;
        },
        replaySearchByOfficerError: (state, action) => {
            state.replayHistoryByCall = [];
            state.replayHitoryByUnitsuccess = false;
        },
        replayHistoryByUnitError: (state, action) => {
            state.replayHistoryByCall = [];
        },
        replayDownloadProgress: (state, action) => {
            state.downloadProgress = action.payload
        },
        setSelectedUnitsSuccess:(state,action)=>{
            state.selectedUnits = action.payload
        },
        setSelectedUnitIDSuccess:(state,action) =>{
            ;
            state.selectedUnitID = action.payload
        },
        setChartDataSuccess: (state, action) => {
            state.lineChartData = {
                options: {
                    ...state.lineChartData.options,
                    // stroke: {
                    //     ...state.lineChartData.options.stroke,
                    //     curve: state.lineChartData.options.stroke.curve
                    // },
                    // markers: {
                    //     ...state.lineChartData.options.markers,
                    //     size: state.lineChartData.options.markers.size
                    // },
                    xaxis: {
                        categories: action.payload.map(val => val.callID)
                    }
                },
                series: [{
                    data: action.payload.map(val => val.speed)
                }]
            }

        }
    },
    extraReducers: {}
});

export const {
    replaySearchByCallsSuccess,
    replaySearchByCallsError,
    replayHistoryByCallSuccess,
    replayHistoryByCallError,
    replaySearchByUnitSuccess,
    replaySearchByUnitError,
    replayHistoryByUnitSuccess,
    replayHistoryByUnitError,
    replayDownloadProgress,
    replaySearchByOfficerSuccess,
    replaySearchByOfficerError,
    replayHistoryByOfficerSuccess,
    setChartDataSuccess,setSelectedUnitsSuccess,
    setSelectedUnitIDSuccess
} = replaySearchSlice.actions;

export default replaySearchSlice.reducer;
