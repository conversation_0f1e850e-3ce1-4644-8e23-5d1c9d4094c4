import React from "react";
import { Grid, IconButton } from "@mui/material";
import { InsertDriveFile, Delete, PictureAsPdf, Download } from "@mui/icons-material";
import { ErrorBoundary } from "react-error-boundary";
import ConfirmationDialog from "../../components/ConfirmationDialog/ConfirmationDialog";
import { useTranslation } from "react-i18next";

const MultiFileViewer = ({ attachments = [], gridProps = {}, fileId = null, fileDelete }) => {
    const PREVIEW_SIZE = 140;
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [deleteObj, setDeleteObject] = React.useState(null);

    const downloadFile = (url) => {
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('target', '_blank');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const onDelete = (obj, index, fileId = null) => {
        setOpen(true);
        setDeleteObject({ fileKey: obj.fileKey, fileId: fileId })
    }

    const handleClose = (value) => {
        if (value) {
            fileDelete(deleteObj);
        }
        setOpen(false); // Always close the dialog
    }

    return (
        <Grid item {...gridProps}>
            <Grid container spacing={2}>
                {attachments.map((fileObj, index) => {
                    const displaySrc = fileObj?.fileUrl ?? null;
                    const fileName = fileObj?.fileName ?? "Unknown File";
                    const fileType = fileObj?.mimeType || "";

                    const renderFilePreview = () => {
                        if (fileType.startsWith("image/")) {
                            return (
                                <img
                                    src={displaySrc}
                                    alt={`file-preview-${index}`}
                                    style={{
                                        width: PREVIEW_SIZE,
                                        height: PREVIEW_SIZE,
                                        objectFit: "cover",
                                        borderRadius: 8,
                                        border: "1px solid lightgray",
                                    }}
                                />
                            );
                        }

                        if (fileType.startsWith("video/")) {
                            return (
                                <video
                                    src={displaySrc}
                                    controls
                                    style={{
                                        width: PREVIEW_SIZE,
                                        height: PREVIEW_SIZE,
                                        objectFit: "cover",
                                        borderRadius: 8,
                                        border: "1px solid lightgray",
                                    }}
                                />
                            );
                        }

                        if (fileType.startsWith("audio/")) {
                            return (
                                <div style={{
                                    width: PREVIEW_SIZE,
                                    height: PREVIEW_SIZE,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderRadius: 8,
                                    border: "1px solid lightgray",
                                    overflow: "hidden"
                                }}>
                                    <audio
                                        src={displaySrc}
                                        controls
                                        style={{
                                            width: "100%",
                                            height: "40px",
                                        }}
                                    />
                                </div>
                            );
                        }

                        if (fileType === "application/pdf") {
                            return (
                                <a
                                    href={displaySrc}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    style={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        textDecoration: "none",
                                    }}
                                >
                                    <PictureAsPdf
                                        fontSize="large"
                                        style={{
                                            width: PREVIEW_SIZE,
                                            height: PREVIEW_SIZE,
                                            objectFit: "cover",
                                            borderRadius: 8,
                                            border: "1px solid lightgray",
                                        }}
                                        color="error"
                                    />
                                </a>
                            );
                        }

                        return (
                            <InsertDriveFile fontSize="large" style={{
                                width: PREVIEW_SIZE,
                                height: PREVIEW_SIZE,
                                objectFit: "cover",
                                borderRadius: 8,
                                border: "1px solid lightgray"
                            }} />
                        );
                    };

                    return (
                        <Grid item key={index}>
                            <div style={{ position: "relative", display: "inline-block", textAlign: "center" }}>
                                {renderFilePreview()}
                                {displaySrc && (
                                    <>
                                        <IconButton
                                            size="small"
                                            onClick={() => downloadFile(displaySrc)}
                                            style={{
                                                position: "absolute",
                                                top: 2,
                                                right: 2,
                                                background: "white",
                                            }}
                                            aria-label="Download File"
                                        >
                                            <Download fontSize="small" color="error" />
                                        </IconButton>
                                        <IconButton
                                            size="small"
                                            onClick={() => onDelete(fileObj, index, fileId)}
                                            style={{
                                                position: "absolute",
                                                top: 2,
                                                left: 2,
                                                background: "white",
                                            }}
                                            aria-label="Delete File"
                                        >
                                            <Delete fontSize="small" color="error" />
                                        </IconButton>
                                    </>
                                )}
                            </div>
                        </Grid>
                    );
                })}
            </Grid>
            <ErrorBoundary
                FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }}>
                <ConfirmationDialog
                    id="delete-file-confirmation"
                    keepMounted
                    open={open}
                    text={t("incidentFileDeleteMsg")}
                    onClose={handleClose}
                ></ConfirmationDialog>
            </ErrorBoundary>
        </Grid>
    );
};

export default MultiFileViewer;
