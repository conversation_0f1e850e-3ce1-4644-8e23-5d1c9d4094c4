import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { saveEmailConfiguration, updateEmailConfiguration, getEmailConfigurations } from '../store/emailConfigurationSlice'
import { useTranslation } from 'react-i18next';
import { Card, AppBar, CardContent, Toolbar, TextField } from '@mui/material';
import { newUserAudit } from '../../userAuditPage/store/userAuditSlice';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import { useParams } from 'react-router-dom';


const schema = yup.object().shape({
    emailUser: yup.string().required('Please enter email user.'),
    emailPassword: yup.string().required('Please enter email password'),
    emailHost: yup.string().required('Please enter email host'),
    emailPort: yup.string().required('Please enter email port'),
    emailFrom: yup.string().email('You must enter a valid email').required('Please enter email from'),
});
const defaultValues = {
    emailUser: '',
    emailPassword: '',
    emailHost: '',
    emailPort: 0,
    emailFrom: ''
};


function emailConfiguration(props) {
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();

    const emailConfigurations = useSelector(({ administration }) => administration.emailConfiguration.data);
    const user = useSelector(({ auth }) => auth.user);
    const routeParams = useParams();
    const agency = routeParams?.id;

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });
    const { isValid, dirtyFields, errors } = formState;

    useEffect(() => {
        dispatch(newUserAudit({
            activity: "Access Server Configuration",
            user: user,
            appName: "Admin",
        }));
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        setValue('emailUser', '');
        setValue('emailPassword', '');
        setValue('emailHost', '');
        setValue('emailPort', 0);
        setValue('emailFrom', '');
        dispatch(getEmailConfigurations());
    }, [dispatch, setValue]);

    useEffect(() => {
        if (emailConfigurations.length === 0) {
            setValue('emailUser', '');
            setValue('emailPassword', '');
            setValue('emailHost', '');
            setValue('emailPort', 0);
            setValue('emailFrom', '');

        }
        else {
            setValue('emailUser', emailConfigurations[0].emailUser);
            setValue('emailPassword', emailConfigurations[0].emailPassword);
            setValue('emailHost', emailConfigurations[0].emailHost);
            setValue('emailPort', emailConfigurations[0].emailPort);
            setValue('emailFrom', emailConfigurations[0].emailFrom);

        }
    }, [emailConfigurations]);

    function onSubmit(model) {
        const data = {
            _id: "0",
            emailUser: model.emailUser,
            emailPassword: model.emailPassword,
            emailHost: model.emailHost,
            emailPort: model.emailPort,
            emailFrom: model.emailFrom
        };

        if (emailConfigurations.length === 0) {
            dispatch(saveEmailConfiguration(data));
        }
        else {
            data._id = emailConfigurations[0]._id;
            dispatch(updateEmailConfiguration(data));

        }

    }



    return (
        <div class="p-16 w-2/4">
            <Card className="w-full mb-16 rounded-8 ml-4 shadow ">
                <AppBar position="static" elevation={0}>
                    <Toolbar className="px-8">
                        <Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
                            {t('emailConfiguration')}
                        </Typography>
                    </Toolbar>
                </AppBar>

                <CardContent>
                    <form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>

                        <Controller
                            name="emailUser"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('user')}
                                    type="text"
                                    error={!!errors.emailUser}
                                    helperText={errors?.emailUser?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="emailPassword"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('password')}
                                    type="text"
                                    error={!!errors.emailPassword}
                                    helperText={errors?.emailPassword?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="emailHost"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('host')}
                                    type="text"
                                    error={!!errors.emailHost}
                                    helperText={errors?.emailHost?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="emailPort"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('port')}
                                    type="number"
                                    error={!!errors.emailPort}
                                    helperText={errors?.emailPort?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="emailFrom"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('from')}
                                    type="text"
                                    error={!!errors.emailFrom}
                                    helperText={errors?.emailFrom?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />


                        <div className="mx-auto">
                            <Button
                                type="submit"
                                variant="contained"
                                color="primary"
                                className="normal-case m-16"
                                aria-label="REGISTER"
                                value="legacy">
                                {t('update')}
                            </Button>

                        </div>
                    </form>

                </CardContent>
            </Card>
        </div>
    );
}

export default emailConfiguration;

