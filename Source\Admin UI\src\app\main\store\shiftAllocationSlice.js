import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const saveShiftAllocation = (data, pageIndex, pageLimit, sortField, sortDirection) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/shitAllocation/CreateShiftAllocation`, encrypt(JSON.stringify(data)))
            .then(async (res) => {
                if (res.status == 200) {
                    res.data = await JSON.parse(decrypt(res.data));
                    dispatch(setLoading(false));
                    dispatch(
                        showMessage({
                            message: res.data.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "right",
                            },
                            variant: "success",
                        })
                    );
                    dispatch(getShiftAllocation(sortField, sortDirection, pageIndex, pageLimit, data.code));
                }
                else {
                    res = JSON.parse(decrypt(res.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: res.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getShiftAllocation = (sortField, sortDirection, pageIndex, pageLimit, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/shitAllocation/getShiftAllocationDetails/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    dispatch(setLoading(false));
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setShiftAllocationTotalCount(listData.totalCount));
                    return dispatch(setShiftAllocationData(listData.ShiftAllocationList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                return console.error(error);
            });

    }
    catch (e) {
        return console.error(e.message);
    }
};

export const removeShiftAllocation = (ID, code, pageIndex, rowsPerPage, sortField, sortDirection) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .delete(`admin/api/shitAllocation/removeShiftAllocation/${ID}/${code}`)
            .then(async (res) => {
                if (res.status == 200) {
                    res = JSON.parse(decrypt(res.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: res.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: "top",
                            horizontal: "right",
                        },
                        variant: "success",
                    })
                    );
                    dispatch(removeShiftAllocationData(ID));
                }
                else {
                    res = JSON.parse(decrypt(res.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: res.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

//for searching
export const searchshitAllocation = (searchText, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        axios.get(`admin/api/shitAllocation/searchShiftAllocations/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchshiftAllocations(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    data: [],
    searchshiftAllocations: [],
    isloading: false,
};

const shiftAllocationSlice = createSlice({
    name: 'shiftallocation',
    initialState,
    reducers: {
        setShiftAllocationData: (state, action) => {
            state.data = action.payload;
        },
        setSearchshiftAllocations: (state, action) => {
            state.searchshiftAllocations = action.payload;
        },
        setShiftAllocationTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
        removeShiftAllocationData: (state, action) => {
            state.data = state.data.filter(x => x._id !== action.payload);
        },


    },
    extraReducers: {}
});

export const {
    setShiftAllocationData,
    setShiftAllocationTotalCount,
    setLoading,
    setSearchshiftAllocations,
    removeShiftAllocationData

} = shiftAllocationSlice.actions;

export default shiftAllocationSlice.reducer;
