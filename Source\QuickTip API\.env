
PORT = 8008
#QUICK_TIP_EXTERNAL_API = http://localhost:44329/
#QUICK_TIP_EXTERNAL_API = http://localhost:44329/Quiktip/V1/ 
QUICK_TIP_EXTERNAL_API = https://quicktipapi-rheal.azurewebsites.net/

# For Local Testing
#REACT_APP_SOCKETAPIURL =  http://localhost:8006


######################DevServer#################

# ConnectionString = mongodb+srv://RPSClusterDev:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
# REACT_APP_SOCKETAPIURL =  https://socketapi-dev2.rpsmobile.com
# # MFA QR Code Configuration
# ISSUER = Saber-Dev
# ALGORITHM = SHA1
# DIGITS = 6
# PERIOD = 30
# OTPTYPE = totp

# ###########################Live Server##########################
#ConnectionString = mongodb+srv://RPSWebLive:<EMAIL>/?retryWrites=true&w=majority

ConnectionString = mongodb+srv://RPSCluserLive:<EMAIL>/
REACT_APP_SOCKETAPIURL = https://socketapi.rpsmobile.com
#######MFA QR Code Configuration
ISSUER = Relativity
ALGORITHM = SHA1
DIGITS = 6
PERIOD = 30
OTPTYPE = totp

####################################################### Default Location Settings #####################
DEFAULT_LAT = -90.787036157692512
DEFAULT_LANG = 35.748592207264608
DEFAULT_MIN_X = -90.787036157692512
DEFAULT_MIN_Y = 35.748592207264608
DEFAULT_MAX_X = -90.551896051161805
DEFAULT_MAX_Y = 35.893300583455186

############################################### JWT token Configurations #################################################

JWT_EXPIRESIN = "4h"
JWT_PRIVATEKEY = "RhealPrivateKey123"

############################################### Set Logger Flag #################################################
IS_LOG = "false"

############################################### Import User Mail  ###################################

ADMIN_EMAIL_FOR_IMPORT_USER = "<EMAIL>"

##################### Email Configuration ###############
EmailUser = AKIA2B3BTLC6WTBGOOEV
EmailPassword = BEoZB167LUdxX1MxYyCeEfcLWgzFqj8X71qDddX6tZr+
EmailHost = email-smtp.us-east-2.amazonaws.com
EmailPort = 587
EmailFrom = <EMAIL>


 