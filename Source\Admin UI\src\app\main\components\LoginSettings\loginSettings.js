import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Card, AppBar, CardContent, Toolbar, TextField } from '@mui/material';
import { newUserAudit } from '../../userAuditPage/store/userAuditSlice';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import "./LoginSettings.css";
import { useParams } from 'react-router-dom';
import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import { getisOtp, updateSettings } from '../../store/loginSettingsSlice';

const schema = yup.object().shape({

});
const defaultValues = {

};

function loginSettings(props) {
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();
    const isOtp = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.loginSettings.isOtpdata);

    const user = useSelector(({ auth }) => auth.user);
    const [isOtpRequired, setIsOtpRequired] = React.useState(false);

    useEffect(() => {
        dispatch(newUserAudit({
            activity: "Access Settings",
            user: user,
            appName: "Admin",
        }));
        dispatch(getisOtp());
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        setIsOtpRequired(isOtp.isOtpRequired);
    }, [isOtp]);

    const handleisOtpRequiredChange = (event) => {
        setIsOtpRequired(event.target.checked);
    };

    const handlesubmit = () => {
        const item = {
            _id: isOtp._id,
            isOtpRequired: isOtpRequired,
        }
        dispatch(updateSettings(item));
    };

    return (
        <div class="p-16 w-2/4">
            <Card className="w-full mb-16 rounded-8 ml-4 shadow ">
                <AppBar position="static" elevation={0}>
                    <Toolbar className="px-8">
                        <Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
                            {t('loginOtpRequired')}
                        </Typography>
                    </Toolbar>
                </AppBar>

                <CardContent>
                    <FormControl component="fieldset" className="full-width">
                        <FormGroup>
                            <div className="flex justify-center">
                                <FormControlLabel
                                    control={<Checkbox checked={isOtpRequired} onChange={handleisOtpRequiredChange} name="isOtpRequired" />}
                                    label={t('isOtpRequired')}
                                />
                                <Button
                                    type="submit"
                                    variant="contained"
                                    color="primary"
                                    className="normal-case m-16"
                                    aria-label="REGISTER"
                                    value="legacy"
                                    onClick={handlesubmit}
                                >
                                    {t("update")}
                                </Button>
                            </div>
                        </FormGroup>
                    </FormControl>
                </CardContent>
            </Card>
        </div>
    );
}

export default loginSettings;

