import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { Card, AppBar, CardContent, Toolbar, TextField } from '@mui/material';
import { newUserAudit } from '../../userAuditPage/store/userAuditSlice';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import { useParams } from 'react-router-dom';
import { getTwitterConfigurations, updateTwitterConfiguration } from '../store/twitterConfigurationSlice';


const schema = yup.object().shape({
});
const defaultValues = {
    baseUrl: '',
    clientID: '',
    redirectUrl: '',
    authUrl: '',
    // responseType: '',
    // scope: '',
    // state: '',
    // codeChallenge: '',
    // codeChallengeMethod: '',
    // grantType: '',
    // grantTypeForRefreshToken: ''
};


function TwitterConfiguration(props) {
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();

    const routeParams = useParams();
    const twitterConfigurations = useSelector(({ administration }) => administration.twitterConfiguration.data);
    const user = useSelector(({ auth }) => auth.user);
    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });
    const { isValid, dirtyFields, errors } = formState;

    useEffect(() => {
        dispatch(newUserAudit({
            activity: "Access Twitter Configuration",
            user: user,
            appName: "Admin",
        }));
        // eslint-disable-next-line
        dispatch(getTwitterConfigurations());
    }, []);

    useEffect(() => {
        if (twitterConfigurations.length === 0) {
            setValue('baseUrl', '');
            setValue('authUrl', '');
            setValue('clientID', '');
            setValue('redirectUrl', '');
            // setValue('responseType', '');
            // setValue('scope', '');
            // setValue('state', '');
            // setValue('codeChallenge', '');
            // setValue('codeChallengeMethod', '');
            // setValue('grantType', '');
            // setValue('grantTypeForRefreshToken', '');


        }
        else {
            setValue('baseUrl', twitterConfigurations[0].baseUrl);
            setValue('clientID', twitterConfigurations[0].clientID);
            setValue('authUrl', twitterConfigurations[0].authUrl);
            setValue('redirectUrl', twitterConfigurations[0].redirectUrl);
            // setValue('responseType', twitterConfigurations[0].responseType);
            // setValue('scope', twitterConfigurations[0].scope);
            // setValue('state', twitterConfigurations[0].state);
            // setValue('codeChallenge', twitterConfigurations[0].codeChallenge);
            // setValue('codeChallengeMethod', twitterConfigurations[0].codeChallengeMethod);
            // setValue('grantType', twitterConfigurations[0].grantType);

            // setValue('grantTypeForRefreshToken', twitterConfigurations[0].grantTypeForRefreshToken);
        }
    }, [twitterConfigurations]);



    function onSubmit(model) {
        const data = {
            _id: "0",
            baseUrl: model.baseUrl,
            clientID: model.clientID,
            redirectUrl: model.redirectUrl,
            authUrl: model.authUrl,
            // responseType: model.responseType,
            // scope: model.scope,
            // state: model.state,
            // codeChallenge: model.codeChallenge,
            // codeChallengeMethod: model.codeChallengeMethod,
            // grantType: model.grantType,
            // grantTypeForRefreshToken: model.grantTypeForRefreshToken,

        };

        if (twitterConfigurations.length !== 0) {
            data._id = twitterConfigurations[0]._id;
            dispatch(updateTwitterConfiguration(data));
        }
    }

    return (
        <div class="p-16 w-2/4">
            <Card className="w-full mb-16 rounded-8 ml-4 shadow ">
                <AppBar position="static" elevation={0}>
                    <Toolbar className="px-8">
                        <Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
                            {t('twitterconfiguration')}
                        </Typography>
                    </Toolbar>
                </AppBar>

                <CardContent>
                    <form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>

                        <Controller
                            name="baseUrl"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('baseUrl')}
                                    type="text"
                                    error={!!errors.baseUrl}
                                    helperText={errors?.baseUrl?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <Controller
                            name="authUrl"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('authUrl')}
                                    type="text"
                                    error={!!errors.authUrl}
                                    helperText={errors?.authUrl?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <Controller
                            name="clientID"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('clientID')}
                                    type="text"
                                    error={!!errors.clientID}
                                    helperText={errors?.clientID?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <Controller
                            name="redirectUrl"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('redirectUrl')}
                                    type="text"
                                    error={!!errors.redirectUrl}
                                    helperText={errors?.redirectUrl?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        {/* <Controller
                            name="responseType"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('RseponseType')}
                                    type="text"
                                    error={!!errors.responseType}
                                    helperText={errors?.responseType?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        /> */}

                        {/* <Controller
                            name="scope"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('scope')}
                                    type="text"
                                    error={!!errors.scope}
                                    helperText={errors?.scope?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <Controller
                            name="state"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('state')}
                                    type="text"
                                    error={!!errors.state}
                                    helperText={errors?.state?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <Controller
                            name="codeChallenge"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('codeChallenge')}
                                    type="text"
                                    error={!!errors.codeChallenge}
                                    helperText={errors?.codeChallenge?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <Controller
                            name="codeChallengeMethod"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('codeChallengeMethod')}
                                    type="text"
                                    error={!!errors.codeChallengeMethod}
                                    helperText={errors?.codeChallengeMethod?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />
                        <Controller
                            name="grantType"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('grantType')}
                                    type="text"
                                    error={!!errors.grantType}
                                    helperText={errors?.grantType?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="grantTypeForRefreshToken"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16"
                                    label={t('grantTypeForRefreshToken')}
                                    type="text"
                                    error={!!errors.grantTypeForRefreshToken}
                                    helperText={errors?.grantTypeForRefreshToken?.message}
                                    variant="outlined"
                                    required
                                />
                            )}
                        /> */}




                        <div className="mx-auto">
                            <Button
                                type="submit"
                                variant="contained"
                                color="primary"
                                className="normal-case m-16"
                                aria-label="REGISTER"
                                value="legacy">
                                {t('update')}
                            </Button>

                        </div>
                    </form>

                </CardContent>
            </Card>
        </div>
    );
}

export default TwitterConfiguration;

