import React, { useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Grid from "@mui/material/Grid";
import "leaflet/dist/leaflet.css";
import { useTranslation } from "react-i18next";
import FormControl from "@mui/material/FormControl";
import FormGroup from "@mui/material/FormGroup";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import "./InvolvementTags.css";
import { selectNavbarTheme } from "app/store/fuse/settingsSlice";
import { Typography } from "@mui/material";

function InvolvementTags(props) {
  const { t } = useTranslation("laguageConfig");
  const InvolvementData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.incident.involvementTags);
  const navbarTheme = useSelector(selectNavbarTheme);
  const [focusedId, setFocusedId] = React.useState(null); // Track which checkbox is focused
  const [selectedTags, setSelectedTags] = React.useState([]);

  const focusStyle = {
    backgroundColor: "#f0f0f0",
    border: "2px solid #3f51b5",
  };

  function checkValue(item) {
    if (selectedTags.length > 0)
      return selectedTags.map(x => x.split('-')[1]).includes(item.Name)
    else
      return selectedTags.includes(item.Name);
  }

  // Effect to initialize the selectedTags state when checkData prop changes
  useEffect(() => {
    if (props.checkData !== undefined) {
      // Set selectedTags to the checkData prop when it changes
      setSelectedTags(props.checkData);
      props.involvmentarray(props.checkData);
    }
  }, [props.checkData]);

  const handleChange = (event) => {
    const tagName = `${event.target.id + "-" + event.target.name}`
    // Create a copy of the selected tags array
    const updatedSelectedTags = [...selectedTags];

    if (event.currentTarget.checked) {
      // If the checkbox is checked, add the tag to the array
      updatedSelectedTags.push(tagName);
      // props.setAgeType(null);
    } else {
      // If the checkbox is unchecked, remove the tag from the array
      const index = updatedSelectedTags.indexOf(tagName);
      if (index !== -1) {
        updatedSelectedTags.splice(index, 1);
      }
    }

    // Update the selected tags state
    setSelectedTags(updatedSelectedTags);
    props.involvmentarray(updatedSelectedTags);
  };

  return (
    <div className="cardPadding">
      <Card>
        <CardContent>
          <Grid item xs={12} sm={12} md={12} lg={12} xl={12}>
            <FormControl component="fieldset">
              <Typography className="font-semibold text-18 text-center">{t("involvementTags")}</Typography>
              <FormGroup className="grid grid-cols-3">
                {InvolvementData &&
                  InvolvementData.map((item, index) => (
                    <FormControlLabel
                      key={item.rank}
                      style={{
                        color:
                          navbarTheme.palette.mode === "light"
                            ? item.color
                            : item.color !== "black"
                              ? item.color
                              : "white",
                      }}
                      control={
                        <Checkbox
                          id={item.rank}
                          name={item.Name}
                          onFocus={() => setFocusedId(item.rank)}
                          onBlur={() => setFocusedId(null)}
                          style={focusedId === item.rank ? focusStyle : {}}
                          onChange={handleChange}
                          checked={checkValue(item)}
                        />
                      }
                      label={t(item.Name)}
                    />
                  ))}
              </FormGroup>
            </FormControl>
          </Grid>
        </CardContent>
      </Card>
    </div>
  );
}


export default InvolvementTags;
