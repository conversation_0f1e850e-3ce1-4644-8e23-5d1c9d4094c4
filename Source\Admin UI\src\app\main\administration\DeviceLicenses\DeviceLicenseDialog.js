import React, { forwardRef, useRef, useImperativeHandle, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { TextField } from '@mui/material';
import Button from '@mui/material/Button';
import { showMessage } from 'app/store/fuse/messageSlice';
import { getDeviceLicenseCode } from '../../utils/utils';
import { saveDeviceLincenses } from '../store/devicelicensesSlice';

const defaultValues = {
    DeviceLicenseID: '',
    DeviceLicenseDescription: ''
};

let update = false
let dataFound = false;
let devicedata;

const DeviceLicenseDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState(null);
    const [code, setCode] = React.useState("");
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [PagingDetails, setPagingDetails] = React.useState();
    const [deviceLicenseCode, setDeviceLivenseCode] = useState("");
    const [newdevicelicenses, setNewDeviceLicenses] = React.useState(false);
    const agencyList = useSelector(({ agency }) => agency.agency.data);
    const DeviceLicensesCount = agencyList.filter(y => y.code === code);
    const [id, setid] = React.useState("");
    const DeviceLincenses = useSelector(({ administration }) => administration.deviceLicenses.deviceLicenses);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues
    });

    const { isValid, dirtyFields, errors } = formState;
    const deviceLicenseDescriptionRef = useRef(null);

    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            deviceLicenseDescriptionRef.current?.focus();
        }, 0);
    };

    const ClearAll = () => {
        setValue('DeviceLicenseID', "");
        setValue('DeviceLicenseDescription', "");
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, PagingDetails, flag) {
            setData(data)
            setCode(code)
            setPagingDetails(PagingDetails)
            devicedata = DeviceLincenses.filter(y => y.DeviceId != data.DeviceId)
            update = flag
            setCallResponseValue(data)
            handleClickOpen1();
        },
    }));

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const setCallResponseValue = async (data) => {
        if (data._id && update) {
            setid(data._id);
            setNewDeviceLicenses(false);
            update = true
            setValue('DeviceLicenseID', data.DeviceId);
            setValue('DeviceLicenseDescription', data.DeviceDescription);
        }
        else {
            if (data.length >= DeviceLicensesCount.map(x => x.noOfDeviceLicenses)[0]) {
                ShowErroMessage(t('DeviceLimitReached'));
            }
            else {
                let data = await getDeviceLicenseCode();
                setDeviceLivenseCode(data);
                setValue('DeviceLicenseID', data);
                setValue('DeviceLicenseDescription', "");
                setNewDeviceLicenses(!newdevicelicenses);
                update = false
            }
        }
    };

    const handleClose = () => {
        setOpen(false);
        ClearAll();
    };

    function onSubmit(model) {
        if (id === null || id === "" || id === undefined) {
            const deviceId = data.filter(y => y.DeviceId === deviceLicenseCode)
            if (deviceId.length > 0 && deviceId[0].DeviceId === deviceLicenseCode) {
                ShowErroMessage(t('Devicealreadyexisting'));
            }
            else {
                saveeditdata(false, model);
                setid("");
            }
        }
        else {
            if (devicedata.length > 0) {
                let x = devicedata.filter(element => element.DeviceId === deviceLicenseCode);
                if (x.length > 0) {
                    dataFound = x[0].DeviceId == model.DeviceLicenseID ? true : false;
                }
                if (dataFound) {
                    ShowErroMessage(t('Devicealreadyexisting'));
                }
                else {
                    saveeditdata(true, model);
                }
            }
            else {
                saveeditdata(true, model);
            }
        }
        dataFound = false;
    }

    function saveeditdata(update, model) {
        dispatch(
            saveDeviceLincenses({
                id: id,
                DeviceId: model.DeviceLicenseID,
                DeviceDescription: model.DeviceLicenseDescription,
                isUpdate: update,
                code: code,
            }
            ));
        setIsUpdate(false);
        setNewDeviceLicenses(false);
        dataFound = false;
        setid(null);
        handleClose();
    }

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("deviceLicenses")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        name="registerForm"
                        noValidate
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        // autoComplete="off"
                        autoSave={false}
                    >
                        <Controller
                            name="DeviceLicenseID"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("deviceId")}
                                    type="text"
                                    error={!!errors.DeviceLicenseID}
                                    helperText={errors?.DeviceLicenseID?.message}
                                    disabled={update ? true : false}
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <Controller
                            name="DeviceLicenseDescription"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mb-16 w-full"
                                    label={t("deviceDescription")}
                                    type="text"
                                    error={!!errors.DeviceLicenseDescription}
                                    helperText={errors?.DeviceLicenseDescription?.message}
                                    multiline
                                    rows={4}
                                    variant="outlined"
                                    required
                                    inputRef={deviceLicenseDescriptionRef}
                                />
                            )}
                        />

                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
});

export default DeviceLicenseDialog;