import React from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const WarrantsAlert = ({ warrants }) => {

    const { t } = useTranslation('languageConfig');

    const getInitials = (fullName) => {
        if (!fullName || typeof fullName !== 'string') return '';
        const parts = fullName.split(',').map(p => p.trim());
        if (parts.length === 2) {
            const [lastName, firstAndMiddle] = parts;
            const firstName = firstAndMiddle.split(' ')[0] || '';
            const last = lastName.split(' ')[0] || '';
            return (firstName[0] || '') + (last[0] || '');
        }
        const names = fullName.trim().split(/\s+/);
        const first = names[0] || '';
        const last = names[names.length - 1] || '';
        return (first[0] || '') + (last[0] || '');
    };

    return (
        <div className="p-8">
            <Box className="text-center mb-16">
                <Typography variant="h3" className="text-blue-800 font-extrabold text-4xl">
                    📄 {t("activeWarrantRegistry")}
                </Typography>
                {/* <Typography variant="subtitle1" className="text-gray-600 text-xl mt-2">
                    Security Management Dashboard
                </Typography> */}
            </Box>

            {(!warrants || warrants.length === 0) ? (
                <div className="text-center text-gray-500 text-xl mt-10">
                    {t("noActiveWarrantsFound")}
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-16">
                    {warrants.map((warrant, index) => (
                        <div
                            key={index}
                            className="rounded-2xl shadow-lg p-6 border-t-4 border-blue-600 transition-all duration-300 hover:shadow-2xl hover:-translate-y-1"
                        >
                            <div className="flex items-center space-x-6 mb-6">
                                <div className="h-40 w-40 rounded-full bg-blue-800 flex items-center justify-center font-extrabold text-2xl text-white">
                                    {getInitials(warrant.displayName)}
                                </div>
                                <div>
                                    <div className="text-xl font-semibold">
                                        {warrant.displayName || ''}
                                    </div>
                                    <div className="text-blue-600 text-sm underline">
                                        {warrant.warrantNumber || ''}
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 text-base gap-4 border-t pt-4">
                                <div>
                                    <div className="font-bold text-gray-500">{t("warrantStatus")}</div>
                                    <div className="font-semibold">{warrant.warrantStatus || ''}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("issuedDate")}</div>
                                    <div className="font-semibold">{warrant.issuedDate || ''}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("dobAbbreviation")}</div>
                                    <div className="font-semibold">{warrant.dob || ''}</div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("dlState")}</div>
                                    <div className="font-semibold">
                                        {warrant.dlNumber || ''} / {warrant.dlState || ''}
                                    </div>
                                </div>
                                <div>
                                    <div className="font-bold text-gray-500">{t("genderRace")}</div>
                                    <div className="font-semibold">
                                        {warrant.sex || ''} / {warrant.race || ''}
                                    </div>
                                </div>
                            </div>

                            <div className="mt-6 bg-gray-100 dark:bg-gray-800 rounded-md p-3 text-base leading-relaxed">
                                <div>
                                    <span className="font-bold text-gray-500">{t("remoteSource")}:</span>{' '}
                                    <span className="font-semibold">{warrant.remotesource || 'Unknown'}</span>
                                </div>
                            </div>

                            {warrant.charges?.length > 0 && (
                                <div className="mt-6">
                                    <div className="text-600 font-bold mb-2 text-gray-500">{t("charges")}:</div>
                                    <ul className="list-disc list-inside space-y-1 text-800 text-sm">
                                        {warrant?.charges?.map((charge, idx) => (
                                            <li key={idx}>
                                                <span className="font-semibold">{charge.offenseDescription}</span>{' '}
                                                <span className="text-500">({charge.statuteCitation})</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default WarrantsAlert;
