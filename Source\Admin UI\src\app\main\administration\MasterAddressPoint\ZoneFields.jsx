import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { useTranslation } from "react-i18next";
import FormTextField from "src/app/main/SharedComponents/SharedFormFields/FormTextField";

const ZoneFields = () => {
  const { t } = useTranslation("laguageConfig");
  const fields = [
    { name: "PoliceZone", label: t("policeZone") },
    { name: "FireZone", label: t("fireZone") },
    { name: "FireAutoAssist", label: t("fireAutoAssist") },
    { name: "FireMutualAssist", label: t("fireMutualAssist") },
    { name: "WreckerServiceZone", label: t("wreckerServiceZone") },
    { name: "EMSZone", label: t("emsZone") },
    { name: "EMSAutoAssist", label: t("emsAutoAssist") },
    { name: "EMSMutualAssist", label: t("emsMutualAssist") },
  ];

  return (
    <Box
      component="fieldset"
      sx={{
        border: "1px solid #ccc",
        padding: 2,
        borderRadius: "5px",
        marginBottom: 2,
      }}
    >
      <legend style={{ padding: "0 10px", fontSize: "1.3rem" }}>
        {t("zone")}
      </legend>
      <Grid container columnSpacing={1} rowSpacing={2}>
        {fields.map((field) => (
          <FormTextField
            key={field.name}
            name={field.name}
            label={field.label}
            gridProps={{ xs: 12, sm: 12, md: 2.2 }}
          />
        ))}
      </Grid>
    </Box>
  );
};

export default ZoneFields;
