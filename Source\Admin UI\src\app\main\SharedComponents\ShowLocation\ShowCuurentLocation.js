import React, { useState, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import { isEmptyOrNull } from '../../utils/utils';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import VectorTileLayer from "react-leaflet-vector-tile-layer";

let defaultlatlan = {
    MaxX: "-90.064005271999974",
    MaxY: "35.441653765000069",
    MinX: "-90.506531612999936",
    MinY: "34.826566366000065"
}

function ShowCuurentLocation(props) {
    const mapRef = useRef();
    const [styleUrl, setStyleUrl] = useState(require('../../../stadiaMapJson/osm_bright.json'));

    // // Commented Since there is no dark mode in call 911 maps
    // useEffect(() => {
    //     if (darkMode) {
    //         setStyleUrl(require('../../../stadiaMapJson/osm_bright_dark.json'));
    //     } else {
    //         setStyleUrl(require('../../../stadiaMapJson/osm_bright.json'));
    //     }
    // }, [darkMode]);

    var customIcon = L.icon({
        iconUrl: require('../../../main/911Call/components/otherCallIcon.png'),
        iconSize: [30, 40]
    });

    return (
        <div style={{ height: '700px', width: '100%' }}>
            <MapContainer
                ref={mapRef}
                center={!isEmptyOrNull(props)
                    ? [props.Latitude, props.Longitude]
                    : [defaultlatlan.MinY, defaultlatlan.MinX]}
                zoom={15}
                style={{ height: '700px', width: '100%' }}
            >
                <VectorTileLayer
                    styleUrl={styleUrl}
                />
                <>
                    {!isEmptyOrNull(props) &&
                        <Marker position={[props.Latitude, props.Longitude]} icon={customIcon}>
                        </Marker>
                    }
                </>
            </MapContainer>
        </div>
    );
}

export default ShowCuurentLocation;