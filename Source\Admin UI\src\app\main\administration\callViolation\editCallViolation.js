import { useParams } from 'react-router';
import { useLocation } from 'react-router-dom';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';

import _ from '@lodash';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import makeStyles from '@mui/styles/makeStyles';

import history from '@history';
import { useTranslation } from 'react-i18next';


import { Grid, CardContent, Card, AppBar, Toolbar, TextField, Typography } from '@mui/material';

import { getSelectedViolation, saveCallViolation } from '../store/callViolationSlice';

import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import { IsCheckMongooseObjectId, isEmptyOrNull } from '../../utils/utils';
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';

const useStyles = makeStyles({
    w50: {
        width: '100%',
        marginBottom: "1.6rem"
    }, root: {
        display: 'flex',
    },
});

const YesNoOptions = [{ id: 1, name: "Yes" }, { id: 2, name: "No" }];

const schema = yup.object().shape({
    statuteTitle: yup.string().required('Please enter Statute Title.'),
    statuteCitation: yup.string().required('Please enter Statute Citation.'),
    statuteDesc: yup.string().required('Please enter Statute Desc.'),
    offenseDescription: yup.string().required('Please enter OffenseDescription.'),
    localCode: yup.string().required('Please enter Local Code.'),
    ncicID: yup.string().required('Please enter Local Code.'),
    violationExample: yup.string().required('Please enter Local Code.'),
    violationType: yup.string().required('Please enter Local Code.'),
    nibrsID: yup.string().required('Please enter Local Code.'),
    name: yup.string().required('Please enter Local Code.'),
    classification: yup.string().required('Please enter Local Code.'),
    iscriminal: yup.string().required('Please select IsCriminal.'),
    isWarrant: yup.string().required('Please select IsWarrant.'),
    isTraffic: yup.string().required('Please select IsTraffic.'),
    isDWI1: yup.string().required('Please select IsDWI1.'),
    shortDescription: yup.string().required('Please enter short description.'),
    isDWI2: yup.string().required('Please select IsDWI2.'),
    isDWI3: yup.string().required('Please select IsDWI2.'),
    lastvalidyear: yup.string().required('Please enter  last valid year.'),
    datelastValid: yup.string().required('Please enter date last valid.'),
    minSentence: yup.string().required('Please enter MinSentence.'),
    maxSentence: yup.string().required('Please enter MaxSentence.'),
    sentenceType: yup.string().required('Please enter SentenceType.'),
    defaultClassificationID: yup.string().required('Please enter DefaultClassificationID.'),
    life: yup.string().required('Please enter Life.'),
    minFines: yup.string().required('Please enter MinFines.'),
    maxFines: yup.string().required('Please enter MaxFines.'),
    nomaximumFine: yup.string().required('Please enter NoMaximumFine.'),
    isSeatbeltdeductible: yup.string().required('Please select IsSeatbeltDeductible.'),
    isInstatelist: yup.string().required('Please select IsInStateList.'),
    isCitationviol: yup.string().required('Please select IsCitationViol.'),
    isLocalordinance: yup.string().required('Please select IsLocalOrdinance.'),
    isDomesticviolence: yup.string().required('Please select IsDomesticViolence.'),
    acflag: yup.string().required('Please enter ACFlag.'),
    cleryCat: yup.string().required('Please enter CleryCat.'),
    mustAppear: yup.string().required('Please enter MustAppear.'),
    fingerprintRequired: yup.string().required('Please select FingerPrintRequired.'),
    sendtodog: yup.string().required('Please enter SendToDOG.'),
    complianceReview: yup.string().required('Please enter ComplianceReview.'),
    bondamountDefault: yup.string().required('Please enter BondAmountDefault.'),
    dnarequired: yup.string().required('Please enter DNARequired.'),
    ndexsubmitype: yup.string().required('Please enter NDEXSubmitType.'),
    arcriminaltrafficid: yup.string().required('Please enter ARCriminalTrafficID.'),
});

const defaultValues = {
    statuteTitle: '',
    statuteCitation: '',
    statuteDesc: '',
    offenseDescription: '',
    localCode: '',
    ncicID: '',
    violationExample: '',
    violationType: '',
    nibrsID: '',
    name: '',
    classification: '',
    iscriminal: '0',
    isWarrant: '0',
    isTraffic: '0',
    isDWI1: '0',
    shortDescription: '',
    isDWI2: '0',
    isDWI3: '0',
    lastvalidyear: '',
    datelastValid: '',
    minSentence: '',
    maxSentence: '',
    sentenceType: '',
    defaultClassificationID: '',
    life: '',
    minFines: '',
    maxFines: '',
    nomaximumFine: '',
    isSeatbeltdeductible: '0',
    isInstatelist: '0',
    isCitationviol: '0',
    isLocalordinance: '0',
    isDomesticviolence: '0',
    acflag: '',
    cleryCat: '',
    mustAppear: '',
    fingerprintRequired: '0',
    sendtodog: '',
    complianceReview: '',
    bondamountDefault: '',
    dnarequired: '',
    ndexsubmitype: '',
    arcriminaltrafficid: '',
};

function EditCallViolations() {
    const classes = useStyles();
    const callViolationData = useSelector(({ administration }) => administration.callViolations.data)
    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();
    const formRef = useRef(null);
    const selectedViolation = useSelector(({ administration }) => administration.callViolations.selectedViolation)
    const routeParams = useParams();
    const location = useLocation();
    const { tempData } = location.state || {};
    const statuteTitleInputRef = useRef(null);
    //Accessing temp tempData required for get request

    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
        defaultValues,
        resolver: yupResolver(schema),
    });

    const { isValid, dirtyFields, errors } = formState;
    const [IsCriminalValue, setIsCriminal] = React.useState(null);
    const [IsWarrantValue, setIsWarrant] = React.useState(null);
    const [IsTrafficValue, setIsTraffic] = React.useState(null);
    const [IsDWI1Value, setIsDWI1Value] = React.useState(null);
    const [IsDWI2Value, setIsDWI2Value] = React.useState(null);
    const [IsDWI3Value, setIsDWI3Value] = React.useState(null);
    const [IsSeatbeltdeductible, setisSeatbeltdeductibleValue] = React.useState(null);
    const [IsInstatelistValue, setIsInstatelistValue] = React.useState(null);
    const [IsCitationviolValue, setIsCitationviolValue] = React.useState(null);
    const [IsLocalordinanceValue, setIsLocalordinanceValue] = React.useState(null);
    const [fingerprintRequiredvalue, setfingerprintRequiredvalue] = React.useState(null);
    const [IsDomesticViolencevalue, setIsDomesticViolencevalue] = React.useState(null);

    useEffect(() => {
        selectedViolation
        if (routeParams.id)
            setSelectedViolationById();
    }, [dispatch, routeParams])
    useEffect(() => {
    }, [selectedViolation])

    function setSelectedViolationById() {

        if (routeParams.id !== '0') {
            var data = callViolationData.filter(x => x._id == routeParams.id)[0]
            setValue('statuteTitle', data.StatuteTitle);
            setValue('classification', data.Classification);
            setValue('iscriminal', data.IsCriminal === true ? "1" : "0");
            const isCriminal = YesNoOptions.find(x => x.name === data.IsCriminal);
            setIsCriminal(isCriminal);
            const isWarrant = YesNoOptions.find(x => x.name === data.IsWarrant)
            setIsWarrant(isWarrant);
            setValue('localCode', data.LocalCode);
            setValue('name', data.Name);
            setValue('offenseDescription', data.OffenseDescription);
            setValue('shortDescription', data.ShortDescription);
            setValue('statuteCitation', data.StatuteCitation);
            setValue('statuteDesc', data.StatuteDesc);
            setValue('violationExample', data.ViolationExample);
            setValue('violationType', data.ViolationType);
            setValue('nibrsID', parseInt(data.NibrsID));
            setValue('ncicID', parseInt(data.NcicID));
            const isInStateList = YesNoOptions.find(x => x.name === data.IsInStateList);
            setIsInstatelistValue(isInStateList);
            setValue('acflag', data.ACFlag);
            const isCitationValue = YesNoOptions.find(x => x.name === data.IsCitationViol);
            setIsCitationviolValue(isCitationValue);
            setValue('cleryCat', data.CleryCat);
            setValue('mustAppear', data.MustAppear);
            const isSeatbeltDeductible = YesNoOptions.find(x => x.name === data.IsSeatbeltDeductible);
            setisSeatbeltdeductibleValue(isSeatbeltDeductible);
            const isTraffic = YesNoOptions.find(x => x.name === data.IsTraffic);
            setIsTraffic(isTraffic);
            const isDWI1 = YesNoOptions.find(x => x.name === data.IsDWI1);
            setIsDWI1Value(isDWI1);
            const isDWI2 = YesNoOptions.find(x => x.name === data.IsDWI2);
            setIsDWI2Value(isDWI2);
            const isDWI3 = YesNoOptions.find(x => x.name === data.IsDWI3);
            setIsDWI3Value(isDWI3);
            const isDomesticViolence = YesNoOptions.find(x => x.name === data.IsDomesticViolence);
            setIsDomesticViolencevalue(isDomesticViolence);
            const isLocalOrdinance = YesNoOptions.find(x => x.name === data.IsLocalOrdinance);
            setIsLocalordinanceValue(isLocalOrdinance);
            setValue('lastvalidyear', data.LastValidYear);
            setValue('datelastValid', data.DateLastValid);
            setValue('sendtodog', data.SendToDOG);
            setValue('complianceReview', data.ComplianceReview);
            setValue('minSentence', data.MinSentence);
            setValue('maxSentence', data.MaxSentence);
            setValue('sentenceType', data.SentenceType);
            setValue('sentenceType', data.SentenceType);
            setValue('life', data.Life);
            setValue('minFines', data.MinFines);
            setValue('maxFines', data.MaxFines);
            setValue('nomaximumFine', data.NoMaximumFine);
            setValue('fingerprintRequired', data.FingerPrintRequired === true ? "1" : "0");
            const isFingerprintRequired = YesNoOptions.find(x => x.name === data.FingerPrintRequired);
            setfingerprintRequiredvalue(isFingerprintRequired);
            setValue('bondamountDefault', data.BondAmountDefault);
            setValue('bondamountDefault', data.BondAmountDefault);
            setValue('ndexsubmitype', data.NDEXSubmitType);
            setValue('arcriminaltrafficid', data.ARCriminalTrafficID);
            setValue('dnarequired', data.DNARequired);
            setValue('defaultClassificationID', data.DefaultClassificationID);

        }
    }

    function onSubmit(model) {

        //alert("hi");
        const data = {
            _id: '0',
            //ViolationID: routeParams.id,
            Classification: model.classification,
            IsCriminal: isEmptyOrNull(IsCriminalValue) ? null : IsCriminalValue?.name,
            IsWarrant: isEmptyOrNull(IsWarrantValue) ? null : IsWarrantValue?.name,
            LocalCode: model.localCode,
            Name: model.name,
            OffenseDescription: model.offenseDescription,
            ShortDescription: model.shortDescription,
            StatuteCitation: model.statuteCitation,
            StatuteDesc: model.statuteDesc,
            StatuteTitle: model.statuteTitle,
            ViolationExample: model.violationExample,
            ViolationType: model.violationType,
            NibrsID: parseInt(model.nibrsID),
            NcicID: parseInt(model.ncicID),
            IsInStateList: isEmptyOrNull(IsInstatelistValue) ? null : IsInstatelistValue?.name,
            ACFlag: model.acflag,
            IsCitationViol: isEmptyOrNull(IsCitationviolValue) ? null : IsCitationviolValue?.name,
            CleryCat: model.cleryCat,
            MustAppear: model.mustAppear,
            IsSeatbeltDeductible: isEmptyOrNull(IsSeatbeltdeductible) ? null : IsSeatbeltdeductible?.name,
            IsTraffic: isEmptyOrNull(IsTrafficValue) ? null : IsTrafficValue?.name,
            IsLocalOrdinance: isEmptyOrNull(IsLocalordinanceValue) ? null : IsLocalordinanceValue?.name,
            LastValidYear: model.lastvalidyear,
            DateLastValid: model.datelastValid,
            IsDWI1: isEmptyOrNull(IsDWI1Value) ? null : IsDWI1Value?.name,
            IsDWI2: isEmptyOrNull(IsDWI2Value) ? null : IsDWI2Value?.name,
            IsDWI3: isEmptyOrNull(IsDWI3Value) ? null : IsDWI3Value?.name,
            IsDomesticViolence: isEmptyOrNull(IsDomesticViolencevalue) ? null : IsDomesticViolencevalue?.name,
            SendToDOG: model.sendtodog,
            ComplianceReview: model.complianceReview,
            DefaultClassificationID: parseInt(model.defaultClassificationID),
            MinSentence: model.minSentence,
            MaxSentence: model.maxSentence,
            SentenceType: model.sentenceType,
            Life: model.life,
            MinFines: model.minFines,
            MaxFines: model.maxFines,
            NoMaximumFine: model.nomaximumFine,
            FingerPrintRequired: isEmptyOrNull(fingerprintRequiredvalue) ? null : fingerprintRequiredvalue?.name,
            BondAmountDefault: model.bondamountDefault,
            DNARequired: model.dnarequired,
            NDEXSubmitType: model.ndexsubmitype,
            ARCriminalTrafficID: parseInt(model.arcriminaltrafficid),
            isUpdate: false,
            code: routeParams.code
        };

        var checkObjectId = IsCheckMongooseObjectId(routeParams.id)
        if (checkObjectId) {
            data.isUpdate = true;
            data._id = routeParams.id;
            data.code = routeParams.code;
        }
        dispatch(saveCallViolation(data, tempData));
        history.push(`/admin/callViolations/${routeParams.code}`)


    }

    const handleIsCriminalChange = (event, newValue) => {
        setIsCriminal(newValue);
    };

    const handleIsWarrantChange = (event, newValue) => {
        setIsWarrant(newValue);
    };

    const handleIsTrafficChange = (event, newValue) => {
        setIsTraffic(newValue);
        // setValue('isTraffic', event.target.value);
    };

    const handleIsDWI1ValueChange = (event, newValue) => {
        setIsDWI1Value(newValue);
        // setValue('isDWI1', event.target.value);
    };

    const handleIsDWI2ValueChange = (event, newValue) => {
        setIsDWI2Value(newValue);
        // setValue('isDWI2', event.target.value);
    };

    const handleIsDWI3ValueChange = (event, newValue) => {
        setIsDWI3Value(newValue);
        // setValue('isDWI3', event.target.value);
    };

    const handleIsSeatbeltdeductibleChange = (event, newValue) => {
        setisSeatbeltdeductibleValue(newValue);
        // setValue('isSeatbeltdeductible', event.target.value);
    };

    const handleIsInstatelistChange = (event, newValue) => {
        setIsInstatelistValue(newValue);
        // setValue('isInstatelist', event.target.value);
    };

    const handleIsCitationviolChange = (event, newValue) => {
        setIsCitationviolValue(newValue);
        // setValue('isCitationviol', event.target.value);
    };

    const handleIsLocalordinanceChange = (event, newValue) => {
        setIsLocalordinanceValue(newValue);
        // setValue('isLocalordinance', event.target.value);
    };

    const handlefingerprintRequiredChange = (event, newValue) => {
        setfingerprintRequiredvalue(newValue);
        // setValue('fingerprintRequired', event.target.value);
    };

    const handleIsDomesticViolenceChange = (event, newValue) => {
        setIsDomesticViolencevalue(newValue);
        // setValue('isDomesticviolence', event.target.value);
    };

    useEffect(() => {
        if (statuteTitleInputRef.current) {
            statuteTitleInputRef.current.focus();
        }
    }, [statuteTitleInputRef]);

    return (
        <div className="p-16">
            <Card className=" m-16 rounded-8 shadow">
                <AppBar position="static" elevation={0}>
                    <Toolbar className="px-8">
                        <Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
                            {t('violation')}
                        </Typography>
                    </Toolbar>
                </AppBar>

                <CardContent style={{ overflowX: "scroll", height: "90%" }}>
                    <div className="w-full p-16 ">
                        <form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)} autoSave={false}
                            autoComplete={false}>
                            <div className="flex -mx-4">

                                <Controller
                                    className={classes.w50}
                                    name="statuteTitle"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('statuteTitle')}
                                            type="text"
                                            inputRef={statuteTitleInputRef}
                                            error={!!errors.statuteTitle}
                                            helperText={errors?.statuteTitle?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="statuteCitation"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('statuteCitation')}
                                            type="text"
                                            error={!!errors.statuteCitation}
                                            helperText={errors?.statuteCitation?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="statuteDesc"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('statutedesc')}
                                            type="text"
                                            //rows={2}
                                            //multiline
                                            error={!!errors.statuteDesc}
                                            helperText={errors?.statuteDesc?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">

                                <Controller
                                    className={classes.w50}
                                    name="offenseDescription"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('offenseDescription')}
                                            type="text"
                                            //rows={2}
                                            //multiline
                                            error={!!errors.offenseDescription}
                                            helperText={errors?.offenseDescription?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                                <Controller
                                    className={classes.w50}
                                    name="localCode"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('localCode')}
                                            type="text"
                                            error={!!errors.localCode}
                                            helperText={errors?.localCode?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />


                                <Controller
                                    className={classes.w50}
                                    name="ncicID"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('ncicId')}
                                            type="number"
                                            error={!!errors.ncicID}
                                            helperText={errors?.ncicID?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">

                                <Controller
                                    className={classes.w50}
                                    name="violationExample"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('violationExample')}
                                            type="text"
                                            error={!!errors.violationExample}
                                            helperText={errors?.violationExample?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="violationType"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('violationType')}
                                            type="text"
                                            error={!!errors.violationType}
                                            helperText={errors?.violationType?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />


                                <Controller
                                    className={classes.w50}
                                    name="nibrsID"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('nibrsId')}
                                            type="number"
                                            error={!!errors.nibrsID}
                                            helperText={errors?.nibrsID?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="name"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('name')}
                                            type="text"
                                            error={!!errors.name}
                                            helperText={errors?.name?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                            </div>



                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="classification"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('classification')}
                                            type="text"
                                            error={!!errors.classification}
                                            helperText={errors?.classification?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="shortDescription"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('shortDescription')}
                                            type="text"
                                            error={!!errors.shortDescription}
                                            helperText={errors?.shortDescription?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>
                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsCriminalValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsCriminalChange}
                                                options={YesNoOptions || []}
                                                value={IsCriminalValue}
                                                fieldName={t('isCriminal')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>
                                    </ErrorBoundary>
                                </FormControl>

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsWarrantValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsWarrantChange}
                                                options={YesNoOptions || []}
                                                value={IsWarrantValue}
                                                fieldName={t('isWarrant')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsTrafficValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsTrafficChange}
                                                options={YesNoOptions || []}
                                                value={IsTrafficValue}
                                                fieldName={t('isTraffic')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>
                            </div>


                            <div className="flex -mx-4">
                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsDWI1Value" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsDWI1ValueChange}
                                                options={YesNoOptions || []}
                                                value={IsDWI1Value}
                                                fieldName={t('isDWI1')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsDWI2Value" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsDWI2ValueChange}
                                                options={YesNoOptions || []}
                                                value={IsDWI2Value}
                                                fieldName={t('isDWI2')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsDWI2Value" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsDWI3ValueChange}
                                                options={YesNoOptions || []}
                                                value={IsDWI3Value}
                                                fieldName={t('isDWI3')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>


                                <Controller
                                    className={classes.w50}
                                    name="lastvalidyear"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('lastValidYear')}
                                            type="text"
                                            error={!!errors.lastvalidyear}
                                            helperText={errors?.lastvalidyear?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="datelastValid"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('dateLastValid')}
                                            type="text"
                                            error={!!errors.datelastValid}
                                            helperText={errors?.datelastValid?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="minSentence"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('minSentence')}
                                            type="text"
                                            error={!!errors.minSentence}
                                            helperText={errors?.minSentence?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="maxSentence"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('maxSentence')}
                                            type="text"
                                            error={!!errors.maxSentence}
                                            helperText={errors?.maxSentence?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="sentenceType"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('sentenceType')}
                                            type="text"
                                            error={!!errors.sentenceType}
                                            helperText={errors?.sentenceType?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="defaultClassificationID"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('defaultClassificationID')}
                                            type="number"
                                            error={!!errors.defaultClassificationID}
                                            helperText={errors?.defaultClassificationID?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="life"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('life')}
                                            type="text"
                                            error={!!errors.life}
                                            helperText={errors?.life?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="minFines"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('minFines')}
                                            type="text"
                                            error={!!errors.minFines}
                                            helperText={errors?.minFines?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="maxFines"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('maxFines')}
                                            type="text"
                                            error={!!errors.maxFines}
                                            helperText={errors?.maxFines?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="nomaximumFine"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('noMaximumFine')}
                                            type="text"
                                            error={!!errors.nomaximumFine}
                                            helperText={errors?.nomaximumFine?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsSeatbeltdeductible" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsSeatbeltdeductibleChange}
                                                options={YesNoOptions || []}
                                                value={IsSeatbeltdeductible}
                                                fieldName={t('isSeatbeltDeductible')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsInstatelistValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsInstatelistChange}
                                                options={YesNoOptions || []}
                                                value={IsInstatelistValue}
                                                fieldName={t('isInStateList')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>


                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsCitationviolValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsCitationviolChange}
                                                options={YesNoOptions || []}
                                                value={IsCitationviolValue}
                                                fieldName={t('isCitationViol')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsLocalordinanceValue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsLocalordinanceChange}
                                                options={YesNoOptions || []}
                                                value={IsLocalordinanceValue}
                                                fieldName={t('isLocalOrdinance')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_IsDomesticViolencevalue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handleIsDomesticViolenceChange}
                                                options={YesNoOptions || []}
                                                value={IsDomesticViolencevalue}
                                                fieldName={t('isDomesticViolence')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>
                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="acflag"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('acFlag')}
                                            type="text"
                                            error={!!errors.acflag}
                                            helperText={errors?.acflag?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="cleryCat"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('cleryCat')}
                                            type="text"
                                            error={!!errors.cleryCat}
                                            helperText={errors?.cleryCat?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />


                                <Controller
                                    className={classes.w50}
                                    name="mustAppear"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('mustAppear')}
                                            type="text"
                                            error={!!errors.mustAppear}
                                            helperText={errors?.mustAppear?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <FormControl fullWidth sx={{ m: 1, minWidth: 80 }}>

                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Autocomplete_fingerprintRequiredvalue" />} onReset={() => { }}>
                                        <div>
                                            <CommonAutocomplete
                                                parentCallback={handlefingerprintRequiredChange}
                                                options={YesNoOptions || []}
                                                value={fingerprintRequiredvalue}
                                                fieldName={t('fingerPrint')}
                                                optionLabel={"name"}
                                                onKeyDown={handleSelectKeyDown}
                                            />
                                        </div>

                                    </ErrorBoundary>
                                </FormControl>

                                <Controller
                                    className={classes.w50}
                                    name="sendtodog"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('sendToDOG')}
                                            type="text"
                                            error={!!errors.sendtodog}
                                            helperText={errors?.sendtodog?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex -mx-4">
                                <Controller
                                    className={classes.w50}
                                    name="complianceReview"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('complianceReview')}
                                            type="text"
                                            error={!!errors.complianceReview}
                                            helperText={errors?.complianceReview?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="bondamountDefault"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('bondAmountDefault')}
                                            type="text"
                                            error={!!errors.bondamountDefault}
                                            helperText={errors?.bondamountDefault?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />


                                <Controller
                                    className={classes.w50}
                                    name="dnarequired"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('dnaRequired')}
                                            type="text"
                                            error={!!errors.dnarequired}
                                            helperText={errors?.dnarequired?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="ndexsubmitype"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('ndexSubmitType')}
                                            type="text"
                                            error={!!errors.ndexsubmitype}
                                            helperText={errors?.ndexsubmitype?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />

                                <Controller
                                    className={classes.w50}
                                    name="arcriminaltrafficid"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-8 mb-16 mx-4"
                                            label={t('arCriminalTrafficID')}
                                            type="number"
                                            error={!!errors.arcriminaltrafficid}
                                            helperText={errors?.arcriminaltrafficid?.message}
                                            variant="outlined"
                                            required
                                            fullWidth
                                        />
                                    )}
                                />
                            </div>

                            <div className="flex justify-center">
                                <Button
                                    type="submit"
                                    variant="contained"
                                    color="primary"
                                    className="normal-case m-16"
                                    aria-label="REGISTER"
                                    value="legacy">
                                    {t('save')}
                                </Button>
                                <Button
                                    type="button"
                                    variant="contained"
                                    color="secondary"
                                    onClick={() => history.push(`/admin/callViolations/${routeParams.code}`)}
                                    className="normal-case m-16"
                                    aria-label="UPDATE"
                                    value="legacy">
                                    {t('back')}
                                </Button>
                            </div>
                        </form>
                    </div>
                </CardContent >
            </Card >
        </div >
    )
}
export default EditCallViolations 