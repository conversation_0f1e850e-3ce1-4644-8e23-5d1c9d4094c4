import i18next from "i18next";

import React from "react";

const ClassOfServiceAdd = React.lazy(() => import("./ClassOfServiceAdd"));
const ClassOfServiceEdit = React.lazy(() => import("./ClassOfServiceAdd"));

const ClassOfServiceAddConfig = {
  settings: {
    layout: {
      config: {},
    },
  },
  routes: [
    {
      path: "911/classOfServiceAdd",
      element: <ClassOfServiceAdd />
    },
    {
      path: "911/classOfServiceEdit",
      element: <ClassOfServiceEdit />
    },
  ],
};

export default ClassOfServiceAddConfig;
