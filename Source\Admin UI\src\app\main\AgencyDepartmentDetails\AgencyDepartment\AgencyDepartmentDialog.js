import React, { forwardRef, useRef, useImperativeHandle, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import { useDispatch, useSelector } from 'react-redux';
import { Controller, useForm } from 'react-hook-form';
import { Autocomplete, TextField } from '@mui/material';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import { saveDepartment } from '../../store/departmentSlice';
import { getUsers } from '../../administration/store/usersSlice';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorMessage from '../../SharedComponents/ErrorMessage/ErrorMessage';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';

const defaultValues = {
    name: "",
    description: "",
};

let update;
let ShiftType;

const AgencyDepartmentDialog = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation("laguageConfig");
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState(null);
    const [code, setCode] = React.useState("");
    const [shiftTypeValue, setShiftTypeValue] = React.useState('');
    const shiftTypeData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.shiftTypeData);
    const TeamData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.teammaster.data);
    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
    const user = useSelector(({ administration }) => administration.user.allusers);
    const userlist = user.filter(y => y.defaultApp === "mobile" && y.isSuperAdmin === false);
    const [usersData, setUsersData] = React.useState([]);
    const [teamsData, setTeamsData] = React.useState([]);
    const [users, setusers] = React.useState([]);
    const [teams, setteams] = React.useState([]);
    const [personName, setPersonName] = React.useState([]);
    const [teamName, setTeamsName] = React.useState([]);
    const [messageFlag, setMessageFlag] = React.useState(false);
    const nameRef = useRef(null);

    const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
        mode: 'onChange',
        defaultValues
    });

    const handleClickOpen1 = () => {
        setOpen(true);
        setTimeout(() => {
            nameRef.current?.focus();
        }, 0);
    };

    useImperativeHandle(ref, () => ({
        handleClickOpen(data, code, flag) {
            setData(data)
            setCode(code)
            update = flag
            setShiftTimeValue(data)
            handleClickOpen1();
        },
    }));

    useEffect(() => {
        dispatch(getUsers("fname", "asc", 0, 10000, false, "ALL"));
    }, []);

    const setShiftTimeValue = data => {
        setusers(userlist.map((x) => ({
            value: x._id,
            _id: x._id,
            fname: x.fname,
            lname: x.lname,
            email: x.email,
            defaultAgency: x.defaultAgency,
            userAgencies: x.userAgencies,
            defaultApp: x.defaultApp,
            department: x.department,
            isSuperAdmin: x.isSuperAdmin,
            agencyAdmin: x.agencyAdmin,
        })))
        setteams(TeamData.map((x) => ({
            value: x._id,
            _id: x._id,
            name: x.name,
            users: x.users,
            department: x.department,
            code: x.code,
            color: x.color,
            isUpdate: x.isUpdate ? x.isUpdate : false,
        })))
        if (data._id !== null && data._id !== undefined) {
            setValue('name', data.name);
            setValue('description', data.description);
            setShiftTypeValue(data.shiftID)
            let x = DepartmentData.filter(x => x._id == data._id)
            let dept = x ? x[0] : []
            setUsersData(dept.users.map((x) => ({
                value: x._id,
                _id: x._id,
                fname: x.fname,
                lname: x.lname,
                email: x.email,
                defaultAgency: x.defaultAgency,
                userAgencies: x.userAgencies,
                defaultApp: x.defaultApp,
                department: x.department,
                isSuperAdmin: x.isSuperAdmin,
                agencyAdmin: x.agencyAdmin,
            })))
            setTeamsData(dept.teams.map((x) => ({
                value: x._id,
                _id: x._id,
                name: x.name,
                users: x.users,
                department: x.department,
                code: x.code,
                color: x.color,
                isUpdate: x.isUpdate ? x.isUpdate : false,
                code: x.code
            })))
            setPersonName(dept.users.map((x) => (x.fname + ' ' + x.lname)));
            setTeamsName(dept.teams.map((x) => (x.name)));
        }
    };

    const clear = () => {
        setValue('name', "");
        setValue('description', "");
        setShiftTypeValue('')
        setPersonName([]);
        setTeamsName([])
        setUsersData([]);
        setMessageFlag(false)
    }

    const handleClose = () => {
        setOpen(false);
        clear();
    };

    const formRef = useRef(null);

    const handleShiftTypeChange = (event, newValue) => {
        if (newValue) {
            setShiftTypeValue(newValue._id);
            setMessageFlag(true);
            ShiftType = shiftTypeData.filter((x) => x._id === newValue._id); // Keep logic unchanged
        }
    };

    function onSubmit(model) {
        let id = update ? data._id : 0
        ShiftType = shiftTypeData.filter(x => x._id == shiftTypeValue)
        dispatch(
            saveDepartment(
                {
                    _id: id,
                    name: model.name,
                    description: model.description,
                    shiftType: ShiftType[0],
                    isUpdate: update,
                    code: code,
                }
            )
        );
        handleClose();
    }

    const handleUsersChange = (event) => {
        setPersonName(event.target.value);
    };
    const handleTeamsChange = (event) => {
        setTeamsName(event.target.value);
    };

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className='flex justify-end mr-14 ml-[94%]'
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title">{t("department")}</DialogTitle>
                <DialogContent dividers>
                    <form
                        className="flex flex-col justify-center w-full pb-16"
                        onSubmit={handleSubmit(onSubmit)}
                        // autoComplete="off"
                        autoSave={false}
                    >
                        <Controller
                            name="name"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mt-16 w-full"
                                    label={t("name")}
                                    type="text"
                                    variant="outlined"
                                    required
                                    inputRef={nameRef}
                                />
                            )}
                        />
                        <Controller
                            name="description"
                            control={control}
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    className="mt-16 w-full"
                                    label={t("description")}
                                    type="text"
                                    variant="outlined"
                                    required
                                />
                            )}
                        />

                        <FormControl required className="mt-16 w-full">
                            <CommonAutocomplete
                                parentCallback={handleShiftTypeChange}
                                options={shiftTypeData || []}
                                value={shiftTypeData.find((type) => type._id === shiftTypeValue) || null}
                                fieldName={t("shiftType")}
                                optionLabel={"name"}
                                onKeyDown={handleSelectKeyDown}
                            />
                        </FormControl>
                        {update &&
                            <>
                                <Controller
                                    name="Users"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-16 w-full"
                                            label={t("users")}
                                            type="text"
                                            variant="outlined"
                                            value={personName}
                                            disabled
                                        />
                                    )}
                                />

                                <Controller
                                    name="Users"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            className="mt-16 w-full"
                                            label={t("teams")}
                                            type="text"
                                            variant="outlined"
                                            value={teamName}
                                            disabled
                                        />
                                    )}
                                />
                            </>
                        }
                        {messageFlag &&
                            <ErrorBoundary
                                FallbackComponent={(props) => <ErrorPage {...props} componentName="ErrorMessage" />} onReset={() => { }}>
                                <ErrorMessage message={`You Need to create ${ShiftType[0].noofteams} teams and ${ShiftType[0].noofshifts} shifts for this schedule.`} />
                            </ErrorBoundary>
                        }
                        <div className='flex justify-end'>
                            <Button
                                variant="contained"
                                color="primary"
                                className=" w-auto mr-16 mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="submit"
                                size="large"
                            >
                                {t("save")}
                            </Button>
                            <Button
                                variant="contained"
                                color="secondary"
                                className=" w-auto mt-8"
                                aria-label="Register"
                                // disabled={_.isEmpty(dirtyFields) || !isValid}
                                type="button"
                                size="large"
                                onClick={handleClose}
                            >
                                {t("cancel")}
                            </Button>
                        </div>

                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
});

export default AgencyDepartmentDialog;