const locale = {
	accept: 'Accept sp',
	accident: 'Accident sp',
	acquiredBy: 'Acquired By sp',
	acquiredDate: 'Acquired Date sp',
	acquiredLocation: 'Acquired Location sp',
	activity: 'Activity sp',
	activityType: 'ActivityType sp',
	actualStopLocation: 'Actual Stop Location sp',
	add: 'Add sp',
	addActivity: 'Add Activity sp',
	addAttachment: 'Add Attachment sp',
	editAttachment: 'Edit Attachment sp',
	addCommunication: 'Add Communication Log sp',
	addDisposition: 'Add Disposition sp',
	addIncidentCommLog: 'Add Incident Communication Logs sp',
	addIncidentLocation: 'Add Incident Location sp',
	addIncidentNote: 'Add Incident Note sp',
	addIncidentOrg: 'Add Incident Organization sp',
	addIncidentPerson: 'Add Incident Person sp',
	addIncidentProperty: 'Add Incident Property sp',
	addIncidentSecurity: 'Add Incident Security sp',
	addIncidentVehicle: 'Add Incident Vehicle sp',
	addIncidentViolation: 'Add Incident Violation sp',
	addNarrative: 'Add Narrative sp',
	addNote: 'Add Note sp',
	addNotes: 'Add Notes sp',
	addOrganization: 'Add Organization sp',
	addPerson: 'Add Person sp',
	addProperty: 'Add Property sp',
	addSecurity: 'Add Security sp',
	addVehicle: 'Add Vehicle sp',
	addViolation: 'Add Violation sp',
	addrNum: 'Addr Num sp',
	address: 'Address sp',
	addressDetails: 'Address Details sp',
	addressType: 'Address Type sp',
	addressValidated: 'Address Validated sp',
	age: 'Age sp',
	ageFrom: 'Age From sp',
	ageTo: 'Age To sp',
	ageType: 'Age Type sp',
	agency: 'Agency sp',
	agencyName: 'Agency name sp',
	agencyOrCaseExhiNo: 'Agency/case Exhi No. sp',
	alert: 'Alert sp',
	allCalls: 'All Calls sp',
	apt: 'Apt sp',
	areYouSure: 'Are you sure? sp',
	arrestDateTime: 'Arrest Date Time sp',
	arrestLocation: 'Arrest Location sp',
	arrestType: 'Arrest Type sp',
	arrested: 'Arrested sp',
	arrestedDetails: 'Arrested Details sp',
	arrestingOfficer: 'Arresting Officer sp',
	article: 'Article sp',
	articleType: 'Article Type sp',
	assignment: 'Assignment sp',
	associatedPersons: 'Associated Persons sp',
	associatedVehicle: 'Associated Vehicles sp',
	association: 'Association sp',
	associationPersonDetails: 'Association Person Details sp',
	associationType: 'Association Type sp',
	attachment: 'Attachment sp',
	attemptedOrCompleted: 'Attempted/Completed sp',
	audioFileSizeLbl: 'Please select audio file less than 1MB. sp',
	automatic: 'Automatic sp',
	availableForAssignment: 'AVAILABLE FOR ASSIGNMENT sp',
	availableInDateOrTimeRange: 'Available In Date/Time Range sp',
	bandMakeManufacturer: 'Brand/Make/Manufacturer sp',
	birthCity: 'Birth City sp',
	birthState: 'Birth State sp',
	boat: 'Boat sp',
	boatDecalNumber: 'Boat Decal Number sp',
	boatHullNumber: 'Boat Hull Number sp',
	branch: 'Branch sp',
	broadcast: 'Broadcast sp',
	building: 'Building sp',
	buildingName: 'Building Name sp',
	buildingNbr: 'Building Nbr sp',
	businessNameLbl: 'Business Name sp',
	caliber: 'Caliber sp',
	callAssigned: 'Call Assigned sp',
	callDisconnectWarning: 'By joining this call, the previous call will be disconnected. sp',
	callDisposition: 'Call Disposition  sp',
	callFound: 'Call Found sp',
	callHistorySearch: 'Call History Search sp',
	callIdLbl: 'Call number sp',
	callNumber: 'Call number sp',
	callPath: 'Call Path sp',
	callPersonArrestedLbl: 'Arrested sp',
	callPersonDobLbl: 'DOB sp',
	callPersonGenderLbl: 'Gender sp',
	callPersonRaceLbl: 'Race sp',
	callPersonRightSideArrestedLbl: 'Arrested sp',
	callPersonRightSideDobLbl: 'DOB sp',
	callPersonRightSideGenderLbl: 'Gender sp',
	callPersonRightSideRaceLbl: 'Race sp',
	callStatus: 'Call status sp',
	callType: 'Call Type sp',
	callUnitRightSideArrivedLbl: 'Arrived sp',
	callUnitRightSideClearedLbl: 'Cleared sp',
	callUnitRightSideDispatchedLbl: 'Dispatched sp',
	callUnitRightSideEnRouteLbl: 'En Route sp',
	callUnitRightSideInStationLbl: 'In Station sp',
	callUnitRightSideTransportingDestLbl: 'Transporting Destination sp',
	callUnitRightSideTransportingLbl: 'Transporting sp',
	callWreckersPhone1Lbl: 'Phone 1 sp',
	callWreckersPhone2Lbl: 'Phone 2 sp',
	callerInfo: 'Caller Info sp',
	callerInformation: 'Caller Information sp',
	calls: 'Calls sp',
	callsFound: 'Calls Found sp',
	callsMap: 'Calls Map sp',
	cancel: 'Cancel sp',
	category: 'Category sp',
	change: 'Change sp',
	checkDoor: 'Checked Door sp',
	chooseEnvironment: 'Choose Environment sp',
	chooseFile: 'Choose File sp',
	chooseLanguage: 'Choose Language sp',
	citation: 'Citation sp',
	city: 'City sp',
	cityName: 'City Name sp',
	clear: 'Clear sp',
	clearedOrInService: 'Cleared / In-Service sp',
	closestLocation: 'Closest Locations sp',
	color: 'Color sp',
	color1Lbl: 'Color 1 sp',
	color2Lbl: 'Color 2 sp',
	commLogAddedSuccessfully: 'Communication Log Added Successfully sp',
	commLogUpdatedSuccessfully: 'Communication Log Updated Successfully sp',
	communicationLogDeletedSuccessfully: 'Communication Log Deleted Successfully sp',
	communicationLogFailedToDelete: 'Communication Log Failed to Delete sp',
	communicationLogs: 'Communication Logs sp',
	communicationNote: 'Communication note sp',
	communicationWith: 'Communication With sp',
	condition: 'Condition sp',
	confimDeletion: 'Confirm Deletion sp',
	contactNameLbl: 'Contact Name sp',
	contrabandFound: 'Contraband Found sp',
	count: 'Count sp',
	county: 'County sp',
	current: 'Current sp',
	currentDispatches: 'Current Dispatches sp',
	custodyHistory: 'Custody History sp',
	darkMode: 'Dark sp',
	date: 'Date sp',
	definedLocation: 'Defined Location sp',
	deleteWarning: 'Are you sure you want to delete? sp',
	description: 'Description sp',
	didntReceiveOtp: "Didn't receive an OTP? sp",
	dir: 'Dir sp',
	direction: 'Direction sp',
	directionsLbl: 'Directions sp',
	dispatchCalls: 'Dispatch Calls sp',
	dispatchChat: 'Dispatch Chat sp',
	dispatchIncidentAgencyLbl: 'Agency sp',
	dispatchIncidentReportingOfficerLbl: 'Reporting Officer sp',
	dispatchIncidents: 'Dispatch Incidents sp',
	dispatchNumber: 'Dispatch number sp',
	dispositionLbl: 'Disposition sp',
	dispositionReason: 'Disposition Reason sp',
	dl: 'DL sp',
	dlId: 'DLID sp',
	dlNumber: 'DL Number sp',
	dlState: 'DL State sp',
	doNotHaveAccessToThisApp: 'Do not have access to this app sp',
	dob: 'Date Of Birth sp',
	dobAbbreviation: 'DOB sp',
	driveByThrough: 'Drive by/through sp',
	driverCategory: 'Driver Category sp',
	driverDetails: 'Driver Details sp',
	driverInformation: 'Driver Information sp',
	drivingLicense: 'Driving License sp',
	drugName: 'Drug Name sp',
	drugType: 'Drug Type sp',
	driver: 'Driver sp',
	editIncidentCommLog: 'Edit Incident Communication Logs sp',
	editIncidentLocation: 'Edit Incident Location sp',
	editIncidentNote: 'Edit Incident Note sp',
	editIncidentOrg: 'Edit Incident Organization sp',
	editIncidentPerson: 'Edit Incident Person sp',
	editIncidentProperty: 'Edit Incident Property sp',
	editIncidentVehicle: 'Edit Incident Vehicle sp',
	editIncidentViolation: 'Edit Incident Violation sp',
	email: 'Email sp',
	emailType: 'Email type sp',
	employer: 'Employer sp',
	ems: 'EMS sp',
	enRoute: 'En Route sp',
	endMessageLbl: 'Yay! You have seen it all sp',
	engaged: 'ENGAGED sp',
	enterLogType: 'Enter Log Type sp',
	enterLoginPin: 'Enter 4 digit Login PIN sp',
	enterNcicToken: 'Enter NCIC Token sp',
	enterPassword: 'Enter Password sp',
	enterVerificationCode: 'Please enter the verification code sent to your email sp',
	enterYourToken: 'Enter your token sp',
	environment: 'Environment sp',
	environmentPointedToDev: 'Environment Pointed To Dev sp',
	environmentPointedToLive: 'Environment Pointed To Live sp',
	estimatedDrugQuantity: 'Estimated Drug Quantity sp',
	estimatedValue: 'Estimated Value sp',
	ethnicity: 'Ethnicity sp',
	everything10To4: 'Everything 10-4 sp',
	evidence: 'Evidence sp',
	evidenceInformation: 'Evidence Information sp',
	evidenceLocation: 'Evidence Location sp',
	evidenceNo: 'Evidence No. sp',
	evidenceStatus: 'Evidence Status sp',
	ext: 'Ext sp',
	extension: 'Extension sp',
	eyes: 'Eyes sp',
	failed: 'Failed! sp',
	failedToAddNote: 'Failed to add note sp',
	failedToAddPerson: 'Failed to add Person sp',
	failedToAddVehicle: 'Failed to add vehicle sp',
	failedToScan: 'Failed to Scan! Please scan again sp',
	female: 'Female sp',
	fileUploadFailed: 'File upload Failed sp',
	fileUploadedSuccessfully: 'File uploaded Successfully sp',
	fillRequiredFields: 'Please fill out required fields sp',
	filterByBranch: 'Filter by Branch sp',
	fire: 'Fire sp',
	firePresetsLbl: 'Fire Presets sp',
	firstName: 'First Name sp',
	floor: 'Floor sp',
	fps: 'FPS sp',
	freeform: 'Freeform sp',
	from: 'From sp',
	fromDate: 'From Date sp',
	gender: 'Gender sp',
	goToLogin: 'Go to Login sp',
	gun: 'Gun sp',
	hair: 'Hair sp',
	height: 'Height sp',
	history: 'History sp',
	idNumber: 'ID number sp',
	idType: 'ID type sp',
	immigDocNumber: 'Immig Doc Number sp',
	immigDocType: 'Immig Doc Type sp',
	impounded: 'Impounded sp',
	incident: 'Incident sp',
	incidentAddedSuccessfully: 'Incident added successfully sp',
	incidentDeletedSuccessfully: 'Incident Deleted Successfully sp',
	incidentDetails: 'Incident Details sp',
	incidentFailedToDelete: 'Incident Failed to Delete sp',
	incidentInformation: 'Incident Information sp',
	incidentLocation: "Incident Location sp",
	incidentNumber: 'Incident Number sp',
	incidentUpdatedSuccessfully: 'Incident Updated Successfully sp',
	incidentViolationToAdd: 'Incident Violation to add sp',
	incidentViolation: "Incident Violation sp",
	incidents: 'Incidents sp',
	incomingCall: 'Incoming call sp',
	incomingVideoCall: 'Incoming Video Call sp',
	incorrectPassword: 'Incorrect password sp',
	individualType: 'Individual Type sp',
	initialLocation: 'Initial Location sp',
	intersection: 'Intersection sp',
	invalidCredentials: 'Invalid Credentials sp',
	invalidDeviceLogin: 'Invalid Device Login sp',
	involmentTags: 'Involvement Tags sp',
	involvementLossType: 'Involvement(Loss Type) sp',
	involvements: 'Involvements sp',
	isRecovered: 'Is Recovered sp',
	isSuspectedVehicle: 'Is Suspected Vehicle sp',
	labResult: 'Lab Result sp',
	languages: 'Languages sp',
	lastLocationDate: 'Last location date sp',
	lastName: 'Last Name sp',
	lat: 'Lat sp',
	latitude: 'Latitude sp',
	latitudeOrLongitude: 'Latitude / Longitude sp',
	legalAlien: 'Legal Alien sp',
	licenseNo: 'License No sp',
	licenseNumber: 'License Number sp',
	licenseState: 'License State sp',
	licenseType: 'License Type sp',
	licenseYear: 'License Year sp',
	loading: 'Loading sp',
	location: 'Location sp',
	locationAlert: 'Location Alert sp',
	locationHistory: 'Location History sp',
	locationHistoryNotFound: 'Location History Not Found sp',
	locationNotAvailable: 'Location not available sp',
	locationPlace: 'LOCATIONPLACE sp',
	locationType: 'Location Type sp',
	logType: 'Log Type sp',
	login: 'Login sp',
	logout: 'Logout sp',
	lon: 'Lon sp',
	longitude: 'Longitude sp',
	make: 'Make sp',
	makeDesc: 'Make Desc sp',
	makeLbl: 'Make sp',
	male: 'Male sp',
	manual: 'Manual sp',
	mapUnavailable: 'Map Unavailable sp',
	maritalStatus: 'Marital Status sp',
	markAsLostOrFound: 'Mark as Lost/Found sp',
	markStop: 'Mark Stop sp',
	measure: 'Measure sp',
	middleName: 'Middle Name sp',
	misdOrFelony: 'Misd/Felony sp',
	model: 'Model sp',
	modelLbl: 'Model sp',
	modelYearLbl: 'Model Year sp',
	myCall: 'My Call sp',
	myCalls: 'My Calls sp',
	myStatus: 'My Status sp',
	name: 'Name sp',
	narrative: 'Narrative sp',
	narratives: 'Narratives sp',
	nationality: 'Nationality sp',
	ncic: 'NCIC sp',
	ncicInformation: 'NCIC Information sp',
	ncicLogin: 'NCIC Login sp',
	ncicSearch: 'NCIC Search sp',
	ncicSearchHistory: 'NCIC Search History sp',
	ncicStatus: 'NCIC Status sp',
	nciciNo: 'NCIC No. sp',
	newCall: 'New Call sp',
	nextAction: 'Next Action sp',
	no: 'No sp',
	noActivities: 'No Activities sp',
	noBranches: 'No Branches sp',
	noCallAssigned: 'No Call Assigned sp',
	noCalls: 'No Calls sp',
	noCallsFound: 'No calls found. sp',
	noContactsFound: 'No Contacts Found sp',
	noOfCallsToDisplay: 'No. of calls to display sp',
	noRecordFound: 'No Records Found sp',
	noUnit: 'No Unit sp',
	noUnits: 'No Units sp',
	notOnDuty: 'NOT ON DUTY sp',
	note: 'Note sp',
	noteAdded: 'Note added successfully sp',
	noteDeletedSuccessfully: 'Note Deleted Successfully sp',
	noteFailedToDelete: 'Note Failed to Delete sp',
	noteUpdatedSuccessfully: 'Note updated successfully sp',
	notes: 'Notes sp',
	noticeLbl: 'Notice sp',
	numSuffix: 'Num Suffix sp',
	number: 'Number sp',
	offDuty: 'Off Duty sp',
	officer: 'Officer sp',
	officerName: 'Officer Name sp',
	officers: 'Officers sp',
	offline: 'Offline sp',
	ok: 'OK sp',
	onDuty: 'On Duty sp',
	onScene: 'On-Scene sp',
	online: 'Online sp',
	options: 'Options sp',
	orgAddedSuccessfully: 'Organization added successfully sp',
	orgType: 'Org Type sp',
	orgUpdatedSuccessfully: 'Organization updated successfully sp',
	organization: 'Organization sp',
	organizationDeletedSuccessfully: 'Organization Deleted Successfully sp',
	organizationFailedToDelete: 'Organization Failed to Delete sp',
	organizationName: 'Organization Name sp',
	organizations: 'Organizations sp',
	otpSentSuccessfully: 'OTP sent successfully on sp',
	ownedAppliedNumber: 'Owned Applied Number sp',
	owner: 'Owner sp',
	ownerDetails: 'Owner Details sp',
	ownerInformation: 'Owner Information sp',
	password: 'Password sp',
	passwordHint: 'Password sp',
	permanent: 'Permanent sp',
	person: 'Person sp',
	personAddedSuccessFully: 'Person added successfully sp',
	personData: 'Person Data sp',
	personDeletedSuccessfully: 'Person Deleted Successfully sp',
	personDetails: 'Person Details sp',
	personEntry: 'Person Entry sp',
	personFailedToDelete: 'Person Failed to Delete sp',
	personGlobalSearch: 'Person Global Search sp',
	personUpdatedSuccessfully: 'Person Updated Successfully sp',
	persons: 'Persons sp',
	pharmacy: 'Pharmacy sp',
	phone: 'Phone sp',
	phoneLbl: 'Phone sp',
	phoneNumber: 'Phone Number sp',
	phoneType: 'Phone Type sp',
	placeName: 'Place Name sp',
	pleaseClickOnSaveButtonToSaveChanges: 'Please click on save button to save changes. sp',
	pleaseEnterPassword: 'Please enter your password sp',
	pleaseEnterValid5DigitZipCode: 'Please enter a valid 5-digit Zip Code sp',
	pleaseEnterYourUserName: 'Please enter your Username sp',
	pleaseFillRequiredField: 'Please fill required fields sp',
	pleaseSelectState: 'Please select a state sp',
	pleaseSelectType: 'Please select type sp',
	pleaseTryAgain: 'Please try again sp',
	pleaseWait: 'Please wait... sp',
	police: 'Police sp',
	postDir: 'Post Dir sp',
	postDirection: 'Post Direction sp',
	preDir: 'PreDir sp',
	preDirection: 'Pre Direction sp',
	prescriptionNo: 'Prescription No sp',
	primaryColor: 'Primary Color sp',
	primaryContact: 'PRIMARY CONTACT sp',
	addNewIncident: "Add New Incident sp",
	primaryLocation: 'PRIMARY LOCATION sp',
	priorityCall: 'Priority Call sp',
	prioritySort: 'Priority sp',
	privacyPolicy: 'Privacy Policy sp',
	probableCauseFor: 'Probable Cause For sp',
	probableCauseOther: 'Probable Cause Other sp',
	properties: 'Properties sp',
	property: 'Property sp',
	propertyAddedSuccessfully: 'Property Added Successfully sp',
	propertyDeletedSuccessfully: 'Property Deleted Successfully sp',
	propertyFailedToDelete: 'Property Failed to Delete sp',
	propertyUpdatedSuccessfully: 'Property Updated Successfully sp',
	quantity: 'Quantity sp',
	race: 'Race sp',
	receivedOn: 'Received on sp',
	recent: 'Recent sp',
	reconciliationHistory: 'Reconciliation History sp',
	recovered: 'Recovered sp',
	recoveredBy: 'Recovered By sp',
	recoveredDate: 'Recovered Date sp',
	recoveredValue: 'Recovered Value sp',
	recoveryInformation: 'Recovery Information sp',
	recoveryLocation: 'Recovery Location sp',
	registrationNumber: 'Registration Number sp',
	reject: 'Reject sp',
	replayHistory: 'Replay History sp',
	report: 'Report sp',
	reportError: 'Report Error sp',
	residentStatus: 'Resident Status sp',
	reviewDate: 'Review Date sp',
	run: 'Run sp',
	runOrRecordPerson: 'Run/Record Person sp',
	runOrRecordTag: 'Run/Record Tag sp',
	sameAsOwner: 'Same As Owner sp',
	save: 'Save sp',
	saved: 'Saved! sp',
	scanDl: 'Scan DL sp',
	search: 'Search sp',
	searchBy: 'Search By sp',
	searchByNameDlSsnPhoneAr: 'Search by Name,DL,SSN,Phone AR sp',
	searchDriverAddress: 'Search Driver Address sp',
	searchForDriver: 'Search For Driver sp',
	searchForOwner: 'Search For Owner sp',
	searchHistory: 'Search History sp',
	searchLoad: 'Search... sp',
	searchOwnerAddress: 'Search Owner Address sp',
	searchQuerySubmitted: 'Search query submitted sp',
	searchString: 'Search String: sp',
	searchType: 'Search Type: sp',
	searchVehicle: 'Search Vehicle sp',
	searchingQuerySubmitted: 'Searching Query Submitted sp',
	secondaryColor: 'Secondary Color sp',
	security: 'Security sp',
	securityAddedSuccessfully: 'Security Added Successfully sp',
	securityCheck: 'Security Check sp',
	securityDeletedSuccessfully: 'Security Deleted Successfully sp',
	securityFailedToDelete: 'Security Failed to Delete sp',
	securityPersonNotAvailable: 'Security person not available sp',
	selectState: 'Select State sp',
	selectType: 'Select Type sp',
	selected: 'Selected sp',
	sendAnAttachment: 'Send an Attachment sp',
	serialNumber: 'Serial Number sp',
	sessionInvalidWarning: 'You have logged in from another device, so this session is no longer valid. sp',
	setStatus: 'Set Status sp',
	sex: 'Sex sp',
	showHydrant: 'Show Hydrant sp',
	somethingWentWrong: 'Something went wrong,Please try after sometime sp',
	sourceType: 'Source type sp',
	specialInstructionsLbl: 'Special Instructions sp',
	ssn: 'SSN sp',
	st: 'ST sp',
	state: 'State sp',
	status: 'Status sp',
	statusChange: 'Status Change sp',
	statusWarning: 'Currently, you are off duty. Please switch to on-duty status and then proceed. sp',
	statuteCitation: 'Statute Citation sp',
	statuteCode: 'Statute Code sp',
	stopCompletion: 'Stop Completion sp',
	stopMarkedSuccessfully: 'Stop Marked Successfully sp',
	street: 'Street sp',
	streetName: 'Street Name sp',
	streetType: 'Street Type sp',
	styleLbl: 'Style sp',
	submit: 'Submit sp',
	submittedToLab: 'Submitted to Lab sp',
	suffix: 'Suffix sp',
	tag: 'Tag sp',
	tagNumberLbl: 'Tag Number sp',
	tagStateLbl: 'Tag State sp',
	tagType: 'Tag Type sp',
	tagYear: 'Tag Year sp',
	testButton: 'Test Button sp',
	testedVia: 'Tested Via sp',
	themes: 'Themes sp',
	timeline: 'Timeline sp',
	title: 'Title sp',
	titleLbl: 'Title sp',
	to: 'To sp',
	toDate: 'To Date sp',
	tokenExpiredWarning: 'Your token has expired and you will need to Sign in again. You will be redirected to the login page in 5 seconds. sp',
	tokenRequired: 'Token Required sp',
	trackMovement: 'Track Movement sp',
	trafficStop: 'Traffic Stop sp',
	trafficStopReasons: 'Traffic Stop Reasons sp',
	transportCall: 'Transport Call sp',
	transportDetails: 'Transport Details sp',
	transportMedical: 'Medical Transport sp',
	type: 'Type sp',
	unableToValidate: 'Unable To Validate sp',
	unit: 'Unit sp',
	unitArrived: 'Arrived sp',
	unitBegin: 'Begin sp',
	unitBranchName: 'Branch Name sp',
	unitCleared: 'Cleared sp',
	unitDest: 'Dest sp',
	unitDispatch: 'Dispatch# sp',
	unitDispatched: 'Dispatched sp',
	unitEnRoute: 'En Route sp',
	unitEnd: 'End sp',
	unitId: 'Unit ID sp',
	unitName: 'Unit Name sp',
	unitStatus: 'Status sp',
	unitTransporting: 'Transporting sp',
	unitType: 'Unit type sp',
	units: 'Units sp',
	unitsAssigned: 'Units Assigned sp',
	unitsLocation: 'Units Location sp',
	usCitizen: 'US Citizen sp',
	useAuthenticator: 'Use Authenticator sp',
	userName: 'Username sp',
	vehicle: 'Vehicle sp',
	vehicleAddedSuccessfully: 'Vehicle added successfully sp',
	vehicleData: 'Vehicle Data sp',
	vehicleDeletedSuccessfully: 'Vehicle Deleted Successfully sp',
	vehicleEntry: 'Vehicle Entry sp',
	vehicleFailedToDelete: 'Vehicle Failed to Delete sp',
	vehicleSearched: 'Vehicle Searched sp',
	vehicleUpdatedSuccessfully: 'Vehicle Updated Successfully sp',
	vehicles: 'Vehicles sp',
	viewCheckHistory: 'View Check History sp',
	vinLbl: 'Vin sp',
	vinNo: 'VIN No sp',
	vinNumber: 'VIN Number sp',
	violationAddedSuccessfully: 'Violation Added Successfully sp',
	violationDeletedSuccessfully: 'Violation Deleted Successfully sp',
	violationFailedToDelete: 'Violation Failed to Delete sp',
	violationUpdatedSuccessfully: 'Violation Updated Successfully sp',
	violations: 'Violations sp',
	walkAround: 'Walk Around sp',
	warningLbl: 'Warning sp',
	warrantyNumber: 'Warrant Number sp',
	watches: 'Watches sp',
	weight: 'Weight sp',
	welcome: 'Welcome sp',
	welcomeToMobileDispatch: 'Welcome to Mobile Dispatch! sp',
	welcomeToSaber: 'Welcome to Saber ! sp',
	wreckers: 'Wreckers sp',
	year: 'Year sp',
	yes: 'Yes sp',
	yourLocation: 'Your Location sp',
	zip: 'Zip sp',
	zipCode: 'Zip Code sp',
	zipPlus4: 'ZIP Plus 4 sp',
	zoneOrPrecinct: 'Zone / Precinct sp',
	zoomLevel: 'Zoom Level sp',
	noteAddded: 'Note added successfully sp',
	files: 'Files sp',
	fileDeletedSuccessfully: 'File Deleted Successfully sp',
	fileFailedToDelete: 'File Failed to Delete sp',
	fileAddedSuccessfully: 'File Added Successfully sp',
	fileFailedToAdd: 'File Failed to Add sp',
	fileUpdatedSuccessfully: 'File Updated Successfully sp',
	fileFailedToUpdate: 'File Failed to Update sp',
	applications: 'Applications sp',
	mobileDispatch: 'Mobile Dispatch sp',
	mobileDispatchSocket: 'Mobile Dispatch Socket sp',
	chat: 'Chat sp',
	currentMap: 'Current Map sp',
	cureentMapNew: 'Current Map New sp',
	relativityAdmin: 'Relativity Admin sp',
	call911: '911 Call sp',
	mySchedule: 'My Schedule sp',
	onDutyCaps: 'ON DUTY sp',
	enroute: 'Enroute sp',
	priority: 'Priority sp',
	timeLine: 'Timeline sp',
	noCallFound: 'No call found. sp',
	associatedVehicles: 'Associated Vehicles sp',
	dark: 'Dark sp',
	form: 'From sp',
	branchName: 'Branch Name sp',
	dispatchHashTag: 'Dispatch# sp',
	unitHashTag: 'Unit# sp',
	dispatched: 'Dispatched sp',
	arrived: 'Arrived sp',
	transporting: 'Transporting sp',
	begin: 'Begin sp',
	end: 'End sp',
	dest: 'Dest sp',
	cleared: 'Cleared sp',
	endMsg: 'Yay! You have seen it all sp',
	disposition: 'Disposition sp',
	tagNumber: 'Tag Number sp',
	style: 'Style sp',
	tagState: 'Tag State sp',
	modelYear: 'Model Year sp',
	color1: 'Color 1 sp',
	color2: 'Color 2 sp',
	vin: 'Vin sp',
	businessName: 'Business Name sp',
	contactName: 'Contact Name sp',
	warnings: 'Warnings sp',
	specialInstructions: 'Special Instructions sp',
	notice: 'Notice sp',
	firePresets: 'Fire Presets sp',
	directions: 'Directions sp',
	phone1: 'Phone 1 sp',
	phone2: 'Phone 2 sp',
	reportingOfficer: 'Reporting Officer sp',
	transportingDestination: 'Transporting Destination sp',
	inStation: 'In Station sp',
	changePassword: 'Change Password sp',
	currentPassword: 'Current Password sp',
	confirmPassword: 'Confirm Password sp',
	passwordConfirmMsg: 'Your password has been changed successfully. You will need to login again to continue. sp',
	confirm: 'Confirm sp',
	profilePicture: 'Profile Picture sp',
	update: 'Update sp',
	noResultFound: 'No results found sp',
	code: 'Code sp',
	ownerAppliedNumber: 'Owner Applied Number sp',
	driverLicesenceNumber: 'Driver Licesence Number sp',
	dateOfBirth: 'Date Of Birth sp',
	verify: 'Verify sp',
	personalInformation: 'Personal Information sp',
	rpsAuthentication: 'RPS Authentication sp',
	alertSoundSetting: 'Alert Sound Setting sp',
	defaultlocationsetting: 'Default location setting sp',
	defaultMFAType: 'Default MFA Type sp',
	defaultAgency: 'Default Agency sp',
	relativityAdminMsg: 'Relativity Admin opened in new Tab. sp',
	sendEmail: 'Send Email sp',
	selectDefaultAgency: 'Select Default Agency sp',
	defaultLocationType: 'Default location type sp',
	defaultLocation: 'Default location sp',
	selectLocation: 'Select location sp',
	authenticator: 'Authenticator sp',
	text: 'Text sp',
	notifications: 'Notifications sp',
	dismissAll: 'Dismiss All sp',
	notificationsMsg: 'There are no notifications for now. sp',
	today: 'Today sp',
	events: 'Events sp',
	quickSettings: 'Quick Settings sp',
	fontSize: 'Font Size sp',
	alsAvail: 'ALS AVAIL sp',
	blsAvail: 'BLS AVAIL sp',
	notAvail: 'NOT AVAIL sp',
	available: 'Available sp',
	documentation: 'Documentation sp',
	viewSettingsMsg: 'View settings as json/query params sp',
	fuseSettingViewer: 'Fuse Settings Viewer sp',
	json: 'JSON sp',
	queryParams: 'Query Params sp',
	close: 'Close sp',
	themeSettings: 'Theme Settings sp',
	themeColorSchemes: 'Theme Color Schemes sp',
	themeRuleMsg: '* Selected color scheme will be applied to all theme layout elements (navbar, toolbar,etc.). You can also select a different color scheme for each layout element at theme settings. sp',
	startChatMsg: 'Start a conversation by typing your message below. sp',
	chatApp: 'Chat App sp',
	selectChatMsg: 'Select a contact to start a conversation!.. sp',
	broadcastMessages: 'Broadcast Messages sp',
	unreadMessages: 'Unread Messages sp',
	away: 'Away sp',
	doNotDisturb: 'Do not disturb sp',
	noMessage: 'No message. sp',
	ncicAuthentication: 'NCIC Authentication sp',
	ncicUsername: 'NCIC UserName sp',
	ncicPassword: 'NCIC Password sp',
	showOnlineUsers: 'Show Online Users sp',
	license: 'License sp',
	expDate: 'ExpDate sp',
	lit: 'LIT sp',
	vinCaps: 'VIN sp',
	cylinders: 'Cylinders sp',
	fuel: 'Fuel sp',
	issueDate: 'IssueDate sp',
	stateZip: 'StateZip sp',
	residenceCounty: 'Residence County sp',
	coverage: 'Coverage sp',
	reason: 'Reason sp',
	policy: 'Policy sp',
	effectiveDate: 'EffectiveDate sp',
	naic: 'Naic sp',
	company: 'Company sp',
	holder: 'Holder sp',
	lienDate: 'LienDate sp',
	cdl: 'CDL sp',
	csn: 'CSN sp',
	dln: 'DLN sp',
	endorsements: 'Endorsements sp',
	expires: 'Expires sp',
	eyeColor: 'EyeColor sp',
	hairColor: 'HairColor sp',
	issued: 'Issued sp',
	medicalCertification: 'Medical Certification sp',
	organDonor: 'Organ Donor sp',
	restrictions: 'Restrictions sp',
	totalPoints: 'Total Points sp',
	voluntaryEnhancedSecurity: 'Voluntary Enhanced Security sp',
	ori: 'ORI sp',
	crimeInfoCenter: 'Crime Info Center sp',
	oca: 'OCA sp',
	doi: 'DOI sp',
	towingService: 'Towing Service sp',
	mis: 'MIS sp',
	srn: 'SRN sp',
	doe: 'DOE sp',
	toe: 'TOE sp',
	noUnitsFound: 'No units found. sp',
	unitsFilter: 'Units Filter sp',
	selectAll: 'Select All sp',
	deselectAll: 'Deselect All sp',
	applyFilter: 'Apply Filter sp',
	unitsFilterApplied: 'Units filter applied sp',
	zoomlevel: 'Zoom level sp',
	allCallsAndUnits: 'All Calls and Units sp',
	locationEntryCaps: 'LOCATION ENTRY sp',
	searchForLocation: 'Search for location (Full Address) sp',
	intersectionFreeform: 'Intersection/ Freeform/ Common Place Name sp',
	streetNumber: 'Street Number sp',
	zipExt: 'ZipExt sp',
	locationTags: 'Location Tags sp',
	organizationEntryCaps: 'ORGANIZATION ENTRY sp',
	searchForOrganization: 'Search for organisation sp',
	organizationType: 'Organization Type sp',
	descriptionNotes: 'Description/Notes sp',
	titleRole: 'Title/Role sp',
	individualEntryCaps: 'Individual Entry sp',
	arrestDetailsCaps: 'ARREST DETAILS sp',
	arrestCircumstances: 'Arrest Circumstances sp',
	arrestDate: 'Arrest Date sp',
	arrestTime: 'Arrest Time sp',
	warrantNumber: 'Warrant Number sp',
	zone: 'Zone / Precinct sp',
	latitudeLongitude: 'Latitude / Longitude sp',
	searchForPerson: 'Search for person sp',
	hispanic: 'Hispanic sp',
	issuedBy: 'Issued By sp',
	associatedPersonDetail: 'Associated Person Detail sp',
	vehicleEntryCaps: 'VEHICLE ENTRY sp',
	searchForVehicle: 'Search for vehicle sp',
	involment: 'Involvement sp',
	wreckerEntryCaps: 'WRECKER ENTRY sp',
	searchWrecker: 'Search Wrecker (Company Name) sp',
	towingCompany: 'Towing Company sp',
	speeding: 'Speeding sp',
	driving: 'Driving sp',
	xLine: 'X-Line sp',
	cellPhone: 'Cell Phone sp',
	defEquip: 'Def Equip sp',
	otherViol: 'Other Viol sp',
	onwer: 'Owner sp',
	isDriver: 'Is Driver sp',
	ownerAddress: 'Owner Address sp',
	citationDetailsHere: 'Citation Details Here sp',
	citationCaps: 'CITATION sp',
	attachDocument: 'ATTACH DOCUMENT/PHOTO/VIDEO/AUDIO sp',
	selectFiles: 'Select Files sp',
	additionalInformation: 'Additional Information sp',
	location1: 'Location 1 sp',
	location2: 'Location 2 sp',
	location3: 'Location 3 sp',
	type1: 'Type 1 sp',
	type2: 'Type 3 sp',
	type3: 'Type 3 sp',
	queryArticle: 'QUERY ARTICLE sp',
	reset: 'Reset sp',
	agencyResults: 'Agency Results sp',
	ncicResults: 'NCIC Results sp',
	queryBoat: 'QUERY BOAT sp',
	queryGun: 'QUERY GUN sp',
	ncicPersonSearch: 'NCIC sp',
	personSearch: "Person Search sp",
	localPersonSearch: "Local sp",
	youDoNotHavePermissionForNCICSearch: "You do not have permission for NCIC Search sp",
	youDoNotHavePermissionForLocalSearch: "You do not have permission for Local Search sp",
	driversLicenseNo: 'Drivers License No sp',
	nameLastFirstMiddle: 'Name (last,first,middle) sp',
	destination: 'Destination sp',
	driverSystemNumber: 'Driver System Number sp',
	socialSecurityNumber: 'Social Security Number sp',
	miscellaneousNumber: 'Miscellaneous Number sp',
	driverHistory: 'Driver History sp',
	y: 'Y sp',
	n: 'N sp',
	vehicleSearchCaps: 'VEHICLE SEARCH sp',
	emsUnitName: 'EMS Unit Name sp',
	unitNumber: 'Unit Number sp',
	officerStatus: 'Officer Status sp',
	jobName: 'Job Name sp',
	availableALS: 'Available - ALS sp',
	unavailable: 'Unavailable sp',
	imageReturn: 'Image Return sp',
	relatedHit: 'Related Hit sp',
	ncicCheck: 'NCIC Check sp',
	acknowlegde: 'ACKNOWLEDGE sp',
	enrouteCaps: 'ENROUTE sp',
	tweet: 'Tweet sp',
	post: 'Post sp',
	ncicResult: 'NCIC Result sp',
	okay: 'Okay sp',
	licenseNum: 'License Number sp',
	messageType: 'Message Type sp',
	messageResponse: 'Message Response sp',
	responseDate: 'Response Date sp',
	scanLicense: 'Scan Drivers License sp',
	immigDocNum: 'Immig Doc Num sp',
	nationalityType: 'Nationality Type sp',
	makeDescription: 'Make Description sp',
	isSuspectVehicle: 'Is Suspect Vehicle sp',
	mileage: 'Mileage sp',
	startMileage: 'Start Mileage sp',
	transportingDetails: 'Transporting Details sp',
	addCallDisposition: 'Add Call Disposition sp',
	ncicToken: 'NCIC Token sp',
	token: 'Enter NCIC Token sp',
	searchResponse: 'Search Response sp',
	putOnDuty: 'Put On Duty sp',
	shiftJob: 'Shift Job sp',
	nonCallStatus: 'Non Call Status sp',
	setCallActiveToOffDutyMsg: 'You are currently active on a dispatch call, do you want to clear/remove yourself from Call? sp',
	setCallInActiveToOffDutyMsg: 'Your current status is “On Duty”. Do you want to take yourself Off Duty with this Log Out? sp',
	dateTime: 'Date/Time sp',
	locationName: 'Location Name sp',
	checkMethod: 'Check Method sp',
	startingMileage: 'Starting Mileage sp',
	endingMileage: 'Ending Mileage sp',
	closestLocations: 'CLOSEST LOCATIONS sp',
	pleaseSelectAudioFileLessThan1MBMsg: 'Please select audio file less than 1MB. sp',
	pleaseClickOnSaveButtonToSaveChangesMsg: 'Please click on save button to save changes. sp',
	agencyname: 'Agency name sp',
	dispatchNumber: 'Dispatch number sp',
	clearedInService: 'Cleared / In-Service sp',
	notesInformation: 'Notes/Information sp',
	zone: 'Zone sp',
	newUser: 'New User sp',
	department: 'Department sp',
	shift: 'Shift sp',
	team: 'Team sp',
	users: 'Users sp',
	userExist: 'This user is already exist in same day, same shift. Please select different user or different shift. sp',
	scheduleIDNotDound: 'Please select a date which is in between create Schedule. sp',
	deleteText: 'Are you sure you want to remove this user? sp',
	map: 'Map sp',
	replay: 'Replay sp',
	clearSelection: 'Clear Selection sp',
	selectedUnits: 'Selected Units sp',
	unitsAvailableDateTime: 'Units Available In Date/Time Range sp',
	officerAvailableDateTime: "Officer's Available In Date/Time Range sp",
	high: 'High sp',
	medium: 'Medium sp',
	low: 'Low sp',
	showAll: 'Show all sp',
	playAll: 'Play all sp',
	maxSpeed: 'Max speed sp',
	playAllSingleUnit: 'Play all/single unit sp',
	apiResponseForUnitnumber: 'API response for Unit number sp',
	coordinates: 'Co-ordinates sp',
	dateTimeWithLocalOffSet: 'DateTime With local offset sp',
	callID: 'CallID sp',
	darkMode: 'Dark Mode sp',
	watchFilter: 'Watch Filter sp',
	all: 'All sp',
	checkHistory: 'Check History sp',
	timeFrame: 'TimeFrame sp',
	parsedResponse: "Parsed Response sp",
	messageId: "Message ID sp",
	chatAudibleTone: "Chat Audible Tone sp",
	probableCause: "Probable Cause sp",
	rawResponse: "Raw Response sp",
	searchByLicenseNumber: "Search by License Number sp",
	searchByVINNumber: "Search by VIN Number sp",
	queryVehicle: 'Vehicle search sp',
	startChat: 'Start a conversation by typing your message below. sp',
	searchByName: "Search by Name sp",
	searchByDL: "Search by DL sp",
	freeForm: "FREE FORM sp",
	markStop: "Mark Stop sp",
	start: "Start sp",
	stop: "Stop sp",
	localCitation: "Local Citation sp",
	time: "Time sp",
	searchByLicenseNumber: "Search By License Number sp",
	searchByVINNumber: "Search By VIN Number sp",
	pinCall: "Pin Call sp",
	unpinCall: "Unpin Call sp",
	includeOffDuty: "Include OFF Duty sp",
	fullScreenToggle: "Full Screen Toggle sp",
	onOffDuty: "On/Off Duty sp",
	speedByTime: "Speed By Time sp",
	legend: "Legend sp",
	sanitation: "Sanitation sp",
	scrollLeft: "Scroll Left sp",
	scrollRight: "Scroll Right sp",
	defaultNCICSettings: "Default NCIC Settings sp",
	audibleToneForChatMessage: "Audible tone for chat message sp",
	upload: "Upload sp",
	punctuateText: "Punctuate Text sp",
	searchForAddress: "Search for address sp",
	areYouSure: "Are you sure you want to delete this record ? sp",
	callInformation: "Call Information sp",
	lastRpsChange: "Last RPS Change sp",
	lastNcicChange: "Last NCIC Change sp",
	addIncident: "Add Incident sp",
	incidentEndMsg: "No more Incidents sp",
	incidents: "Incidents sp",
	addIncident: "Add Incident sp",
	incidentEndMsg: "No more Incidents sp",
	incidentDeleteMsg: "Are you sure you want to delete this incident ? sp",
	incidentPersonDeleteMsg: "Are you sure you want to delete this incident person ? sp",
	incidentVehicleDeleteMsg: "Are you sure you want to delete this incident vehicle ? sp",
	edit: "Edit sp",
	individualType: "Individual Type sp",
	businessName: "Business Name sp",
	contactName: "Contact Name sp",
	ext: "Ext sp",
	placeName: "Place name sp",
	address: "Address sp",
	reportedBy: "Reported By sp",
	incidentNumber: "Incident Number sp",
	zipCode: "Zip Code sp",
	violation: "Violation sp",
	reportedDate: "Reported Date sp",
	persons: "Persons sp",
	vehicles: "Vehicles sp",
	organization: "Organization sp",
	property: "Property sp",
	narrative: "Narrative sp",
	files: "Files sp",
	communicationLogs: "Communication Logs sp",
	security: "Security sp",
	locationPlaceOcurred: "Location/Place Ocurred sp",
	nfirsEquipmentType: "NFIRS Equipment Type sp",
	addNFIRS: "Add Nfirs sp",
	type: "Type sp",
	date: "Date sp",
	gpsLocation: "GPS Location sp",
	phoneNumber: "Phone Number sp",
	email: "Email sp",
	comments: "Comments sp",
	details: "Details sp",
	delete: "Delete sp",
	customer: "Customer sp",
	status: "Status sp",
	dateValidation: "Start date must less than end date! sp",
	dateTime: "Date Time sp",
	from: "From sp",
	tipComment: "Tip Comment sp",
	commentType: "Comment Type sp",
	answered: "Answered sp",
	quikTipDetails: "QuikTip Details sp",
	tipTypes: "Tip Types sp",
	addTipType: "Add Tip Type sp",
	notificationCenter: "Notification Center sp",
	sendMessage: "Send Message sp",
	tipType: "Tip Type sp",
	description: "Description sp",
	deleteMsg: "Are you sure you want to delete ? sp",
	sender: "Sender sp",
	pushedMessage: "Pushed Message sp",
	createDateTime: "Created Date Time sp",
	message: "Message sp",
	tipDetail: "Tip Details sp",
	attachments: "Attachments sp",
	tipComments: "Tip Comments sp",
	comment: "Comment sp",
	addInternalComment: "Add Internal Comment sp",
	addPublicComment: "Add Public Comment sp",
	addQuestion: "Add Question sp",
	messageMustHavevalue: "Message Must Have Value! sp",
	commentShouldNotBeEmptyOrNull: "Comment Should Not Be Empty! sp",
	changeStatus: "Change Status sp",
	name: "Name sp",
	users: "Users sp",
	teamDeleteMsg: "Are you sure you want to delete team ? sp",
	confirmationmsg: "This user is present in other team. Are you sure you want this user ? sp",
	department: "Department sp",
	departmentRequired: "You must choose department... sp",
	teamLimit: "No of teams in this department is Exceeds.. sp",
	teamlead: "Team Leader sp",
	colorCode: "Color Code sp",
	cancel: "Cancel sp",
	addTeam: "Add Team sp",
	twitterAuthorize: "Twitter Authorize sp",
	unit: "Units sp",
	unitName: "Unit Name sp",
	agency: "Agency sp",
	branch: "Branch sp",
	station: "Station sp",
	manufacturer: "Manufacturer sp",
	style: "Style sp",
	"color1/color2": "Color 1 / Color 2 sp",
	category: "Category sp",
	emsLevel: "EMS Level sp",
	unitPositions: "Unit Positions sp",
	oilType: "Oil Type sp",
	pmcsSchedule: "PMCS Schedule sp",
	nextServiceDate: "Next Service Date sp",
	nextServiceMiles: "Next Service Miles sp",
	latestMileage: "Latest Mileage sp",
	estimatedEndofLife: "Estimated End of Life sp",
	inServiceDate: "In Service Date sp",
	tireSize: "Tire Size sp",
	fueltype: "Fuel Type sp",
	vinNumber: "VIN Number sp",
	tagState: "Tag State sp",
	tagNumber: " Tag Number sp",
	nextMaintenance: "Next Maintenance sp",
	role: "Role sp",
	capabilityTags: "Capability Tags sp",
	issuedAssignedTo: "Issued/Assigned To sp",
	confirm: "Confirm sp",
	unitDeleteMsg: "Are you sure you want to delete Units ? sp",
	no: "No sp",
	yes: "Yes sp",
	save: "Save sp",
	back: "Back sp",
	selectFile: "Select File sp",
	preview: "Preview sp",
	policecar: "Police Car sp",
	fireTruck: "Fire Truck sp",
	ems: "EMS sp",
	unitType: "Unit Type sp",
	addRole: "Add Role sp",
	acicNcic: "ACIC/NCIC sp",
	neim: "NEIM sp",
	action: "Action sp",
	pleaseSelectImage: "Please select image sp",
	addUnits: "Add Unit sp",
	userAudit: "User Audit sp",
	search: "Search sp",
	appName: "Application Name sp",
	activity: "Activity sp",
	activityDateTime: "Activity date time sp",
	incident: "Incident sp",
	viewDetails: "View Details sp",
	intersectionErrorLogs: "Intersection Error Logs sp",
	importLogsIntersectionPoint: "Import Logs - Intersection Point sp",
	filePath: "File Path sp",
	fileName: "File Name sp",
	updatedCount: "Updated Count sp",
	insertedCount: "Inserted Count sp",
	matchCount: "Match Count sp",
	failedCount: "Failed Count sp",
	totalCount: "Total Count sp",
	viewImportLogs: "View Import Logs sp",
	masterAddressList: "Master Address List sp",
	masterIntersectionList: "Master Intersection List sp",
	municipalityType: "Municipality Type sp",
	addressNumber: "Address Number sp",
	addressNumberComp: "Address Number Comp sp",
	addressBuilding: "Address Building sp",
	legacyAddress: "Legacy Address sp",
	localName: "Local Name sp",
	nearestXStDist: "Nearest Distance sp",
	secondNearestXStDist: "SecondNearest Distance sp",
	masterAddressErrorLogs: "Master Address Error Logs sp",
	importLogsMasterAddressPoint: "Import Logs - Master Address Point sp",
	importDate: "Import Date sp",
	deleteWarning: "Are you sure you want to delete? sp",
	others: "Others sp",
	soundAlertSetting: "Sound Alert Setting sp",
	deleteShiftText: "Are you sure you want to remove the shift? sp",
	addNumber: "Add Number sp",
	deletethisVehicleMsg: "Are you sure you want to delete this vehicle ? sp",
	areYouSureYouWantDelete: "Are You Sure You Want Delete sp",
	selectFiles: "Select Files sp",
	noFileSelectedMsg: "No file is selected. sp",
	fileIsSelected: "1 file is selected. sp",
	accidentalCall: "Accidental Call sp",
	callBackDeadCall: "Call Back Dead Call sp",
	callBackNoAnswer: "Call Back No Answer sp",
	nonAccidentalCallNeedOfficer: "Non Accidental Call Need Officer sp",
	callForService: "Call For Service - Initial Entry From 911 sp",
	showRectangle: "Show Rectangle sp",
	archiveChatOlderThan: "Archive chat older than sp",
	permanentLogin: "Permanent Login sp",
	userAgencyDetails: "User Agency Details sp",
	clear: "Clear sp",
	lastLoginDate: "Last Login Date sp",
	objectId: "ObjectId sp",
	subAddress: "SubAddress sp",
	selectAll: "Select All sp",
	deleteDepartmentMsg: "Are you sure you want to remove this department? sp",
	available: "Available sp",
	unAvailable: "UnAvailable sp",
	where: "Where sp",
	downloadTemplate: "Download Template sp",
	nibrsCode: "Nibrs Code sp",
	addNibrsCode: "Add Nibrs Code sp",
	stateViolation: "State Violation sp",
	violationTypes: "Violation Types sp",
	linkedClassification: "Linked Classification sp",
	defaultClassification: "Default Classification sp",
	jailTypes: "Jail Types sp",
	fingerPrintRequired: "FingerPrint Required sp",
	citationViolation: "Citation Violation sp",
	criminalViolation: "Criminal Violation sp",
	warrentViolation: "Warrent Violation sp",
	trafficViolation: "Traffic Violation sp",
	localOrdinance: "Local Ordinance sp",
	violationClassification: "Violation Classification sp",
	community: "Community sp",
	weaponType: "Weapon Type sp",
	warningMsgForCriminalActivity: " You can only select up to 3 criminal activities. sp",
	criminalActivity: "Criminal Activity sp",
	gangType: "Gang Type sp",
	warningMsgForGangType: "Can select up to 2 gangs sp",
	biasMotivation: "Bias Motivation sp",
	addClassification: "Add Classification sp",
	classificationMapping: "Classification Mapping sp",
	reset: "Reset sp",
	oneDefaultClassificationNeedsToBeSelected: "One Default Classification Needs To Be Selected. sp",
	locationTypes: 'Location Types sp',
	earliestDate: "Earliest Date/Time sp",
	latestDate: "Latest Date/Time sp",
	reportDate: "Report Date/Time",
	updateRequest: "Update Request sp",
	sendUpdateRequest: "Send Update Request sp",
	updatedFields: "Updated Fields sp",
	updatedOn: "Updated On sp",
	updatedBy: "Updated By sp",
	updateAddress: "Update Address sp",
	approve: "Approve sp",
	reject: "Reject sp",
	acceptedRejectedBy: "Accepted/Rejected By sp",
	acceptedRejectedByOn: "Accepted/Rejected On sp",
	masterAddressUpdateRequest: "Master Address Update Request sp",
	oldFields: "Old sp",
	newFields: "New sp",
	dispatchVersion: "Dispatch Version sp",
	socketVersion: "Socket Version sp",
	pollingVersion: "Polling Version sp",
	importViolation: "Import Violation sp",
	otherJurisdition: "Other Jurisdiction sp",
	outOfJurisdiction: "Out Of Jurisdiction sp",
	ARStatuteCode: "AR Statute Code sp",
	TextStatuteTitle: "Text Statute Title sp",
	OffenseDescriptionAbbreviated: "Offense Description Abbreviated sp",
	Type: "Type sp",
	Class: "Class sp",
	NIBRSCode: "NIBRS Code sp",
	BeginDate: "Begin Date sp",
	EndDate: "End Date sp",
	JailTypes: "Jail Types sp",
	ViolationTypes: "Violation Types sp",
	resetPassword: "Reset Password sp",
	rpsCredentials: "RPS Credentials sp",
	mugshotCredentials: "Mugshot Credentials sp",
	disableUser: "Disable User sp",
	enableUser: "Enable User sp",
	options: "Options sp",
	sourceFields: "Source Fields sp",
	destinationFields: "Destination Fields sp",
	selectField: "Select Field sp",
	death: "Death sp",
	createdDate: "Created Date sp",
	lastModifiedDate: "Last Modified Date sp",
	rpsCredentialsFor: "RPS Credentails For sp",
	pleaseEnterUsernameAndPassword: "Please Enter Your Username And Password sp",
	mugshotCredentialsFor: "Mugshot Credentials For sp",
	backtoAgency: "Back to agency sp",
	printPDF: "Print PDF sp",
	audit: "Audit sp",
	generateShift: "Generate Shift sp",
	viewAndEdit: "View & Edit sp",
	reSchedule: "Re-Schedule sp",
	generateShiftAllocation: "Generate Shift Allocation sp",
	changePasswordFor: "Change Password For sp",
	sendEmailToUser: "Send Email to User sp",
	deselectAll: "Deselect All sp",
	displayFields: "Display Fields sp",
	statute: "Statute sp",
	searchFields: "Search Fields sp",
	changeAgency: "Change Agency sp",
	userNotFound: "User not found sp",
	userDisabled: "User disabled successfully sp",
	selectAgencyToBeChanged: "Select agency to be changed sp",
	sortColumns: "Sort Columns sp",
	addressValidateMsg: "You need to validate the address. sp",
	stateMsg: "State should same as agency sp",
	searchForAddress: "Search for address sp",
	incidentOrganization: "Incident Organization sp",
	selectUsers: "Select users sp",
	citationTypeToAdd: "Citation Type to Add sp",
	formatted: "Formatted sp",
	callBackAnswer: "Call Back Answer sp",
	shiftLimitExceedsForDepartment: "The shift limit is exceeds for this department please choose other department. sp",
	shiftAlreadyExists: "This shift is already exists, please select different shift name. sp",
	ageFrom: "Age From sp",
	ageTo: "Age To sp",
	ageType: "Age Type sp",
	issuingAuthority: "Issuing Authority sp",
	dateIssued: "Date Issued sp",
	dateExpires: "Date Expires sp",
	revoked: "Revoked sp",
	revokedDate: "Revoked Date sp",
	idInfo: "Id Info sp",
	phoneNumberType: "Phone Number Type sp",
	extension: "Extension sp",
	extendedDescription: "Extended Description sp",
	skinTone: "Skin Tone sp",
	eyeColor: "Eye Color sp",
	hairLength: "Hair Length sp",
	eyeWear: "Eyewear sp",
	hairStyle: "Hair Style sp",
	demeanor: "Demeanor sp",
	facialHairColor: "Facial Hair Color sp",
	dexterity: "Dexterity sp",
	facialHairStyle: "Facial Hair Style sp",
	speech: "Speech sp",
	build: "Build sp",
	dental: "Dental/Teeth sp",
	clothing: "Clothing and General Appearance sp",
	addAddress: "Please add address to send update request sp",
	recent: "Recent sp",
	suspectedOfUsing: "Suspected of Using sp",
	methodOfEntry: "Method of Entry sp",
	EarliestLessLatestDate: "Earliest Date should be less or equal to Latest Date sp",
	EarliestLessReportDate: "Earliest Date should less or equal to Report Date sp",
	LatestLessEarliestDate: "Latest Date should be equal or greater than Earliest Date sp",
	LatestLessReportDate: "Latest Date should be less or equal to Report Date sp",
	ReportDateGreater: "Report Date should be greater or equal to Earliest and Latest Date sp",
	primaryColor: "Primary Color sp",
	secondaryColor: "Secondary Color sp",
	vehicleChangeHistory: "Vehicle change history sp",
	vin: "Vin sp",
	lastNcicChange: "Last Ncic Change Date sp",
	lastRpsChange: "Last Rps Change Date sp",
	recentPerson: "Person(s) sp",
	recentAddress: "Address(es) sp",
	recentVehicle: "Vehicle(s) sp",
	recentOrganisation: "Organisation(s) sp",
	fireArmType: "Fire Arm Type sp",
	gunType: "Gun Type sp",
	gunDescription: "Gun Description sp",
	noReportDate: "No Report Date sp",
	reportedPerson: "Reported Person sp",
	baby: "Baby",
	newborn: "Newborn",
	neonatal: "Neonatal",
	brandLabel: "Brand sp",
	pleaseSelectLogType: "Please select log type sp",
	gatheringResults: "Gathering Results sp",
	dLScan: "DL SCAN sp",
	nCICSearch: "NCIC SEARCH sp",
	localSearch: "LOCAL SEARCH sp",
	globalSearch: "GLOBAL SEARCH sp",
	manualEntry: "MANUAL ENTRY sp",
	recentList: "RECENT LIST sp",
	residence: "Residence sp",
	USCitizen: "US Citizen sp",
	legalAlien: "Legal Alien sp",
	nationality: "Nationality sp",
	arrestInformation: "Arrest Information sp",
	arrestDate: "Arrest Date sp",
	arrestNumber: "Arrest Number sp",
	arrestSequence: "Arrest Sequence sp",
	arrestType: "Arrest Type sp",
	arresteeArmed: "Weapons At Arrest sp",
	multipleArresteeSegmentIndicator: "Multiple Arrestee Segment Indicator sp",
	juvenileDisposition: "Juvenile Disposition sp",
	searchByVehicle: "Search by Vehicle sp",
	reportingOfficer: "Reporting officer sp",
	exceptionalClearance: "Exceptional clearance sp",
	exceptionalClearanceDate: "Exceptional clearance date sp",
	cargoTheft: "Cargo theft sp",
	domesticViolence: "Domestic violence sp",
	sealed: "Sealed sp",
	active: "Active sp",
	awaitingInformation: "Awaiting information sp",
	closed: "Closed sp",
	inactive: "Inactive sp",
	unfounded: "Unfounded sp",
	LEOKAInformation: "LEOKA Information sp",
	circumstance: "Circumstance sp",
	addrNumber: "Addr Number sp",
	stateMsg: "State should same as agency sp",
	involvementNotes: "Involvement / Notes sp",
	involvement: "Involvement sp",
	notes: "Notes sp",
	st: "ST sp",
	tag: "Tag sp",
	year: "Year sp",
	make: "Make sp",
	model: "Model sp",
	primaryColor: "Primary Color sp",
	secondaryColor: "Secondary Color sp",
	vin: "Vin sp",
	registrationScan: "REGISTRATION SCAN sp",
	nCICSearch: "NCIC SEARCH sp",
	localSearch: "LOCAL SEARCH sp",
	globalSearch: "GLOBAL SEARCH sp",
	manualEntry: "MANUAL ENTRY sp",
	recentList: "RECENT LIST sp",
	country: "Country sp",
	unitID: "Unit ID sp",
	attemptedCompleted: "Attempted/Completed sp",
	misdFelony: "Misd/Felony sp",
	searchbyName: "Search by Name sp",
	incidentPerson: "Incident Person sp",
	involvementTags: "Involvement Tags sp",
	victimConnectedToOffense: "Victim Connected To Offense sp",
	offenseConnectedToVictim: "Offense Connected To Victim sp",
	victim: "Victim sp",
	victims: "Victim(s) sp",
	selectPerson: "Select Person sp",
	selectViolation: "Select Violation sp",
	checkAllVictims: "Check all victims against whom this offense was attempted or committed sp",
	personVictimDeleteMsg: "Are you sure you want to delete this person victim ? sp",
	warningMsgForDelete: "This person is associated with multiple violations. Please remove the person from the violation before deleting. sp",
	noViolation: "No offense is currently available to link. You need to create an offense first. sp",
	invalidHeightEntry: "Invalid height entry sp",
	heightFeet: "{{feet}} ft sp",
	heightFeetInches: "{{feet}} ft {{inches}} in sp",
	heightInches: "{{inches}} in sp",
	brand: "Brand sp",
	recoveryCondition: "Recovery Condition sp",
	prescriptionNumber: "Prescription Number sp",
	evidenceNumber: "Evidence Number sp",
	agencyExhibitNumber: "Agency/Case Exhibit Number sp",
	markLostFound: "Mark as Lost/Found Item sp",
	victimSuspectInfo: "The Victim's relationship to each suspect/offender is reported when the victim was the object of a Crime Against Person, i.e. Assault Offense, Homicide Offense, Kidnaping/Abduction, Forcible Sex Offense, or Nonforcible Sex Offense sp",
	victimRelationship: "Victim Relationship sp",
	victimSuspectRelationship: "Victim To Suspect Relationship(s) sp",
	relationship: "Relationship sp",
	suspect: "Suspect sp",
	addressNoPrefix: "Address No Prefix sp",
	streetNumberOrMileMarker: "Street Number Or Mile Marker sp",
	addressInfo: "Address Info sp",
	addrNoPrefix: "Addr No Prefix sp",
	numberSuffix: "Number Suffix sp",
	preMod: "Pre-Mod sp",
	direction: "Direction sp",
	preType: "Pre-Type sp",
	preSep: "Pre-Sep sp",
	streetNameFull: "Street Name Full sp",
	postMod: "Post Mod sp",
	milePost: "Mile Post sp",
	site: "Site sp",
	subStie: "Sub Site sp",
	structure: "Structure sp",
	wing: "Wing sp",
	streetDirOfTravel: "Street Dir of Travel sp",
	unitPreType: "Unit Pre Type sp",
	unitValue: "Unit Value sp",
	room: "Room sp",
	section: "Section sp",
	row: "Row sp",
	seat: "Seat sp",
	personChangeHistory: "Person Change History sp",
	personInvolmentTagsChangeHistory: "Person Involvement Tags History sp",
	vehicleChangeHistory: "Vehicle Change History sp",
	victimCircumstances: "Victim Circumstances sp",
	noCircumstancesRequired: "No circumstances are required for this offense sp",
	homicideAssaultCircumstances: "Homicide Assault Circumstances sp",
	negligentManslaughterCircumstances: "Negligent Manslaughter Circumstances sp",
	justifiableHomicideCircumstances: "Justifiable Homicide Circumstances sp",
	circumstances: "Circumstances sp",
	additionalJustifiableHomicideCircumstances: "Additional Justifiable Homicide Circumstances sp",
	justifiableHomicideFactor: "Justifiable Homicide Factor sp",
	cityPostalComm: "City (PostalComm) sp",
	postalCommunity: "Postal Community sp",
	postalCode: "Postal Code sp",
	zipPostCode: "Zip (PostCode) sp",
	zipPlus: "Zip+ sp",
	country: "Country sp",
	injuryType: "Injury Type sp",
	injury: "Injury sp",
	socialMedia: "Social Media sp",
	userName: "User Name/ ID sp",
	platform: "Platform sp",
	customPlatform: "Enter Custom Platform Name sp",
	customUrl: "Custom URL sp",
	suspectVictimRelationship: "Suspect To Victim Relationship(s) sp",
	createMasterOrganizationSaveSuccess: "Master Organization created successfully! sp",
	createMasterOrganizationSaveFailed: "Error while creating Master Organization! sp",
	masterOrganizationUpdateSuccess: "Master Organization updated successfully! sp",
	masterOrganizationUpdateFailed: "Error while updating Master Organization! sp",
	searchOrganizations: "Search Organizations sp",
	businessName: "Business Name sp",

	incidentFileDeleteMsg: "Are you sure you want to delete this incident file sp?",
	incidentNoteDeleteMsg: "Are you sure you want to delete this incident narrative sp?",
	incidentPropertyDeleteMsg: "Are you sure you want to delete this incident Property sp?",
	incidentCommunicationDeleteMsg: "Are you sure you want to delete this incident Communication Logs sp?",
	incidentSecurityDeleteMsg: "Are you sure you want to delete this incident Security sp?",
	incidentViolationDeleteMsg: "Are you sure you want to delete this incident Violation sp?",
	incidentOrganizationDeleteMsg: "Are you sure you want to delete this incident Organization sp?",
	deleteIncidentDetail: "Delete Incident Detail sp",
	personChangeHistory: "Person Change History sp",
	addCitation: "Add Citation sp",
	citiationEndMsg: "No more Citations sp",
	citation: "Citation sp",
	citationDeleteMsg: "Are you sure you want to delete this citation ? sp",
	citationViolationDeleteMsg: "Are you sure you want to delete this citation violation ? sp",
	citationPersonDeleteMsg: "Are you sure you want to delete this citation person ? sp",
	citationVehicleDeleteMsg: "Are you sure you want to delete this citation vehicle ? sp",
	citationInformation: "Citation Information sp",
	citationLocation: "Citation Location sp",
	addNewCitation: "Add New Citation sp",
	counts: "Counts sp",
	vehicleChangeHistory: "Vehicle Change History sp",
	streetAddress: "Street Address sp",
	address2: "Address 2 sp",
	smtType: "Type sp",
	smtLocation: "Location sp",
	smtDescription: "Description sp",
	smt: "SMT sp",
	stateProvince: "State / Province sp",
	incidentReport: "INCIDENT REPORT sp",
	reportDate: "Report Dates sp",
	reportNumber: "Report Number sp",
	noReportNumber: "No Report Number",
	drugs: "Drugs sp",
	alcohol: "Alcohol sp",
	computer: "Computer sp",
	officerAssignment: "Officer Assignment sp",
	leoka: "LEOKA sp",
	basicDescription: "Basic Description sp",
	changeNcicPassword: "Change NCIC Password sp",
	ncicPasswordExpired: "NCIC Password Expired sp",
	currentPassword: "Current Password sp",
	newPassword: "New Password sp",
	confirmPassword: "Confirm Password sp",
	newPasswordRequired: "New Password is required sp",
	passwordsDoNotMatch: "Passwords do not match sp",
	info: "Info sp",
	personAlert: "Person Alerts sp",
	bannedPersonRegistry: "Banned Persons Registry sp",
	securityManagementDashboard: "Security Management Dashboard sp",
	nobannedPersonFound: "No banned person found. sp",
	genderRace: "Gender / Race sp",
	banStartDate: "Ban Start Date sp",
	banExpires: "Ban Expires sp",
	bannedBy: "Banned By sp",
	bannedLocation: "Banned Location sp",
	noAddressOnFile: "No address on file sp",
	remoteSource: "Remote Source sp",
	lifetimeBan: "Lifetime Ban sp",
	civilPapersAlert: "Civil Papers Alert sp",
	noCivilPapersFound: "No civil papers found. sp",
	tracking: "Tracking sp",
	docket: "Docket sp",
	paperType: "Paper Type sp",
	courtDate: "Court Date sp",
	requester: "Requester sp",
	plaintiff: "Plaintiff sp",
	defendant: "Defendant sp",
	targetDOB: "Target DOB sp",
	targetRaceSex: "Target Sex / Race sp",
	inmatesRegistry: "Inmates Registry sp",
	noInmatesFound: "No inmates found. sp",
	booking: "Booking sp",
	bookingDate: "Booking Date sp",
	charges: "Charges sp",
	protectionOrdersRegistry: "Protection Orders Registry sp",
	noProtetionOrdersFound: "No protection orders found. sp",
	order: "Order sp",
	orderDate: "Order Date sp",
	dobSexRace: "DOB / Sex / Race sp",
	nextCourtDate: "Next Court Date sp",
	dateServed: "Date Served sp",
	home: "Home sp",
	work: "Work sp",
	taggedPersons: "Tagged Persons sp",
	homeAddress: "Home Address sp",
	workAddress: "Work Address sp",
	contactRace: "Contact Race sp",
	contactSex: "Contact Sex sp",
	contactDL: "Contact DL sp",
	activeWarrantRegistry: "Active Warrants Registry sp",
	warrantStatus: "Warrant Status sp",
	issuedDate: "Issued Date sp",
	activeWarrantRegistry: "Active Warrants Registry sp",
	dlState: "DL / State sp",
	genderRace: "Gender / Race sp",
}

export default locale;
