import i18next from 'i18next';
import React from 'react';

const Unit = React.lazy(() => import('./Unit/Unit'));
const AddEdit = React.lazy(() => import('./Unit/AddEditUnit'));

const UnitAppConfig = {
    settings: {
        layout: {}
    },
    routes: [
        {
            path: '/admin/unit/:code',
            element: <Unit />
        },
        {
            path: '/admin/unit/:code/:id',
            element: <AddEdit />
        },
    ]
};

export default UnitAppConfig;