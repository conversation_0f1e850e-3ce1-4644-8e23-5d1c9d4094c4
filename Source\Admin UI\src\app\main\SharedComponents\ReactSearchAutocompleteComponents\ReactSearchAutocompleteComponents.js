import React from "react";
import { ReactSearchAutocomplete } from 'react-search-autocomplete'
import "./ReactSearchAutocompleteComponents.css"

function ReactSearchAutocompleteComponents(props) {

    const handleOnSearch = (value) => {
        props.passChildOnsearch(value)
    };

    const handleOnHover = (result) => {
        console.log(result);
    };

    const handleOnSelect = (item) => {
        props.pashandleOnSelect(item)
    };

    const handleOnFocus = () => {
        console.log("Focused");
    };

    const handleOnClear = () => {
        props.passHandleOnClear("clear")
    };
    return (
        <ReactSearchAutocomplete
            //className="eyOMfT"
            items={props.items}
            fuseOptions={{ keys: [props.keys] }} // Search on both fields
            resultStringKeyName={props.resultStringKeyName} // String to display in the results
            onSearch={handleOnSearch}
            onHover={handleOnHover}
            onSelect={handleOnSelect}
            onFocus={handleOnFocus}
            onClear={handleOnClear}
            showIcon={false}
            placeholder={props.placeholder}
            maxResults={200}
            styling={{
                // height: "50px",
                border: "1px solid #ccc",
                borderRadius: "4px",
                backgroundColor: "white",
                boxShadow: "none",
                hoverBackgroundColor: "lightgreen",
                color: "grey",
                fontSize: "15px",
                //fontFamily: "Courier",
                iconColor: "grey",
                lineColor: "grey",
                placeholderColor: "grey",
                clearIconMargin: "3px 8px 0 0",
                zIndex: 4,

            }}
        />
    );
}

export default ReactSearchAutocompleteComponents;
