import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../../security/crypto';
import history from "@history";

export const getPersonDetails = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/personMaster/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setPersonDetailsTotalCount(listData.totalCount));
                    dispatch(setPersonDetails(listData.personMasterList));
                    return dispatch(setLoading(false));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    }
    catch (e) {
        return console.error(e);
    }
};

export const SavePerson = (data, tempData) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/personMaster/PersonCreate`, encrypt(JSON.stringify(data)))
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    //access the tempData required for getrequest
                    await dispatch(getPersonDetails(tempData.orderId, tempData.orderDirection, tempData.pageIndex, tempData.rowsPerPage, tempData.searchText, data.code));
                    dispatch(setLoading(false));
                    return dispatch(personSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const UpdatePerson = (data, tempData) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/personMaster/PersonUpdate`, encrypt(JSON.stringify(data)))
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(
                        showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    await dispatch(getPersonDetails(tempData.orderId, tempData.orderDirection, tempData.pageIndex, tempData.rowsPerPage, tempData.searchText, data.code));
                    dispatch(setLoading(false));
                    return dispatch(personSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const removePerson = (id, sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/personMaster/deletePerson/${id}/${code}`)
            .then(async response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }))
                    await dispatch(getPersonDetails(sortField, sortDirection, pageIndex, pageLimit, searchText, code));
                    dispatch(setLoading(false));
                    return dispatch(personSuccess(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                dispatch(showMessage({
                    message: 'Error Occured!', autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'error'
                }));
                return dispatch(personSuccess(false));
            });
    }
    catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

const initialState = {
    data: [],
    totalCount: 0,
    personSuccess: false,
    isloading: false,
};

const personDetailSlice = createSlice({
    name: 'administration/personMaster',
    initialState,
    reducers: {
        setPersonDetails: (state, action) => {
            state.data = action.payload;
        },
        setPersonDetailsTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        personSuccess: (state, action) => {
            state.personSuccess = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
    },
    extraReducers: {}
});

export const {
    setPersonDetails,
    setPersonDetailsTotalCount,
    personSuccess,
    setLoading,
} = personDetailSlice.actions;

export default personDetailSlice.reducer;
