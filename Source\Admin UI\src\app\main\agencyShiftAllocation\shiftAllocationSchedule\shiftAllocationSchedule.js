import React, { useState, useEffect, useRef } from "react";
import _ from "@lodash";
import Button from '@mui/material/Button';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { useParams } from "react-router";
import history from '@history';
import { Grid } from '@mui/material';
import ArrowBackOutlinedIcon from "@mui/icons-material/ArrowBackOutlined";
import Tooltip from "@mui/material/Tooltip";
import Stack from "@mui/material/Stack";
import Typography from '@mui/material/Typography';
import FusePageCarded from "@fuse/core/FusePageCarded";
import { motion } from "framer-motion";
import FuseScrollbars from "@fuse/core/FuseScrollbars";
import AssignmentIcon from '@mui/icons-material/Assignment';
import { Tree, TreeItem, TreeItemsGroup } from 'smart-webcomponents-react/tree';
import { checkValueEmptyOrNull, convertTo12HourFormat, scheduleDataCalculate } from "../../utils/utils";
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import moment from "moment";
import { clearData, saveShiftAllocationSchedule } from "../../store/shiftAllocationScheduleSlice";
import { showMessage } from "app/store/fuse/messageSlice";
import CircularProgressLoader from "../../SharedComponents/CircularProgressLoader/CircularProgressLoader";
import Schedule from "../../components/Schedule/Schedule";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";
import { ErrorBoundary } from "react-error-boundary";

function shiftAllocationSchedule() {

    const { t } = useTranslation('laguageConfig');
    const { control, setValue, formState, handleSubmit, reset, trigger, setError } = useForm({
        mode: 'onChange',
    });
    const routeParams = useParams();
    const user = useSelector(({ auth }) => auth.user);
    const agencyCode = routeParams.code !== "list" ? routeParams.code : user.data.defaultAgency
    const navigateBack = () => {
        if (routeParams.shiftID === '0') {
            history.push(`/admin/ShiftAllocation/${routeParams.code}/0`);
        }
        else {
            history.push(`/admin/ShiftAllocation/${routeParams.code}/${routeParams.shiftID}`);
        }
    };

    const ShiftAllocationData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shiftallocation.data);
    const TeamData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.teammaster.data);
    const shiftsArray = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.shift.data);
    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.department.masterdata);
    const isloadingvalue = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.schedule.isloading);
    const scheduler = useRef(null);
    const calendar = useRef(null);
    const treeref = useRef(null);
    const primaryContainer = useRef(null);
    const dispatch = useDispatch();

    let filterdata = ShiftAllocationData.filter(x => x._id === routeParams.id)
    let filteredDepeartmentData = DepartmentData.filter(x => x._id === filterdata[0].department[0]._id)
    let filteredShiftsData = shiftsArray.filter(x => x.department[0]._id === filterdata[0].department[0]._id)
    let displayToday = moment(filterdata[0].StartDate).format('DD-MM-YYYY')
    let dsiplayEndDate = moment(filterdata[0].EndDate).format('DD-MM-YYYY')

    const data = scheduleDataCalculate(filterdata, TeamData, shiftsArray, filteredDepeartmentData, agencyCode);

    useEffect(() => {
        dispatch(clearData())
    }, []);

    useEffect(() => {
        if (scheduler.current !== null) {
            // scheduler.current.legendLocation = 'header';
            // legendLocation = scheduler.current.legendLocation;
        }
        else {
        }
    }, [primaryContainer, scheduler, calendar, treeref]);

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const saveShiftAllocationData = () => {
        if (data) {
            let datas = [];
            let Dept = [{
                _id: filteredDepeartmentData[0]._id,
                name: filteredDepeartmentData[0].name
            }]
            let a = data.map((x) => x.data.user.map((y) =>
                datas.push({
                    userID: y.value,
                    userName: y.label,
                    userEmail: y.email,
                    scheduleType: filteredDepeartmentData[0].shiftType[0].code,
                    department: Dept,
                    shiftColor: x.backgroundColor,
                    startDateTime: new Date(x.dateStart),
                    endDateTime: new Date(x.dateEnd),
                    // startDateTime: new Date(x.dateStart.getTime() - (x.dateStart.getTimezoneOffset() * 60000)),
                    // endDateTime: new Date(x.dateEnd.getTime() - (x.dateEnd.getTimezoneOffset() * 60000)),
                    teamName: x.label.split(', ')[0],
                    teamId: x.data.teamId,
                    shiftStartTime: x.data.StartTime,
                    shiftEndTime: x.data.EndTime,
                    shiftcode: x.data.shiftcode,
                    shiftId: x.data.shiftId,
                    ShiftName: x.data.shiftcode,
                    shiftDuration: x.data.ShiftDuration.split(' ')[0],
                    scheduleAllocationID: routeParams.id,
                    CreatedBy: new Date()
                })
            )
            );
            dispatch(saveShiftAllocationSchedule(datas, routeParams.code))
        }
        else {
            ShowErroMessage(t("PublishMessage"))
        }
    }

    const [loading, setLoading] = useState();
    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <FusePageCarded
                classes={{
                    content: "flex",
                    header: "min-h-72 h-72 sm:h-136 sm:min-h-136",
                }}
                header={
                    <div style={{ width: "100%" }}>
                        <div className="flex flex-1 items-center justify-between">
                            <div className="flex items-center">
                                <Tooltip title="Back to agency" style={{ float: "right" }}>
                                    <Stack direction="row" spacing={2}>
                                        <Button
                                            className="backButton"
                                            variant="contained"
                                            startIcon={<ArrowBackOutlinedIcon />}
                                            onClick={navigateBack}
                                        >
                                            {t("back")}
                                        </Button>
                                    </Stack>
                                </Tooltip>
                            </div>
                        </div>
                        <div
                            className="flex flex-1 items-center justify-between"
                        //style={routeParams.code === "list" ? { paddingTop: "29px" } : {}}
                        >
                            <div className="flex items-center pb-25">
                                <AssignmentIcon style={{ fontSize: '40px' }} />
                                <Typography
                                    component={motion.span}
                                    initial={{ x: -20 }}
                                    animate={{ x: 0, transition: { delay: 0.2 } }}
                                    delay={300}
                                    className="hidden sm:flex mx-0 sm:mx-12"
                                    variant="h6"
                                >
                                    <div className="flex flex-col" style={{ fontSize: "1.9rem" }}>
                                        <div>
                                            {t("department")} {" - " + checkValueEmptyOrNull(filteredDepeartmentData[0].name)}
                                        </div>
                                        <div>
                                            {t("scheduleType")} {" - " + checkValueEmptyOrNull(filteredDepeartmentData[0].shiftType[0].name)}
                                        </div>
                                        <div>
                                            {t("schedule")} - {t("from")}: {displayToday + " - "} {t("to")}:  {dsiplayEndDate}
                                        </div>
                                    </div>
                                </Typography>
                            </div>
                            <div className="flex items-center pb-25">
                                <Button
                                    variant="contained"
                                    color="secondary"
                                    className=" w-auto mr-16 mt-8 float-right"
                                    aria-label="Register"
                                    onClick={saveShiftAllocationData}
                                    // disabled={_.isEmpty(dirtyFields) || !isValid}
                                    type="submit"
                                    size="large"
                                >
                                    {t("savePublish")}
                                </Button>
                            </div>
                        </div>
                    </div>
                }
                content={
                    <div className="w-full flex flex-col">
                        <FuseScrollbars
                            className="flex-grow overflow-x-auto m-16"
                        >
                            <Grid container>
                                <Grid item xs={12} sm={2} md={2} lg={2} xl={2}>
                                    <Tree ref={treeref} filterable={false} toggleElementPosition="far" style={{ width: '99%' }}>
                                        <TreeItemsGroup expanded>Legend
                                            {filteredShiftsData.map((label) => (
                                                <TreeItem ><FiberManualRecordIcon style={{ color: label.color }} />{checkValueEmptyOrNull(label.ShiftName)} - {convertTo12HourFormat(checkValueEmptyOrNull(label.StartTime))} To {convertTo12HourFormat(checkValueEmptyOrNull(label.EndTime))}</TreeItem>
                                            ))}
                                        </TreeItemsGroup>
                                    </Tree>
                                </Grid>

                                <Grid item xs={12} sm={10} md={10} lg={10} xl={10}>
                                    <ErrorBoundary
                                        FallbackComponent={(props) => <ErrorPage {...props} componentName="Schedule" />} onReset={() => { }} >
                                        <Schedule data={data} todayDate={filterdata[0].StartDate} isDragAndDrop={true} dropFlag={true}></Schedule>
                                    </ErrorBoundary>
                                </Grid>
                            </Grid>
                        </FuseScrollbars>
                    </div>
                }
                innerScroll
            />
        </>

    );
}
export default shiftAllocationSchedule;
