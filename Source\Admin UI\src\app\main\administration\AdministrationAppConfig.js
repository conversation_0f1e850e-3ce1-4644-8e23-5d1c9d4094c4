import i18next from 'i18next';
import React from 'react';
import { Navigate } from 'react-router-dom';
import en from '../../common-i18n/en';
import sp from '../../common-i18n/sp';
import { element } from 'prop-types';

i18next.addResourceBundle('en', 'laguageConfig', en);
i18next.addResourceBundle('sp', 'laguageConfig', sp);

const ProfilePage = React.lazy(() => import('./userProfile/ProfilePage'));
const ServerConfigurationAdd = React.lazy(() => import('./server-configurationAdd/ServerConfigurationAdd'));
const CallICons = React.lazy(() => import('./callIcons/CallIcons'));
const CallCategory = React.lazy(() => import('./CallCategory/CallCategoryNew'));
const CallResponse = React.lazy(() => import('./CallResponse/CallResonseNew'));
const DeviceLicenses = React.lazy(() => import('./DeviceLicenses/DeviceLicensesNew'));
const CallTypes = React.lazy(() => import('./callTypes/CallTypesNew'));
const CallViolation = React.lazy(() => import('./callViolation/CallViolationNew'));
const EditCallViolations = React.lazy(() => import('./callViolation/editCallViolation'));
const AgencyPerson = React.lazy(() => import('../AgencyPersonDetails/AgencyNewPerson'));
const EditPerson = React.lazy(() => import('../AgencyPersonDetails/AddPersons/AddPersons'));
const AgencyVehicle = React.lazy(() => import('../AgencyVehicleDetails/AgencyNewVehicle'));
const EditVehicle = React.lazy(() => import('../AgencyVehicleDetails/AddVehicles/AddVehicles'));
const ChangedPassword = React.lazy(() => import('../ChangedPassword/ChangedPassword'));
const UserListNewTest = React.lazy(() => import('./UsersNewTable/userListNewTest'));
const RegisterNewUser = React.lazy(() => import('./userAddNew/RegisterNewUser'));
const EmailConfiguration = React.lazy(() => import('./EmailConfiguration/emailConfiguration'));
const SupportedUnitType = React.lazy(() => import('./SupportedUnitType/SupportedUnitType'));
const TwitterConfiguration = React.lazy(() => import('./TwitterConfiguration/TwitterConfiguration'));
const AgencyUserTwitterConfiguration = React.lazy(() => import('./AgencyUsersTwitter/agencyUsersTwitter'));
const ArchiveChatHistories = React.lazy(() => import('./archiveChatHistories/archiveChatHistories'));
const NonCallStatus = React.lazy(() => import('./NonCallStatus/NonCallStatus'));
const ProbableCause = React.lazy(() => import('./ProbableCause/ProbableCause'));
const CommunicationLogs = React.lazy(() => import('./CommunicationLogs/CommunicationLog'));
const IntersectionPointList = React.lazy(() => import('./IntersectionPoint/IntersectionPointPages/IntersectionList'));
const IntersectionPointAdd = React.lazy(() => import('./IntersectionPoint/IntersectionPointPages/IntersectionAdd'));
const AddMasterAddress = React.lazy(() => import('./MasterAddressPoint/MasterAddressAdd'));
const MasterAddressTable = React.lazy(() => import('./MasterAddressPoint/MasterAddressTable'));
const CountyTable = React.lazy(() => import('./MasterAddressPoint/CountyTable'));
const StateTable = React.lazy(() => import('./StateViolation/StateTable'));
const IntersectionImportLogs = React.lazy(() => import('./IntersectionPoint/IntersectionPointPages/IntersectionImportLogs'));
const MasterImportLogs = React.lazy(() => import('./MasterAddressPoint/MasterImportLogs'));
const IntersectionImportErrorLogs = React.lazy(() => import('./IntersectionPoint/IntersectionPointPages/IntersectionImportErrorLogs'));
const MasterImportErrorLogs = React.lazy(() => import('./MasterAddressPoint/MasterImportErrorLogs'));
const StateViolation = React.lazy(() => import('./StateViolation/StateViolation'));
const StateViolationAdd = React.lazy(() => import('./StateViolation/StateViolationAdd'));
const NibrsCode = React.lazy(() => import('./NibrsCode/NibrsCode'));
const ViolationClassification = React.lazy(() => import('./StateViolation/violationClassification'));

const AdministrationAppConfig = {
	settings: {
		layout: {}
	},
	routes: [
		{
			path: '/admin/userProfile',
			element: <ProfilePage />
		},
		{
			path: '/admin/serverconfiguration',
			element: <ServerConfigurationAdd />
		},
		{
			path: '/admin/serverconfiguration/:id',
			element: <ServerConfigurationAdd />
		},
		{
			path: '/admin/emailConfiguration',
			element: <EmailConfiguration />
		},
		{
			path: '/admin/archiveChatHistories',
			element: <ArchiveChatHistories />
		},
		{
			path: '/admin/twitterConfiguration',
			element: <TwitterConfiguration />
		},
		{
			path: '/admin/twitter',
			element: <AgencyUserTwitterConfiguration />
		},
		{
			path: '/admin/callIcons',
			element: <CallICons />
		},
		{
			path: '/admin/callCategory/:code',
			element: <CallCategory />
		},
		{
			path: '/admin/NonCallStatus/:code',
			element: <NonCallStatus />
		},
		{
			path: '/admin/ProbableCause/:code',
			element: <ProbableCause />
		},
		{
			path: '/admin/CommunicationLog/:code',
			element: <CommunicationLogs />
		},
		{
			path: '/admin/callResponse/:code',
			element: <CallResponse />
		},
		{
			path: '/admin/devicelicenses/:code',
			element: <DeviceLicenses />
		},
		{
			path: '/admin/callTypes/:code',
			element: <CallTypes />
		},
		{
			path: '/admin/callViolations/:code',
			element: <CallViolation />
		},
		{
			path: '/admin/supportedUnitType/:code',
			element: <SupportedUnitType />
		},
		{
			path: '/admin/callViolation/:code/:id',
			element: <EditCallViolations />
		},
		{
			path: '/admin/person/:code',
			element: <AgencyPerson />
		},
		{
			path: '/admin/person/:code/:id',
			element: <EditPerson />
		},
		{
			path: '/admin/vehicle/:code',
			element: <AgencyVehicle />
		},
		{
			path: '/admin/vehicle/:code/:id',
			element: <EditVehicle />
		},
		{
			path: '/admin/UserChangePassword/:id',
			element: <ChangedPassword />
		},
		{
			path: '/admin/users',
			element: <UserListNewTest />
		},
		{
			path: '/admin/create_New_User',
			element: <RegisterNewUser />
		},
		{
			path: '/admin',
			element: () => <Navigate to="/admin/users" />
		},
		{
			path: '/admin/intersectionPoint',
			element: <IntersectionPointList />
		},
		{
			path: '/admin/intersectionPointAdd',
			element: <IntersectionPointAdd />
		},
		{
			path: '/admin/addMasterAddress',
			element: <AddMasterAddress />
		},
		{
			path: '/admin/masterAddress',
			element: <MasterAddressTable />
		},
		{
			path: '/admin/counties',
			element: <CountyTable />
		},
		{
			path: '/admin/states',
			element: <StateTable />
		},
		{
			path: '/admin/intersectionImportLogs',
			element: <IntersectionImportLogs />
		},
		{
			path: '/admin/masterImportLogs',
			element: <MasterImportLogs />
		},
		{
			path: '/admin/intersectionImportErrorLogs',
			element: <IntersectionImportErrorLogs />
		},
		{
			path: '/admin/masterImportErrorLogs',
			element: <MasterImportErrorLogs />
		},
		{
			path: '/admin/states/stateViolation',
			element: <StateViolation />
		},
		{
			path: '/admin/stateViolationAdd',
			element: <StateViolationAdd />
		},
		{
			path: '/admin/nibrs',
			element: <NibrsCode />
		},
		{
			path: '/admin/states/violationClassification',
			element: <ViolationClassification />
		},

	]
};

export default AdministrationAppConfig;
