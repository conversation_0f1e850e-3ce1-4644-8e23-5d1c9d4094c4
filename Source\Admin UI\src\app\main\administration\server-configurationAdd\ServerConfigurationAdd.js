import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Icon from '@mui/material/Icon';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { saveServerConfiguration, updateServerConfiguration, getServerConfigurations } from '../store/serverConfigurationsSlice'
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { showMessage } from 'app/store/fuse/messageSlice';
import { Card, AppBar, CardContent, Toolbar, TextField } from '@mui/material';
import { newUserAudit } from '../../../main/userAuditPage/store/userAuditSlice';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import _ from '@lodash';
import { useParams } from 'react-router-dom';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowBackOutlinedIcon from '@mui/icons-material/ArrowBackOutlined';
import Tooltip from '@mui/material/Tooltip';
import history from '@history';
import Stack from '@mui/material/Stack';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { decrypt } from 'src/app/security';

const schema = yup.object().shape({
	initialCode: yup.string()
		.required('Please enter Initial code.'),
	agencyName: yup
		.string()
		.required('Please click on search button'),
	agencyURL: yup
		.string()
		.required('Please click on search button'),
});
const defaultValues = {
	initialCode: '',
	agencyName: '',
	agencyURL: ''
};

function ServerConfigurationAdd(props) {
	const { t } = useTranslation('laguageConfig');
	const dispatch = useDispatch();

	const serverConfigurations = useSelector(({ administration }) => administration.serverConfigurations.data);
	const isloading = useSelector(({ administration }) => administration.serverConfigurations.isloading);
	const user = useSelector(({ auth }) => auth.user);
	const routeParams = useParams();
	const agency = routeParams?.id;

	const { control, setValue, formState, handleSubmit, reset, trigger, setError, getValues } = useForm({
		mode: 'onChange',
		defaultValues,
		resolver: yupResolver(schema),
	});
	const { isValid, dirtyFields, errors } = formState;

	useEffect(() => {
		dispatch(newUserAudit({
			activity: "Access Server Configuration",
			user: user,
			appName: "Admin",
		}));
		// eslint-disable-next-line
	}, []);

	const [loading, setLoading] = useState();
	useEffect(() => {
		setLoading(isloading)
	}, [isloading]);



	useEffect(() => {
		var agencycode = agency !== undefined ? agency : null;
		setValue('initialCode', '');
		setValue('agencyName', '');
		setValue('agencyURL', '');
		dispatch(getServerConfigurations(agencycode));
	}, [dispatch, setValue]);

	useEffect(() => {
		if (serverConfigurations.length === 0) {
			setValue('initialCode', '');
			setValue('agencyName', '');
			setValue('agencyURL', '');

		}
		else {
			setValue('initialCode', serverConfigurations[0].initialCode);
			setValue('agencyName', serverConfigurations[0].agencyName);
			setValue('agencyURL', serverConfigurations[0].agencyURL);

		}
	}, [serverConfigurations]);

	function onSubmit(model) {

		const data = {
			_id: "0",
			initialCode: model.initialCode,
			agencyName: model.agencyName,
			agencyURL: model.agencyURL
		};

		if (serverConfigurations.length === 0) {
			data.agencycode = agency !== undefined ? agency : null;
			dispatch(saveServerConfiguration(data));
			//history.push(`/admin/agencyOptionsList/${routeParams.id}`)
		}
		else {
			data._id = serverConfigurations[0]._id;
			data.agencycode = agency !== undefined ? agency : null;
			//data.agencycode = agency;
			dispatch(updateServerConfiguration(data));
			//history.push(`/admin/agencyOptionsList/${routeParams.id}`)
		}

	}


	const getAgencyURL = () => {
		const initialCode = getValues('initialCode');
		if (initialCode === '') {
			setValue('agencyURL', '');
			setValue('agencyName', '');
			dispatch(showMessage({
				message: 'Please enter initial code..', autoHideDuration: 2000,
				anchorOrigin: {
					vertical: 'top',
					horizontal: 'right'
				},
				variant: 'warning'
			}));
		}
		else {

			axios.get("admin/api/agencies/GetAgencyDetailsByAgencyCode/" + initialCode)
				.then(response => {
					if (response.status === 200) {
						response.data = JSON.parse(decrypt(response.data));
						setValue('agencyURL', response.data.URL);
						setValue('agencyName', response.data.AgencyName);
						dispatch(showMessage({
							message: 'Agency details found sucessfully..', autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'success'
						}));
					}
					else {

						setValue('agencyURL', '');
						setValue('agencyName', '');
						dispatch(showMessage({
							message: 'Agency details not found..', autoHideDuration: 2000,
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}));
					}
				}).catch(error => {

					setValue('agencyURL', '');
					setValue('agencyName', '');
					return dispatch(
						showMessage({
							message: 'Agency details not found..',
							autoHideDuration: 2000,//ms
							anchorOrigin: {
								vertical: 'top',
								horizontal: 'right'
							},
							variant: 'warning'
						}),
					);
				});

		}
	};

	return (
		<div class="p-16 w-2/4">
			{loading && <CircularProgressLoader loading={loading} />}
			<Card className="w-full mb-16 rounded-8 ml-4 shadow ">
				<AppBar position="static" elevation={0}>
					<Toolbar className="px-8">
						<Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
							{/* Server Configuration */}
							{t('serverConfiguration')}
						</Typography>
						{
							routeParams.id !== undefined &&
							<Typography variant="subtitle1" color="inherit" className="flex-1 px-12">
								<Tooltip title="Back to agency" style={{ float: 'right' }}>
									<Stack direction="row" spacing={2}>
										<Button className="backButton" variant="contained" startIcon={<ArrowBackOutlinedIcon />}
											onClick={() => history.push(`/admin/agencyOptionsList/${routeParams.id}`)}
										>
											{t('back')}

										</Button>
									</Stack>
								</Tooltip>
							</Typography>
						}
					</Toolbar>
				</AppBar>

				<CardContent>
					<form className="flex flex-col justify-center w-full" onSubmit={handleSubmit(onSubmit)}>
						<Controller
							type="number"
							name="initialCode"
							control={control}
							render={({ field }) => (
								<TextField
									{...field}
									className="mb-16"
									label={t('initialCode')}
									type="text"
									error={!!errors.initialCode}
									helperText={errors?.initialCode?.message}
									variant="outlined"
									InputProps={{
										className: 'pr-2',
										endAdornment: (
											<InputAdornment position="end">
												<IconButton onClick={() => getAgencyURL()} size="large">
													<Icon className="text-20" color="action">
														search
													</Icon>
												</IconButton>
											</InputAdornment>
										),
									}}
									required
								/>
							)}
						/>

						<Controller
							name="agencyName"
							control={control}
							render={({ field }) => (
								<TextField
									{...field}
									className="mb-16"
									label={t('agencyName')}
									type="text"
									error={!!errors.agencyName}
									helperText={errors?.agencyName?.message}
									variant="outlined"
									disabled
									required
								/>
							)}
						/>
						<Controller
							name="agencyURL"
							control={control}
							render={({ field }) => (
								<TextField
									{...field}
									className="mb-16"
									label={t('url')}
									type="text"
									error={!!errors.agencyURL}
									helperText={errors?.agencyURL?.message}
									variant="outlined"
									disabled
									required
								/>
							)}
						/>
						<div className="mx-auto">
							<Button
								type="submit"
								variant="contained"
								color="primary"
								className="normal-case m-16"
								aria-label="REGISTER"
								//disabled={_.isEmpty(dirtyFields) || !isValid}
								value="legacy">
								{t('update')}
							</Button>

						</div>
					</form>

				</CardContent>
			</Card>
		</div>
	);
}

export default ServerConfigurationAdd;

