import React, { useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from "react-redux";
import {
    enableUser, hardDeleteUser, removeUser, setEditUser, getSuperAdminUsers, setLoading,
    SetSuperAdminRowsPerPageValue, SetSuperAdminPageValue,
    setUsersDelete,
    setUsersHardDelete,
    setUsersEnable
} from '../store/usersSlice';
import { Tooltip, IconButton, Switch } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import LockResetIcon from '@mui/icons-material/LockReset';
import LockIcon from '@mui/icons-material/Lock';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import './UserNewTable.css';
import TablePagination from '@mui/material/TablePagination';
import history from '@history';
import { showMessage } from 'app/store/fuse/messageSlice';
import ChangePasswordDlg from '../../Dialog/ChangePasswordDilalog/ChangePasswordDlg';
import { checkData, getAgencyByAgencyID, getNavbarTheme, getRowsPerPageOptions, getUserID, useWindowResizeHeight } from '../../utils/utils';
import ConfirmationDialog from '../../components/ConfirmationDialog/ConfirmationDialog';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import ChangeRPSCredentialsDialog from '../../Dialog/ChangeRPSCredentialsDialog/ChangeRPSCredentialsDialog';
import ChangeMugshotCreadentialsDialog from '../../Dialog/ChangeMugshotCredentialsDialog/ChangeMugshotCreadentialsDialog';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { useDebounce } from '@fuse/hooks';
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";
import AccountCircleIcon from '@mui/icons-material/AccountCircle';

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

const rows = [
    {
        id: '_id',
        align: 'left',
        disablePadding: false,
        label: 'ID',
        sort: true
    },
    {
        id: 'fname',
        align: 'left',
        disablePadding: false,
        label: 'NAME',
        sort: true
    },
    {
        id: 'profilePic',
        align: 'left',
        disablePadding: false,
        label: 'Photo',
        sort: true
    },
    {
        id: 'email',
        align: 'left',
        disablePadding: false,
        label: 'EMAIL',
        sort: true
    },
    {
        id: 'userAgenciesArray',
        align: 'left',
        disablePadding: false,
        label: 'AGENCIES',
        sort: true
    },
    {
        id: 'userAgencies',
        align: 'left',
        disablePadding: false,
        label: 'userAgencies',
        sort: true
    },
    {
        id: 'phone',
        align: 'left',
        disablePadding: false,
        label: 'PHONE',
        sort: true
    },
    {
        id: 'isSuperAdmin',
        align: 'left',
        disablePadding: false,
        label: 'IsSuperAdmin',
        sort: true
    },
    {
        id: 'isActive',
        align: 'left',
        disablePadding: false,
        label: 'IsActive',
        sort: true
    },
    {
        id: 'isEnabled',
        align: 'left',
        disablePadding: false,
        label: 'IsEnabled',
        sort: true
    },
    {
        id: 'remove',
        align: 'left',
        disablePadding: false,
        label: 'Remove',
        sort: true
    },
    {
        id: 'mugshotusername',
        align: 'left',
        disablePadding: false,
        label: 'mugshotusername',
        sort: true
    },
    {
        id: 'mugshotpassword',
        align: 'left',
        disablePadding: false,
        label: 'mugshotpassword',
        sort: true
    },
    {
        id: 'lastLoginDateTime',
        align: 'left',
        disablePadding: false,
        label: 'lastLoginDateTime',
        sort: true
    },
    {
        id: 'source',
        align: 'left',
        disablePadding: false,
        label: 'source',
        sort: true
    },
    {
        id: 'action',
        align: 'left',
        disablePadding: false,
        label: 'ACTIONLBL',
        sort: true
    }
];

function SuperAdminUsersTab(props) {
    const isColumnHidden = props.isColumnHidden;
    const searchText = props.searchText;
    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const BackgroundColor = navbarTheme.palette.mode === 'light' ? 'white' : navbarTheme.palette.primary.main;
    document.documentElement.style.setProperty('--dynamic-bg-color', BackgroundColor);
    document.documentElement.style.setProperty('--dynamic-text-color', Color);

    const SuperAdminUsers = useSelector(({ administration }) => administration.user.SuperAdminUsers);
    const isloading = useSelector(({ administration }) => administration.user.isloading);
    const selectedAgency = useSelector(({ agency }) => agency.agency.filterAgencyValue);
    const userTotalCount = useSelector(({ administration }) => administration.user.SuperAdminUserTotalCount);
    const user = useSelector(({ auth }) => auth.user);
    const userDelete = useSelector(({ administration }) => administration.user.userDelete);
    const userHardDelete = useSelector(({ administration }) => administration.user.userHardDelete);
    const userEnable = useSelector(({ administration }) => administration.user.userEnable);
    // const searchText = useSelector(({ administration }) => administration.user.searchText);
    const selectedUser = useSelector(({ administration }) => administration.user.selectedUser);

    const { t } = useTranslation('laguageConfig');
    const dispatch = useDispatch();
    const [value, setValue] = React.useState(0);
    const gridRef = useRef(null);
    const gridContainerRef = useRef(null);
    const rpsAuthRef = useRef(null);
    const mugshotAuthRef = useRef(null);
    const noteRef = useRef();
    const [order, setOrder] = useState({
        direction: "asc",
        id: "fname",
    });
    const [open, setOpen] = useState(false);
    const [data, setData] = useState(SuperAdminUsers);
    const [totalCount, setTotalCount] = useState(userTotalCount);
    const [valueEnableUser, setValueEnableUser] = useState(false);
    const [openEnableUserDialog, setEnableUserDialog] = useState(false);
    const [openHardDeleteDialog, setHardDeleteDialog] = useState(false);
    const [valueHardDelete, setHardDeleteValue] = useState(false);

    const rowsPerPage = useSelector(({ administration }) => administration.user.superAdminRowsPerPage);
    const page = useSelector(({ administration }) => administration.user.superAdminPage);
    let colorCode = getNavbarTheme();
    const [gridWidth, setGridWidth] = useState(1200);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 375);

    useEffect(() => {
        if (SuperAdminUsers !== undefined && SuperAdminUsers !== null) {
            setData(SuperAdminUsers);
            setTotalCount(userTotalCount);
        }
    }, [SuperAdminUsers, userTotalCount]);

    function handleChangePage(event, value) {
        dispatch(SetSuperAdminPageValue(value))
    }

    function handleChangeRowsPerPage(event) {
        dispatch(SetSuperAdminRowsPerPageValue(event.target.value))
        dispatch(SetSuperAdminPageValue(0));
    }

    const search = useDebounce((search, page, rowsPerPage) => {
        dispatch(getSuperAdminUsers(order.id, order.direction, page * rowsPerPage, rowsPerPage, selectedAgency, search === '' ? null : search));
    }, 200);

    useEffect(() => {
        if (userDelete || userHardDelete || userEnable) {
            if (searchText !== '') {
                search(dispatch, searchText, page, rowsPerPage);
            } else {
                dispatch(getSuperAdminUsers(order.id, order.direction, page * rowsPerPage, rowsPerPage, selectedAgency, searchText === '' ? null : searchText));
            }
            dispatch(setUsersDelete(false));
            dispatch(setUsersHardDelete(false));
            dispatch(setUsersEnable(false));
        }
    }, [dispatch, userDelete, userHardDelete, userEnable, rowsPerPage, page, searchText]);

    useEffect(() => {
        if (selectedAgency !== '') {
            if (searchText !== '') {
                search(searchText, page, rowsPerPage);
            } else {
                dispatch(getSuperAdminUsers(order.id, order.direction, page * rowsPerPage, rowsPerPage, selectedAgency, searchText === '' ? null : searchText));
            }
        }
    }, [dispatch, rowsPerPage, page, selectedAgency, searchText]);

    const handleClickEdit = async (user) => {
        dispatch(setLoading(true))
        const selectedUserData = await getUserID(user._id)
        if (selectedUserData !== null) {
            dispatch(setEditUser(selectedUserData));
            dispatch(setLoading(false))
        }
        else {
            dispatch(setLoading(false))
            ShowErroMessage(t("userNotFound"))
        }
    };

    useEffect(() => {
        if (selectedUser) {
            history.push("/admin/create_New_User");
        }
    }, [selectedUser]);

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            dispatch(removeUser(value));
        }
    }

    function ShowErroMessage(message) {
        dispatch(showMessage({
            message: message,
            autoHideDuration: 2000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }, variant: 'warning'
        }));
    }

    const handleClickDelete = async (userID, n) => {
        if (n.isActive) {
            setValue(userID);
            setOpen(true);
        }
        else {
            //In case of super admin no need to show the limit reach msg 
            // const filterAgencyData = await getAgencyByAgencyID(n.agencyID)
            // const countuser = data.filter(y => y.defaultAgency === n.defaultAgency)
            // if (countuser.length >= parseInt(filterAgencyData.noOfUserLicenses)) {
            //     ShowErroMessage(t('userLimitReachedMsg'));
            // }
            // else {
            setValueEnableUser(n)
            setEnableUserDialog(true);
            // }
        }
    };

    const handleClickEnableUserClose = (n) => {
        setEnableUserDialog(false);
        if (n) {
            dispatch(enableUser(valueEnableUser._id,));
        }
    }

    const handleClickHardDeleteClose = (n) => {
        setHardDeleteDialog(false);
        if (n) {
            dispatch(hardDeleteUser(valueHardDelete.email, valueHardDelete._id));
        }
    }

    const handleClickHardDelete = (n) => {
        setHardDeleteValue(n)
        setHardDeleteDialog(true);
    }

    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {(x.isActive) &&
                        <Tooltip title={t("edit")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => handleClickEdit(x)}

                                size="large">
                                <EditIcon />
                            </IconButton>
                        </Tooltip>
                    }

                    {(user.data.email !== (x.email)) &&
                        <Tooltip title={t("resetPassword")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => noteRef.current.handleClickOpen(x)}
                                size="large">
                                <LockResetIcon />
                            </IconButton>
                        </Tooltip>
                    }

                    {(user.data.email !== (x.email)) &&
                        <Tooltip title={t("rpsCredentials")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => rpsAuthRef.current.handleClickOpen(x, false)}
                                size="large">
                                <ManageAccountsIcon />
                            </IconButton>
                        </Tooltip>
                    }

                    {(user.data.email !== (x.email)) &&
                        <Tooltip title={t("mugshotCredentials")}>
                            <IconButton
                                aria-label="edit"
                                color="inherit"
                                onClick={() => mugshotAuthRef.current.handleClickOpen(x, false)}
                                size="large">
                                <LockIcon />
                            </IconButton>
                        </Tooltip>
                    }

                </>
            );
        }
    };

    const editTemplate = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        (user.data.isSuperAdmin) &&
                        <>
                            <Tooltip title={x.isActive ? "Disable user" : "Enable user"}>
                                <Switch checked={x.isActive}
                                    onClick={() => handleClickDelete(x._id, x)}
                                    color="info" />
                            </Tooltip>
                        </>
                    }
                    {(!x.isActive) && user.data.isSuperAdmin &&
                        <Tooltip title="Delete">
                            <IconButton
                                aria-label="Delete"
                                color="info"
                                onClick={() => handleClickHardDelete(x)}
                                size="large">
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>
                    }
                    {
                        (x.isActive) && user.data.agencyAdmin &&
                        <Tooltip title="Delete">
                            <IconButton
                                aria-label="Delete"
                                color="info"
                                onClick={() => handleClickDelete(x._id, x)}
                                size="large">
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>
                    }
                </>
            );
        }
    };

    // useEffect(() => {
    //     if (props.flag == true) {
    //         // add the group description to the grid's groupDescriptions collection
    //         const peopleGroup = new IgrColumnGroupDescription();
    //         peopleGroup.field = "userAgenciesArray";
    //         peopleGroup.displayName = "";
    //         // Add the group description to the groupDescriptions property of the grid's data source
    //         if (gridRef.current) {
    //             gridRef.current.groupDescriptions.add(peopleGroup);
    //             // Call refresh to update the grid with the grouping
    //             gridRef.current.refresh();
    //         }
    //         if (onToolbarRef.current !== null && gridRef.current !== null) {
    //             onToolbarRef.current.targetGrid = gridRef.current;
    //         }
    //     }
    // }, [rowsPerPage, page, props.flag]);

    const rowData = data !== undefined && data !== null ? data.map(item => {
        const row = {};
        rows.forEach(rowConfig => {
            row[rowConfig.id] = item[rowConfig.id];
            row["userAgenciesArray"] = item.userAgencies.length > 0 ? item.userAgencies.map(number => number.label).join(", ") : "";
            row["fname"] = item.fname + ' ' + item.lname;
            if (item.profilePic == undefined || item.profilePic == null || item.profilePic == "") {
                let icons = require('../../../asset/img/No-Image.png'); // Set default image URL
                row["profilePic"] = icons
            }
            row["email"] = item.email ?? "";
            row["action"] = ActionIcons(item);
            row["isEnabled"] = editTemplate(item);
            row["defaultAgency"] = item.defaultAgency;
            row["lastLoginDateTime"] = item.lastLoginDateTime !== undefined ? new Date(item.lastLoginDateTime).toLocaleString() : "";
            row["source"] = item.source;
            let filterAgecny = item.userAgencies.filter(x => x.agencyCode === item.defaultAgency);
            row["agencyID"] = filterAgecny.length > 0 ? filterAgecny[0].value : "0";
        });
        return row;
    }) : {};

    const rowsPerPageOptions = getRowsPerPageOptions();

    useEffect(() => {
        const updateGridWidth = () => {
            if (gridContainerRef.current) {
                const containerWidth = gridContainerRef.current.offsetWidth;
                setGridWidth(containerWidth);
            }
        };

        // Initial calculation
        updateGridWidth();

        // Add event listener for window resizing
        window.addEventListener('resize', updateGridWidth);
        return () => window.removeEventListener('resize', updateGridWidth);
    }, []);

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };

    //method for rendering image in grid
    const renderImage = (ctx) => {
        const item = ctx.dataContext;
        return (
            <>
                {(item.implicit.includes("static/media/No-Image.33e7df0963941edbeb70.png")) ?
                    <AccountCircleIcon /> :
                    <img
                        src={item.implicit}
                        alt={item.name}
                        style={{ height: '30px', width: '30px', objectFit: 'contain' }}
                    />
                }
            </>

        );
    };

    return (
        <div className="w-full flex flex-col">
            {isloading && <CircularProgressLoader loading={isloading} />}
            <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                <TablePagination
                    className="tablePaging"
                    component="div"
                    count={totalCount}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    backIconButtonProps={{
                        'aria-label': 'Previous Page'
                    }}
                    nextIconButtonProps={{
                        'aria-label': 'Next Page'
                    }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    rowsPerPageOptions={rowsPerPageOptions}
                ></TablePagination>
            </div>
            <div className="flex-grow">
                <div className="igrGridClass">

                    <div>
                        <IgrGrid
                            id="grid"
                            autoGenerate="false"
                            data={rowData}
                            primaryKey="_id"
                            ref={gridRef}
                            height={`${gridHeight}px`}
                            rowHeight={60}
                            groupRowTemplate={groupByRowTemplate}
                            allowFiltering={false}
                            filterMode="ExcelStyleFilter"
                            moving={true}
                            allowPinning={true}
                            pinning={pinningConfig}
                        >
                            <IgrGridToolbar>
                                <IgrGridToolbarActions>
                                    <IgrGridToolbarAdvancedFiltering />
                                    <IgrGridToolbarHiding />
                                    <IgrGridToolbarPinning />
                                </IgrGridToolbarActions>
                            </IgrGridToolbar>
                            <IgrColumn
                                field="profilePic"
                                header={t("profilePicture")}
                                resizable={true}
                                width="80px"
                                horizontalAlignment="stretch"
                                bodyTemplate={renderImage}
                            />
                            <IgrColumn
                                key="Name"
                                field="fname"
                                header={t("name")}
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="email"
                                header={t("email")}
                                field="email"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="agencies"
                                header={t("agencies")}
                                field="userAgenciesArray"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                                hidden={isColumnHidden}
                            />
                            <IgrColumn
                                key="phone"
                                header={t("phone")}
                                field="phone"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="lastLoginDate"
                                header={t("lastLoginDate")}
                                field="lastLoginDateTime"
                                width="270px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="source"
                                header={t("source")}
                                field="source"
                                width="250px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="isEnabled"
                                header={t("isEnabled")}
                                field="isActive"
                                width="120px"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                                pinned={true}
                                bodyTemplate={editTemplate}
                            />
                            <IgrColumn
                                key="action"
                                header={t("action")}
                                field="action"
                                width="200px"
                                resizable={true}
                                pinned={true}
                                bodyTemplate={ActionIcons}
                            />
                        </IgrGrid>
                    </div>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                        <ConfirmationDialog
                            id="ringtone-menu"
                            keepMounted
                            open={open}
                            text={t("disableUserMsg")}
                            onClose={handleClose}
                            value={value}
                        >
                        </ConfirmationDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                        <ConfirmationDialog
                            id="ringtone-menu"
                            keepMounted
                            open={openHardDeleteDialog}
                            text={t("deleteUserMsg")}
                            onClose={handleClickHardDeleteClose}
                            value={valueHardDelete}
                        >
                        </ConfirmationDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                        <ConfirmationDialog
                            id="ringtone-menu"
                            keepMounted
                            open={openEnableUserDialog}
                            text={t("enableUserMsg")}
                            onClose={handleClickEnableUserClose}
                            value={valueEnableUser}
                        >
                        </ConfirmationDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangePasswordDlg" />} onReset={() => { }} >
                        <ChangePasswordDlg ref={noteRef} ></ChangePasswordDlg>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangeRPSCredentialsDialog" />} onReset={() => { }} >
                        <ChangeRPSCredentialsDialog ref={rpsAuthRef} ></ChangeRPSCredentialsDialog>
                    </ErrorBoundary>

                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangeMugshotCreadentialsDialog" />} onReset={() => { }} >
                        <ChangeMugshotCreadentialsDialog ref={mugshotAuthRef} ></ChangeMugshotCreadentialsDialog>
                    </ErrorBoundary>

                </div>
            </div>
        </div>
    )
}

export default SuperAdminUsersTab;