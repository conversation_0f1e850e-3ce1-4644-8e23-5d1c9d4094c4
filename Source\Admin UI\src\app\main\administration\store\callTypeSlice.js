import { createSlice } from "@reduxjs/toolkit";
import { showMessage } from "app/store/fuse/messageSlice";
import axios from "axios";
import { encrypt, decrypt } from "../../../security/crypto";

export const getCallTypes = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios.get(`admin/api/callType/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                dispatch(setLoading(false));
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setCallTypeTotalCount(listData.totalCount));
                    return dispatch(setCallTypes(listData.callTypeList));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const GetMaxCallTypeId = () => async (dispatch) => {
    try {
        await axios.get(`admin/api/callType/GetMaxCallTypeId`)
            .then((response) => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    return data;
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                return console.error(error);
            });
    }
    catch (e) {
        return console.error(e.message);
    }
};

export const saveCallType = (data, pageIndex, pageLimit, sortField, sortDirection, searchText) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios.post(`admin/api/callType`, encrypt(JSON.stringify(data)))
            .then((response) => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    if (!response.exist) {
                        dispatch(showMessage({
                            message: data.isUpdate ? response.message : response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "right",
                            }, variant: "success",
                        })
                        );
                        dispatch(getCallTypes(sortField, sortDirection, pageIndex, pageLimit, null, data.code));
                        return dispatch(setCallResponse(true));
                    } else {
                        return dispatch(showMessage({
                            message: response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: "top",
                                horizontal: "center",
                            }, variant: "warning",
                        })
                        );
                    }
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                response = JSON.parse(decrypt(response.response.data));
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: response.data.message,
                    autoHideDuration: 2000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'right'
                    },
                    variant: 'warning'

                }))
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const removeType = (id, code, pageIndex, pageLimit, sortField, sortDirection, searchText) => async (dispatch) => {
    dispatch(setLoading(true));
    try {
        await axios.delete(`admin/api/callType/${id}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: "top",
                            horizontal: "right",
                        },
                        variant: "success",
                    }));
                    dispatch(getCallTypes(sortField, sortDirection, pageIndex, pageLimit, null, code));
                    return dispatch(setCallResponse(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }))
                }
            })
            .catch((error) => {
                response = JSON.parse(decrypt(response.response.data));
                dispatch(setLoading(false));
                return dispatch(showMessage({
                    message: response.data.message, //text or html
                    autoHideDuration: 2000, //ms
                    anchorOrigin: {
                        vertical: "top",
                        horizontal: "right",
                    },
                    variant: "warning",
                })
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

//for Updating show alert check box
export const UpdateShowAlertFlag = (data, sortField, sortDirection, pageIndex, pageLimit, searchText) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/callType/updateCallTypeShowAlertFlag`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status === 200) {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    return dispatch(getCallTypes(sortField, sortDirection, pageIndex, pageLimit, searchText, data.agencyCode));
                } else {
                    response.data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }));
                }
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

const initialState = {
    data: [],
    success: false,
    isloading: false,
};

const callTypesSlice = createSlice({
    name: "administration/callTypes",
    initialState,
    reducers: {
        setCallTypes: (state, action) => {
            state.data = action.payload;
        },
        setCallResponse: (state, action) => {
            state.success = action.payload;
        },
        setCallTypeTotalCount: (state, action) => {
            state.totalCount = action.payload;
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
    },
    extraReducers: {},
});

export const {
    setCallTypes,
    setCallResponse,
    setIconsData,
    setCallTypeTotalCount,
    setLoading
} = callTypesSlice.actions;

export default callTypesSlice.reducer;