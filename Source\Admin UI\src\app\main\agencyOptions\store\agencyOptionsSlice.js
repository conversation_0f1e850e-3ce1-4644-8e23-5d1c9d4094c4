import { createSlice } from '@reduxjs/toolkit';


const initialState = {
    agencyData:[],
};

const agencyOptionSlice = createSlice({
    name: 'agencyOptions',
    initialState,
    reducers: {
        setAgencyNames: (state, action) => {
            
            state.agencyData = action.payload;
        }
    },
    extraReducers: {}
});

export default agencyOptionSlice.reducer;

// Actions
export const {
    setAgencyNames,
} = agencyOptionSlice.actions;

export const setAgencyName = (agencyName) => async dispatch => {
    
    try {
        return dispatch(setAgencyNames(agencyName));
    } catch (e) {
        return console.error(e.message);
    }
}
