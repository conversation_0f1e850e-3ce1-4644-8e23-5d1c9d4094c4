import React from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const CivilPapersAlert = ({ civilPapers }) => {

  const { t } = useTranslation('languageConfig');

  const getInitials = (name) => {
    if (!name || typeof name !== 'string') return '';
    const parts = name.split(',').map(p => p.trim());

    if (parts.length === 2) {
      const [last, firstMiddle] = parts;
      const firstInitial = firstMiddle.split(' ')[0]?.[0] || '';
      const lastInitial = last?.[0] || '';
      return (firstInitial + lastInitial).toUpperCase();
    }

    const names = name.trim().split(/\s+/);
    return ((names[0]?.[0] || '') + (names[names.length - 1]?.[0] || '')).toUpperCase();
  };

  if (!Array.isArray(civilPapers)) {
    return (
      <div className="p-8 text-center text-red-500">
        {t("noCivilPapersFound")}
      </div>
    );
  }

  return (
    <div className="p-8">
      <Box className="text-center mb-16">
        <Typography variant="h3" className="text-indigo-600 font-extrabold text-4xl">
          📄{t("civilPapersAlert")}
        </Typography>
        {/* <Typography variant="subtitle1" className="text-gray-600 text-xl mt-2">
          Legal Notifications Dashboard
        </Typography> */}
      </Box>

      {civilPapers.length === 0 ? (
        <div className="text-center text-gray-500 text-xl mt-10">
          {t("noCivilPapersFound")}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-16">
          {civilPapers.map((paper, index) => (
            <div
              key={index}
              className="rounded-2xl shadow-lg p-6 border-t-4 border-indigo-500 transition-all duration-300 hover:shadow-2xl hover:-translate-y-1"
            >
              <div className="flex items-center space-x-6 mb-6">
                <div className="h-40 w-40 rounded-full bg-indigo-600 flex items-center justify-center font-extrabold text-2xl text-white">
                  {getInitials(paper.target)}
                </div>
                <div>
                  <div className="text-xl font-semibold">
                    {paper.target || ''}
                  </div>
                  <div className="text-blue-600 text-sm">
                    {t("tracking")} #: {paper.trackingNumber || 'N/A'}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 text-base gap-4 border-t pt-4">
                <div>
                  <div className="font-medium text-gray-500">{t("docket")} #</div>
                  <div className="font-semibold">{paper.docketNumber || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("paperType")}</div>
                  <div className="font-semibold">{paper.civilPaperType || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("courtDate")}</div>
                  <div className="font-semibold">{paper.courtDate || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("requester")}</div>
                  <div className="font-semibold">{paper.requester || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("plaintiff")}</div>
                  <div className="font-semibold">{paper.plaintiff || 'N/A'}</div>
                </div>
                <div>
                  <div className="font-bold text-gray-500">{t("defendant")}</div>
                  <div className="font-semibold">{paper.defendant || 'N/A'}</div>
                </div>
              </div>

              <div className="mt-6 bg-gray-100 dark:bg-gray-800 rounded-md p-3 text-base leading-relaxed">
                <div>
                  <span className="font-bold text-gray-500">{t("targetDOB")}:</span>{' '}
                  <span className="font-semibold">{paper.targetDOB || 'N/A'}</span>
                </div>
                <div>
                  <span className="font-bold text-gray-500">{t("targetRaceSex")}:</span>{' '}
                  <span className="font-semibold">{paper.targetSex || 'N/A'} / {paper.targetRace || 'N/A'}</span>
                </div>
                <div>
                  <span className="font-bold text-gray-500">{t("remoteSource")}:</span>{' '}
                  <span className="font-semibold">{paper.remotesource || 'Unknown'}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CivilPapersAlert;
