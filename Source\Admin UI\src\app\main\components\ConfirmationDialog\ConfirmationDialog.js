import { <PERSON><PERSON><PERSON>, <PERSON>con<PERSON>utton, <PERSON>ton, DialogContent, DialogTitle, Dialog, DialogActions, Switch } from '@mui/material';
import { makeStyles } from '@mui/styles';
import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

function ConfirmationDialog(props) {
    const { onClose, value: valueProp, open, ...other } = props;

    const { t } = useTranslation("laguageConfig");
    
    const handleCancel = () => {
        onClose(false);
    };

    const handleOk = () => {
        onClose(true);
    };

    return (
        <Dialog style={{ marginBottom: props.marginBottom }}
            disableEscapeKeyDown
            maxWidth="xs"
            aria-labelledby="confirmation-dialog-title"
            open={open}
            {...other}>
            <DialogTitle id="confirmation-dialog-title" color="primary">{t("confirm")}</DialogTitle>
            <DialogContent dividers>
                <div className='m-16'>
                    {props.text}
                </div>
            </DialogContent>
            <DialogActions>
                <Button autoFocus onClick={handleCancel} variant="contained" color="primary">
                    {t("no")}
                </Button>
                <Button onClick={handleOk} variant="contained" color="primary">
                    {t("yes")}
                </Button>
            </DialogActions>
        </Dialog>
    );
}

export default ConfirmationDialog;
