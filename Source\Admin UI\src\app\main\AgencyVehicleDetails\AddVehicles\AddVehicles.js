import React from "react";
import { <PERSON><PERSON>ontent, Card, AppBar, Toolbar, Typography } from "@mui/material";
import VehicleData from "./VehicleData";
import "./AddVehicles.css";
import { useTranslation } from "react-i18next";
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";

function AddVehicles() {
  const { t } = useTranslation("laguageConfig");

  return (
    <div className="p-16">
      <Card className=" m-16 rounded-8 shadow">
        <AppBar position="static" elevation={0}>
          <Toolbar className="px-8">
            <Typography
              variant="subtitle1"
              color="inherit"
              className="flex-1 px-12"
            >
              {t("vehicle")}
            </Typography>
          </Toolbar>
        </AppBar>

        <CardContent style={{ overflowX: "scroll", height: "90%" }}>
          <ErrorBoundary
            FallbackComponent={(props) => <ErrorPage {...props} componentName="VehicleData" />} onReset={() => { }} >
            <VehicleData />
          </ErrorBoundary>
        </CardContent>
      </Card>
    </div>
  );
}

export default AddVehicles;