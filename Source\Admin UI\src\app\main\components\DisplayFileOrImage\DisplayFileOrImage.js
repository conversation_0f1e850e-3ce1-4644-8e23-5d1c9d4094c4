import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import Grid from '@mui/material/Grid';
import "./DisplayFileOrImage.css"
import { getIcon } from '../../utils/utils';
import ViewImageDialog from '../../Dialog/ViewImageDialog';
import { ErrorBoundary } from "react-error-boundary";
import ErrorPage from "../../SharedComponents/ErrorPage/ErrorPage";

// const controlStyle = { padding: 0, width: '200px', height: '200px' };
const controlStyleAudio = { padding: 0, width: '205px', height: '50px', };
const controlStyleVideo = { padding: 0, width: '205px', height: '205px', };


function DisplayFileOrImage(props) {
    const dispatch = useDispatch();
    const { t } = useTranslation('laguageConfig');

    const imageRef = useRef();

    return <>
        {props.value.fileType.includes('image') &&
            <>
                <img src={props.value.fileUrl} onClick={() => imageRef.current.openCitation(props.value.fileUrl, props.value.fileType, props.value.name, props.value.description)} alt="..." className="cardImage"></img>
                <h4><b>{props.value.name}</b></h4>
            </>
        }
        {props.value.fileType.includes('pdf') &&
            <>
                <img src={getIcon("pdf")}
                    alt="..."
                    className="cardImage"></img>
                <h4><b>{props.value.name}</b></h4>
            </>
        }
        {props.value.fileType.includes('video') &&
            <>
                <video src={props.value.fileUrl} onClick={() => imageRef.current.openCitation(props.value.fileUrl, props.value.fileType, props.value.name, props.value.description)} controls style={controlStyleVideo} className="cardImage"></video>
                <h4><b>{props.value.name}</b></h4>
            </>
        }
        {props.value.fileType.includes('audio') &&
            <>
                <audio src={props.value.fileUrl} onClick={() => imageRef.current.openCitation(props.value.fileUrl, props.value.fileType, props.value.name, props.value.description)} controls style={controlStyleAudio}></audio>
                <h4><b>{props.value.name}</b></h4>
            </>
        }
        {props.value.fileType.includes('word') &&
            <>
                <img src={getIcon("word")} alt="..." onClick={() => imageRef.current.openCitation(props.value.fileUrl, props.value.fileType, props.value.name, props.value.description)} className="cardImage"></img>
                <h4><b>{props.value.name}</b></h4>
            </>
        }
        {props.value.fileType.includes('excel') &&
            <>
                <img src={getIcon("excel")} alt="..." onClick={() => imageRef.current.openCitation(props.value.fileUrl, props.value.fileType, props.value.name, props.value.description)} className="cardImage"></img>
                <h4><b>{props.value.name}</b></h4>
            </>
        }

        <ErrorBoundary
            FallbackComponent={(props) => <ErrorPage {...props} componentName="ViewImageDialog" />} onReset={() => { }} >
            <ViewImageDialog ref={imageRef} />
        </ErrorBoundary>
    </>
}


export default DisplayFileOrImage;