import "./UserEntityHistory.css";
import { useSelector } from 'react-redux';
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import Divider from '@mui/material/Divider';
import ListItemText from '@mui/material/ListItemText';
import { convertObjectToStringValue } from "../../utils/utils";

function UserEntityHistory(props) {
    const { userEntityHistoryData } = props;

    const displayValue = (item) => {
        let obj = {};
        for (let i = 0; i < item.fieldChanged.length; i++) {
            obj[item.fieldChanged[i]] = item.oldValues[i];
        }
        let stringData = convertObjectToStringValue(obj);
        return stringData;
    }

    const displayNameAndTime = (item) => {
        const label = item.modifiedBy !== undefined ? item.modifiedBy + " | " + new Date(item.modifiedDate).toUTCString().split('GMT') : '';
        return label;
    }

    return (
        <div>
            {userEntityHistoryData.length > 0 &&
                <Card>
                    <CardContent >
                        <List sx={{
                            width: '100%',
                            bgcolor: 'background.paper',
                            position: 'relative',
                            overflow: 'auto',
                            maxHeight: 90,
                            '& ul': { padding: 0 },
                        }}
                        >
                            {userEntityHistoryData && userEntityHistoryData.map((item, i) =>
                                <ListItem className='pointer' display='flex'>
                                    <div>
                                        <ListItemText primary={displayNameAndTime(item)}
                                            primaryTypographyProps={{
                                                fontWeight: 700,
                                                fontSize: 14,
                                            }} />

                                        <ListItemText primary={displayValue(item)}
                                            primaryTypographyProps={{
                                                fontWeight: 500,
                                                fontSize: 14,
                                            }} />
                                        <Divider />
                                    </div>
                                </ListItem>
                            )}
                        </List>
                    </CardContent>
                </Card>
            }
        </div>
    )
}

export default UserEntityHistory;