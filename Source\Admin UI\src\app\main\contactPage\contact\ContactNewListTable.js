import _ from '@lodash';
import TablePagination from '@mui/material/TablePagination';
import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import withRouter from '@fuse/core/withRouter';
import EditIcon from '@mui/icons-material/Edit';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import DeleteIcon from '@mui/icons-material/Delete';
import history from '@history';
import { useParams } from "react-router";
import { getContactList, setContactID, removeContact, searchContact } from '../store/contactSlice';
import { useTranslation } from 'react-i18next';
import { checkData, getNavbarTheme, getRowsPerPageOptions, useWindowResizeHeight } from '../../utils/utils';
import ConfirmationDialog from '../../components/ConfirmationDialog/ConfirmationDialog';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';
import { useDebounce } from '@fuse/hooks';
import CircularProgressLoader from '../../SharedComponents/CircularProgressLoader/CircularProgressLoader';
import { selectNavbarTheme } from 'app/store/fuse/settingsSlice';
import {
    IgrGridModule,
    IgrGridToolbar,
    IgrGridToolbarActions,
    IgrGridToolbarAdvancedFiltering,
    IgrGridToolbarHiding,
    IgrGridToolbarPinning,
    IgrGrid,
    IgrColumn,
    ColumnPinningPosition,
    ColumnPinning
} from "@infragistics/igniteui-react-grids";

// import "@infragistics/igniteui-react-grids/grids/combined";
// import "@infragistics/igniteui-react-grids/grids/themes/light/bootstrap.css";

IgrGridModule.register();

const pinningConfig = new ColumnPinning();
pinningConfig.columns = ColumnPinningPosition.End;

function ContactNewListTable(props) {
    const dispatch = useDispatch();
    const routeParams = useParams();
    const contact = useSelector(({ contact }) => contact.contact.data);
    const searchcontacts = useSelector(({ contact }) => contact.contact.searchcontacts);
    const searchText = useSelector(({ contact }) => contact.contact.searchText);
    const contactListData = useSelector(({ contact }) => contact.contact.data);
    const contactTotalCount = useSelector(({ contact }) => contact.contact.totalCount);
    //.....Call Custom hook to handle window height adjustments dynamically
    const gridHeight = useWindowResizeHeight(window.innerHeight, 275);
    const { t } = useTranslation('laguageConfig');
    const navbarTheme = useSelector(selectNavbarTheme);
    const Color = navbarTheme.palette.mode === 'light' ? 'black' : 'white';
    const gridRef = useRef(null);
    const [data, setData] = useState(contactListData);
    const [pageIndex, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(100);
    const [removeID, setRemoveID] = useState(0);
    const [countData, setCountData] = React.useState(contactTotalCount);
    const [order, setOrder] = useState({
        direction: 'asc',
        id: 'Title'
    });
    const [gridWidth, setGridWidth] = useState(1200); // Default width
    const [currentSortDirection, setCurrentSortDirection] = useState("Ascending");

    let colorCode = getNavbarTheme();

    const search = useDebounce((search, pageIndex, rowsPerPage) => {
        dispatch(getContactList(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage,
            search === '' ? null : search, routeParams.code));
    }, 500);


    useEffect(() => {
        if (searchText !== '') {
            search(searchText, pageIndex, rowsPerPage);
        } else {
            dispatch(getContactList(order.id, order.direction, pageIndex * rowsPerPage, rowsPerPage,
                searchText === '' ? null : searchText, routeParams.code));
        }
    }, [dispatch, searchText, pageIndex, rowsPerPage, order, routeParams.code]);

    useEffect(() => {
        setData(contactListData);
        setCountData(contactTotalCount)
    }, [contactListData]);

    const isloadingvalue = useSelector(({ contact }) => contact.contact.isloading);
    const [loading, setLoading] = useState();

    useEffect(() => {
        setLoading(isloadingvalue)
    }, [isloadingvalue]);

    function handleChangePage(event, value) {
        setPage(value);
    }

    function handleChangeRowsPerPage(event) {
        setRowsPerPage(event.target.value);
        setPage(0);
    }

    const handleClickEdit = (contact) => {
        dispatch(setContactID(contact._id));
        history.push(`/contactEdit/${routeParams.code}`);
    };

    const [open, setOpen] = React.useState(false);
    const [value, setValue] = React.useState();

    const handleClickDelete = (agencyID) => {
        setValue(agencyID);
        setOpen(true);
        setRemoveID(agencyID);
    };

    const handleClose = (newValue) => {
        setOpen(false);
        if (newValue) {
            //after user select yes
            dispatch(removeContact(removeID, routeParams.code));
        }
    };

    const ActionIcons = (n) => {
        // let x = checkData(n)

        if (n && n.dataContext) {
            let x = n.dataContext.cell.row.data;

            return (
                <>
                    {
                        <div style={{ display: "flex" }}>
                            <Tooltip title={t("edit")}>
                                <IconButton
                                    aria-label="edit"
                                    color="inherit"
                                    onClick={() => handleClickEdit(x)}
                                    size="large">
                                    <EditIcon />
                                </IconButton>
                            </Tooltip>

                            <Tooltip title={t("delete")}>
                                <IconButton
                                    aria-label="delete"
                                    color="inherit"
                                    onClick={() => handleClickDelete(x._id)}
                                    size="large">
                                    <DeleteIcon />
                                </IconButton>
                            </Tooltip>
                        </div>
                    }
                </>
            );
        }
    };

    const onSortChanged = () => {
        // Access sortDescriptions directly from the gridRef
        const sortDescriptions = gridRef.current?.sortDescriptions;

        if (sortDescriptions && sortDescriptions.count > 0) {
            const sortDesc = sortDescriptions.item(0);  // Get the first sort description
            // Update the order state
            const direction = sortDesc.sortDirection === 1 ? "asc" : "desc"; // Adjust based on Ignite UI's sort direction values
            const fieldName = sortDesc.field;
            setCurrentSortDirection(sortDesc.sortDirection === 1 ? "Ascending" : "Descending");
            setOrder({ id: fieldName, direction: direction });
        } else {
            console.log("No sorting applied.");
        }
    };

    const rowsPerPageOptions = getRowsPerPageOptions();

    const groupByRowTemplate = (ctx) => {
        const groupRow = ctx.dataContext.implicit;
        return (
            <div style={{ padding: '10px', fontSize: '15px' }}>
                <span>{groupRow.value}</span> - {groupRow.records.length} Items
            </div>
        );
    };


    return (
        <>
            {loading && < CircularProgressLoader loading={loading} />}
            <div className="w-full flex flex-col">
                <div className="igrGridClass">
                    <div className="sticky" style={{ backgroundColor: colorCode.backgroundColor, color: colorCode.color }}>
                        <TablePagination
                            className="tablePaging"
                            component="div"
                            count={countData}
                            rowsPerPage={rowsPerPage}
                            page={pageIndex}
                            backIconButtonProps={{
                                'aria-label': 'Previous Page'
                            }}
                            nextIconButtonProps={{
                                'aria-label': 'Next Page'
                            }}
                            onPageChange={handleChangePage}
                            onRowsPerPageChange={handleChangeRowsPerPage}
                            rowsPerPageOptions={rowsPerPageOptions}
                        /></div>

                    <div>
                        <IgrGrid
                            id="grid"
                            autoGenerate="false"
                            data={data}
                            primaryKey="_id"
                            ref={gridRef}
                            height={`${gridHeight}px`}
                            rowHeight={60}
                            groupRowTemplate={groupByRowTemplate}
                            filterMode="ExcelStyleFilter"
                            moving={true}
                            allowFiltering={false}
                            allowAdvancedFiltering={true}
                            allowPinning={true}
                            pinning={pinningConfig}

                        >
                            <IgrGridToolbar>
                                <IgrGridToolbarActions>
                                    <IgrGridToolbarAdvancedFiltering />
                                    <IgrGridToolbarHiding />
                                    <IgrGridToolbarPinning />
                                </IgrGridToolbarActions>
                            </IgrGridToolbar>
                            <IgrColumn
                                key="title"
                                field="title"
                                header={t("title")}
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="fName"
                                header={t("firstName")}
                                field="fName"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="mName"
                                header={t("middleName")}
                                field="mName"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="lName"
                                header={t("lastName")}
                                field="lName"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="address"
                                header={t("address")}
                                field="address"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="city"
                                header={t("city")}
                                field="city"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="phone"
                                header={t("phone")}
                                field="phone"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="email"
                                header={t("email")}
                                field="email"
                                resizable={true}
                                groupable={true}
                                sortable={true}
                            />
                            <IgrColumn
                                key="action"
                                field="action"
                                header={t("action")}
                                resizable={true}
                                pinned={true}
                                bodyTemplate={ActionIcons}
                            />
                        </IgrGrid>
                    </div>
                </div>
                <ErrorBoundary
                    FallbackComponent={(props) => <ErrorPage {...props} componentName="ConfirmationDialog" />} onReset={() => { }} >
                    <ConfirmationDialog
                        id="ringtone-menu"
                        keepMounted
                        open={open}
                        text={t("contactsDeleteMsg")}
                        onClose={handleClose}
                        value={removeID}
                    >
                    </ConfirmationDialog>
                </ErrorBoundary>

            </div>
        </>
    );
}

export default withRouter(ContactNewListTable);