import { createSlice } from '@reduxjs/toolkit';
import { showMessage } from 'app/store/fuse/messageSlice';
import axios from 'axios';
import { encrypt, decrypt } from '../../security/crypto';

export const getTeamDetails = (searchText, code) => async dispatch => {
    const data = { searchText, code }
    try {
        dispatch(setLoading(true));
        await axios
            .post(`admin/api/team/teamDetails`, encrypt(JSON.stringify(data)))
            .then(response => {
                dispatch(teamListSuccess(JSON.parse(decrypt(response.data))));
                dispatch(setLoading(false));
                if (response.status == 200) {
                    dispatch(
                        showMessage({
                            message: 'Team details found.',
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        })
                    );
                } else {
                    dispatch(
                        showMessage({
                            message: 'Team details not found.',
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'warning'
                        })
                    );
                }
            })
            .catch(error => {
                dispatch(setLoading(false));
                dispatch(teamListError(error));
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const getTeams = (sortField, sortDirection, pageIndex, pageLimit, searchText, code) => async (dispatch) => {
    try {
        dispatch(setLoading(true));
        await axios
            .get(`admin/api/team/${sortField}/${sortDirection}/${pageIndex}/${pageLimit}/${searchText}/${code}`)
            .then((response) => {
                if (response.status == 200) {
                    let listData = JSON.parse(decrypt(response.data));
                    dispatch(setTeam(listData.teammastersList));
                    dispatch(setLoading(false));
                    return dispatch(setTeamsTotalCount(listData.totalCount));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                    dispatch(setLoading(false));
                }
            }).catch(error => {
                // dispatch(setLoading(false));
                return console.error(error);
            });
    }
    catch (e) {
        // dispatch(setLoading(false));
        return console.error(e.message);
    }
};

export const saveTeam = (data, sortField, sortDirection, pageIndex, pageLimit) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.post(`admin/api/team`, encrypt(JSON.stringify(data)))
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(
                        showMessage({
                            message: data.isUpdate ? response.message : response.message,
                            autoHideDuration: 2000,
                            anchorOrigin: {
                                vertical: 'top',
                                horizontal: 'right'
                            },
                            variant: 'success'
                        }))
                    dispatch(getTeams(sortField, sortDirection, pageIndex, pageLimit, null, data.code));
                    return dispatch(setTeamResponse(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return console.error(error);
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

export const removeTeam = (ID, code, pageIndex, pageLimit, sortField, sortDirection) => async dispatch => {
    try {
        dispatch(setLoading(true));
        await axios.delete(`admin/api/team/${ID}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    response = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.message, autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'success'
                    }));
                    dispatch(getTeams(sortField, sortDirection, pageIndex, pageLimit, null, code));
                    return dispatch(setTeamResponse(true));
                }
                else {
                    response = JSON.parse(decrypt(response.response.data));
                    dispatch(setLoading(false));
                    dispatch(showMessage({
                        message: response.data.message,
                        autoHideDuration: 2000,
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'

                    }))
                }
            }).catch(error => {
                dispatch(setLoading(false));
                return dispatch(
                    showMessage({
                        message: error.response.data,//text or html
                        autoHideDuration: 2000,//ms
                        anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'right'
                        },
                        variant: 'warning'
                    }),
                );
            });
    } catch (e) {
        dispatch(setLoading(false));
        return console.error(e.message);
    }
}

//for searching
export const searchTeam = (searchText, code) => async dispatch => {
    dispatch(setLoading(true));
    try {
        axios.get(`admin/api/team/searchTeam/${searchText}/${code}`)
            .then(response => {
                if (response.status == 200) {
                    let data = JSON.parse(decrypt(response.data));
                    dispatch(setLoading(false));
                    return dispatch(setSearchTeams(data));
                }
            })
    } catch (e) {
        return console.error(e.message);
    }
}

const initialState = {
    teamsuccess: false,
    teamdata: [],
    data: [],
    searchTeams: [],
    success: false,
    isloading: false,
    nfirsdata: [],
    totalCount: 0
};

const teamSlice = createSlice({
    name: 'teammaster',
    initialState,
    reducers: {
        teamListSuccess: (state, action) => {
            state.teamsuccess = true;
            state.teamdata = action.payload;
        },
        setSearchTeams: (state, action) => {
            state.searchTeams = action.payload;
        },
        teamListError: (state, action) => {
            state.teamsuccess = false;
            state.teamdata = [];
        },
        setTeam: (state, action) => {
            state.data = action.payload;
        },
        setTeamResponse: (state, action) => {
            state.success = action.payload;
        },
        setTeamsTotalCount: (state, action) => {
            state.totalCount = action.payload
        },
        setLoading: (state, action) => {
            state.isloading = action.payload;
        },
    },
    extraReducers: {}
});

export const {
    teamListSuccess,
    setTeam,
    setTeamResponse,
    setTeamsTotalCount,
    setLoading,
    setSearchTeams,
    teamListError,
} = teamSlice.actions;

export default teamSlice.reducer;