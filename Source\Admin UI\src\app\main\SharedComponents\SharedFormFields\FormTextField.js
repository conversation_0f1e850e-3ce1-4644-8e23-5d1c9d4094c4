import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import { Controller, useFormContext } from "react-hook-form";

const FormTextField = ({
  name,
  label,
  control: controlProp, // Rename prop to avoid confusion
  error,
  helperText,
  required = false,
  inputProps,
  inputRef,
  gridProps = {},
  multiline = false,
  rows,
  rules,
  sx,
  type = "text",
  onKeyDown,
}) => {
  const context = controlProp ? {} : useFormContext();
  const formControl = controlProp || context.control;
  const formErrors = controlProp ? undefined : context.formState.errors;

  if (!formControl) {
    console.warn("FormTextField: No valid control provided.");
    return null;
  }

  return (
    <Grid item {...gridProps}>
      <Controller
        name={name}
        control={formControl}
        rules={rules}
        render={({ field }) => (
          <TextField
            {...field}
            className="w-full"
            label={label}
            variant="outlined"
            required={required}
            inputProps={inputProps}
            inputRef={inputRef}
            multiline={multiline} // Enable multiline
            rows={multiline ? rows : 1} // Default 1 row for single-line, else custom row count
            type={type} // Set the type for number or any other type
            InputLabelProps={{
              shrink: field.value ? true : undefined,
            }}
            error={formErrors?.[name] || error}
            helperText={formErrors?.[name]?.message || helperText}
            sx={sx}
            onKeyDown={onKeyDown}
          />
        )}
      />
    </Grid>
  );
};

export default FormTextField;
