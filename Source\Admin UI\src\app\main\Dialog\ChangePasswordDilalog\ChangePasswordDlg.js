import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import Icon from "@mui/material/Icon";
import IconButton from "@mui/material/IconButton";
import "./ChangePasswordDlg.css";
import ChangePassword from '../../SharedComponents/ChangePassword/ChangePassword';
import { updateChangePassword } from 'src/app/auth/store/userSlice';
import { useDispatch, useSelector } from 'react-redux';
import { ErrorBoundary } from 'react-error-boundary';
import ErrorPage from '../../SharedComponents/ErrorPage/ErrorPage';

const controlStyle = { padding: 0, width: '850px', height: '500px' };
const controlStyleAudio = { padding: 0, width: '305px', height: '50px', };

const ChangePasswordDlg = forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const { t } = useTranslation('laguageConfig');
    const [open, setOpen] = React.useState(false);
    const [data, setData] = React.useState(null);
    const [closeDta, setcloseDta] = React.useState(null);
    const handleClickOpen1 = () => {
        setOpen(true);
    };
    useImperativeHandle(ref, () => ({
        handleClickOpen(data) {
            setData(data)
            handleClickOpen1();
        },
    }));


    const handleClose = () => {
        setOpen(false);
    };
    const formRef = useRef(null);

    const handledata = () => {
        handleClose();
        dispatch(updateChangePassword());
    }

    return (
        <div>
            <Dialog
                fullWidth={true}
                maxWidth='md'
                open={open}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
            >
                <IconButton
                    onClick={handleClose}
                    aria-label="show more"
                    className="closeButton"
                >
                    <Icon>close</Icon>
                </IconButton>
                <DialogTitle id="responsive-dialog-title"><h1><b>{t("changePasswordFor")} {data === null ? "" : data.fname}</b></h1></DialogTitle>
                <DialogContent dividers>
                    <ErrorBoundary
                        FallbackComponent={(props) => <ErrorPage {...props} componentName="ChangePassword" />} onReset={() => { }}>
                        <ChangePassword data={data} isDialog={true} newFunc={handledata} ></ChangePassword>
                    </ErrorBoundary>
                </DialogContent>
            </Dialog>
        </div>
    );
});
export default ChangePasswordDlg;

