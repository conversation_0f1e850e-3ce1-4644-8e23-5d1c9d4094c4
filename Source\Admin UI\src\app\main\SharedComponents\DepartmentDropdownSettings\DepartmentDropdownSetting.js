import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import "./DepartmentDropdownSetting.css";
import { getQuikTipDepartment } from "../../store/quikTipSlice";
import CommonAutocomplete, { handleSelectKeyDown } from '../../SharedComponents/ReuseComponents/CommonAutocomplete';

function DepartmentDropdownSetting(props) {
    const { t } = useTranslation("laguageConfig");
    const dispatch = useDispatch();

    const DepartmentData = useSelector(({ dispatchCallReducer }) => dispatchCallReducer.quiktip.departmentData);
    const [departmentValue, setDepartmentValue] = useState(null);

    useEffect(() => {
        dispatch(getQuikTipDepartment(props.code));
    }, [dispatch, props.code]);

    useEffect(() => {
        if (props.clear) {
            setDepartmentValue(null);
            props.setclear(false);
        } else {
            const selectedDept = DepartmentData.find(dept => dept._id === props.departmentIdValue) || null;
            setDepartmentValue(selectedDept);
        }
    }, [props.clear, props.departmentIdValue, DepartmentData]);

    const handleDepartmentChange = (_, newValue) => {
        props.departmentId(newValue ? newValue._id : "");
        setDepartmentValue(newValue);
    };

    return (
        <div className="flex flex-col justify-center w-full">
            <CommonAutocomplete
                parentCallback={handleDepartmentChange}
                options={(props.flag ? [{ name: "All", _id: "All" }, ...DepartmentData] : DepartmentData) || []}
                value={departmentValue}
                fieldName={t("department")}
                optionLabel={"name"}
                onKeyDown={handleSelectKeyDown}
            />
        </div>
    );
}

export default DepartmentDropdownSetting;
